{"skeleton": {"hash": "EqB1zj4AMSs", "spine": "4.2.40", "x": -80.42, "y": -2.31, "width": 168.8, "height": 295.18, "images": "./Images/Monster_Flegma_Flex/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "low_cntr", "parent": "root", "icon": "straightLine"}, {"name": "Body1", "parent": "low_cntr", "length": 118.09, "rotation": 90, "y": 30}, {"name": "Body2", "parent": "Body1", "length": 118.09, "x": 118.09}, {"name": "Body_Low", "parent": "Body1", "rotation": -90, "x": 45, "scaleX": 0.9546, "scaleY": 0.9546, "icon": "arrows"}, {"name": "Body_High", "parent": "Body2", "rotation": -90, "x": 56.91, "icon": "arrows"}, {"name": "Ear_L", "parent": "Body2", "length": 33.51, "rotation": -68.53, "x": 114.5, "y": -45.07}, {"name": "Ear_R", "parent": "Body2", "length": 33.99, "rotation": 75.68, "x": 115.2, "y": 36.58}, {"name": "eye", "parent": "Body2", "length": 20, "rotation": -90, "x": 44.44, "y": -1.7}, {"name": "brow", "parent": "eye", "x": -0.05, "y": 35.43, "icon": "straightLine"}, {"name": "pupil", "parent": "eye", "x": 1, "y": -1.5}, {"name": "Eyelid", "parent": "eye", "y": 0.6, "icon": "arrowDown"}, {"name": "mouth", "parent": "Body1", "rotation": -90, "x": 50.54, "y": 0.21, "icon": "mouth"}, {"name": "mouth_cntr", "parent": "mouth", "y": 5, "icon": "mouth"}, {"name": "mouth_R", "parent": "mouth", "x": -57.07, "y": -6.37, "icon": "mouth"}, {"name": "mouth_L", "parent": "mouth", "rotation": -1.2, "x": 57.51, "y": -6.64, "icon": "mouth"}, {"name": "blot", "parent": "root", "rotation": -0.04, "x": 0.03, "y": 46.3, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "root", "y": 46.3, "scaleX": -1}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "body_outline", "bone": "low_cntr", "attachment": "body_outline"}, {"name": "body", "bone": "low_cntr", "attachment": "body"}, {"name": "mouth_base", "bone": "low_cntr", "attachment": "mouth_base"}, {"name": "mouth", "bone": "mouth", "attachment": "mouth"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "Eye_red", "bone": "eye", "color": "ffffff00", "attachment": "Eye_red"}, {"name": "pupil", "bone": "pupil", "attachment": "pupil"}, {"name": "eyelid_closed", "bone": "eye"}, {"name": "eyelid", "bone": "eye", "attachment": "eyelid"}, {"name": "brow", "bone": "brow", "attachment": "brow"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop4", "bone": "blot_drop4"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "color": "f74346ff", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [123, 12, 13, 0, 112, 113, 111, 0, 4, 3, 4, 0, 119, 89, 90, 119, 88, 89, 85, 119, 84, 116, 113, 114, 123, 13, 14, 117, 53, 54, 55, 117, 54, 40, 41, 120, 56, 117, 55, 59, 60, 117, 56, 57, 117, 57, 58, 117, 69, 70, 118, 86, 87, 119, 86, 119, 85, 70, 71, 118, 71, 72, 118, 69, 118, 68, 117, 58, 59, 120, 39, 40, 38, 39, 120, 38, 120, 37, 37, 120, 36, 120, 35, 36, 120, 34, 35, 117, 51, 52, 117, 52, 53, 117, 50, 51, 2, 0, 1, 3, 0, 2, 115, 116, 114, 116, 0, 113, 87, 88, 119, 97, 98, 99, 96, 97, 99, 122, 79, 80, 79, 122, 78, 77, 78, 122, 122, 96, 101, 111, 112, 0, 123, 14, 15, 11, 12, 123, 93, 94, 83, 84, 93, 83, 119, 91, 92, 90, 91, 119, 119, 93, 84, 93, 119, 92, 63, 64, 47, 48, 63, 47, 62, 63, 48, 117, 48, 49, 117, 49, 50, 62, 48, 117, 65, 118, 74, 61, 62, 117, 66, 118, 65, 73, 74, 118, 67, 118, 66, 60, 61, 117, 72, 73, 118, 68, 118, 67, 42, 31, 32, 122, 83, 95, 46, 122, 45, 82, 83, 122, 47, 122, 46, 81, 82, 122, 122, 80, 81, 77, 122, 76, 47, 64, 122, 76, 122, 75, 64, 65, 122, 122, 74, 75, 65, 74, 122, 101, 99, 100, 95, 96, 122, 31, 44, 122, 123, 15, 16, 123, 16, 17, 4, 110, 111, 110, 4, 5, 110, 5, 6, 18, 123, 17, 109, 110, 6, 19, 123, 18, 20, 123, 19, 121, 22, 23, 121, 23, 24, 121, 24, 25, 26, 121, 25, 27, 121, 26, 122, 7, 8, 122, 8, 9, 6, 7, 122, 109, 6, 122, 108, 109, 122, 10, 11, 123, 9, 10, 123, 20, 122, 9, 20, 9, 123, 105, 103, 104, 101, 102, 103, 28, 121, 27, 105, 107, 103, 101, 103, 108, 106, 107, 105, 108, 103, 107, 101, 108, 122, 29, 121, 28, 121, 122, 21, 21, 22, 121, 122, 121, 29, 122, 20, 21, 29, 30, 122, 31, 122, 30, 101, 96, 99, 45, 122, 44, 120, 32, 33, 120, 33, 34, 83, 94, 95, 42, 32, 120, 43, 31, 42, 43, 44, 31, 41, 42, 120], "vertices": [2, 16, -91.44, 155.48, 0.02155, 17, -91.44, 166.15, 0.97845, 2, 16, -79.94, 155.42, 0.00946, 17, -79.94, 166.09, 0.99054, 2, 16, -69.91, 150.54, 0.153, 17, -69.91, 161.22, 0.847, 2, 16, -62.67, 142.49, 0.39795, 17, -62.67, 153.16, 0.60205, 2, 16, -58.03, 132.48, 0.70584, 17, -58.03, 143.16, 0.29416, 2, 16, -54.37, 122.1, 0.93146, 17, -54.37, 132.77, 0.06854, 2, 16, -51.37, 116.89, 0.95362, 17, -51.37, 127.57, 0.04638, 2, 16, -15.19, 125.03, 0.7559, 17, -15.19, 135.7, 0.2441, 2, 16, 24.12, 124.82, 0.76179, 17, 24.12, 135.5, 0.23821, 2, 16, 60.95, 114.28, 0.74063, 17, 60.95, 124.96, 0.25937, 2, 16, 64.53, 121.5, 0.64513, 17, 64.53, 132.17, 0.35487, 2, 16, 69.68, 131.96, 0.36729, 17, 69.68, 142.64, 0.63271, 2, 16, 79.67, 136.25, 0.19886, 17, 79.67, 146.93, 0.80114, 2, 16, 94.77, 136.68, 0.10171, 17, 94.77, 147.35, 0.89829, 2, 16, 107.76, 131.63, 0.19647, 17, 107.76, 142.31, 0.80353, 2, 16, 116.99, 122.41, 0.36288, 17, 116.99, 133.08, 0.63712, 2, 16, 120.96, 111.3, 0.46208, 17, 120.96, 121.98, 0.53792, 2, 16, 121.28, 96.99, 0.59174, 17, 121.28, 107.67, 0.40826, 2, 16, 114.38, 88.55, 0.74078, 17, 114.38, 99.22, 0.25922, 2, 16, 106.76, 83.12, 0.86799, 17, 106.76, 93.79, 0.13201, 1, 16, 103.35, 77.75, 1, 1, 16, 109.67, 66.42, 1, 2, 16, 119.94, 67.03, 0.98349, 17, 119.94, 77.71, 0.01651, 2, 16, 132.6, 60.29, 0.81787, 17, 132.6, 70.96, 0.18213, 2, 16, 140.75, 51.32, 0.70315, 17, 140.75, 61.99, 0.29685, 2, 16, 146.01, 38.65, 0.62876, 17, 146.01, 49.32, 0.37124, 2, 16, 145.9, 26.69, 0.60882, 17, 145.9, 37.36, 0.39118, 2, 16, 143.74, 12.61, 0.63067, 17, 143.74, 23.28, 0.36933, 2, 16, 136.06, 4.88, 0.74414, 17, 136.06, 15.55, 0.25586, 2, 16, 126.82, -1.06, 0.88743, 17, 126.82, 9.62, 0.11257, 1, 16, 125.67, -20.51, 1, 2, 16, 121.55, -38.13, 0.92415, 17, 121.55, -27.45, 0.07585, 2, 16, 129.12, -40.4, 0.85626, 17, 129.12, -29.72, 0.14374, 2, 16, 140.41, -43.47, 0.65141, 17, 140.41, -32.79, 0.34859, 2, 16, 147.26, -47.46, 0.45337, 17, 147.26, -36.79, 0.54663, 2, 16, 152.63, -54.78, 0.22414, 17, 152.63, -44.1, 0.77586, 2, 16, 153.74, -61.85, 0.12863, 17, 153.74, -51.18, 0.87137, 2, 16, 151.71, -70.29, 0.06435, 17, 151.71, -59.62, 0.93565, 2, 16, 145.26, -76.07, 0.0759, 17, 145.26, -65.39, 0.9241, 2, 16, 138, -77.7, 0.11319, 17, 138, -67.03, 0.88681, 2, 16, 130.99, -76.37, 0.22246, 17, 130.99, -65.69, 0.77754, 2, 16, 125.92, -70.34, 0.41853, 17, 125.92, -59.66, 0.58147, 2, 16, 121.63, -61.36, 0.66246, 17, 121.63, -50.68, 0.33754, 2, 16, 114.11, -56.22, 0.99426, 17, 114.11, -45.54, 0.00574, 2, 16, 109.39, -62.19, 0.99314, 17, 109.39, -51.52, 0.00686, 2, 16, 104.08, -70.29, 0.99213, 17, 104.08, -59.62, 0.00787, 2, 16, 98.23, -77.3, 0.99754, 17, 98.23, -66.62, 0.00246, 2, 16, 93.97, -85.14, 0.60983, 17, 93.97, -74.46, 0.39017, 2, 16, 97.01, -91.23, 0.67939, 17, 97.01, -80.56, 0.32061, 2, 16, 103.02, -96.06, 0.58167, 17, 103.02, -85.39, 0.41833, 2, 16, 108.98, -98.6, 0.43988, 17, 108.98, -87.92, 0.56012, 2, 16, 115.14, -104.37, 0.3264, 17, 115.14, -93.69, 0.6736, 2, 16, 120.46, -109.51, 0.20757, 17, 120.46, -98.84, 0.79243, 2, 16, 123.61, -117.18, 0.2256, 17, 123.61, -106.51, 0.7744, 2, 16, 123.75, -124.78, 0.33257, 17, 123.75, -114.11, 0.66743, 2, 16, 122.83, -135.18, 0.27292, 17, 122.83, -124.51, 0.72708, 2, 16, 117.73, -142.58, 0.13068, 17, 117.73, -131.9, 0.86932, 1, 17, 109, -138.14, 1, 1, 17, 95.25, -139.18, 1, 2, 16, 84.83, -147.21, 0.09602, 17, 84.83, -136.53, 0.90398, 2, 16, 77.55, -137.79, 0.39368, 17, 77.55, -127.11, 0.60632, 2, 16, 73.21, -127.21, 0.70124, 17, 73.21, -116.53, 0.29876, 2, 16, 71.81, -117.86, 0.86966, 17, 71.81, -107.18, 0.13034, 2, 16, 68.55, -110.05, 0.95079, 17, 68.55, -99.37, 0.04921, 2, 16, 61.36, -108.15, 0.97473, 17, 61.36, -97.47, 0.02527, 2, 16, 55.02, -111.57, 0.95012, 17, 55.02, -100.89, 0.04988, 2, 16, 53.41, -124.06, 0.83978, 17, 53.41, -113.38, 0.16022, 2, 16, 51.04, -133.11, 0.7483, 17, 51.04, -122.43, 0.2517, 2, 16, 43.46, -144.46, 0.50798, 17, 43.46, -133.78, 0.49202, 2, 16, 32.23, -151.98, 0.20256, 17, 32.23, -141.31, 0.79744, 2, 16, 20.02, -153.76, 0.00552, 17, 20.02, -143.08, 0.99448, 1, 17, 7.05, -141.49, 1, 2, 16, -2.78, -142.74, 0.29991, 17, -2.78, -132.07, 0.70009, 2, 16, -8.4, -131.45, 0.61942, 17, -8.4, -120.77, 0.38058, 2, 16, -13.03, -122.99, 0.74609, 17, -13.03, -112.31, 0.25391, 2, 16, -31.22, -117.17, 0.81636, 17, -31.22, -106.5, 0.18364, 2, 16, -45.05, -112.94, 0.63451, 17, -45.05, -102.26, 0.36549, 2, 16, -57.27, -107.16, 0.59263, 17, -57.27, -96.48, 0.40737, 2, 16, -68.13, -107.64, 0.49938, 17, -68.13, -96.96, 0.50062, 2, 16, -76.58, -106.52, 0.44983, 17, -76.58, -95.84, 0.55017, 2, 16, -83.33, -100.13, 0.5575, 17, -83.33, -89.45, 0.4425, 2, 16, -88.15, -90.15, 0.81245, 17, -88.15, -79.48, 0.18755, 2, 16, -88.26, -80.44, 0.9861, 17, -88.26, -69.77, 0.0139, 2, 16, -95.72, -73.06, 0.99035, 17, -95.72, -62.38, 0.00965, 2, 16, -104.3, -81.53, 0.52724, 17, -104.3, -70.85, 0.47276, 1, 17, -113.86, -84.25, 1, 1, 17, -122.68, -93, 1, 1, 17, -137.76, -93.12, 1, 2, 16, -142.75, -98.36, 0.08531, 17, -142.75, -87.68, 0.91469, 2, 16, -145.35, -88.18, 0.33535, 17, -145.35, -77.5, 0.66465, 2, 16, -141.21, -79.25, 0.46148, 17, -141.21, -68.57, 0.53852, 2, 16, -136.39, -71.78, 0.55661, 17, -136.39, -61.1, 0.44339, 2, 16, -124.53, -67.19, 0.68332, 17, -124.53, -56.51, 0.31668, 2, 16, -111.98, -64.08, 0.77501, 17, -111.98, -53.4, 0.22499, 2, 16, -107.61, -58.37, 0.83963, 17, -107.61, -47.7, 0.16037, 2, 16, -108.21, -51.16, 0.84286, 17, -108.21, -40.48, 0.15714, 2, 16, -113.09, -45.61, 0.39981, 17, -113.09, -34.93, 0.60019, 2, 16, -121.92, -41.62, 0.33627, 17, -121.92, -30.95, 0.66373, 2, 16, -129.74, -32.25, 0.50107, 17, -129.74, -21.57, 0.49893, 2, 16, -132.4, -23.08, 0.61634, 17, -132.4, -12.4, 0.38366, 2, 16, -132.14, -9.04, 0.65964, 17, -132.14, 1.64, 0.34036, 2, 16, -128.12, -1.22, 0.65438, 17, -128.12, 9.45, 0.34562, 2, 16, -132.14, 5.32, 0.6746, 17, -132.14, 16, 0.3254, 2, 16, -135.89, 19.92, 0.90838, 17, -135.89, 30.6, 0.09162, 2, 16, -135.98, 38.73, 0.80863, 17, -135.98, 49.4, 0.19137, 2, 16, -131.5, 51.11, 0.71293, 17, -131.5, 61.79, 0.28707, 2, 16, -122.28, 64.09, 0.68699, 17, -122.28, 74.77, 0.31301, 2, 16, -110.65, 72.32, 0.78083, 17, -110.65, 83, 0.21917, 2, 16, -96.51, 77.3, 0.94418, 17, -96.51, 87.98, 0.05582, 2, 16, -89.66, 86.44, 0.67426, 17, -89.66, 97.12, 0.32574, 2, 16, -94.5, 92.74, 0.67915, 17, -94.5, 103.42, 0.32085, 2, 16, -103.85, 97.95, 0.53234, 17, -103.85, 108.62, 0.46766, 2, 16, -113.25, 104.93, 0.55156, 17, -113.25, 115.61, 0.44844, 2, 16, -121.49, 116.98, 0.28889, 17, -121.49, 127.66, 0.71111, 2, 16, -121.75, 134.38, 0.03582, 17, -121.75, 145.06, 0.96418, 1, 17, -115.8, 157.18, 1, 1, 17, -104.8, 164.73, 1, 2, 16, 100.89, -121.94, 0.71328, 17, 100.89, -111.26, 0.28672, 2, 16, 21.91, -128.43, 0.72168, 17, 21.91, -117.75, 0.27832, 2, 16, -125.41, -82.66, 0.48636, 17, -125.41, -71.99, 0.51364, 2, 16, 138.17, -59.25, 0.48188, 17, 138.17, -48.58, 0.51812, 2, 16, 122.26, 37.92, 0.90859, 17, 122.26, 48.59, 0.09141, 2, 16, 1, 9.05, 0.31422, 17, 1, 19.73, 0.68578, 2, 16, 94.76, 111.57, 0.81578, 17, 94.76, 122.25, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"color": "f74346ff", "width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"color": "f74346ff", "width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"color": "f74346ff", "width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"color": "f74346ff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"color": "f74346ff", "width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"color": "f74346ff", "width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"color": "f74346ff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"color": "f74346ff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"color": "f74346ff", "rotation": -0.04, "width": 30, "height": 29}}, "body": {"body": {"type": "mesh", "uvs": [0.93786, 0.00339, 0.99387, 0.03445, 0.99382, 0.08537, 0.96318, 0.09768, 0.89919, 0.09893, 0.83694, 0.10793, 0.7843, 0.13562, 0.75557, 0.1746, 0.75647, 0.21939, 0.76688, 0.26511, 0.78423, 0.30984, 0.80159, 0.35457, 0.81894, 0.39931, 0.8363, 0.44404, 0.86495, 0.50273, 0.88876, 0.55491, 0.91742, 0.6113, 0.93847, 0.66578, 0.95067, 0.72301, 0.95045, 0.76721, 0.95023, 0.80986, 0.93508, 0.84317, 0.91441, 0.87494, 0.95107, 0.90513, 0.98016, 0.93877, 0.97873, 0.9765, 0.95307, 0.99659, 0.03721, 0.99681, 0.00569, 0.97816, 0.00612, 0.93001, 0.03152, 0.90227, 0.07002, 0.87569, 0.03683, 0.83043, 0.01479, 0.78017, 0.01232, 0.73137, 0.01217, 0.68059, 0.02285, 0.63037, 0.0473, 0.57518, 0.07175, 0.52054, 0.09843, 0.47084, 0.12288, 0.42814, 0.14831, 0.38262, 0.17051, 0.33832, 0.18858, 0.28835, 0.20578, 0.2437, 0.22015, 0.19456, 0.22144, 0.14833, 0.19199, 0.12145, 0.14512, 0.10812, 0.09845, 0.10548, 0.04569, 0.10477, 0.01872, 0.08923, 0.01841, 0.03792, 0.06202, 0.01036, 0.10616, 0.01032, 0.18639, 0.03032, 0.27706, 0.04453, 0.38712, 0.04553, 0.49718, 0.04653, 0.60724, 0.04754, 0.7173, 0.04854, 0.80855, 0.03457, 0.89458, 0.00318, 0.50059, 0.72211, 0.49104, 0.3513], "triangles": [59, 7, 58, 58, 46, 57, 46, 56, 57, 47, 55, 56, 6, 59, 60, 24, 26, 23, 25, 26, 24, 22, 23, 26, 63, 22, 26, 27, 30, 31, 29, 30, 27, 28, 29, 27, 27, 31, 63, 27, 63, 26, 62, 0, 1, 2, 3, 1, 1, 4, 62, 3, 4, 1, 61, 62, 4, 5, 61, 4, 60, 61, 5, 49, 50, 51, 51, 52, 49, 54, 52, 53, 49, 54, 55, 54, 49, 52, 48, 49, 55, 48, 55, 47, 46, 47, 56, 8, 45, 7, 6, 7, 59, 7, 45, 58, 58, 45, 46, 8, 64, 45, 44, 45, 64, 43, 44, 64, 42, 43, 64, 41, 42, 64, 63, 64, 14, 9, 64, 8, 10, 64, 9, 11, 64, 10, 12, 64, 11, 13, 64, 12, 14, 64, 13, 40, 41, 64, 64, 39, 40, 63, 39, 64, 6, 60, 5, 63, 14, 15, 63, 15, 16, 63, 16, 17, 38, 39, 63, 37, 38, 63, 36, 37, 63, 35, 36, 63, 63, 17, 18, 34, 35, 63, 19, 63, 18, 33, 34, 63, 20, 63, 19, 32, 33, 63, 21, 63, 20, 22, 63, 21, 31, 32, 63], "vertices": [3, 7, -100.31, -49.1, 0.02247, 6, 34.36, 11.7, 0.89997, 3, 137.96, -72.76, 0.07756, 5, 4, 81.72, 202.1, 1e-05, 5, 81.72, 72.1, 0.01241, 7, -111.2, -42.65, 0.02273, 6, 39.42, 0.09, 0.93916, 3, 129.02, -81.72, 0.0257, 4, 4, 81.71, 187.44, 3e-05, 5, 81.71, 57.44, 0.04046, 7, -114.82, -28.44, 0.02286, 6, 34.05, -13.55, 0.93665, 4, 4, 76.81, 183.89, 5e-05, 5, 76.81, 53.89, 0.07089, 7, -110.95, -23.79, 0.02286, 6, 28.19, -15.06, 0.90621, 5, 4, 66.57, 183.53, 0.00013, 5, 66.57, 53.53, 0.10105, 7, -101.12, -20.91, 0.02365, 6, 18.53, -11.64, 0.83786, 3, 110.44, -66.57, 0.03732, 5, 4, 56.61, 180.94, 0.00113, 5, 56.61, 50.94, 0.26604, 7, -92.11, -15.94, 0.0414, 6, 8.31, -10.41, 0.62397, 3, 107.85, -56.61, 0.06746, 5, 4, 48.19, 172.97, 0.00515, 5, 48.19, 42.97, 0.60833, 7, -85.92, -6.13, 0.02885, 6, -2.45, -14.75, 0.19588, 3, 99.84, -48.18, 0.16179, 4, 4, 43.59, 161.74, 0.00829, 5, 43.59, 31.74, 0.85321, 7, -84.25, 5.89, 0.02134, 6, -10.83, -23.51, 0.11716, 4, 4, 43.74, 148.84, 0.0129, 5, 43.74, 18.84, 0.97678, 7, -87.58, 18.35, 0.00512, 6, -15.42, -35.57, 0.0052, 4, 4, 45.4, 135.67, 0.01778, 5, 45.4, 5.67, 0.98188, 7, -92.45, 30.7, 0.00034, 6, -18.69, -48.44, 0, 4, 4, 48.18, 122.79, 0.03311, 5, 48.18, -7.21, 0.9668, 7, -98.32, 42.49, 9e-05, 6, -20.82, -61.44, 0, 3, 4, 50.96, 109.91, 0.06641, 5, 50.96, -20.09, 0.93358, 7, -104.2, 54.29, 1e-05, 2, 4, 53.73, 97.02, 0.12556, 5, 53.73, -32.98, 0.87444, 2, 4, 56.51, 84.14, 0.23068, 5, 56.51, -45.86, 0.76932, 3, 4, 61.09, 67.24, 0.3966, 5, 61.09, -62.76, 0.59948, 6, -29.13, -117.86, 0.00392, 5, 4, 64.9, 52.21, 0.50986, 5, 64.9, -77.79, 0.47029, 7, -131.99, 106.74, 0, 6, -31.08, -133.24, 0.00474, 1, 63.24, 125.88, 0.01512, 5, 4, 69.49, 35.97, 0.65368, 5, 69.49, -94.03, 0.32911, 7, -140.45, 121.34, 0, 6, -32.76, -150.04, 0.00325, 1, 67.29, 109.83, 0.01396, 4, 4, 72.86, 20.28, 0.72936, 5, 72.86, -109.72, 0.23495, 6, -35.37, -165.87, 0.00178, 1, 70.27, 94.56, 0.0339, 4, 4, 74.81, 3.8, 0.80386, 5, 74.81, -126.2, 0.1536, 6, -39.58, -181.92, 0.00061, 1, 71.88, 78.65, 0.04193, 4, 4, 74.77, -8.93, 0.81713, 5, 74.77, -138.93, 0.10179, 6, -44.28, -193.76, 0.00018, 1, 71.68, 66.44, 0.0809, 4, 4, 74.74, -21.21, 0.77695, 5, 74.74, -151.21, 0.06302, 6, -48.8, -205.17, 7e-05, 1, 71.53, 54.7, 0.15996, 4, 4, 72.31, -30.81, 0.71799, 5, 72.31, -160.81, 0.03671, 6, -54.57, -213.21, 0, 1, 69.14, 45.55, 0.2453, 4, 4, 69.01, -39.96, 0.58514, 5, 69.01, -169.96, 0.01981, 6, -61, -220.52, 0, 1, 65.93, 36.82, 0.39505, 4, 4, 74.87, -48.65, 0.46286, 5, 74.87, -178.65, 0.0146, 6, -58.72, -230.76, 0, 1, 71.52, 28.53, 0.52254, 4, 4, 79.53, -58.34, 0.27446, 5, 79.53, -188.34, 0.00695, 6, -57.93, -241.48, 0, 1, 75.94, 19.29, 0.71859, 4, 4, 79.3, -69.21, 0.13443, 5, 79.3, -199.21, 0.00276, 6, -62.12, -251.51, 0, 1, 75.71, 8.93, 0.8628, 4, 4, 75.19, -74.99, 0.09443, 5, 75.19, -204.99, 0.00267, 6, -68.06, -255.39, 0, 1, 71.79, 3.4, 0.9029, 5, 4, -71.35, -75.06, 0.0758, 5, -71.35, -205.06, 0.00861, 7, -31.46, 263.76, 0, 6, -204.46, -201.82, 5e-05, 1, -68.13, 3.32, 0.91554, 4, 4, -76.39, -69.68, 0.13685, 5, -76.39, -199.68, 0.0035, 7, -25.24, 259.8, 0, 1, -72.93, 8.47, 0.85964, 4, 4, -76.32, -55.82, 0.32304, 5, -76.32, -185.82, 0.0139, 7, -21.88, 246.35, 0, 1, -72.9, 21.68, 0.66306, 5, 4, -72.26, -47.83, 0.45922, 5, -72.26, -177.83, 0.02874, 7, -23.84, 237.61, 0, 6, -195.34, -176.15, 2e-05, 1, -69.06, 29.29, 0.51201, 5, 4, -66.1, -40.17, 0.53222, 5, -66.1, -170.17, 0.04218, 7, -27.91, 228.66, 0, 6, -186.81, -171.28, 0.00012, 1, -63.21, 36.58, 0.42548, 5, 4, -71.41, -27.14, 0.65073, 5, -71.41, -157.14, 0.06846, 7, -19.54, 217.35, 2e-05, 6, -186.98, -157.2, 0.00018, 1, -68.36, 49.02, 0.28061, 5, 4, -74.93, -12.67, 0.74427, 5, -74.93, -142.67, 0.1017, 7, -12.55, 204.2, 7e-05, 6, -184.96, -142.44, 0.0002, 1, -71.84, 62.86, 0.15377, 5, 4, -75.33, 1.39, 0.78027, 5, -75.33, -128.61, 0.14261, 7, -8.69, 190.68, 0.00014, 6, -180.19, -129.22, 0.00023, 1, -72.35, 76.33, 0.07675, 5, 4, -75.35, 16.01, 0.77009, 5, -75.35, -113.99, 0.20902, 7, -5.05, 176.51, 0.00028, 6, -174.86, -115.6, 0.00024, 1, -72.59, 90.43, 0.02037, 5, 4, -73.64, 30.48, 0.71217, 5, -73.64, -99.52, 0.28363, 7, -3.12, 162.07, 0.00035, 6, -167.97, -102.76, 0.00023, 1, -71.2, 104.47, 0.00362, 5, 4, -69.73, 46.37, 0.61513, 5, -69.73, -83.63, 0.38425, 7, -2.98, 145.71, 0.00028, 6, -158.51, -89.4, 0.00024, 1, -67.72, 120.04, 9e-05, 4, 4, -65.82, 62.11, 0.49437, 5, -65.82, -67.89, 0.50528, 7, -2.88, 129.49, 0.00011, 6, -149.11, -76.19, 0.00023, 2, 4, -61.55, 76.42, 0.37062, 5, -61.55, -53.58, 0.62938, 2, 4, -57.64, 88.72, 0.26061, 5, -57.64, -41.28, 0.73939, 2, 4, -53.57, 101.83, 0.16852, 5, -53.57, -28.17, 0.83148, 4, 4, -50.02, 114.59, 0.10054, 5, -50.02, -15.41, 0.89946, 7, -5.21, 74.73, 0, 6, -115.2, -33.14, 0, 4, 4, -47.13, 128.98, 0.04991, 5, -47.13, -1.02, 0.95007, 7, -4.45, 60.07, 1e-05, 6, -107.25, -20.8, 0, 4, 4, -44.37, 141.84, 0.02539, 5, -44.37, 11.84, 0.97459, 7, -3.94, 46.93, 2e-05, 6, -99.98, -9.84, 1e-05, 4, 4, -42.08, 155.99, 0.0127, 5, -42.08, 25.99, 0.94693, 7, -2.66, 32.65, 0.04035, 6, -92.66, 2.49, 2e-05, 5, 4, -41.87, 169.31, 0.00721, 5, -41.87, 39.31, 0.733, 7, 0.43, 19.7, 0.08164, 6, -87.59, 14.8, 2e-05, 3, 96.16, 41.85, 0.17813, 5, 4, -46.58, 177.05, 0.0031, 5, -46.58, 47.05, 0.55103, 7, 6.91, 13.37, 0.27611, 6, -89.15, 23.73, 0.00808, 3, 103.94, 46.57, 0.16168, 5, 4, -54.08, 180.89, 0.00081, 5, -54.08, 50.89, 0.2404, 7, 15.13, 11.5, 0.67313, 6, -94.72, 30.05, 0.00885, 3, 107.79, 54.08, 0.07682, 5, 4, -61.55, 181.65, 0.00065, 5, -61.55, 51.65, 0.19247, 7, 22.55, 12.61, 0.77464, 6, -101.39, 33.49, 0.00472, 3, 108.56, 61.55, 0.02753, 3, 4, -69.99, 181.85, 0.00037, 5, -69.99, 51.85, 0.12848, 7, 30.78, 14.5, 0.87116, 5, 4, -74.3, 186.33, 0.00022, 5, -74.3, 56.33, 0.06705, 7, 36.07, 11.23, 0.92762, 6, -111.55, 42.51, 0.00075, 3, 113.24, 74.3, 0.00435, 4, 4, -74.35, 201.1, 0, 5, -74.35, 71.1, 0.00019, 7, 39.77, -3.07, 0.96426, 3, 128.02, 74.35, 0.03555, 4, 4, -67.38, 209.04, 0, 5, -67.38, 79.04, 9e-05, 7, 34.98, -12.49, 0.92302, 3, 135.95, 67.38, 0.07689, 5, 4, -60.31, 209.05, 0, 5, -60.31, 79.05, 8e-05, 7, 28.14, -14.25, 0.80505, 6, -90.21, 58.54, 0.02159, 3, 135.97, 60.31, 0.17328, 5, 4, -47.48, 203.29, 0.00019, 5, -47.48, 73.29, 0.05584, 7, 14.27, -11.84, 0.55213, 6, -80.38, 48.48, 0.04217, 3, 130.2, 47.48, 0.34968, 5, 4, -32.97, 199.2, 0.00068, 5, -32.97, 69.2, 0.11922, 7, -0.8, -11.47, 0.16092, 6, -68.37, 39.37, 0.02852, 3, 126.11, 32.97, 0.69066, 5, 4, -15.36, 198.91, 0.00133, 5, -15.36, 68.91, 0.12424, 7, -17.93, -15.54, 0.00681, 6, -52.09, 32.65, 0, 3, 125.81, 15.36, 0.86761, 5, 4, 2.25, 198.62, 0.00137, 5, 2.25, 68.62, 0.12806, 7, -35.06, -19.62, 0.00702, 6, -35.81, 25.94, 0, 3, 125.52, -2.25, 0.86355, 5, 4, 19.86, 198.33, 0.0012, 5, 19.86, 68.33, 0.12758, 7, -52.2, -23.7, 0.00857, 6, -19.53, 19.23, 0.03333, 3, 125.24, -19.86, 0.82932, 5, 4, 37.47, 198.04, 0.00115, 5, 37.47, 68.04, 0.12191, 7, -69.33, -27.77, 0.05362, 6, -3.24, 12.51, 0.14131, 3, 124.95, -37.47, 0.68201, 5, 4, 52.07, 202.07, 7e-05, 5, 52.07, 72.07, 0.03329, 7, -82.48, -35.28, 0.07918, 6, 11.82, 10.91, 0.6337, 3, 128.98, -52.07, 0.25376, 3, 7, -93.58, -47.45, 0.02217, 6, 27.94, 14.29, 0.84125, 3, 138.02, -65.83, 0.13658, 5, 4, 2.79, 4.06, 0.60625, 5, 2.79, -125.94, 0.21069, 7, -83.72, 168.76, 0, 6, -106.51, -155.33, 0.00203, 1, 2.69, 78.91, 0.18103, 4, 4, 1.27, 110.85, 0.08866, 5, 1.27, -19.15, 0.91133, 7, -55.82, 65.67, 1e-05, 6, -68.85, -55.38, 0], "hull": 63, "edges": [0, 124, 0, 2, 2, 4, 4, 6, 10, 12, 16, 18, 48, 50, 50, 52, 52, 54, 56, 58, 80, 82, 106, 108, 54, 56, 44, 46, 46, 48, 40, 42, 42, 44, 58, 60, 60, 62, 62, 64, 64, 66, 38, 40, 34, 36, 36, 38, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 52, 126, 76, 78, 78, 80, 88, 90, 90, 92, 82, 84, 84, 86, 86, 88, 92, 94, 94, 96, 102, 104, 100, 102, 104, 106, 120, 122, 122, 124, 108, 110, 110, 112, 116, 118, 118, 120, 112, 114, 114, 116, 6, 8, 8, 10, 96, 98, 98, 100, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 160, "height": 288}}, "body_outline": {"body_outline": {"type": "mesh", "uvs": [0.06394, 0.01278, 0.12929, 0.00614, 0.19813, 0.02156, 0.29438, 0.04312, 0.41427, 0.04345, 0.55284, 0.04392, 0.68964, 0.04642, 0.76907, 0.03611, 0.8181, 0.015, 0.87248, 0.00051, 0.94289, 0.01172, 0.99423, 0.04641, 0.99815, 0.0994, 0.96593, 0.12664, 0.89804, 0.1386, 0.84254, 0.13906, 0.79626, 0.15727, 0.77787, 0.18432, 0.77797, 0.23456, 0.79563, 0.29056, 0.81253, 0.34677, 0.83389, 0.4035, 0.85525, 0.46022, 0.8833, 0.51516, 0.91135, 0.5701, 0.93138, 0.62631, 0.9514, 0.68253, 0.95536, 0.72821, 0.95759, 0.76943, 0.94354, 0.81347, 0.92776, 0.85306, 0.9587, 0.87813, 0.97733, 0.91491, 0.97913, 0.95415, 0.93581, 0.99309, 0.05005, 0.9934, 0.00821, 0.96511, 0, 0.92708, 0.01897, 0.88438, 0.05624, 0.85301, 0.01863, 0.78689, 0.00542, 0.71546, 0.00678, 0.66791, 0.02563, 0.60821, 0.05126, 0.54852, 0.08129, 0.48902, 0.11133, 0.42952, 0.14266, 0.37089, 0.16767, 0.31187, 0.18881, 0.25131, 0.20538, 0.19497, 0.19587, 0.16127, 0.15885, 0.14325, 0.08862, 0.14304, 0.0347, 0.13115, 0.01732, 0.09525, 0.01672, 0.0462], "triangles": [4, 51, 3, 17, 5, 6, 50, 4, 5, 6, 7, 16, 35, 29, 30, 35, 39, 25, 32, 30, 31, 35, 37, 38, 35, 30, 34, 34, 32, 33, 35, 36, 37, 35, 38, 39, 32, 34, 30, 51, 52, 3, 52, 2, 3, 2, 52, 55, 53, 55, 52, 2, 55, 1, 0, 1, 56, 53, 54, 55, 1, 55, 56, 16, 7, 15, 14, 15, 8, 15, 7, 8, 11, 14, 8, 12, 13, 14, 10, 8, 9, 12, 14, 11, 10, 11, 8, 45, 23, 44, 17, 6, 16, 45, 22, 23, 45, 46, 22, 46, 21, 22, 46, 47, 21, 47, 20, 21, 47, 48, 20, 48, 19, 20, 48, 49, 19, 49, 18, 19, 49, 50, 18, 50, 17, 18, 17, 50, 5, 4, 50, 51, 24, 39, 23, 27, 29, 35, 24, 25, 39, 41, 23, 40, 43, 23, 42, 44, 23, 43, 29, 27, 28, 26, 35, 25, 27, 35, 26, 23, 39, 40, 42, 23, 41], "vertices": [1, 7, 41.02, -16.25, 1, 2, 7, 30.62, -20.97, 0.88571, 3, 143.09, 61.06, 0.11429, 3, 6, -78.98, 56.79, 0.05587, 7, 18, -19.4, 0.61842, 3, 138.45, 49.22, 0.32571, 3, 6, -65.95, 44.7, 0.03984, 7, 0.36, -17.21, 0.16301, 3, 131.96, 32.67, 0.79714, 1, 3, 131.86, 12.05, 1, 1, 3, 131.72, -11.79, 1, 3, 6, -3.05, 18.89, 0.14335, 7, -65.76, -33.06, 0.0595, 3, 130.97, -35.32, 0.79714, 3, 6, 10.8, 16.78, 0.57486, 7, -78.23, -39.45, 0.09942, 3, 134.07, -48.98, 0.32571, 3, 6, 20.98, 19.61, 0.78668, 7, -84.83, -47.69, 0.02189, 3, 140.42, -57.41, 0.19143, 3, 6, 31.28, 20.24, 0.86431, 7, -92.81, -54.23, 0.02141, 3, 144.78, -66.77, 0.11429, 2, 6, 41.31, 12.67, 0.97714, 7, -105.38, -53.96, 0.02286, 2, 6, 45.71, -0.28, 0.97714, 7, -116.52, -46.02, 0.02286, 4, 4, 88.38, 188.11, 0, 5, 88.38, 58.11, 0.00019, 6, 40.5, -15.37, 0.97696, 7, -121.12, -30.74, 0.02286, 4, 4, 82.84, 179.91, 0, 5, 82.84, 49.91, 0.00269, 6, 32.34, -20.97, 0.97446, 7, -117.78, -21.42, 0.02286, 4, 4, 71.16, 176.31, 8e-05, 5, 71.16, 46.31, 0.10684, 6, 20.16, -20.05, 0.87023, 7, -107.36, -15.05, 0.02286, 4, 4, 61.62, 176.17, 0.0004, 5, 61.62, 46.17, 0.18188, 6, 11.22, -16.68, 0.78996, 7, -98.14, -12.55, 0.02777, 4, 4, 53.66, 170.69, 0.00312, 5, 53.66, 40.69, 0.58779, 6, 1.81, -18.87, 0.38535, 7, -91.78, -5.27, 0.02374, 4, 4, 50.49, 162.55, 0.00793, 5, 50.49, 32.55, 0.84078, 6, -4.11, -25.29, 0.13143, 7, -90.73, 3.4, 0.01985, 3, 4, 50.51, 147.42, 0.01334, 5, 50.51, 17.42, 0.98576, 7, -94.49, 18.05, 0.0009, 3, 4, 53.55, 130.57, 0.01859, 5, 53.55, 0.57, 0.98124, 7, -101.6, 33.63, 0.00017, 3, 4, 56.46, 113.65, 0.04587, 5, 56.46, -16.35, 0.95412, 7, -108.61, 49.31, 1e-05, 2, 4, 60.13, 96.57, 0.12412, 5, 60.13, -33.43, 0.87588, 2, 4, 63.8, 79.5, 0.26508, 5, 63.8, -50.5, 0.73492, 3, 4, 68.63, 62.96, 0.43785, 5, 68.63, -67.04, 0.55669, 6, -23.68, -124.6, 0.00546, 3, 4, 73.45, 46.43, 0.59967, 5, 73.45, -83.57, 0.3961, 6, -25.24, -141.76, 0.00423, 3, 4, 76.9, 29.5, 0.72509, 5, 76.9, -100.5, 0.27259, 6, -28.23, -158.76, 0.00232, 4, 4, 80.34, 12.58, 0.81349, 5, 80.34, -117.42, 0.18534, 6, -31.22, -175.77, 0.00091, 1, 77.31, 87.11, 0.00026, 4, 4, 81.02, -1.16, 0.86646, 5, 81.02, -131.16, 0.12115, 6, -35.62, -188.82, 0.00023, 1, 77.74, 73.88, 0.01215, 4, 4, 81.41, -13.57, 0.86376, 5, 81.41, -143.57, 0.07553, 6, -39.8, -200.51, 3e-05, 1, 77.95, 62, 0.06068, 4, 4, 78.99, -26.83, 0.77494, 5, 78.99, -156.83, 0.04179, 6, -46.9, -211.96, 0, 1, 75.54, 49.34, 0.18326, 4, 4, 76.28, -38.75, 0.60163, 5, 76.28, -168.75, 0.01909, 6, -53.79, -222.05, 0, 1, 72.87, 37.98, 0.37928, 3, 4, 81.6, -46.29, 0.37247, 5, 81.6, -176.29, 0.00518, 1, 77.92, 30.79, 0.62235, 3, 4, 84.8, -57.36, 0.18122, 5, 84.8, -187.36, 0.00079, 1, 80.96, 20.24, 0.81798, 2, 4, 85.11, -69.17, 0.06046, 1, 81.25, 8.97, 0.93954, 2, 4, 77.66, -80.89, 0.01203, 1, 74.13, -2.22, 0.98797, 2, 4, -74.69, -80.99, 0.01202, 1, -71.3, -2.31, 0.98798, 2, 4, -81.89, -72.47, 0.06044, 1, -78.17, 5.82, 0.93956, 3, 4, -83.3, -61.02, 0.18059, 5, -83.3, -191.02, 0.00137, 1, -79.53, 16.74, 0.81804, 3, 4, -80.04, -48.17, 0.36853, 5, -80.04, -178.17, 0.00902, 1, -76.44, 28.99, 0.62245, 4, 4, -73.63, -38.73, 0.59341, 5, -73.63, -168.73, 0.03421, 7, -20.26, 229.13, 0, 1, -70.38, 37.98, 0.37237, 4, 4, -80.1, -18.83, 0.74654, 5, -80.1, -148.83, 0.07557, 7, -9.07, 211.45, 5e-05, 1, -76.7, 56.97, 0.17783, 4, 4, -82.37, 2.67, 0.80725, 5, -82.37, -127.33, 0.13546, 7, -1.55, 191.18, 0.00017, 1, -79.08, 77.56, 0.05712, 4, 4, -82.13, 16.99, 0.77933, 5, -82.13, -113.01, 0.20935, 7, 1.76, 177.25, 0.00033, 1, -79.13, 91.36, 0.01099, 4, 4, -78.89, 34.95, 0.69701, 5, -78.89, -95.05, 0.30239, 7, 3.07, 159.04, 0.00038, 1, -76.34, 108.83, 0.00023, 3, 4, -74.48, 52.92, 0.57243, 5, -74.48, -77.08, 0.42733, 7, 3.24, 140.54, 0.00024, 2, 4, -69.32, 70.83, 0.4241, 5, -69.32, -59.17, 0.5759, 2, 4, -64.15, 88.74, 0.26432, 5, -64.15, -41.26, 0.73568, 2, 4, -58.76, 106.39, 0.14124, 5, -58.76, -23.61, 0.85876, 3, 4, -54.46, 124.15, 0.06099, 5, -54.46, -5.85, 0.93901, 6, -115.84, -22.61, 0, 3, 4, -50.82, 142.38, 0.0259, 5, -50.82, 12.38, 0.97409, 6, -105.78, -6.98, 1e-05, 4, 4, -47.97, 159.34, 0.01008, 5, -47.97, 29.34, 0.93847, 6, -96.92, 7.76, 3e-05, 7, 3.88, 30.87, 0.05143, 3, 4, -49.61, 169.48, 0.00415, 5, -49.61, 39.48, 0.72626, 7, 7.97, 21.45, 0.26959, 3, 4, -55.98, 174.91, 0.00097, 5, -55.98, 44.91, 0.29004, 7, 15.49, 17.76, 0.70898, 3, 4, -68.06, 174.97, 0.00025, 5, -68.06, 44.97, 0.17296, 7, 27.21, 20.69, 0.82679, 3, 4, -77.33, 178.55, 1e-05, 5, -77.33, 48.55, 0.02643, 7, 37.08, 19.52, 0.97356, 3, 4, -80.32, 189.36, 0, 5, -80.32, 59.36, 0.00042, 7, 42.65, 9.79, 0.99958, 1, 7, 46.4, -4.49, 1], "hull": 57, "edges": [6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 70, 72, 72, 74, 82, 84, 100, 102, 78, 80, 80, 82, 74, 76, 76, 78, 62, 64, 64, 66, 66, 68, 68, 70, 60, 62, 28, 30, 34, 36, 30, 32, 32, 34, 24, 26, 26, 28, 14, 16, 16, 18, 8, 10, 10, 12, 4, 6, 112, 0, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 2, 2, 4, 108, 110, 110, 112, 106, 108, 102, 104, 104, 106, 96, 98, 98, 100, 92, 94, 94, 96, 88, 90, 90, 92, 84, 86, 86, 88], "width": 172, "height": 301}}, "brow": {"brow": {"width": 54, "height": 19}}, "eye": {"eye": {"width": 56, "height": 79}}, "eyelid": {"eyelid": {"type": "mesh", "uvs": [0.65152, 0.02533, 0.82566, 0.17233, 0.92972, 0.4006, 0.97751, 0.56333, 0.97763, 0.82659, 0.8632, 0.92374, 0.72823, 0.9751, 0.51037, 0.97491, 0.29251, 0.97471, 0.15735, 0.89717, 0.02135, 0.78224, 0.02171, 0.57273, 0.08459, 0.34303, 0.19806, 0.15216, 0.34847, 0.02518, 0.52509, 0.60235], "triangles": [5, 6, 15, 15, 6, 7, 7, 8, 15, 8, 9, 15, 5, 15, 4, 9, 10, 15, 15, 3, 4, 10, 11, 15, 11, 12, 15, 12, 13, 15, 13, 14, 15, 15, 2, 3, 15, 1, 2, 15, 0, 1, 15, 14, 0], "vertices": [1, 8, 6.75, 36.86, 1, 1, 8, 14.52, 31.06, 1, 2, 8, 19.16, 22.03, 0.97339, 11, 19.16, 21.44, 0.02661, 1, 8, 21.29, 15.6, 1, 2, 8, 21.29, 5.2, 0.56327, 11, 21.29, 4.6, 0.43673, 2, 8, 16.19, 1.36, 0.31751, 11, 16.19, 0.76, 0.68249, 2, 8, 10.17, -0.67, 0.20508, 11, 10.17, -1.27, 0.79492, 2, 8, 0.46, -0.66, 0.15203, 11, 0.46, -1.26, 0.84797, 2, 8, -9.25, -0.65, 0.17995, 11, -9.25, -1.25, 0.82005, 2, 8, -15.28, 2.41, 0.32369, 11, -15.28, 1.81, 0.67631, 2, 8, -21.34, 6.95, 0.62553, 11, -21.34, 6.35, 0.37447, 1, 8, -21.33, 15.23, 1, 2, 8, -18.52, 24.31, 0.94736, 11, -18.52, 23.71, 0.05264, 1, 8, -13.46, 31.85, 1, 1, 8, -6.76, 36.87, 1, 2, 8, 1.12, 14.06, 0.50155, 11, 1.12, 13.46, 0.49845], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 16, 18, 18, 20, 30, 20, 8, 30, 12, 14, 14, 16], "width": 44, "height": 39}}, "eyelid_closed": {"eyelid_closed": {"x": -0.05, "y": 3.43, "width": 44, "height": 65}}, "Eye_red": {"Eye_red": {"x": -0.05, "y": 3.43, "width": 44, "height": 65}}, "mouth": {"mouth": {"type": "mesh", "uvs": [0.37275, 0.03934, 0.50369, 0.039, 0.59294, 0.03877, 0.6822, 0.03854, 0.75863, 0.13252, 0.83507, 0.22651, 0.97437, 0.4625, 0.99775, 0.59385, 0.99135, 0.71307, 0.96571, 0.74092, 0.92788, 0.68004, 0.87449, 0.87059, 0.82713, 0.75831, 0.80359, 0.42583, 0.74212, 0.36366, 0.68066, 0.30149, 0.61028, 0.66269, 0.55669, 0.6495, 0.50904, 0.22149, 0.4814, 0.22082, 0.43078, 0.67921, 0.37254, 0.68111, 0.31196, 0.3239, 0.24013, 0.40537, 0.1683, 0.48685, 0.13613, 0.95999, 0.07352, 0.96559, 0.00689, 0.68441, 0.00735, 0.51804, 0.12093, 0.31526, 0.23451, 0.14873], "triangles": [13, 4, 5, 14, 4, 13, 10, 5, 6, 6, 9, 10, 12, 13, 5, 6, 7, 9, 7, 8, 9, 10, 12, 5, 11, 12, 10, 19, 0, 1, 18, 1, 2, 19, 1, 18, 15, 2, 3, 15, 3, 4, 22, 30, 0, 14, 15, 4, 23, 30, 22, 17, 18, 2, 16, 17, 2, 15, 16, 2, 20, 0, 19, 21, 22, 0, 20, 21, 0, 24, 29, 30, 23, 24, 30, 25, 29, 24, 26, 27, 28, 28, 29, 26, 25, 26, 29], "vertices": [3, 14, 40.32, 12.87, 0.3043, 13, -16.75, 1.5, 0.66954, 15, -74.27, 13.14, 0.02616, 3, 14, 56.95, 12.88, 0.14403, 13, -0.12, 1.51, 0.77744, 15, -57.64, 13.14, 0.07853, 3, 14, 68.28, 12.88, 0.05066, 13, 11.21, 1.51, 0.76853, 15, -46.3, 13.15, 0.1808, 3, 14, 79.62, 12.89, 0.01183, 13, 22.55, 1.52, 0.65135, 15, -34.97, 13.16, 0.33683, 3, 14, 89.33, 10.54, 0.00132, 13, 32.25, -0.83, 0.47177, 15, -25.26, 10.81, 0.52691, 3, 14, 99.03, 8.19, 0, 13, 41.96, -3.18, 0.28752, 15, -15.55, 8.46, 0.71247, 2, 13, 59.65, -9.08, 0.14305, 15, 2.14, 2.56, 0.85695, 2, 13, 62.62, -12.36, 0.0562, 15, 5.11, -0.73, 0.9438, 2, 13, 61.81, -15.34, 0.01945, 15, 4.3, -3.71, 0.98055, 2, 13, 58.55, -16.04, 0.01643, 15, 1.04, -4.4, 0.98357, 2, 13, 53.75, -14.52, 0.03733, 15, -3.76, -2.88, 0.96267, 2, 13, 46.97, -19.28, 0.08768, 15, -10.54, -7.65, 0.91232, 2, 13, 40.95, -16.48, 0.18077, 15, -16.56, -4.84, 0.81923, 2, 13, 37.96, -8.16, 0.32281, 15, -19.55, 3.47, 0.67719, 3, 14, 87.23, 4.76, 0, 13, 30.16, -6.61, 0.49962, 15, -27.36, 5.03, 0.50037, 3, 14, 79.42, 6.32, 0.00015, 13, 22.35, -5.05, 0.67781, 15, -35.16, 6.58, 0.32204, 3, 14, 70.48, -2.71, 0.00121, 13, 13.41, -14.08, 0.82245, 15, -44.1, -2.45, 0.17634, 3, 14, 63.68, -2.38, 0.00479, 13, 6.61, -13.76, 0.91425, 15, -50.91, -2.12, 0.08095, 3, 14, 57.63, 8.32, 0.01495, 13, 0.56, -3.05, 0.95431, 15, -56.96, 8.58, 0.03074, 3, 14, 54.12, 8.33, 0.04001, 13, -2.95, -3.04, 0.95016, 15, -60.47, 8.6, 0.00983, 3, 14, 47.69, -3.13, 0.09529, 13, -9.38, -14.5, 0.90213, 15, -66.9, -2.86, 0.00257, 3, 14, 40.29, -3.17, 0.19732, 13, -16.78, -14.55, 0.80212, 15, -74.29, -2.91, 0.00056, 3, 14, 32.6, 5.76, 0.35151, 13, -24.47, -5.62, 0.64844, 15, -81.99, 6.02, 4e-05, 2, 14, 23.48, 3.72, 0.53906, 13, -33.6, -7.65, 0.46094, 2, 14, 14.35, 1.68, 0.72179, 13, -42.72, -9.69, 0.27821, 2, 14, 10.27, -10.15, 0.86174, 13, -46.8, -21.52, 0.13826, 2, 14, 2.32, -10.29, 0.93735, 13, -54.75, -21.66, 0.06265, 2, 14, -6.15, -3.26, 0.94064, 13, -63.22, -14.63, 0.05936, 2, 14, -6.09, 0.9, 0.86599, 13, -63.16, -10.47, 0.13401, 3, 14, 8.34, 5.97, 0.71474, 13, -48.73, -5.4, 0.28451, 15, -106.25, 6.24, 0.00075, 3, 14, 22.76, 10.13, 0.51108, 13, -34.31, -1.24, 0.48288, 15, -91.82, 10.4, 0.00604], "hull": 31, "edges": [0, 60, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 30, 32, 32, 34, 44, 46, 46, 48, 56, 58, 58, 60, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 26, 28, 28, 30, 16, 18, 18, 20, 20, 22, 34, 36, 36, 38], "width": 127, "height": 25}}, "mouth_base": {"mouth_base": {"type": "mesh", "uvs": [0.44799, 0.01439, 0.56781, 0.01519, 0.66813, 0.12686, 0.76718, 0.26597, 0.86622, 0.40509, 0.95484, 0.52955, 0.99304, 0.67441, 0.99287, 0.87102, 0.93439, 0.98704, 0.85254, 0.98419, 0.77706, 0.9842, 0.66077, 0.94421, 0.54449, 0.90421, 0.43974, 0.92504, 0.33499, 0.94587, 0.23024, 0.9667, 0.1255, 0.98754, 0.04809, 0.96301, 0.00713, 0.87104, 0.0068, 0.64576, 0.07832, 0.48376, 0.16894, 0.33219, 0.25832, 0.22013, 0.34771, 0.10806, 0.76697, 0.65836, 0.20806, 0.63695, 0.49897, 0.47711], "triangles": [24, 3, 4, 6, 24, 5, 24, 4, 5, 6, 7, 24, 8, 9, 24, 10, 11, 24, 9, 10, 24, 7, 8, 24, 26, 0, 1, 26, 1, 2, 23, 0, 26, 3, 26, 2, 24, 26, 3, 11, 12, 26, 26, 22, 23, 13, 26, 12, 26, 25, 22, 26, 14, 25, 24, 11, 26, 13, 14, 26, 25, 21, 22, 20, 21, 25, 25, 18, 19, 25, 19, 20, 16, 17, 18, 15, 25, 14, 25, 16, 18, 15, 16, 25], "vertices": [3, 14, 50.25, 41.96, 0.09436, 13, -6.82, 30.59, 0.86865, 15, -64.33, 42.23, 0.03699, 3, 14, 66.91, 41.91, 0.02889, 13, 9.83, 30.54, 0.85432, 15, -47.68, 42.18, 0.11678, 3, 14, 80.85, 34.99, 0.00545, 13, 23.78, 23.62, 0.72184, 15, -33.74, 35.25, 0.27272, 3, 14, 94.62, 26.36, 0.00027, 13, 37.55, 14.99, 0.5051, 15, -19.97, 26.63, 0.49463, 2, 13, 51.31, 6.37, 0.27591, 15, -6.2, 18, 0.72409, 2, 13, 63.63, -1.35, 0.1073, 15, 6.12, 10.29, 0.8927, 2, 13, 68.94, -10.33, 0.05315, 15, 11.43, 1.3, 0.94685, 2, 13, 68.92, -22.52, 0.07709, 15, 11.4, -10.88, 0.92291, 2, 13, 60.79, -29.71, 0.10394, 15, 3.27, -18.08, 0.89606, 3, 14, 106.48, -18.17, 1e-05, 13, 49.41, -29.54, 0.17118, 15, -8.1, -17.9, 0.82882, 3, 14, 95.99, -18.17, 0.00308, 13, 38.92, -29.54, 0.31687, 15, -18.59, -17.9, 0.68005, 3, 14, 79.83, -15.69, 0.02475, 13, 22.76, -27.06, 0.52429, 15, -34.76, -15.42, 0.45096, 3, 14, 63.66, -13.21, 0.09335, 13, 6.59, -24.58, 0.67266, 15, -50.92, -12.94, 0.23399, 3, 14, 49.1, -14.5, 0.23602, 13, -7.97, -25.87, 0.67709, 15, -65.48, -14.23, 0.08689, 3, 14, 34.54, -15.79, 0.44598, 13, -22.53, -27.16, 0.53402, 15, -80.04, -15.53, 0.02, 3, 14, 19.98, -17.08, 0.67263, 13, -37.09, -28.45, 0.32528, 15, -94.6, -16.82, 0.00208, 3, 14, 5.42, -18.37, 0.85306, 13, -51.65, -29.74, 0.14693, 15, -109.16, -18.11, 1e-05, 2, 14, -5.34, -16.85, 0.93094, 13, -62.41, -28.22, 0.06906, 2, 14, -11.03, -11.15, 0.93566, 13, -68.1, -22.52, 0.06434, 2, 14, -11.08, 2.82, 0.92261, 13, -68.15, -8.55, 0.07739, 2, 14, -1.13, 12.86, 0.83227, 13, -58.21, 1.49, 0.16773, 2, 14, 11.46, 22.26, 0.64112, 13, -45.61, 10.89, 0.35888, 3, 14, 23.89, 29.21, 0.41914, 13, -33.19, 17.83, 0.58016, 15, -90.7, 29.47, 0.00069, 3, 14, 36.31, 36.15, 0.22343, 13, -20.76, 24.78, 0.76863, 15, -78.27, 36.42, 0.00794, 2, 13, 37.52, -9.34, 0.10125, 15, -20, 2.3, 0.89875, 2, 14, 16.9, 3.36, 0.89396, 13, -40.17, -8.01, 0.10604, 3, 14, 57.34, 13.27, 0.00049, 13, 0.27, 1.9, 0.99618, 15, -57.25, 13.54, 0.00334], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 12, 14, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 8, 10, 4, 6, 6, 8, 42, 44, 44, 46, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18, 18, 20, 48, 14, 14, 16, 10, 12, 16, 48, 36, 50], "width": 139, "height": 62}}, "pupil": {"Eye_cross_R": {"width": 25, "height": 28}, "pupil": {"width": 16, "height": 16}}}}], "animations": {"t0_000000": {"slots": {"body_outline": {"rgba": [{"color": "0000007c"}]}}}, "t0_405c80": {"slots": {"body_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "body": {"attachment": [{"name": "body"}, {"time": 0.3667}]}, "body_outline": {"attachment": [{"name": "body_outline"}, {"time": 0.3667}]}, "brow": {"attachment": [{"name": "brow"}, {"time": 0.3667}]}, "eye": {"attachment": [{"name": "eye"}, {"time": 0.3667}]}, "eyelid": {"attachment": [{}]}, "eyelid_closed": {"attachment": [{}]}, "Eye_red": {"rgba": [{"color": "ffffff00", "curve": [0.056, 1, 0.111, 1, 0.056, 1, 0.111, 1, 0.056, 1, 0.111, 1, 0.056, 0, 0.111, 1]}, {"time": 0.1667, "color": "ffffffff"}], "attachment": [{"name": "Eye_red"}, {"time": 0.3667}]}, "mouth": {"attachment": [{"name": "mouth"}, {"time": 0.3667}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}, {"time": 0.3667}]}, "pupil": {"attachment": [{"name": "pupil"}, {"time": 0.0333, "name": "Eye_cross_R"}, {"time": 0.3667}]}}, "bones": {"blot": {"translate": [{"x": -0.86, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -0.86, "y": 15.4, "curve": [0.422, -0.86, 0.544, -0.87, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -0.87, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7, "curve": "stepped"}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.311, 1.137, 0.467, 1.2, 0.311, 1.137, 0.467, 1.2]}, {"time": 0.5667, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{"curve": "stepped"}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "Body1": {"rotate": [{}], "scale": [{"y": 1.11, "curve": [0.022, 1, 0.044, 1, 0.022, 1.11, 0.044, 1]}, {"time": 0.0667, "curve": [0.144, 1, 0.222, 1, 0.144, 1, 0.222, 1.11]}, {"time": 0.3, "y": 1.11}]}, "Body2": {"rotate": [{}], "translate": [{"x": -31.65, "curve": [0.022, -31.65, 0.044, -17.02, 0.022, 0, 0.044, 0]}, {"time": 0.0667, "x": -17.02, "curve": [0.178, -17.02, 0.289, -61.65, 0.178, 0, 0.289, 0]}, {"time": 0.4, "x": -61.65}], "scale": [{"y": 1.134, "curve": [0.022, 1, 0.044, 1, 0.022, 1.134, 0.044, 1]}, {"time": 0.0667, "curve": [0.144, 1, 0.222, 1, 0.144, 1, 0.222, 1.134]}, {"time": 0.3, "y": 1.134}]}, "Body_Low": {"rotate": [{}]}, "Body_High": {"rotate": [{}]}, "low_cntr": {"scale": [{"curve": [0.011, 1, 0.022, 0.8, 0.011, 1, 0.022, 1.2]}, {"time": 0.0333, "x": 0.8, "y": 1.2, "curve": [0.186, 0.8, 0.281, 1.292, 0.186, 1.2, 0.281, 0.463]}, {"time": 0.4333, "x": 1.292, "y": 0.463}]}, "Eyelid": {"translate": [{"y": 33.95}]}, "pupil": {"translate": [{"y": -10.12, "curve": [0.022, -2.07, 0.044, -6.21, 0.022, 0.04, 0.044, 20.37]}, {"time": 0.0667, "x": -6.21, "y": 20.37, "curve": "stepped"}, {"time": 0.1, "x": -6.21, "y": 20.37, "curve": [0.197, 0.36, 0.3, -0.89, 0.188, 0.65, 0.3, 1.21]}, {"time": 0.4, "x": -0.89, "y": 1.21}]}, "eye": {"translate": [{}]}, "brow": {"translate": [{"y": -0.38, "curve": [0.011, 0, 0.022, 0, 0.011, -0.38, 0.022, 7.48]}, {"time": 0.0333, "y": 7.48, "curve": [0.122, 0, 0.211, 0, 0.122, 7.48, 0.211, -0.38]}, {"time": 0.3, "y": -0.38}], "scale": [{"y": 1.029}, {"time": 0.0333, "y": -1.029, "curve": "stepped"}, {"time": 0.1, "y": -1.029, "curve": "stepped"}, {"time": 0.1667, "y": 1.029}]}, "mouth_cntr": {"rotate": [{}], "translatex": [{}], "translatey": [{"value": -0.86}]}, "Ear_L": {"rotate": [{"value": -33.52, "curve": [0.011, -33.52, 0.022, -81.39]}, {"time": 0.0333, "value": -81.39, "curve": [0.078, -81.39, 0.122, 41.73]}, {"time": 0.1667, "value": 41.73, "curve": [0.233, 41.73, 0.3, -48.67]}, {"time": 0.3667, "value": -48.67, "curve": [0.433, -48.67, 0.5, -33.52]}, {"time": 0.5667, "value": -33.52}]}, "Ear_R": {"rotate": [{"value": 20.52, "curve": [0.011, 20.52, 0.022, 65.52]}, {"time": 0.0333, "value": 65.52, "curve": [0.078, 65.52, 0.122, -39.21]}, {"time": 0.1667, "value": -39.21, "curve": [0.233, -39.21, 0.3, 32.24]}, {"time": 0.3667, "value": 32.24, "curve": [0.433, 32.24, 0.5, 20.52]}, {"time": 0.5667, "value": 20.52}]}, "mouth_R": {"rotate": [{"value": 5.51}], "translate": [{"x": -1.07, "y": -10.6}]}, "mouth_L": {"rotate": [{"value": -12.05}], "translate": [{"x": -1.29, "y": -10.57}]}}}, "t1_Hit": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "brow": {"attachment": [{"name": "brow"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyelid": {"attachment": [{}]}, "eyelid_closed": {"attachment": [{}]}, "Eye_red": {"rgba": [{"color": "ffffff00", "curve": [0.056, 1, 0.111, 1, 0.056, 1, 0.111, 1, 0.056, 1, 0.111, 1, 0.056, 0, 0.111, 1]}, {"time": 0.1667, "color": "ffffffff"}], "attachment": [{"name": "Eye_red"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"Body1": {"rotate": [{}], "scale": [{"y": 1.11, "curve": [0.022, 1, 0.044, 1, 0.022, 1.11, 0.044, 1]}, {"time": 0.0667, "curve": [0.144, 1, 0.222, 1, 0.144, 1, 0.222, 1.11]}, {"time": 0.3, "y": 1.11}]}, "Body2": {"rotate": [{}], "translate": [{"x": -31.65, "curve": [0.022, -31.65, 0.044, -17.02, 0.022, 0, 0.044, 0]}, {"time": 0.0667, "x": -17.02, "curve": [0.178, -17.02, 0.289, -31.65, 0.178, 0, 0.289, 0]}, {"time": 0.4, "x": -31.65}], "scale": [{"y": 1.134, "curve": [0.022, 1, 0.044, 1, 0.022, 1.134, 0.044, 1]}, {"time": 0.0667, "curve": [0.144, 1, 0.222, 1, 0.144, 1, 0.222, 1.134]}, {"time": 0.3, "y": 1.134}]}, "Body_Low": {"rotate": [{}]}, "Body_High": {"rotate": [{}]}, "low_cntr": {"scale": [{"curve": [0.011, 1, 0.022, 0.8, 0.011, 1, 0.022, 1.2]}, {"time": 0.0333, "x": 0.8, "y": 1.2, "curve": [0.097, 0.8, 0.137, 1.1, 0.097, 1.2, 0.137, 0.9]}, {"time": 0.2, "x": 1.1, "y": 0.9, "curve": [0.289, 1.1, 0.378, 1, 0.289, 0.9, 0.378, 1]}, {"time": 0.4667}]}, "Eyelid": {"translate": [{"y": 33.95}]}, "pupil": {"translate": [{"y": -10.12, "curve": [0.022, -2.07, 0.044, -6.21, 0.022, 0.04, 0.044, 20.37]}, {"time": 0.0667, "x": -6.21, "y": 20.37, "curve": "stepped"}, {"time": 0.1, "x": -6.21, "y": 20.37, "curve": [0.197, 0.36, 0.3, 0, 0.188, 0.65, 0.3, -10.12]}, {"time": 0.4, "y": -10.12}]}, "eye": {"translate": [{}]}, "brow": {"translate": [{"y": -0.38, "curve": [0.011, 0, 0.022, 0, 0.011, -0.38, 0.022, 7.48]}, {"time": 0.0333, "y": 7.48, "curve": [0.122, 0, 0.211, 0, 0.122, 7.48, 0.211, -0.38]}, {"time": 0.3, "y": -0.38}], "scale": [{"y": 1.029}, {"time": 0.0333, "y": -1.029, "curve": "stepped"}, {"time": 0.1, "y": -1.029, "curve": "stepped"}, {"time": 0.1667, "y": 1.029}]}, "mouth_cntr": {"rotate": [{}], "translatex": [{}], "translatey": [{"value": -0.86}]}, "Ear_L": {"rotate": [{"value": -33.52, "curve": [0.011, -33.52, 0.022, -81.39]}, {"time": 0.0333, "value": -81.39, "curve": [0.078, -81.39, 0.122, 41.73]}, {"time": 0.1667, "value": 41.73, "curve": [0.233, 41.73, 0.3, -48.67]}, {"time": 0.3667, "value": -48.67, "curve": [0.433, -48.67, 0.5, -33.52]}, {"time": 0.5667, "value": -33.52}]}, "Ear_R": {"rotate": [{"value": 20.52, "curve": [0.011, 20.52, 0.022, 65.52]}, {"time": 0.0333, "value": 65.52, "curve": [0.078, 65.52, 0.122, -39.21]}, {"time": 0.1667, "value": -39.21, "curve": [0.233, -39.21, 0.3, 32.24]}, {"time": 0.3667, "value": 32.24, "curve": [0.433, 32.24, 0.5, 20.52]}, {"time": 0.5667, "value": 20.52}]}, "mouth_R": {"rotate": [{"value": 5.51}], "translate": [{"x": -1.07, "y": -10.6}]}, "mouth_L": {"rotate": [{"value": -12.05}], "translate": [{"x": -1.29, "y": -10.57}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_IDLE": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "brow": {"attachment": [{"name": "brow"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyelid": {"attachment": [{"name": "eyelid"}]}, "eyelid_closed": {"attachment": [{}]}, "Eye_red": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"Body1": {"rotate": [{"value": 2.55, "curve": [0.333, 2.55, 0.667, -2.89]}, {"time": 1, "value": -2.89, "curve": [1.333, -2.89, 1.667, 2.55]}, {"time": 2, "value": 2.55}], "scale": [{}]}, "Body2": {"rotate": [{"value": -2.96, "curve": [0.136, -6.76, 0.268, -10]}, {"time": 0.4, "value": -10, "curve": [0.733, -10, 1.067, 10]}, {"time": 1.4, "value": 10, "curve": [1.601, 10, 1.802, 2.82]}, {"time": 2, "value": -2.96}], "translate": [{}], "scale": [{}]}, "Body_Low": {"rotate": [{"value": 7.16, "curve": [0.04, 7.65, 0.073, 8]}, {"time": 0.1, "value": 8, "curve": [0.3, 8, 0.899, -8]}, {"time": 1.1, "value": -8, "curve": [1.273, -8, 1.748, 3.96]}, {"time": 2, "value": 7.16}]}, "Body_High": {"rotate": [{"value": -5.66, "curve": [0.111, -8.18, 0.207, -10]}, {"time": 0.2667, "value": -10, "curve": [0.462, -10, 1.067, 10]}, {"time": 1.2667, "value": 10, "curve": [1.404, 10, 1.742, 0.22]}, {"time": 2, "value": -5.66}]}, "low_cntr": {"scale": [{"x": 1.005, "y": 0.995, "curve": [0.079, 1.028, 0.156, 1.05, 0.079, 0.972, 0.156, 0.95]}, {"time": 0.2333, "x": 1.05, "y": 0.95, "curve": [0.4, 1.05, 0.567, 0.95, 0.4, 0.95, 0.567, 1.05]}, {"time": 0.7333, "x": 0.95, "y": 1.05, "curve": [0.9, 0.95, 1.067, 1.05, 0.9, 1.05, 1.067, 0.95]}, {"time": 1.2333, "x": 1.05, "y": 0.95, "curve": [1.4, 1.05, 1.567, 0.95, 1.4, 0.95, 1.567, 1.05]}, {"time": 1.7333, "x": 0.95, "y": 1.05, "curve": [1.823, 0.95, 1.912, 0.978, 1.823, 1.05, 1.912, 1.022]}, {"time": 2, "x": 1.005, "y": 0.995}]}, "Ear_R": {"rotate": [{"value": 0.46, "curve": [0.012, 0.18, 0.023, 0]}, {"time": 0.0333, "curve": [0.144, 0, 0.256, 16.54]}, {"time": 0.3667, "value": 16.54, "curve": [0.478, 16.54, 0.589, 0]}, {"time": 0.7, "curve": [0.811, 0, 0.922, 16.54]}, {"time": 1.0333, "value": 16.54, "curve": [1.144, 16.54, 1.256, 0]}, {"time": 1.3667, "curve": [1.478, 0, 1.589, 16.54]}, {"time": 1.7, "value": 16.54, "curve": [1.8, 16.54, 1.901, 3.07]}, {"time": 2, "value": 0.46}]}, "Ear_L": {"rotate": [{"value": -4.78, "curve": [0.034, -2.04, 0.067, 0]}, {"time": 0.1, "curve": [0.211, 0, 0.322, -22.13]}, {"time": 0.4333, "value": -22.13, "curve": [0.544, -22.13, 0.656, 0]}, {"time": 0.7667, "curve": [0.878, 0, 0.989, -22.13]}, {"time": 1.1, "value": -22.13, "curve": [1.211, -22.13, 1.322, 0]}, {"time": 1.4333, "curve": [1.544, 0, 1.656, -22.13]}, {"time": 1.7667, "value": -22.13, "curve": [1.845, -22.13, 1.923, -11.28]}, {"time": 2, "value": -4.78}]}, "Eyelid": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, -15.69]}, {"time": 1, "y": -15.69, "curve": [1.333, 0, 1.667, 0, 1.333, -15.69, 1.667, 0]}, {"time": 2}]}, "pupil": {"translate": [{"curve": [0.333, 0, 0.667, -0.11, 0.333, 0, 0.667, -9.04]}, {"time": 1, "x": -0.11, "y": -9.04, "curve": [1.333, -0.11, 1.667, 0, 1.333, -9.04, 1.667, 0]}, {"time": 2}]}, "eye": {"translate": [{"y": -3.7, "curve": [0.333, 0, 0.667, 0, 0.333, -3.7, 0.667, 5.36]}, {"time": 1, "y": 5.36, "curve": [1.333, 0, 1.667, 0, 1.333, 5.36, 1.667, -3.7]}, {"time": 2, "y": -3.7}]}, "brow": {"translate": [{"y": -0.78, "curve": [0.069, 0, 0.134, 0, 0.069, -0.32, 0.134, 0]}, {"time": 0.2, "curve": [0.533, 0, 0.867, 0, 0.533, 0, 0.867, -4.49]}, {"time": 1.2, "y": -4.49, "curve": [1.468, 0, 1.736, 0, 1.468, -4.49, 1.736, -2.7]}, {"time": 2, "y": -0.78}], "scale": [{}]}, "mouth_cntr": {"rotate": [{"value": -4.44, "curve": [0.079, -8.7, 0.156, -12.77]}, {"time": 0.2333, "value": -12.77, "curve": [0.4, -12.77, 0.567, 5.72]}, {"time": 0.7333, "value": 5.72, "curve": [0.823, 5.72, 0.911, 0.36]}, {"time": 1, "value": -4.44, "curve": [1.079, -8.7, 1.156, -12.77]}, {"time": 1.2333, "value": -12.77, "curve": [1.4, -12.77, 1.567, 5.72]}, {"time": 1.7333, "value": 5.72, "curve": [1.823, 5.72, 1.912, 0.5]}, {"time": 2, "value": -4.44}], "translatex": [{"curve": [0.16, -4.36, 0.385, -4.18]}, {"time": 0.5, "curve": [0.62, 4.36, 0.84, 4.36]}, {"time": 1, "curve": [1.16, -4.36, 1.385, -4.18]}, {"time": 1.5, "curve": [1.651, 5.5, 1.843, 4.28]}, {"time": 2}], "translatey": [{"value": -8.57, "curve": "stepped"}, {"time": 0.0333, "value": -8.57, "curve": [0.2, -8.57, 0.3, 2.95]}, {"time": 0.4667, "value": 2.95, "curve": "stepped"}, {"time": 0.5, "value": 2.95, "curve": [0.667, 2.95, 0.811, -8.57]}, {"time": 0.9667, "value": -8.57, "curve": "stepped"}, {"time": 1.0333, "value": -8.57, "curve": [1.2, -8.57, 1.3, 2.95]}, {"time": 1.4667, "value": 2.95, "curve": "stepped"}, {"time": 1.5, "value": 2.95, "curve": [1.656, 2.95, 1.821, -8.57]}, {"time": 1.9667, "value": -8.57}]}, "mouth_R": {"rotate": [{}], "translate": [{}]}, "mouth_L": {"rotate": [{}], "translate": [{}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_IDLE2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "brow": {"attachment": [{"name": "brow"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyelid": {"attachment": [{}]}, "eyelid_closed": {"attachment": [{}]}, "Eye_red": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"Body1": {"rotate": [{}], "scale": [{"y": 1.11, "curve": [0.222, 1, 0.444, 1, 0.222, 1.11, 0.444, 1.126]}, {"time": 0.6667, "y": 1.126, "curve": [0.889, 1, 1.111, 1, 0.889, 1.126, 1.111, 1.11]}, {"time": 1.3333, "y": 1.11}]}, "Body2": {"rotate": [{}], "translate": [{"x": -31.65, "curve": [0.168, -32.54, 0.334, -34.29, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.29, "curve": [0.722, -34.29, 0.944, -31.15, 0.722, 0, 0.944, 0]}, {"time": 1.1667, "x": -31.15, "curve": [1.223, -31.15, 1.279, -31.35, 1.223, 0, 1.279, 0]}, {"time": 1.3333, "x": -31.65}], "scale": [{"y": 1.134, "curve": [0.222, 1, 0.444, 1, 0.222, 1.134, 0.444, 1.108]}, {"time": 0.6667, "y": 1.108, "curve": [0.889, 1, 1.111, 1, 0.889, 1.108, 1.111, 1.134]}, {"time": 1.3333, "y": 1.134}]}, "Body_Low": {"rotate": [{}]}, "Body_High": {"rotate": [{}]}, "low_cntr": {"scale": [{"curve": [0.222, 1, 0.444, 1.05, 0.222, 1, 0.444, 0.95]}, {"time": 0.6667, "x": 1.05, "y": 0.95, "curve": [0.889, 1.05, 1.111, 1, 0.889, 0.95, 1.111, 1]}, {"time": 1.3333}]}, "Eyelid": {"translate": [{"y": 33.95}]}, "pupil": {"translate": [{"y": -10.12, "curve": "stepped"}, {"time": 0.2, "y": -10.12}, {"time": 0.2667, "x": 3.56, "y": -11.29, "curve": "stepped"}, {"time": 0.4667, "x": 3.56, "y": -11.29}, {"time": 0.5333, "x": -3.21, "y": -12.49, "curve": "stepped"}, {"time": 0.9667, "x": -3.21, "y": -12.49}, {"time": 1.0333, "x": -0.89, "y": -15.08, "curve": "stepped"}, {"time": 1.2333, "x": -0.89, "y": -15.08}, {"time": 1.3, "y": -10.12}]}, "eye": {"translate": [{}]}, "brow": {"translate": [{"y": -0.38, "curve": [0.046, 0, 0.09, 0, 0.046, -0.15, 0.09, 0]}, {"time": 0.1333, "curve": [0.356, 0, 0.578, 0, 0.356, 0, 0.578, -3.61]}, {"time": 0.8, "y": -3.61, "curve": [0.979, 0, 1.157, 0, 0.979, -3.61, 1.157, -1.29]}, {"time": 1.3333, "y": -0.38}], "scale": [{"y": 1.029, "curve": [0.046, 1, 0.09, 1, 0.046, 1.012, 0.09, 1]}, {"time": 0.1333, "curve": [0.356, 1, 0.578, 1, 0.356, 1, 0.578, 1.283]}, {"time": 0.8, "y": 1.283, "curve": [0.979, 1, 1.157, 1, 0.979, 1.283, 1.157, 1.101]}, {"time": 1.3333, "y": 1.029}]}, "mouth_cntr": {"rotate": [{}], "translatex": [{}], "translatey": [{"value": -0.86, "curve": [0.157, -2.04, 0.312, -4]}, {"time": 0.4667, "value": -4, "curve": [0.689, -4, 0.911, 0]}, {"time": 1.1333, "curve": [1.201, 0, 1.268, -0.35]}, {"time": 1.3333, "value": -0.86}]}, "Ear_L": {"rotate": [{"value": -33.52, "curve": [0.035, -33.25, 0.067, -33.03]}, {"time": 0.1, "value": -33.03, "curve": [0.322, -33.03, 0.544, -40.36]}, {"time": 0.7667, "value": -40.36, "curve": [0.956, -40.36, 1.146, -35.08]}, {"time": 1.3333, "value": -33.52}]}, "Ear_R": {"rotate": [{"value": 20.52, "curve": [0.035, 20.13, 0.067, 19.82]}, {"time": 0.1, "value": 19.82, "curve": [0.322, 19.82, 0.544, 30.45]}, {"time": 0.7667, "value": 30.45, "curve": [0.956, 30.45, 1.146, 22.79]}, {"time": 1.3333, "value": 20.52}]}, "mouth_R": {"rotate": [{"value": 5.51}], "translate": [{"x": -1.07, "y": -10.6}]}, "mouth_L": {"rotate": [{"value": -12.05}], "translate": [{"x": -1.29, "y": -10.57}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_IDLE3": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "brow": {"attachment": [{"name": "brow"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyelid": {"attachment": [{}]}, "eyelid_closed": {"attachment": [{}]}, "Eye_red": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Eye_red"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"Body1": {"rotate": [{}], "scale": [{"y": 1.11, "curve": [0.222, 1, 0.444, 1, 0.222, 1.11, 0.444, 1.126]}, {"time": 0.6667, "y": 1.126, "curve": [0.889, 1, 1.111, 1, 0.889, 1.126, 1.111, 1.11]}, {"time": 1.3333, "y": 1.11}]}, "Body2": {"rotate": [{}], "translate": [{"x": -31.65, "curve": [0.168, -32.54, 0.334, -34.29, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.29, "curve": [0.722, -34.29, 0.944, -31.15, 0.722, 0, 0.944, 0]}, {"time": 1.1667, "x": -31.15, "curve": [1.223, -31.15, 1.279, -31.35, 1.223, 0, 1.279, 0]}, {"time": 1.3333, "x": -31.65}], "scale": [{"y": 1.134, "curve": [0.222, 1, 0.444, 1, 0.222, 1.134, 0.444, 1.108]}, {"time": 0.6667, "y": 1.108, "curve": [0.889, 1, 1.111, 1, 0.889, 1.108, 1.111, 1.134]}, {"time": 1.3333, "y": 1.134}]}, "Body_Low": {"rotate": [{}]}, "Body_High": {"rotate": [{}]}, "low_cntr": {"scale": [{"curve": [0.222, 1, 0.444, 1.05, 0.222, 1, 0.444, 0.95]}, {"time": 0.6667, "x": 1.05, "y": 0.95, "curve": [0.889, 1.05, 1.111, 1, 0.889, 0.95, 1.111, 1]}, {"time": 1.3333}]}, "Eyelid": {"translate": [{"y": 33.95}]}, "pupil": {"translate": [{"y": -10.12, "curve": "stepped"}, {"time": 0.2, "y": -10.12}, {"time": 0.2667, "x": 3.56, "y": -11.29, "curve": "stepped"}, {"time": 0.4667, "x": 3.56, "y": -11.29}, {"time": 0.5333, "x": -3.21, "y": -12.49, "curve": "stepped"}, {"time": 0.9667, "x": -3.21, "y": -12.49}, {"time": 1.0333, "x": -0.89, "y": -15.08, "curve": "stepped"}, {"time": 1.2333, "x": -0.89, "y": -15.08}, {"time": 1.3, "y": -10.12}]}, "eye": {"translate": [{}]}, "brow": {"translate": [{"y": -0.38, "curve": [0.046, 0, 0.09, 0, 0.046, -0.15, 0.09, 0]}, {"time": 0.1333, "curve": [0.356, 0, 0.578, 0, 0.356, 0, 0.578, -3.61]}, {"time": 0.8, "y": -3.61, "curve": [0.979, 0, 1.157, 0, 0.979, -3.61, 1.157, -1.29]}, {"time": 1.3333, "y": -0.38}], "scale": [{"y": 1.029, "curve": [0.046, 1, 0.09, 1, 0.046, 1.012, 0.09, 1]}, {"time": 0.1333, "curve": [0.356, 1, 0.578, 1, 0.356, 1, 0.578, 1.283]}, {"time": 0.8, "y": 1.283, "curve": [0.979, 1, 1.157, 1, 0.979, 1.283, 1.157, 1.101]}, {"time": 1.3333, "y": 1.029}]}, "mouth_cntr": {"rotate": [{}], "translatex": [{}], "translatey": [{"value": -0.86, "curve": [0.157, -2.04, 0.312, -4]}, {"time": 0.4667, "value": -4, "curve": [0.689, -4, 0.911, 0]}, {"time": 1.1333, "curve": [1.201, 0, 1.268, -0.35]}, {"time": 1.3333, "value": -0.86}]}, "Ear_L": {"rotate": [{"value": -33.52, "curve": [0.035, -33.25, 0.067, -33.03]}, {"time": 0.1, "value": -33.03, "curve": [0.322, -33.03, 0.544, -40.36]}, {"time": 0.7667, "value": -40.36, "curve": [0.956, -40.36, 1.146, -35.08]}, {"time": 1.3333, "value": -33.52}]}, "Ear_R": {"rotate": [{"value": 20.52, "curve": [0.035, 20.13, 0.067, 19.82]}, {"time": 0.1, "value": 19.82, "curve": [0.322, 19.82, 0.544, 30.45]}, {"time": 0.7667, "value": 30.45, "curve": [0.956, 30.45, 1.146, 22.79]}, {"time": 1.3333, "value": 20.52}]}, "mouth_R": {"rotate": [{"value": 5.51}], "translate": [{"x": -1.07, "y": -10.6}]}, "mouth_L": {"rotate": [{"value": -12.05}], "translate": [{"x": -1.29, "y": -10.57}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}}}