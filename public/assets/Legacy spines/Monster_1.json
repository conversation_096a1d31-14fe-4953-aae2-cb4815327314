{"skeleton": {"hash": "L0ahZjrTsQU", "spine": "4.2.38", "x": -185.45, "y": -5.72, "width": 346.09, "height": 300.05, "images": "./Images/Monster_1/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "Low_cntr", "parent": "root", "icon": "straightLine"}, {"name": "horh_L", "parent": "Low_cntr", "length": 62.9, "rotation": 69.88, "x": 103.69, "y": 215.95}, {"name": "horn_R", "parent": "Low_cntr", "length": 65.16, "rotation": 118.52, "x": -116.32, "y": 212.34}, {"name": "mouth", "parent": "Low_cntr", "x": 6.76, "y": 59.06, "icon": "mouth"}, {"name": "eye", "parent": "Low_cntr", "x": 20.29, "y": 141.56, "icon": "eye"}, {"name": "pupil", "parent": "eye", "x": 2.71, "y": 18.94}, {"name": "brow", "parent": "eye", "length": 33.41, "rotation": -3.09, "x": -0.45, "y": 57.26, "inherit": "noScale"}, {"name": "Arm_R", "parent": "Low_cntr", "length": 54.22, "rotation": -128.59, "x": -111.81, "y": 104.14, "color": "abe323ff"}, {"name": "Arm_R2", "parent": "Arm_R", "length": 52.96, "rotation": 26.8, "x": 54.22, "color": "abe323ff"}, {"name": "Arm_L", "parent": "Low_cntr", "length": 42.62, "rotation": -66.96, "x": 100.09, "y": 96.03, "color": "abe323ff"}, {"name": "Arm_L2", "parent": "Arm_L", "length": 46.38, "rotation": -7.25, "x": 42.62, "color": "abe323ff"}, {"name": "Explosion", "parent": "root", "x": -1.83, "y": 109.8, "icon": "asterisk"}, {"name": "Smoke_1", "parent": "Explosion"}, {"name": "Smoke_2", "parent": "Explosion"}, {"name": "Smoke_3", "parent": "Explosion"}, {"name": "Smoke_4", "parent": "Explosion"}, {"name": "Smoke_5", "parent": "Explosion"}, {"name": "Smoke_6", "parent": "Explosion"}, {"name": "Smoke_7", "parent": "Explosion"}, {"name": "Smoke_8", "parent": "Explosion"}, {"name": "Smoke_9", "parent": "Explosion"}, {"name": "Smoke_10", "parent": "Explosion"}, {"name": "Smoke_11", "parent": "Explosion"}, {"name": "Smoke_12", "parent": "Explosion"}], "slots": [{"name": "hand_L", "bone": "Low_cntr", "attachment": "hand_L"}, {"name": "hand_R_outline", "bone": "Low_cntr", "attachment": "hand_R_outline"}, {"name": "horh_L", "bone": "horh_L", "attachment": "horh_L"}, {"name": "horn_R", "bone": "horn_R", "attachment": "horn_R"}, {"name": "body", "bone": "Low_cntr", "attachment": "body"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "pupil", "bone": "pupil", "attachment": "pupil"}, {"name": "brow", "bone": "brow", "attachment": "brow"}, {"name": "mouth", "bone": "mouth", "attachment": "mouth"}, {"name": "hand_R_colors", "bone": "Low_cntr", "attachment": "hand_R_colors"}, {"name": "Smoke_1", "bone": "Smoke_1", "color": "56687eff"}, {"name": "Smoke_2", "bone": "Smoke_2", "color": "56687eff"}, {"name": "Smoke_3", "bone": "Smoke_3", "color": "56687eff"}, {"name": "Smoke_4", "bone": "Smoke_4", "color": "56687eff"}, {"name": "Smoke_5", "bone": "Smoke_5", "color": "56687eff"}, {"name": "Smoke_6", "bone": "Smoke_6", "color": "56687eff"}, {"name": "Smoke_7", "bone": "Smoke_7", "color": "56687eff"}, {"name": "Smoke_8", "bone": "Smoke_8", "color": "56687eff"}, {"name": "Smoke_9", "bone": "Smoke_9", "color": "56687eff"}, {"name": "Smoke_10", "bone": "Smoke_10", "color": "56687eff"}, {"name": "Smoke_11", "bone": "Smoke_11", "color": "56687eff"}, {"name": "Smoke_12", "bone": "Smoke_12", "color": "56687eff"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"x": -6.91, "y": 121.33, "width": 255, "height": 252}}, "brow": {"brow": {"x": 0.2, "y": 1.02, "rotation": 3.09, "width": 79, "height": 29}}, "eye": {"eye": {"x": -1.7, "y": -1.23, "width": 114, "height": 144}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [0.24441, 0.00855, 0.34948, 0.09987, 0.43195, 0.19275, 0.51441, 0.28563, 0.57141, 0.38371, 0.62841, 0.48179, 0.6655, 0.56272, 0.8811, 0.56086, 0.98729, 0.62615, 0.98679, 0.70807, 0.85385, 0.78832, 0.81938, 0.90414, 0.68557, 0.99163, 0.30316, 0.98275, 0.11651, 0.97257, 0.05295, 0.90245, 0.05331, 0.79677, 0.2251, 0.69154, 0.22473, 0.64806, 0.17936, 0.53609, 0.13011, 0.42671, 0.05233, 0.32385, 0.05202, 0.25445, 0.16163, 0.02878, 0.50569, 0.73404], "triangles": [18, 19, 5, 18, 5, 6, 24, 17, 18, 9, 7, 8, 6, 24, 18, 10, 6, 7, 10, 7, 9, 24, 6, 10, 11, 24, 10, 15, 16, 13, 17, 13, 16, 13, 14, 15, 13, 17, 24, 12, 13, 24, 11, 12, 24, 23, 0, 1, 22, 2, 21, 1, 22, 23, 2, 22, 1, 21, 2, 3, 20, 21, 3, 20, 3, 4, 19, 20, 4, 19, 4, 5], "vertices": [1, 10, -18.58, 13.16, 1, 2, 10, -5.16, 16.01, 0.99996, 11, -49.42, 9.86, 4e-05, 2, 10, 7.77, 17.23, 0.98855, 11, -36.75, 12.7, 0.01145, 2, 10, 20.7, 18.45, 0.90721, 11, -24.07, 15.54, 0.09279, 2, 10, 33.48, 17.67, 0.71879, 11, -11.3, 16.37, 0.28121, 2, 10, 46.25, 16.88, 0.44202, 11, 1.47, 17.2, 0.55798, 2, 10, 56.5, 15.54, 0.19396, 11, 11.81, 17.17, 0.80604, 2, 10, 62.62, 30.51, 0.04909, 11, 15.99, 32.79, 0.95091, 2, 10, 73.13, 34.7, 0.00395, 11, 25.88, 38.27, 0.99605, 1, 11, 35.57, 35.49, 1, 1, 11, 42.35, 23.21, 1, 1, 11, 55.36, 16.85, 1, 1, 11, 62.98, 4.26, 1, 1, 11, 54.13, -23.04, 1, 1, 11, 49.11, -36.17, 1, 2, 10, 76.98, -43.09, 0.0006, 11, 39.51, -38.41, 0.9994, 2, 10, 65.03, -37.98, 0.00665, 11, 27.01, -34.85, 0.99335, 2, 10, 58.16, -21.05, 0.07694, 11, 18.07, -18.93, 0.92306, 2, 10, 53.22, -18.99, 0.25516, 11, 12.91, -17.5, 0.74484, 2, 10, 39.22, -16.73, 0.53359, 11, -1.27, -17.02, 0.46641, 2, 10, 25.39, -14.86, 0.79465, 11, -15.22, -16.92, 0.20535, 2, 10, 11.47, -15.28, 0.94916, 11, -28.98, -19.09, 0.05084, 2, 10, 3.6, -11.96, 0.99802, 11, -37.2, -16.79, 0.00198, 1, 10, -18.72, 6.47, 1, 1, 11, 28.82, -0.1, 1], "hull": 24, "edges": [0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 42, 44, 44, 46, 28, 30, 6, 8, 8, 10, 36, 38, 38, 40, 40, 42, 46, 0, 2, 4, 4, 6, 20, 22, 22, 24], "width": 75, "height": 123}}, "hand_R_colors": {"hand_R_colors": {"type": "mesh", "uvs": [0.89833, 0.00902, 0.98464, 0.00947, 0.98567, 0.1236, 0.88941, 0.19522, 0.79314, 0.26685, 0.70205, 0.3712, 0.62006, 0.47891, 0.56329, 0.60092, 0.53785, 0.72386, 0.7332, 0.84855, 0.731, 0.91595, 0.62939, 0.9562, 0.35542, 0.99312, 0.17606, 0.9887, 0.02965, 0.77025, 0.02946, 0.68626, 0.08601, 0.64837, 0.27898, 0.63852, 0.33123, 0.52137, 0.40584, 0.39369, 0.5005, 0.28621, 0.58255, 0.19929, 0.68399, 0.13286, 0.78543, 0.06643], "triangles": [18, 19, 6, 7, 18, 6, 17, 18, 7, 8, 17, 7, 14, 15, 16, 9, 11, 8, 10, 11, 9, 17, 14, 16, 8, 14, 17, 8, 12, 14, 14, 12, 13, 11, 12, 8, 2, 3, 23, 22, 23, 3, 0, 2, 23, 2, 0, 1, 4, 22, 3, 21, 22, 4, 5, 21, 4, 20, 21, 5, 19, 20, 5, 6, 19, 5], "vertices": [1, 8, 2.25, -6.41, 1, 1, 8, -1.32, -1.86, 1, 1, 8, 8, 5.67, 1, 2, 8, 17.91, 5.32, 0.99972, 9, -30.01, 21.11, 0.00028, 2, 8, 27.81, 4.96, 0.93611, 9, -21.33, 16.34, 0.06389, 2, 8, 40.18, 7.03, 0.7614, 9, -9.36, 12.6, 0.2386, 2, 8, 52.44, 9.79, 0.47614, 9, 2.83, 9.53, 0.52386, 2, 8, 64.83, 14.8, 0.20641, 9, 16.15, 8.43, 0.79359, 2, 8, 75.98, 21.52, 0.04779, 9, 29.13, 9.4, 0.95221, 1, 9, 39.28, 24.88, 1, 1, 9, 46.23, 26.19, 1, 1, 9, 51.76, 20.38, 1, 1, 9, 59.31, 3.21, 1, 1, 9, 61.31, -8.65, 1, 1, 9, 40.86, -22.94, 1, 1, 9, 32.23, -24.75, 1, 1, 9, 27.56, -21.86, 1, 2, 8, 79.8, 2.38, 0.07651, 9, 23.91, -9.41, 0.92349, 2, 8, 68, -2.56, 0.26413, 9, 11.15, -8.5, 0.73587, 2, 8, 54.4, -7.01, 0.56286, 9, -3, -6.34, 0.43714, 2, 8, 41.62, -9.09, 0.81968, 9, -15.34, -2.44, 0.18032, 2, 8, 31.06, -10.49, 0.9654, 9, -25.4, 1.08, 0.0346, 1, 8, 21.37, -9.53, 1, 1, 8, 11.68, -8.56, 1], "hull": 24, "edges": [0, 46, 0, 2, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 10, 12, 12, 14, 4, 6, 6, 8, 38, 40, 40, 42, 42, 44, 44, 46, 26, 28, 22, 24, 2, 4], "width": 67, "height": 105}}, "hand_R_outline": {"hand_R_outline": {"type": "mesh", "uvs": [0.84233, 0.01587, 0.96394, 0.01653, 0.9999, 0.20015, 0.93408, 0.2774, 0.86569, 0.33808, 0.80133, 0.42015, 0.75482, 0.50678, 0.71196, 0.59418, 0.69469, 0.66656, 0.86995, 0.77505, 0.87115, 0.8757, 0.77861, 0.93468, 0.65672, 0.95283, 0.428, 0.99213, 0.28586, 0.99214, 0.18134, 0.95861, 0.02589, 0.76015, 0.02544, 0.66137, 0.08506, 0.58546, 0.27017, 0.53296, 0.32266, 0.44908, 0.38828, 0.34424, 0.46486, 0.24872, 0.56144, 0.17119, 0.66899, 0.10297], "triangles": [20, 21, 6, 7, 20, 6, 19, 20, 7, 8, 19, 7, 19, 16, 17, 9, 12, 8, 11, 9, 10, 19, 8, 16, 11, 12, 9, 18, 19, 17, 16, 8, 13, 16, 13, 15, 12, 13, 8, 14, 15, 13, 0, 2, 24, 2, 0, 1, 3, 24, 2, 23, 24, 3, 4, 23, 3, 22, 23, 4, 5, 22, 4, 21, 22, 5, 6, 21, 5], "vertices": [1, 8, 0.24, -13.55, 1, 1, 8, -5.54, -6.18, 1, 2, 8, 10.1, 9.84, 0.99979, 9, -34.94, 28.67, 0.00021, 2, 8, 20.57, 11.71, 0.9871, 9, -24.76, 25.62, 0.0129, 2, 8, 29.59, 12.17, 0.91212, 9, -16.49, 21.96, 0.08788, 2, 8, 40.45, 14.49, 0.73541, 9, -5.76, 19.14, 0.26459, 2, 8, 50.87, 18.23, 0.47314, 9, 5.23, 17.78, 0.52686, 2, 8, 61.2, 22.24, 0.22433, 9, 16.26, 16.71, 0.77567, 2, 8, 68.87, 26.67, 0.0685, 9, 25.11, 17.19, 0.9315, 2, 8, 70.72, 45.4, 0.01013, 9, 35.2, 33.09, 0.98987, 2, 8, 80.18, 53.07, 0.00058, 9, 47.1, 35.66, 0.99942, 1, 9, 55.54, 30.15, 1, 1, 9, 59.61, 21.41, 1, 1, 9, 67.86, 5.14, 1, 1, 9, 70.1, -5.57, 1, 1, 9, 67.77, -14.28, 1, 1, 9, 46.71, -30.9, 1, 2, 8, 100.52, -14.01, 4e-05, 9, 35.02, -33.38, 0.99996, 2, 8, 90.48, -16.15, 0.01252, 9, 25.09, -30.76, 0.98748, 2, 8, 76.62, -8.97, 0.10937, 9, 15.96, -18.11, 0.89063, 2, 8, 66.17, -12.14, 0.31689, 9, 5.2, -16.22, 0.68311, 2, 8, 53.1, -16.1, 0.61062, 9, -8.25, -13.87, 0.38938, 2, 8, 40.39, -18.7, 0.84675, 9, -20.77, -10.46, 0.15325, 2, 8, 28.42, -18.74, 0.97253, 9, -31.47, -5.1, 0.02747, 2, 8, 16.8, -17.41, 0.99964, 9, -41.25, 1.32, 0.00036], "hull": 25, "edges": [0, 48, 0, 2, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 2, 4, 10, 12, 12, 14, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 22, 24, 30, 32, 24, 26], "width": 77, "height": 121}}, "horh_L": {"horh_L": {"x": 23.09, "y": 0.58, "rotation": -69.88, "width": 81, "height": 113}}, "horn_R": {"horn_R": {"x": 24.86, "y": -6.57, "rotation": -118.52, "width": 88, "height": 102}}, "mouth": {"mouth": {"x": -1.67, "y": -9.73, "width": 165, "height": 44}}, "pupil": {"pupil": {"x": 0.1, "y": -1.17, "width": 21, "height": 30}}, "Smoke_1": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_2": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_3": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_4": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_5": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_6": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_7": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_8": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_9": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_10": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_11": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_12": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}}}], "animations": {"t1_death": {"slots": {"body": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.196, 0.25]}, {"time": 0.2667, "color": "ffffff00"}]}, "brow": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.196, 0.24]}, {"time": 0.2667, "color": "ffffff00"}]}, "eye": {"rgba": [{"color": "ffffffff", "curve": [0.057, 1, 0.109, 1, 0.057, 1, 0.109, 1, 0.057, 1, 0.109, 1, 0.057, 1, 0.125, 0.25]}, {"time": 0.1667, "color": "ffffff00"}]}, "hand_L": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.195, 0.26]}, {"time": 0.2667, "color": "ffffff00"}]}, "hand_R_colors": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.196, 0.25]}, {"time": 0.2667, "color": "ffffff00"}]}, "hand_R_outline": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.199, 0.24]}, {"time": 0.2667, "color": "ffffff00"}]}, "horh_L": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.202, 0.26]}, {"time": 0.2667, "color": "ffffff00"}]}, "horn_R": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.203, 0.27]}, {"time": 0.2667, "color": "ffffff00"}]}, "mouth": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.203, 0.24]}, {"time": 0.2667, "color": "ffffff00"}]}, "pupil": {"rgba": [{"color": "ffffffff", "curve": [0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.176, 1, 0.091, 1, 0.203, 0.24]}, {"time": 0.2667, "color": "ffffff00"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_3": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_4": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6333}]}, "Smoke_5": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}, "Smoke_6": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_9": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}}, "bones": {"Low_cntr": {"translate": [{}], "scale": [{"x": 0.995, "y": 1.005}]}, "mouth": {"scale": [{}], "shear": [{}]}, "Arm_R": {"rotate": [{"value": 1.02}]}, "Arm_R2": {"rotate": [{"value": 0.64}]}, "Arm_L2": {"rotate": [{"value": -0.67}]}, "Arm_L": {"rotate": [{"value": 0.69}]}, "Smoke_1": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 56.04}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -12.81, "y": 15.25, "curve": [0.211, -81.13, 0.389, -129.93, 0.211, 96.58, 0.389, 120.78]}, {"time": 0.5667, "x": -129.93, "y": 120.78}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.189, 2, 0.344, 0, 0.189, 2, 0.344, 0]}, {"time": 0.5, "x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -57.38}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 51.24, "y": 40.87, "curve": [0.211, 125.25, 0.389, 106.75, 0.211, 170.19, 0.389, 137.86]}, {"time": 0.5667, "x": 106.75, "y": 137.86}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_3": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 12.2, "y": 1.83, "curve": [0.211, 96.79, 0.389, 108.58, 0.211, -63.24, 0.389, -62.83]}, {"time": 0.5667, "x": 108.58, "y": -62.83}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -28.45}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -25.62, "y": -27.45, "curve": [0.211, -73.67, 0.389, -59.49, 0.211, -65.72, 0.389, -112.82]}, {"time": 0.5667, "x": -58.74, "y": -117.91, "curve": [0.589, -58.65, 0.611, -71.98, 0.589, -118.54, 0.611, -96.99]}, {"time": 0.6333, "x": -71.98, "y": -96.99}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.222, 2, 0.411, 0, 0.222, 2, 0.411, 0]}, {"time": 0.6, "x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 24.28}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -28.67, "y": 32.94, "curve": [0.211, -149.04, 0.389, -132.98, 0.211, 111.02, 0.389, 111.02]}, {"time": 0.5667, "x": -132.98, "y": 111.02}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.189, 2, 0.344, 0, 0.189, 2, 0.344, 0]}, {"time": 0.5, "x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -5.43}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 4.88, "y": 54.29, "curve": [0.211, 4.88, 0.389, -26.84, 0.211, 189.3, 0.389, 155.55]}, {"time": 0.5667, "x": -26.84, "y": 155.55}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -32.21}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -44.53, "y": 6.71, "curve": [0.211, -243.8, 0.389, -150.06, 0.211, 6.71, 0.389, 15.86]}, {"time": 0.5667, "x": -150.06, "y": 15.86}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 50.3}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 11.59, "y": -22.57, "curve": [0.211, 11.59, 0.389, -3.05, 0.211, -112.04, 0.389, -152.5]}, {"time": 0.5667, "x": -3.05, "y": -152.5}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.2, 2, 0.367, 0, 0.2, 2, 0.367, 0]}, {"time": 0.5333, "x": 0, "y": 0}]}, "Smoke_9": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -54.71}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 34.77, "y": 7.93, "curve": [0.211, 220.21, 0.389, 174.46, 0.211, 50.22, 0.389, 40.87]}, {"time": 0.5667, "x": 174.46, "y": 40.87}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 12.37}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -32.94, "y": -12.2, "curve": [0.211, -187.47, 0.389, -164.7, 0.211, -49.61, 0.389, 1.83]}, {"time": 0.5667, "x": -164.7, "y": 1.83}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": -5.49, "y": 13.42, "curve": [0.211, -34.77, 0.389, -43.31, 0.211, 13.42, 0.389, -123.83]}, {"time": 0.5667, "x": -43.31, "y": -123.83}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.178, 2, 0.322, 0, 0.178, 2, 0.322, 0]}, {"time": 0.4667, "x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -58.46}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "x": 42.09, "y": -1.22, "curve": [0.211, 42.09, 0.389, 125.05, 0.211, -7.73, 0.389, 86.62]}, {"time": 0.5667, "x": 125.05, "y": 86.62}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}}}, "t1_IDLE_Jumps_tight": {"slots": {"body": {"rgba": [{"color": "ffffffff"}]}, "brow": {"rgba": [{"color": "ffffffff"}]}, "eye": {"rgba": [{"color": "ffffffff"}]}, "hand_L": {"rgba": [{"color": "ffffffff"}]}, "hand_R_colors": {"rgba": [{"color": "ffffffff"}]}, "hand_R_outline": {"rgba": [{"color": "ffffffff"}]}, "horh_L": {"rgba": [{"color": "ffffffff"}]}, "horn_R": {"rgba": [{"color": "ffffffff"}]}, "mouth": {"rgba": [{"color": "ffffffff"}]}, "pupil": {"rgba": [{"color": "ffffffff"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_3": {"attachment": [{}]}, "Smoke_4": {"attachment": [{}]}, "Smoke_5": {"attachment": [{}]}, "Smoke_6": {"attachment": [{}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_9": {"attachment": [{}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}}, "bones": {"Low_cntr": {"translate": [{"curve": [0.168, 0, 0.332, 0, 0.15, -0.39, 0.244, 91.76]}, {"time": 0.5, "y": 93.32, "curve": [0.642, 0, 0.858, 0, 0.74, 94.78, 0.871, 0]}, {"time": 1}], "scale": [{"x": 0.995, "y": 1.005, "curve": [0.09, 1.021, 0.178, 1.05, 0.09, 0.979, 0.178, 0.95]}, {"time": 0.2667, "x": 1.05, "y": 0.95, "curve": [0.433, 1.05, 0.6, 0.95, 0.433, 0.95, 0.6, 1.05]}, {"time": 0.7667, "x": 0.95, "y": 1.05, "curve": [0.845, 0.95, 0.923, 0.972, 0.845, 1.05, 0.923, 1.028]}, {"time": 1, "x": 0.995, "y": 1.005}]}, "mouth": {"scale": [{}], "shear": [{}]}, "Arm_R": {"rotate": [{"value": 1.02, "curve": [0.057, 0.45, 0.112, 0]}, {"time": 0.1667, "curve": [0.333, 0, 0.5, 3.89]}, {"time": 0.6667, "value": 3.89, "curve": [0.778, 3.89, 0.89, 2.17]}, {"time": 1, "value": 1.02}]}, "Arm_R2": {"rotate": [{"value": 0.64, "curve": [0.022, 0.24, 0.044, 0]}, {"time": 0.0667, "curve": [0.233, 0, 0.4, 12.67]}, {"time": 0.5667, "value": 12.67, "curve": [0.678, 12.67, 0.79, 7.06]}, {"time": 0.9, "value": 3.31, "curve": [0.934, 2.21, 0.967, 1.24]}, {"time": 1, "value": 0.64}]}, "Arm_L2": {"rotate": [{"value": -0.67, "curve": [0.022, -0.25, 0.044, 0]}, {"time": 0.0667, "curve": [0.233, 0, 0.4, -15.16]}, {"time": 0.5667, "value": -15.16, "curve": [0.678, -15.16, 0.79, -7.3]}, {"time": 0.9, "value": -3.42, "curve": [0.934, -2.28, 0.967, -1.29]}, {"time": 1, "value": -0.67}]}, "Arm_L": {"rotate": [{"value": 0.69, "curve": [0.045, 1.48, 0.076, 2.04]}, {"time": 0.1667, "value": 2.04, "curve": [0.322, 2.04, 0.478, -2.95]}, {"time": 0.6333, "value": -2.95, "curve": [0.797, -2.95, 0.895, -1.15]}, {"time": 1, "value": 0.69}]}, "Smoke_3": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_1": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_9": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "t1_IDLE_Jumps_tight2": {"slots": {"body": {"rgba": [{"color": "ffffffff"}]}, "brow": {"rgba": [{"color": "ffffffff"}]}, "eye": {"rgba": [{"color": "ffffffff"}]}, "hand_L": {"rgba": [{"color": "ffffffff"}]}, "hand_R_colors": {"rgba": [{"color": "ffffffff"}]}, "hand_R_outline": {"rgba": [{"color": "ffffffff"}]}, "horh_L": {"rgba": [{"color": "ffffffff"}]}, "horn_R": {"rgba": [{"color": "ffffffff"}]}, "mouth": {"rgba": [{"color": "ffffffff"}]}, "pupil": {"rgba": [{"color": "ffffffff"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_3": {"attachment": [{}]}, "Smoke_4": {"attachment": [{}]}, "Smoke_5": {"attachment": [{}]}, "Smoke_6": {"attachment": [{}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_9": {"attachment": [{}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}}, "bones": {"Low_cntr": {"translate": [{"curve": [0.136, 0, 0.264, 0, 0.122, -0.39, 0.192, 91.76]}, {"time": 0.4, "y": 93.32, "curve": [0.515, 0, 0.685, 0, 0.595, 94.78, 0.695, 0]}, {"time": 0.8}], "scale": [{"x": 0.995, "y": 1.005, "curve": [0.073, 1.021, 0.162, 1.05, 0.073, 0.979, 0.162, 0.95]}, {"time": 0.2333, "x": 1.05, "y": 0.95, "curve": [0.369, 1.05, 0.498, 0.95, 0.369, 0.95, 0.498, 1.05]}, {"time": 0.6333, "x": 0.95, "y": 1.05, "curve": [0.697, 0.95, 0.738, 0.972, 0.697, 1.05, 0.738, 1.028]}, {"time": 0.8, "x": 0.995, "y": 1.005}]}, "mouth": {"scale": [{}], "shear": [{}]}, "Arm_R": {"rotate": [{"value": 115.89, "curve": [0.135, 115.89, 0.265, 80.8]}, {"time": 0.4, "value": 80.8, "curve": [0.535, 80.8, 0.665, 115.89]}, {"time": 0.8, "value": 115.89}], "translate": [{"x": 7.46, "y": -18.94, "curve": [0.028, 7.94, 0.04, 8.27, 0.028, -20.39, 0.04, -21.37]}, {"time": 0.0667, "x": 8.27, "y": -21.37, "curve": [0.202, 8.27, 0.365, 0.54, 0.202, -21.37, 0.365, 2.05]}, {"time": 0.5, "x": 0.54, "y": 2.05, "curve": [0.609, 0.54, 0.693, 5.5, 0.609, 2.05, 0.693, -12.99]}, {"time": 0.8, "x": 7.46, "y": -18.94}], "scale": [{"x": -1}]}, "Arm_L": {"rotate": [{"value": -105.78, "curve": [0.135, -105.78, 0.265, -64.58]}, {"time": 0.4, "value": -64.58, "curve": [0.535, -64.58, 0.665, -105.78]}, {"time": 0.8, "value": -105.78}], "translate": [{"x": -6.87, "y": -7.62, "curve": [0.028, -7.16, 0.04, -7.35, 0.028, -9.31, 0.04, -10.46]}, {"time": 0.0667, "x": -7.35, "y": -10.46, "curve": [0.202, -7.35, 0.365, -2.76, 0.202, -10.46, 0.365, 16.88]}, {"time": 0.5, "x": -2.76, "y": 16.88, "curve": [0.609, -2.76, 0.693, -5.71, 0.609, 16.88, 0.693, -0.67]}, {"time": 0.8, "x": -6.87, "y": -7.62}], "scale": [{"x": -1}]}, "Arm_L2": {"rotate": [{"value": -39.68, "curve": [0.109, -30.19, 0.225, -6.74]}, {"time": 0.3333, "value": -6.74, "curve": [0.469, -6.74, 0.598, -43.5]}, {"time": 0.7333, "value": -43.5, "curve": [0.761, -43.5, 0.774, -42.1]}, {"time": 0.8, "value": -39.68}]}, "Arm_R2": {"rotate": [{"value": 30.68, "curve": [0.109, 21.4, 0.225, -1.53]}, {"time": 0.3333, "value": -1.53, "curve": [0.469, -1.53, 0.598, 34.41]}, {"time": 0.7333, "value": 34.41, "curve": [0.761, 34.41, 0.774, 33.04]}, {"time": 0.8, "value": 30.68}]}, "Smoke_3": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_1": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_9": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "t2_IDLE_Eye": {"bones": {"eye": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.111, 1.21, 0.156, 4.55, 0.111, -0.73, 0.156, -2.73]}, {"time": 0.2, "x": 4.55, "y": -2.73, "curve": "stepped"}, {"time": 0.8667, "x": 4.55, "y": -2.73, "curve": [0.922, 4.55, 0.978, 4.55, 0.922, -2.73, 0.978, -5.91]}, {"time": 1.0333, "x": 4.55, "y": -5.91, "curve": "stepped"}, {"time": 1.5, "x": 4.55, "y": -5.91, "curve": [1.533, 4.55, 1.567, 0.91, 1.533, -5.91, 1.567, -5.91]}, {"time": 1.6, "x": 0.91, "y": -5.91, "curve": "stepped"}, {"time": 1.9333, "x": 0.91, "y": -5.91, "curve": [1.967, 0.91, 2, -3.18, 1.967, -5.91, 2, 0]}, {"time": 2.0333, "x": -3.18, "curve": "stepped"}, {"time": 2.6667, "x": -3.18, "curve": [2.711, -3.18, 2.756, -1.82, 2.711, 0, 2.756, -1.82]}, {"time": 2.8, "x": -1.82, "y": -1.82, "curve": "stepped"}, {"time": 3.1333, "x": -1.82, "y": -1.82, "curve": [3.167, -1.82, 3.2, -1.82, 3.167, -1.82, 3.2, 0.45]}, {"time": 3.2333, "x": -1.82, "y": 0.45, "curve": "stepped"}, {"time": 3.6333, "x": -1.82, "y": 0.45, "curve": [3.678, -1.82, 3.722, 0, 3.678, 0.45, 3.722, -1.82]}, {"time": 3.7667, "y": -1.82, "curve": "stepped"}, {"time": 4.4667, "y": -1.82, "curve": [4.522, 0, 4.578, 0, 4.522, -1.82, 4.578, 0]}, {"time": 4.6333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.089, 1, 0.111, 1.1, 0.089, 1, 0.111, 0.9]}, {"time": 0.1333, "x": 1.1, "y": 0.9, "curve": [0.167, 1.1, 0.2, 1, 0.167, 0.9, 0.2, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.6333, "curve": [0.656, 1, 0.695, 1.265, 0.656, 1, 0.689, 0.381]}, {"time": 0.7, "x": 1.464, "y": 0.063, "curve": "stepped"}, {"time": 0.7667, "x": 1.464, "y": 0.063, "curve": [0.809, 1.234, 0.878, 1, 0.795, 0.417, 0.878, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667, "curve": [1.489, 1, 1.511, 1.1, 1.489, 1, 1.511, 0.9]}, {"time": 1.5333, "x": 1.1, "y": 0.9, "curve": [1.567, 1.1, 1.6, 1, 1.567, 0.9, 1.6, 1]}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 1.1, 1.922, 1, 1.944, 0.9]}, {"time": 1.9667, "x": 1.1, "y": 0.9, "curve": [2, 1.1, 2.033, 1, 2, 0.9, 2.033, 1]}, {"time": 2.0667, "curve": "stepped"}, {"time": 2.4667, "curve": [2.489, 1, 2.528, 1.265, 2.489, 1, 2.522, 0.381]}, {"time": 2.5333, "x": 1.464, "y": 0.063, "curve": "stepped"}, {"time": 2.6, "x": 1.464, "y": 0.063, "curve": [2.642, 1.234, 2.711, 1, 2.628, 0.417, 2.711, 1]}, {"time": 2.7667, "curve": "stepped"}, {"time": 3.0667, "curve": [3.089, 1, 3.111, 1.1, 3.089, 1, 3.111, 0.9]}, {"time": 3.1333, "x": 1.1, "y": 0.9, "curve": [3.167, 1.1, 3.2, 1, 3.167, 0.9, 3.2, 1]}, {"time": 3.2333, "curve": "stepped"}, {"time": 3.6, "curve": [3.622, 1, 3.644, 1.1, 3.622, 1, 3.644, 0.9]}, {"time": 3.6667, "x": 1.1, "y": 0.9, "curve": [3.7, 1.1, 3.733, 1, 3.7, 0.9, 3.733, 1]}, {"time": 3.7667, "curve": "stepped"}, {"time": 4.4667, "curve": [4.489, 1, 4.511, 1.1, 4.489, 1, 4.511, 0.9]}, {"time": 4.5333, "x": 1.1, "y": 0.9, "curve": [4.567, 1.1, 4.6, 1, 4.567, 0.9, 4.6, 1]}, {"time": 4.6333}]}, "brow": {"scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.089, 1, 0.111, 1.1, 0.089, 1, 0.111, 0.9]}, {"time": 0.1333, "x": 1.1, "y": 0.9, "curve": [0.167, 1.1, 0.2, 1, 0.167, 0.9, 0.2, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.6333, "curve": [0.656, 1, 0.7, 1.647, 0.656, 1, 0.68, 0.867]}, {"time": 0.7, "x": 1.869, "y": 0.595, "curve": "stepped"}, {"time": 0.7667, "x": 1.869, "y": 0.595, "curve": [0.781, 1.63, 0.878, 1, 0.803, 0.895, 0.878, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.4667, "curve": [1.489, 1, 1.511, 1.1, 1.489, 1, 1.511, 0.9]}, {"time": 1.5333, "x": 1.1, "y": 0.9, "curve": [1.567, 1.1, 1.6, 1, 1.567, 0.9, 1.6, 1]}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 1.1, 1.922, 1, 1.944, 0.9]}, {"time": 1.9667, "x": 1.1, "y": 0.9, "curve": [2, 1.1, 2.033, 1, 2, 0.9, 2.033, 1]}, {"time": 2.0667, "curve": "stepped"}, {"time": 2.4667, "curve": [2.489, 1, 2.533, 1.647, 2.489, 1, 2.514, 0.867]}, {"time": 2.5333, "x": 1.869, "y": 0.595, "curve": "stepped"}, {"time": 2.6, "x": 1.869, "y": 0.595, "curve": [2.615, 1.63, 2.711, 1, 2.637, 0.895, 2.711, 1]}, {"time": 2.7667, "curve": "stepped"}, {"time": 3.0667, "curve": [3.089, 1, 3.111, 1.1, 3.089, 1, 3.111, 0.9]}, {"time": 3.1333, "x": 1.1, "y": 0.9, "curve": [3.167, 1.1, 3.2, 1, 3.167, 0.9, 3.2, 1]}, {"time": 3.2333, "curve": "stepped"}, {"time": 3.6, "curve": [3.622, 1, 3.644, 1.1, 3.622, 1, 3.644, 0.9]}, {"time": 3.6667, "x": 1.1, "y": 0.9, "curve": [3.7, 1.1, 3.733, 1, 3.7, 0.9, 3.733, 1]}, {"time": 3.7667, "curve": "stepped"}, {"time": 4.4667, "curve": [4.489, 1, 4.511, 1.1, 4.489, 1, 4.511, 0.9]}, {"time": 4.5333, "x": 1.1, "y": 0.9, "curve": [4.567, 1.1, 4.6, 1, 4.567, 0.9, 4.6, 1]}, {"time": 4.6333}]}, "mouth": {"translate": [{"curve": "stepped"}, {"time": 0.6333, "curve": [0.656, 0, 0.678, 0, 0.656, 0, 0.678, 7.28]}, {"time": 0.7, "y": 7.28, "curve": "stepped"}, {"time": 0.7667, "y": 7.28, "curve": [0.822, 0, 0.878, 0, 0.822, 7.28, 0.878, 0]}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.4667, "curve": [2.489, 0, 2.511, 0, 2.489, 0, 2.511, 7.28]}, {"time": 2.5333, "y": 7.28, "curve": "stepped"}, {"time": 2.6, "y": 7.28, "curve": [2.656, 0, 2.711, 0, 2.656, 7.28, 2.711, 0]}, {"time": 2.7667}]}, "pupil": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.1, 0, 0.133, 21.38, 0.1, 0, 0.133, -6.37]}, {"time": 0.1667, "x": 21.38, "y": -6.37, "curve": "stepped"}, {"time": 0.6, "x": 21.38, "y": -6.37, "curve": [0.611, 21.38, 0.622, 25.01, 0.611, -6.37, 0.622, -11.82]}, {"time": 0.6333, "x": 25.01, "y": -11.82, "curve": "stepped"}, {"time": 0.8667, "x": 25.01, "y": -11.82, "curve": [0.911, 25.01, 0.956, 11.82, 0.911, -11.82, 0.956, -34.56]}, {"time": 1, "x": 11.82, "y": -34.56, "curve": "stepped"}, {"time": 1.5, "x": 11.82, "y": -34.56, "curve": [1.533, 11.82, 1.567, 0.45, 1.533, -34.56, 1.567, -31.84]}, {"time": 1.6, "x": 0.45, "y": -31.84, "curve": "stepped"}, {"time": 1.9333, "x": 0.45, "y": -31.84, "curve": [1.967, 0.45, 2, -22.29, 1.967, -31.84, 2, -15.92]}, {"time": 2.0333, "x": -22.29, "y": -15.92, "curve": "stepped"}, {"time": 2.6667, "x": -22.29, "y": -15.92, "curve": [2.711, -22.29, 2.756, -23.19, 2.711, -15.92, 2.756, -26.38]}, {"time": 2.8, "x": -23.19, "y": -26.38, "curve": "stepped"}, {"time": 3.1333, "x": -23.19, "y": -26.38, "curve": [3.167, -23.19, 3.2, -22.29, 3.167, -26.38, 3.2, -13.19]}, {"time": 3.2333, "x": -22.29, "y": -13.19, "curve": "stepped"}, {"time": 3.6333, "x": -22.29, "y": -13.19, "curve": [3.678, -22.29, 3.722, -6.37, 3.678, -13.19, 3.722, -26.83]}, {"time": 3.7667, "x": -6.37, "y": -26.83, "curve": "stepped"}, {"time": 4.4667, "x": -6.37, "y": -26.83, "curve": [4.522, -6.37, 4.578, 0, 4.522, -26.83, 4.578, 0]}, {"time": 4.6333, "curve": "stepped"}, {"time": 4.9}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.078, 1.039, 0.089, 1.117, 0.078, 1.061, 0.089, 1.184]}, {"time": 0.1, "x": 1.117, "y": 1.184, "curve": [0.122, 1.117, 0.144, 1.039, 0.122, 1.184, 0.144, 1.061]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 1.023, 0.911, 1.068, 0.889, 1.139, 0.911, 1.416]}, {"time": 0.9333, "x": 1.068, "y": 1.416, "curve": [0.956, 1.068, 0.978, 1.023, 0.956, 1.416, 0.978, 1.139]}, {"time": 1, "curve": "stepped"}, {"time": 1.5}, {"time": 1.5667, "x": 1.651, "y": 0.809}, {"time": 1.6, "curve": "stepped"}, {"time": 1.9333}, {"time": 1.9667, "x": 1.223, "y": 1.329}, {"time": 2.0333, "curve": "stepped"}, {"time": 3.6333}, {"time": 3.7, "x": 1.082, "y": 1.413}, {"time": 3.7667, "curve": "stepped"}, {"time": 4.4667}, {"time": 4.5333, "x": 1.132, "y": 1.558}, {"time": 4.6333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.078, 0, 0.089, 0, 0.078, 14.31, 0.089, 42.94]}, {"time": 0.1, "y": 42.94, "curve": [0.122, 0, 0.144, 0, 0.122, 42.94, 0.144, 14.31]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 0, 0.911, 0, 0.889, -6.07, 0.911, -18.21]}, {"time": 0.9333, "y": -18.21, "curve": [0.956, 0, 0.978, 0, 0.956, -18.21, 0.978, -6.07]}, {"time": 1, "curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 1.9333}, {"time": 1.9667, "y": 28.42}, {"time": 2.0333, "curve": "stepped"}, {"time": 3.6333}, {"time": 3.7, "y": 25.76}, {"time": 3.7667, "curve": "stepped"}, {"time": 4.4667}, {"time": 4.5333, "y": -4.94}, {"time": 4.6333}]}}}}}