{"skeleton": {"hash": "u+yORoedF4Y", "spine": "4.2.38", "x": -170.25, "y": -4.87, "width": 316.57, "height": 347.26, "images": "./Images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "Low_cntr", "parent": "root", "icon": "straightLine"}, {"name": "Body_cntr", "parent": "Low_cntr", "rotation": 0.33, "x": -6.09, "y": 139.27, "icon": "square"}, {"name": "Horn", "parent": "Body_cntr", "length": 80, "rotation": 96.45, "x": -55.53, "y": 90.71}, {"name": "Leg_F_L", "parent": "Body_cntr", "length": 36.92, "rotation": -90, "x": -25.12, "y": -87.96, "inherit": "noRotationOrReflection"}, {"name": "Tail", "parent": "Body_cntr", "length": 51.1, "rotation": -3.68, "x": 101.31, "y": -3.78, "color": "abe323ff"}, {"name": "Leg_B_L", "parent": "Body_cntr", "length": 36.92, "rotation": -90, "x": 63.82, "y": -87.95, "inherit": "noRotationOrReflection"}, {"name": "Leg_B_R", "parent": "Body_cntr", "length": 36.92, "rotation": -90, "x": 43.56, "y": -87.97, "inherit": "noRotationOrReflection"}, {"name": "Leg_F_R", "parent": "Body_cntr", "length": 36.92, "rotation": -90, "x": -51.96, "y": -87.93, "inherit": "noRotationOrReflection"}, {"name": "Face", "parent": "Body_cntr", "x": -78.89, "y": 26.88, "icon": "arrowsB"}, {"name": "Jetpack", "parent": "Body_cntr", "rotation": 0.33, "x": 93.16, "y": 30.14, "scaleX": 0.01, "scaleY": 0.01, "inherit": "noRotationOrReflection", "icon": "fire"}, {"name": "Fire_scale", "parent": "Jetpack", "rotation": -88.07, "x": 4, "y": -90, "scaleX": 0.01, "scaleY": 0.01}, {"name": "fire", "parent": "Fire_scale", "length": 56.54}, {"name": "eye_black", "parent": "Face", "x": -9.69, "y": -13.7, "color": "abe323ff"}, {"name": "eye_black2", "parent": "Face", "x": 26.56, "y": -14.06}, {"name": "Jetpack2", "parent": "Body_cntr", "rotation": 0.33, "x": 63.32, "y": 53.63, "scaleX": 0.01, "scaleY": 0.01, "inherit": "noRotationOrReflection", "icon": "fire"}, {"name": "Rainbow", "parent": "Low_cntr", "x": 2.33, "y": 78.21, "scaleY": 0.01, "inherit": "noScale", "color": "ff2d2dff", "icon": "particles"}, {"name": "Body_parts", "parent": "Body_cntr", "y": -9.35, "icon": "suiteHearts"}, {"name": "star1", "parent": "Body_cntr", "x": 16.77, "y": 157.21}, {"name": "star2", "parent": "Body_cntr", "x": 16.77, "y": 157.21}, {"name": "EyeBrow_L", "parent": "Face", "x": 29.04, "y": 0.84, "icon": "straightLine"}, {"name": "EyeBrow_R", "parent": "Face", "x": -16.35, "y": 1.96, "icon": "straightLine"}, {"name": "Leg_Outline_big2", "parent": "Leg_B_L", "x": -3.31, "icon": "arrowRight"}, {"name": "Leg_Outline_big3", "parent": "Leg_B_R", "x": -3.44, "icon": "arrowRight"}, {"name": "Leg_Outline_big", "parent": "Leg_F_L", "x": -3.83, "icon": "arrowRight"}, {"name": "Leg_Outline_big4", "parent": "Leg_F_R", "x": -3.96, "icon": "arrowRight"}, {"name": "Rainbow_v2", "parent": "Rainbow", "length": 35.47, "rotation": -90, "color": "ff2d2dff"}, {"name": "Rainbow_v2b", "parent": "Rainbow_v2", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2c", "parent": "Rainbow_v2b", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2d", "parent": "Rainbow_v2c", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2e", "parent": "Rainbow_v2d", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2f", "parent": "Rainbow_v2e", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2g", "parent": "Rainbow_v2f", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2h", "parent": "Rainbow_v2g", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2i", "parent": "Rainbow_v2h", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2j", "parent": "Rainbow_v2i", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2k", "parent": "Rainbow_v2j", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2l", "parent": "Rainbow_v2k", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2m", "parent": "Rainbow_v2l", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2n", "parent": "Rainbow_v2m", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2o", "parent": "Rainbow_v2n", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2p", "parent": "Rainbow_v2o", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2q", "parent": "Rainbow_v2p", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2r", "parent": "Rainbow_v2q", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2s", "parent": "Rainbow_v2r", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2t", "parent": "Rainbow_v2s", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2u", "parent": "Rainbow_v2t", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2v", "parent": "Rainbow_v2u", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2w", "parent": "Rainbow_v2v", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Rainbow_v2x", "parent": "Rainbow_v2w", "length": 35.47, "x": 35.47, "color": "ff2d2dff"}, {"name": "Propeller", "parent": "Body_cntr", "rotation": -0.33, "x": 104.62, "y": 20.95}, {"name": "Tail_front", "parent": "Body_cntr", "rotation": -3.68, "x": 111.81, "y": -11.48, "color": "abe323ff", "icon": "pencil"}, {"name": "P<PERSON>e_eye", "parent": "Face", "x": -32.57, "y": -19.22, "skin": true}, {"name": "Pepe_eye2", "parent": "Face", "x": 27.65, "y": -17.68, "skin": true}, {"name": "<PERSON>y_ear", "parent": "Body_parts", "length": 44.76, "rotation": 54.97, "x": 58.07, "y": 110.17, "skin": true}, {"name": "plunger", "parent": "Body_parts", "x": 85.32, "y": 140.15, "skin": true, "icon": "romanI"}, {"name": "Scared_eye1", "parent": "eye_black", "x": -6.9, "skin": true, "color": "abe323ff"}, {"name": "Scared_eye2", "parent": "eye_black2", "x": 14.4, "skin": true}, {"name": "Forelock_hair1", "parent": "Body_parts", "length": 87.92, "rotation": -113.53, "x": -4.26, "y": 108.29, "skin": true, "color": "abe323ff"}, {"name": "Forelock_hair2", "parent": "Body_parts", "length": 86.44, "rotation": -79.46, "x": 23.26, "y": 110.51, "skin": true, "color": "abe323ff"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "parent": "Face", "length": 62.94, "rotation": 72.55, "x": 71.28, "y": 44.97, "skin": true}, {"name": "Shinobi_2", "parent": "Body_parts", "length": 42.33, "rotation": 43.45, "x": 123.66, "y": 47.03, "skin": true, "color": "abe323ff"}, {"name": "Shinobi_1", "parent": "Body_parts", "length": 37.31, "rotation": 68, "x": 119.5, "y": 62.54, "skin": true, "color": "abe323ff"}, {"name": "Shinobi_2b", "parent": "Shinobi_2", "length": 42.33, "x": 42.33, "skin": true, "color": "abe323ff"}, {"name": "Shinobi_1b", "parent": "Shinobi_1", "length": 37.31, "x": 37.31, "skin": true, "color": "abe323ff"}, {"name": "potato1", "parent": "Body_parts", "length": 142.59, "rotation": 66.81, "x": 73.84, "y": -11.63, "skin": true, "color": "abe323ff"}, {"name": "potato2", "parent": "Body_parts", "length": 93.19, "rotation": 101.25, "x": 51.75, "y": -0.81, "skin": true, "color": "abe323ff"}, {"name": "potato3", "parent": "Body_parts", "length": 153.46, "rotation": 129.62, "x": -43.76, "y": -4.45, "skin": true, "color": "abe323ff"}, {"name": "Duck_wing", "parent": "Body_parts", "length": 48.92, "rotation": 4.91, "x": 23.65, "y": -0.67, "skin": true}, {"name": "Poop_propeller", "parent": "Body_cntr", "rotation": -0.33, "x": 132.66, "y": 20.79, "skin": true, "color": "abe323ff"}, {"name": "Poop_jetpack", "parent": "Body_cntr", "rotation": -0.33, "x": 119.74, "y": 28.11, "skin": true, "color": "abe323ff"}, {"name": "Trump_eye_L", "parent": "Face", "x": 27.12, "y": 13.99, "skin": true}, {"name": "<PERSON>_eye_R", "parent": "Face", "x": -37.73, "y": 14.16, "skin": true}, {"name": "Trump_Hat", "parent": "Body_parts", "x": -25.57, "y": 159.51, "skin": true, "icon": "tophat"}, {"name": "Chainsaw_Eye_L", "parent": "Face", "x": 43.92, "y": 21.05, "skin": true}, {"name": "Chainsaw_Eye_R", "parent": "Face", "x": -49.55, "y": 21.05, "skin": true}, {"name": "chain", "parent": "Horn", "length": 100, "rotation": 33.01, "x": 34.09, "y": 12.87, "skin": true}, {"name": "chainsaw_part", "parent": "chain", "rotation": 177.67, "x": -29.28, "y": 60.3, "skin": true}, {"name": "chainsaw_part2", "parent": "chain", "rotation": 177.67, "x": 14.92, "y": 59.89, "skin": true}, {"name": "chainsaw_part3", "parent": "chain", "rotation": 164.02, "x": 60.58, "y": 49.16, "skin": true}, {"name": "chainsaw_part4", "parent": "chain", "rotation": 115.86, "x": 97.15, "y": 20.62, "skin": true}, {"name": "chainsaw_part5", "parent": "chain", "rotation": 62.29, "x": 99.66, "y": -18.35, "skin": true}, {"name": "chainsaw_part6", "parent": "chain", "rotation": 20.57, "x": 67.39, "y": -48.46, "skin": true}, {"name": "chainsaw_part7", "parent": "chain", "rotation": 5.28, "x": 25.84, "y": -59.83, "skin": true}, {"name": "chainsaw_part8", "parent": "chain", "rotation": -4.5, "x": -19.91, "y": -62.44, "skin": true}], "slots": [{"name": "Rainbow2", "bone": "Rainbow", "attachment": "Rainbow4"}, {"name": "potato3", "bone": "potato3", "attachment": "potato2"}, {"name": "Wing2", "bone": "Body_parts", "attachment": "Wing"}, {"name": "Jetpack2", "bone": "Jetpack2"}, {"name": "Leg_Outline_big3", "bone": "Leg_Outline_big3", "attachment": "Leg_Outline_big3"}, {"name": "Leg_Outline_big4", "bone": "Leg_Outline_big4", "attachment": "Leg_Outline_big4"}, {"name": "Leg_color3", "bone": "Leg_B_R", "attachment": "Leg_color3"}, {"name": "Tail", "bone": "Tail", "attachment": "Tail"}, {"name": "Shinobi_1", "bone": "Shinobi_1", "attachment": "Shinobi_1"}, {"name": "Body_outline_big", "bone": "Body_parts", "attachment": "Body_outline_big"}, {"name": "Leg_color4", "bone": "Leg_F_R", "attachment": "Leg_color4"}, {"name": "Leg_Outline_big2", "bone": "Leg_Outline_big2", "attachment": "Leg_Outline_big2"}, {"name": "Leg_Outline_big", "bone": "Leg_Outline_big", "attachment": "Leg_Outline_big"}, {"name": "Leg_color", "bone": "Leg_F_L", "attachment": "Leg_color"}, {"name": "Leg_color2", "bone": "Leg_B_L", "attachment": "Leg_color2"}, {"name": "Body", "bone": "Body_parts", "attachment": "Body"}, {"name": "Leg_feature", "bone": "Leg_F_L", "attachment": "Leg_feature"}, {"name": "Leg_feature2", "bone": "Leg_B_L", "attachment": "Leg_feature2"}, {"name": "Tail_front", "bone": "Tail_front", "attachment": "Tail_front"}, {"name": "Horn_under_face", "bone": "Horn", "attachment": "Horn_under_face"}, {"name": "Face/Mouth", "bone": "Face", "attachment": "Mouth"}, {"name": "Face/Trump_brows", "bone": "Face", "attachment": "Trump_brows"}, {"name": "Face/EyeBrow_L", "bone": "EyeBrow_L"}, {"name": "Face/EyeBrow_R", "bone": "EyeBrow_R"}, {"name": "Face/eye_black", "bone": "eye_black", "attachment": "eye"}, {"name": "Face/eye_black2", "bone": "eye_black2", "attachment": "eye2"}, {"name": "Face/<PERSON>_eye_L", "bone": "Trump_eye_L", "attachment": "Trump_eye_L"}, {"name": "Face/<PERSON>_eye_R", "bone": "<PERSON>_eye_R", "attachment": "<PERSON>_eye_R"}, {"name": "Face/Face", "bone": "Face", "attachment": "Face"}, {"name": "Face/Pepe_Eye1", "bone": "P<PERSON>e_eye", "attachment": "Pepe_Eye1"}, {"name": "Face/Pepe_Eye2", "bone": "Pepe_eye2", "attachment": "Pepe_Eye2"}, {"name": "plunger", "bone": "plunger", "attachment": "plunger"}, {"name": "potato2", "bone": "potato2", "attachment": "potato2"}, {"name": "potato1", "bone": "potato1", "attachment": "potato1"}, {"name": "Body_features", "bone": "Body_parts", "attachment": "Body_features"}, {"name": "Shinobi_2", "bone": "Shinobi_2", "attachment": "Shinobi_2"}, {"name": "Horn_outline_big", "bone": "Horn", "attachment": "Horn_outline"}, {"name": "Horn", "bone": "Horn", "attachment": "Horn"}, {"name": "Capy_ear2", "bone": "Body_parts", "attachment": "Capy_ear2"}, {"name": "<PERSON>y_ear", "bone": "<PERSON>y_ear", "attachment": "<PERSON>y_ear"}, {"name": "fire", "bone": "fire", "attachment": "fire"}, {"name": "Jetpack", "bone": "Jetpack"}, {"name": "star1", "bone": "star1", "attachment": "star-2"}, {"name": "star2", "bone": "star2", "attachment": "star-2"}, {"name": "Propeller", "bone": "Propeller"}, {"name": "Wing", "bone": "Body_parts", "attachment": "Wing"}, {"name": "Scared_eye1", "bone": "Scared_eye1", "attachment": "Scared_eye1"}, {"name": "Scared_eye2", "bone": "Scared_eye2", "attachment": "Scared_eye2"}, {"name": "Hat", "bone": "Trump_Hat", "attachment": "Hat"}, {"name": "Chainsaw_Eye_L", "bone": "Chainsaw_Eye_L", "attachment": "Chainsaw_Eye_L"}, {"name": "Chainsaw_Eye_R", "bone": "Chainsaw_Eye_R", "attachment": "Chainsaw_Eye_L"}, {"name": "chainsaw_part", "bone": "chainsaw_part", "attachment": "chainsaw_part"}, {"name": "chainsaw_part2", "bone": "chainsaw_part2", "attachment": "chainsaw_part"}, {"name": "chainsaw_part3", "bone": "chainsaw_part3", "attachment": "chainsaw_part"}, {"name": "chainsaw_part4", "bone": "chainsaw_part4", "attachment": "chainsaw_part"}, {"name": "chainsaw_part5", "bone": "chainsaw_part5", "attachment": "chainsaw_part"}, {"name": "chainsaw_part6", "bone": "chainsaw_part6", "attachment": "chainsaw_part"}, {"name": "chainsaw_part7", "bone": "chainsaw_part7", "attachment": "chainsaw_part"}, {"name": "chainsaw_part8", "bone": "chainsaw_part8", "attachment": "chainsaw_part"}], "transform": [{"name": "eyes_pepe", "skin": true, "bones": ["Pepe_eye2", "P<PERSON>e_eye"], "target": "eye_black", "mixRotate": 0, "mixX": 0, "mixScaleX": 0.4, "mixScaleY": 1.2, "mixShearY": 0}, {"name": "eyes_trump", "order": 1, "skin": true, "bones": ["Trump_eye_L", "<PERSON>_eye_R"], "target": "eye_black", "mixRotate": 0, "mixX": 0, "mixScaleY": 0.9, "mixShearY": 0}, {"name": "eye_chainsaw", "order": 28, "skin": true, "bones": ["Chainsaw_Eye_L", "Chainsaw_Eye_R"], "target": "eye_black", "mixRotate": 0, "mixX": 0, "mixShearY": 0}, {"name": "Poop_jetpack", "order": 27, "skin": true, "bones": ["Jetpack"], "target": "Poop_jetpack", "mixRotate": 0, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "Poop_propeller", "order": 26, "skin": true, "bones": ["Propeller"], "target": "Poop_propeller", "mixRotate": 0, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "Forelock_hair1", "order": 29, "skin": true, "bone": "Forelock_hair1", "rotate": 1, "inertia": 0.35, "strength": 50, "damping": 0.9}, {"name": "Forelock_hair2", "order": 30, "skin": true, "bone": "Forelock_hair2", "rotate": 1, "inertia": 0.35, "strength": 50, "damping": 0.9}, {"name": "potato1", "order": 35, "skin": true, "bone": "potato1", "x": 0.1, "y": 0.5, "rotate": 1, "inertia": 0.75, "damping": 0.85, "mass": 1.5}, {"name": "potato2", "order": 36, "skin": true, "bone": "potato2", "x": 0.1, "y": 0.5, "rotate": 1, "inertia": 0.5, "damping": 0.9}, {"name": "potato3", "order": 37, "skin": true, "bone": "potato3", "x": 0.1, "y": 0.5, "rotate": 1, "inertia": 0.75, "damping": 0.85, "mass": 2}, {"name": "Rainbow_v2", "order": 2, "bone": "Rainbow_v2", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2b", "order": 3, "bone": "Rainbow_v2b", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2c", "order": 4, "bone": "Rainbow_v2c", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2d", "order": 5, "bone": "Rainbow_v2d", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2e", "order": 6, "bone": "Rainbow_v2e", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2f", "order": 7, "bone": "Rainbow_v2f", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2g", "order": 8, "bone": "Rainbow_v2g", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2h", "order": 9, "bone": "Rainbow_v2h", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2i", "order": 10, "bone": "Rainbow_v2i", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2j", "order": 11, "bone": "Rainbow_v2j", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2k", "order": 12, "bone": "Rainbow_v2k", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2l", "order": 13, "bone": "Rainbow_v2l", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2m", "order": 14, "bone": "Rainbow_v2m", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2n", "order": 15, "bone": "Rainbow_v2n", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2o", "order": 16, "bone": "Rainbow_v2o", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2p", "order": 17, "bone": "Rainbow_v2p", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2q", "order": 18, "bone": "Rainbow_v2q", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2r", "order": 19, "bone": "Rainbow_v2r", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2s", "order": 20, "bone": "Rainbow_v2s", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2t", "order": 21, "bone": "Rainbow_v2t", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2u", "order": 22, "bone": "Rainbow_v2u", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2v", "order": 23, "bone": "Rainbow_v2v", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2w", "order": 24, "bone": "Rainbow_v2w", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Rainbow_v2x", "order": 25, "bone": "Rainbow_v2x", "rotate": 1, "inertia": 0.25, "strength": 200, "damping": 0.5, "gravity": 350}, {"name": "Shinobi_1", "order": 33, "skin": true, "bone": "Shinobi_1", "rotate": 1, "inertia": 0.3, "damping": 0.9}, {"name": "Shinobi_1b", "order": 34, "skin": true, "bone": "Shinobi_1b", "rotate": 1, "inertia": 0.3, "damping": 0.9}, {"name": "Shinobi_2", "order": 31, "skin": true, "bone": "Shinobi_2", "rotate": 1, "inertia": 0.3, "damping": 0.9}, {"name": "Shinobi_2b", "order": 32, "skin": true, "bone": "Shinobi_2b", "rotate": 1, "inertia": 0.3, "damping": 0.9}], "skins": [{"name": "default", "attachments": {"fire": {"fire": {"path": "v3/fire", "x": 25.36, "y": -0.23, "rotation": 88.07, "width": 112, "height": 83}}, "Propeller": {"st_1": {"path": "v3/st_1", "x": 0.5, "y": -4.09, "width": 78, "height": 235}, "st_2": {"path": "v3/st_2", "x": -5.5, "y": 11.91, "width": 84, "height": 221}, "st_3": {"path": "v3/st_3", "x": -4, "y": 2.91, "width": 87, "height": 245}, "st_4": {"path": "v3/st_4", "x": -1, "y": 2.91, "width": 79, "height": 245}, "st_5": {"path": "v3/st_5", "x": -1, "y": 2.91, "width": 79, "height": 245}, "st_6": {"path": "v3/st_6", "y": 2.91, "width": 79, "height": 245}, "st_7": {"path": "v3/st_7", "y": -0.09, "width": 79, "height": 255}, "st_66": {"path": "v3/st_6", "y": -3.83, "scaleY": -1, "width": 79, "height": 245}}, "Rainbow2": {"Rainbow4": {"type": "mesh", "path": "v3/Rainbow2", "uvs": [1, 1, 0, 1, 0, 0.97826, 0, 0.95652, 0, 0.93478, 0, 0.91304, 0, 0.8913, 0, 0.86957, 0, 0.84783, 0, 0.82609, 0, 0.80435, 0, 0.78261, 0, 0.76087, 0, 0.73913, 0, 0.71739, 0, 0.69565, 0, 0.67391, 0, 0.65217, 0, 0.63043, 0, 0.6087, 0, 0.58696, 0, 0.56522, 0, 0.54348, 0, 0.52174, 0, 0.5, 0, 0.47826, 0, 0.45652, 0, 0.43478, 0, 0.41304, 0, 0.3913, 0, 0.36957, 0, 0.34783, 0, 0.32609, 0, 0.30435, 0, 0.28261, 0, 0.26087, 0, 0.23913, 0, 0.21739, 0, 0.19565, 0, 0.17391, 0, 0.15217, 0, 0.13043, 0, 0.1087, 0, 0.08696, 0, 0.06522, 0, 0.04348, 0, 0.02174, 0, 0, 1, 0, 1, 0.02174, 1, 0.04348, 1, 0.06522, 1, 0.08696, 1, 0.1087, 1, 0.13043, 1, 0.15217, 1, 0.17391, 1, 0.19565, 1, 0.21739, 1, 0.23913, 1, 0.26087, 1, 0.28261, 1, 0.30435, 1, 0.32609, 1, 0.34783, 1, 0.36957, 1, 0.3913, 1, 0.41304, 1, 0.43478, 1, 0.45652, 1, 0.47826, 1, 0.5, 1, 0.52174, 1, 0.54348, 1, 0.56522, 1, 0.58696, 1, 0.6087, 1, 0.63043, 1, 0.65217, 1, 0.67391, 1, 0.69565, 1, 0.71739, 1, 0.73913, 1, 0.76087, 1, 0.78261, 1, 0.80435, 1, 0.82609, 1, 0.84783, 1, 0.86957, 1, 0.8913, 1, 0.91304, 1, 0.93478, 1, 0.95652, 1, 0.97826], "triangles": [0, 2, 93, 0, 1, 2, 2, 3, 93, 3, 92, 93, 3, 4, 92, 4, 91, 92, 4, 5, 91, 5, 90, 91, 5, 6, 90, 6, 89, 90, 6, 7, 89, 7, 88, 89, 7, 8, 88, 8, 87, 88, 8, 9, 87, 9, 86, 87, 9, 10, 86, 10, 85, 86, 10, 11, 85, 11, 84, 85, 11, 12, 84, 12, 83, 84, 12, 13, 83, 13, 82, 83, 13, 14, 82, 14, 81, 82, 16, 17, 79, 14, 15, 81, 15, 80, 81, 15, 16, 80, 16, 79, 80, 17, 78, 79, 17, 18, 78, 18, 77, 78, 18, 19, 77, 19, 76, 77, 19, 20, 76, 20, 75, 76, 20, 21, 75, 21, 74, 75, 21, 22, 74, 22, 73, 74, 23, 72, 73, 22, 23, 73, 23, 24, 72, 24, 71, 72, 24, 25, 71, 25, 70, 71, 25, 26, 70, 26, 69, 70, 26, 27, 69, 27, 68, 69, 27, 28, 68, 28, 67, 68, 28, 29, 67, 29, 66, 67, 30, 65, 66, 29, 30, 66, 30, 31, 65, 31, 64, 65, 31, 32, 64, 32, 63, 64, 32, 33, 63, 33, 62, 63, 33, 34, 62, 34, 61, 62, 34, 35, 61, 35, 60, 61, 35, 36, 60, 36, 59, 60, 36, 37, 59, 37, 58, 59, 37, 38, 58, 38, 57, 58, 38, 39, 57, 39, 56, 57, 39, 40, 56, 40, 55, 56, 40, 41, 55, 41, 54, 55, 41, 42, 54, 42, 53, 54, 42, 43, 53, 43, 52, 53, 43, 44, 52, 44, 51, 52, 44, 45, 51, 45, 50, 51, 45, 46, 50, 46, 49, 50, 47, 48, 49, 46, 47, 49], "vertices": [3, 47, 101.14, 113.85, 0.0074, 48, 65.67, 113.85, 0.27061, 49, 30.2, 113.85, 0.72198, 3, 47, 101.14, -115.15, 0.00508, 48, 65.67, -115.15, 0.29652, 49, 30.2, -115.15, 0.6984, 4, 46, 118.37, -115.15, 0.00031, 47, 82.9, -115.15, 0.03131, 48, 47.43, -115.15, 0.36463, 49, 11.96, -115.15, 0.60375, 4, 46, 100.13, -115.15, 0.00928, 47, 64.66, -115.15, 0.12202, 48, 29.19, -115.15, 0.4856, 49, -6.27, -115.15, 0.3831, 4, 46, 81.89, -115.15, 0.0488, 47, 46.42, -115.15, 0.28023, 48, 10.95, -115.15, 0.50147, 49, -24.51, -115.15, 0.1695, 4, 46, 63.65, -115.15, 0.15154, 47, 28.18, -115.15, 0.43767, 48, -7.28, -115.15, 0.36054, 49, -42.75, -115.15, 0.05024, 4, 46, 45.41, -115.15, 0.33576, 47, 9.94, -115.15, 0.4712, 48, -25.52, -115.15, 0.18535, 49, -60.99, -115.15, 0.00768, 4, 45, 62.64, -115.15, 0.15118, 46, 27.17, -115.15, 0.47629, 47, -8.29, -115.15, 0.31024, 48, -43.76, -115.15, 0.06229, 4, 44, 79.87, -115.15, 0.06729, 45, 44.4, -115.15, 0.30786, 46, 8.94, -115.15, 0.47799, 47, -26.53, -115.15, 0.14687, 4, 44, 61.63, -115.15, 0.1771, 45, 26.16, -115.15, 0.43949, 46, -9.3, -115.15, 0.33569, 47, -44.77, -115.15, 0.04772, 4, 43, 78.86, -115.15, 0.0616, 44, 43.39, -115.15, 0.34343, 45, 7.93, -115.15, 0.42809, 46, -27.54, -115.15, 0.16688, 4, 43, 60.62, -115.15, 0.17354, 44, 25.16, -115.15, 0.47863, 45, -10.31, -115.15, 0.28744, 46, -45.78, -115.15, 0.06039, 4, 42, 77.85, -115.15, 0.07209, 43, 42.38, -115.15, 0.34235, 44, 6.92, -115.15, 0.45292, 45, -28.55, -115.15, 0.13264, 4, 42, 59.61, -115.15, 0.1915, 43, 24.15, -115.15, 0.47307, 44, -11.32, -115.15, 0.2916, 45, -46.79, -115.15, 0.04383, 4, 41, 76.84, -115.15, 0.07015, 42, 41.38, -115.15, 0.36141, 43, 5.91, -115.15, 0.43916, 44, -29.56, -115.15, 0.12928, 4, 41, 58.6, -115.15, 0.18619, 42, 23.14, -115.15, 0.49101, 43, -12.33, -115.15, 0.28214, 44, -47.8, -115.15, 0.04066, 4, 40, 75.83, -115.15, 0.09547, 41, 40.37, -115.15, 0.343, 42, 4.9, -115.15, 0.4372, 43, -30.57, -115.15, 0.12432, 4, 40, 57.6, -115.15, 0.23153, 41, 22.13, -115.15, 0.45507, 42, -13.34, -115.15, 0.2728, 43, -48.81, -115.15, 0.0406, 4, 39, 74.82, -115.15, 0.06437, 40, 39.36, -115.15, 0.40952, 41, 3.89, -115.15, 0.40629, 42, -31.58, -115.15, 0.11982, 4, 39, 56.59, -115.15, 0.18367, 40, 21.12, -115.15, 0.52462, 41, -14.35, -115.15, 0.25446, 42, -49.82, -115.15, 0.03724, 4, 38, 73.82, -115.15, 0.08878, 39, 38.35, -115.15, 0.34905, 40, 2.88, -115.15, 0.45272, 41, -32.59, -115.15, 0.10945, 4, 38, 55.58, -115.15, 0.22253, 39, 20.11, -115.15, 0.4673, 40, -15.36, -115.15, 0.27607, 41, -50.83, -115.15, 0.03411, 4, 37, 72.81, -115.15, 0.09181, 38, 37.34, -115.15, 0.3904, 39, 1.87, -115.15, 0.4031, 40, -33.6, -115.15, 0.11468, 4, 37, 54.57, -115.15, 0.22547, 38, 19.1, -115.15, 0.49649, 39, -16.37, -115.15, 0.24394, 40, -51.84, -115.15, 0.03409, 4, 36, 71.8, -115.15, 0.08837, 37, 36.33, -115.15, 0.39139, 38, 0.86, -115.15, 0.41684, 39, -34.61, -115.15, 0.1034, 4, 36, 53.56, -115.15, 0.22744, 37, 18.09, -115.15, 0.48628, 38, -17.38, -115.15, 0.25508, 39, -52.85, -115.15, 0.0312, 4, 35, 70.79, -115.15, 0.10147, 36, 35.32, -115.15, 0.39563, 37, -0.15, -115.15, 0.39779, 38, -35.62, -115.15, 0.10512, 4, 35, 52.55, -115.15, 0.25053, 36, 17.08, -115.15, 0.48645, 37, -18.39, -115.15, 0.23165, 38, -53.86, -115.15, 0.03137, 4, 34, 69.78, -115.15, 0.09968, 35, 34.31, -115.15, 0.42217, 36, -1.16, -115.15, 0.38795, 37, -36.63, -115.15, 0.0902, 4, 33, 87.01, -115.15, 0.04015, 34, 51.54, -115.15, 0.2418, 35, 16.07, -115.15, 0.50218, 36, -19.4, -115.15, 0.21588, 4, 33, 68.77, -115.15, 0.12483, 34, 33.3, -115.15, 0.39652, 35, -2.17, -115.15, 0.39604, 36, -37.64, -115.15, 0.08262, 4, 32, 86, -115.15, 0.0254, 33, 50.53, -115.15, 0.28563, 34, 15.06, -115.15, 0.46511, 35, -20.41, -115.15, 0.22386, 4, 32, 67.76, -115.15, 0.09656, 33, 32.29, -115.15, 0.45802, 34, -3.18, -115.15, 0.35763, 35, -38.65, -115.15, 0.08779, 4, 31, 84.99, -115.15, 0.03643, 32, 49.52, -115.15, 0.24606, 33, 14.05, -115.15, 0.52291, 34, -21.42, -115.15, 0.1946, 4, 31, 66.75, -115.15, 0.11527, 32, 31.28, -115.15, 0.41491, 33, -4.19, -115.15, 0.39778, 34, -39.66, -115.15, 0.07204, 4, 30, 83.98, -115.15, 0.05444, 31, 48.51, -115.15, 0.25888, 32, 13.04, -115.15, 0.47414, 33, -22.43, -115.15, 0.21254, 4, 30, 65.74, -115.15, 0.15064, 31, 30.27, -115.15, 0.40908, 32, -5.2, -115.15, 0.35872, 33, -40.67, -115.15, 0.08156, 4, 29, 82.97, -115.15, 0.05102, 30, 47.5, -115.15, 0.31017, 31, 12.03, -115.15, 0.44798, 32, -23.44, -115.15, 0.19083, 4, 29, 64.73, -115.15, 0.15112, 30, 29.26, -115.15, 0.4496, 31, -6.21, -115.15, 0.33016, 32, -41.68, -115.15, 0.06913, 4, 28, 81.96, -115.15, 0.04423, 29, 46.49, -115.15, 0.32401, 30, 11.02, -115.15, 0.46037, 31, -24.45, -115.15, 0.17139, 4, 28, 63.72, -115.15, 0.1331, 29, 28.25, -115.15, 0.4866, 30, -7.22, -115.15, 0.31844, 31, -42.69, -115.15, 0.06187, 4, 27, 80.95, -115.15, 0.05688, 28, 45.48, -115.15, 0.28592, 29, 10.01, -115.15, 0.50569, 30, -25.46, -115.15, 0.1515, 4, 27, 62.71, -115.15, 0.16536, 28, 27.24, -115.15, 0.42033, 29, -8.23, -115.15, 0.3647, 30, -43.7, -115.15, 0.04961, 4, 26, 79.94, -115.15, 0.05174, 27, 44.47, -115.15, 0.33579, 28, 9, -115.15, 0.4236, 29, -26.47, -115.15, 0.18888, 4, 26, 61.7, -115.15, 0.16723, 27, 26.23, -115.15, 0.4825, 28, -9.24, -115.15, 0.27932, 29, -44.71, -115.15, 0.07096, 4, 26, 43.46, -115.15, 0.3718, 27, 7.99, -115.15, 0.48746, 28, -27.48, -115.15, 0.12259, 29, -62.94, -115.15, 0.01816, 4, 26, 25.22, -115.15, 0.59208, 27, -10.25, -115.15, 0.37278, 28, -45.72, -115.15, 0.03302, 29, -81.18, -115.15, 0.00211, 3, 26, 6.98, -115.15, 0.68677, 27, -28.49, -115.15, 0.3073, 28, -63.95, -115.15, 0.00593, 3, 26, 6.98, 113.85, 0.64974, 27, -28.49, 113.85, 0.33897, 28, -63.95, 113.85, 0.01129, 4, 26, 25.22, 113.85, 0.55727, 27, -10.25, 113.85, 0.39967, 28, -45.72, 113.85, 0.04185, 29, -81.18, 113.85, 0.00122, 4, 26, 43.46, 113.85, 0.34639, 27, 7.99, 113.85, 0.49896, 28, -27.48, 113.85, 0.14075, 29, -62.94, 113.85, 0.0139, 4, 26, 61.7, 113.85, 0.15234, 27, 26.23, 113.85, 0.48546, 28, -9.24, 113.85, 0.30341, 29, -44.71, 113.85, 0.05878, 4, 26, 79.94, 113.85, 0.05398, 27, 44.47, 113.85, 0.3375, 28, 9, 113.85, 0.43338, 29, -26.47, 113.85, 0.17513, 4, 27, 62.71, 113.85, 0.16707, 28, 27.24, 113.85, 0.44269, 29, -8.23, 113.85, 0.33625, 30, -43.7, 113.85, 0.05399, 4, 27, 80.95, 113.85, 0.06216, 28, 45.48, 113.85, 0.29419, 29, 10.01, 113.85, 0.48048, 30, -25.46, 113.85, 0.16317, 4, 28, 63.72, 113.85, 0.13698, 29, 28.25, 113.85, 0.47483, 30, -7.22, 113.85, 0.34009, 31, -42.69, 113.85, 0.04811, 4, 28, 81.96, 113.85, 0.04316, 29, 46.49, 113.85, 0.31916, 30, 11.02, 113.85, 0.48983, 31, -24.45, 113.85, 0.14784, 4, 29, 64.73, 113.85, 0.15236, 30, 29.26, 113.85, 0.4821, 31, -6.21, 113.85, 0.30984, 32, -41.68, 113.85, 0.05571, 4, 29, 82.97, 113.85, 0.05248, 30, 47.5, 113.85, 0.33072, 31, 12.03, 113.85, 0.45531, 32, -23.44, 113.85, 0.16149, 4, 30, 65.74, 113.85, 0.15852, 31, 30.27, 113.85, 0.44043, 32, -5.2, 113.85, 0.31868, 33, -40.67, 113.85, 0.08237, 4, 30, 83.98, 113.85, 0.05261, 31, 48.51, 113.85, 0.29764, 32, 13.04, 113.85, 0.44404, 33, -22.43, 113.85, 0.20571, 4, 31, 66.75, 113.85, 0.1404, 32, 31.28, 113.85, 0.41141, 33, -4.19, 113.85, 0.37712, 34, -39.66, 113.85, 0.07108, 4, 31, 84.99, 113.85, 0.04831, 32, 49.52, 113.85, 0.26041, 33, 14.05, 113.85, 0.49758, 34, -21.42, 113.85, 0.19369, 4, 32, 67.76, 113.85, 0.11207, 33, 32.29, 113.85, 0.43909, 34, -3.18, 113.85, 0.35971, 35, -38.65, 113.85, 0.08913, 4, 32, 86, 113.85, 0.03393, 33, 50.53, 113.85, 0.27264, 34, 15.06, 113.85, 0.46587, 35, -20.41, 113.85, 0.22757, 4, 33, 68.77, 113.85, 0.11661, 34, 33.3, 113.85, 0.40185, 35, -2.17, 113.85, 0.40596, 36, -37.64, 113.85, 0.07557, 4, 33, 87.01, 113.85, 0.03464, 34, 51.54, 113.85, 0.24161, 35, 16.07, 113.85, 0.52091, 36, -19.4, 113.85, 0.20284, 4, 34, 69.78, 113.85, 0.10116, 35, 34.31, 113.85, 0.43398, 36, -1.16, 113.85, 0.37254, 37, -36.63, 113.85, 0.09233, 4, 34, 88.02, 113.85, 0.02904, 35, 52.55, 113.85, 0.26254, 36, 17.08, 113.85, 0.47581, 37, -18.39, 113.85, 0.23261, 4, 35, 70.79, 113.85, 0.10818, 36, 35.32, 113.85, 0.39126, 37, -0.15, 113.85, 0.40487, 38, -35.62, 113.85, 0.09569, 4, 35, 89.03, 113.85, 0.03122, 36, 53.56, 113.85, 0.2267, 37, 18.09, 113.85, 0.50206, 38, -17.38, 113.85, 0.24002, 4, 36, 71.8, 113.85, 0.09103, 37, 36.33, 113.85, 0.40616, 38, 0.86, 113.85, 0.42039, 39, -34.61, 113.85, 0.08242, 4, 37, 54.57, 113.85, 0.22918, 38, 19.1, 113.85, 0.51084, 39, -16.37, 113.85, 0.21799, 40, -51.84, 113.85, 0.04199, 4, 37, 72.81, 113.85, 0.08611, 38, 37.34, 113.85, 0.40835, 39, 1.87, 113.85, 0.38045, 40, -33.6, 113.85, 0.12508, 4, 38, 55.58, 113.85, 0.23271, 39, 20.11, 113.85, 0.44274, 40, -15.36, 113.85, 0.28091, 41, -50.83, 113.85, 0.04364, 4, 38, 73.82, 113.85, 0.09352, 39, 38.35, 113.85, 0.33687, 40, 2.88, 113.85, 0.44122, 41, -32.59, 113.85, 0.12838, 4, 39, 56.59, 113.85, 0.17932, 40, 21.12, 113.85, 0.50005, 41, -14.35, 113.85, 0.28258, 42, -49.82, 113.85, 0.03805, 4, 39, 74.82, 113.85, 0.06524, 40, 39.36, 113.85, 0.37406, 41, 3.89, 113.85, 0.43828, 42, -31.58, 113.85, 0.12242, 4, 40, 57.6, 113.85, 0.20025, 41, 22.13, 113.85, 0.48368, 42, -13.34, 113.85, 0.28125, 43, -48.81, 113.85, 0.03482, 4, 40, 75.83, 113.85, 0.07544, 41, 40.37, 113.85, 0.35893, 42, 4.9, 113.85, 0.44702, 43, -30.57, 113.85, 0.11862, 4, 41, 58.6, 113.85, 0.18599, 42, 23.14, 113.85, 0.48837, 43, -12.33, 113.85, 0.27575, 44, -47.8, 113.85, 0.04989, 4, 41, 76.84, 113.85, 0.06631, 42, 41.38, 113.85, 0.35439, 43, 5.91, 113.85, 0.43566, 44, -29.56, 113.85, 0.14365, 4, 42, 59.61, 113.85, 0.17899, 43, 24.15, 113.85, 0.47129, 44, -11.32, 113.85, 0.30089, 45, -46.79, 113.85, 0.04883, 4, 42, 77.85, 113.85, 0.06424, 43, 42.38, 113.85, 0.34173, 44, 6.92, 113.85, 0.44702, 45, -28.55, 113.85, 0.14701, 4, 43, 60.62, 113.85, 0.17707, 44, 25.15, 113.85, 0.46266, 45, -10.31, 113.85, 0.31342, 46, -45.78, 113.85, 0.04685, 4, 43, 78.86, 113.85, 0.0644, 44, 43.39, 113.85, 0.32614, 45, 7.93, 113.85, 0.46269, 46, -27.54, 113.85, 0.14676, 4, 44, 61.63, 113.85, 0.15944, 45, 26.16, 113.85, 0.47205, 46, -9.3, 113.85, 0.32198, 47, -44.77, 113.85, 0.04653, 4, 44, 79.87, 113.85, 0.05391, 45, 44.4, 113.85, 0.32141, 46, 8.93, 113.85, 0.48001, 47, -26.53, 113.85, 0.14467, 4, 45, 62.64, 113.85, 0.15212, 46, 27.17, 113.85, 0.47875, 47, -8.29, 113.85, 0.30765, 48, -43.76, 113.85, 0.06148, 4, 46, 45.41, 113.85, 0.34152, 47, 9.94, 113.85, 0.4702, 48, -25.52, 113.85, 0.17959, 49, -60.99, 113.85, 0.00869, 4, 46, 63.65, 113.85, 0.15668, 47, 28.18, 113.85, 0.44263, 48, -7.28, 113.85, 0.34847, 49, -42.75, 113.85, 0.05222, 4, 46, 81.89, 113.85, 0.05217, 47, 46.42, 113.85, 0.28916, 48, 10.95, 113.85, 0.48571, 49, -24.51, 113.85, 0.17296, 4, 46, 100.13, 113.85, 0.01117, 47, 64.66, 113.85, 0.12704, 48, 29.19, 113.85, 0.47189, 49, -6.28, 113.85, 0.38991, 4, 46, 118.37, 113.85, 0.00059, 47, 82.9, 113.85, 0.03367, 48, 47.43, 113.85, 0.34165, 49, 11.96, 113.85, 0.62409], "hull": 94, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 0, 0, 2, 94, 96], "width": 229, "height": 839}}, "star1": {"star-2": {"path": "v3/star-2", "width": 49, "height": 61}}, "star2": {"star-2": {"path": "v3/star-2", "width": 49, "height": 61}}}}, {"name": "Alien", "attachments": {"Body": {"Body": {"name": "Alien/body", "x": 2.58, "y": 21.99, "width": 219, "height": 207}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "Alien/horn", "x": 35.9, "y": -3.16, "rotation": -96.45, "width": 53, "height": 106}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Alien/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Alien/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Alien/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Alien/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/eye_black": {"eye": {"name": "Alien/eye_l", "x": -11.68, "y": 0.07, "width": 50, "height": 60}, "eye_cross2": {"path": "v3/eye_cross2", "x": -17.15, "y": 0.1, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"name": "Alien/eye_r", "x": 13.95, "y": -0.08, "width": 51, "height": 60}, "eye_cross": {"path": "v3/eye_cross", "x": 13.55, "y": -0.08, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "Alien/mouth", "x": 7.21, "y": -42.53, "rotation": -0.33, "width": 34, "height": 32}}}}, {"name": "Base", "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Tail2", "type": "<PERSON><PERSON><PERSON>", "path": "v3/Tail2", "skin": "Scared", "parent": "Tail", "width": 102, "height": 135}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "Capy", "bones": ["<PERSON>y_ear"], "attachments": {"Body": {"Body": {"name": "Capy/body", "x": 2.58, "y": 21.99, "width": 219, "height": 207}}, "Body_features": {"Body_features": {"name": "Capy/wool", "type": "mesh", "uvs": [0.47451, 0, 0.97817, 0.33958, 1, 0.8406, 1, 1, 0.92402, 1, 0.55296, 0.92214, 0.5723, 0.84622, 0.88233, 0.94969, 0.95511, 0.83209, 0.49218, 0.09425, 0.11443, 0.24402, 0.04328, 0.79757, 0, 0.69252, 0, 0.66328, 0.03184, 0.21214, 0.45206, 0], "triangles": [1, 9, 0, 10, 14, 15, 9, 10, 15, 9, 15, 0, 13, 14, 10, 11, 12, 13, 10, 11, 13, 8, 9, 1, 2, 8, 1, 4, 7, 8, 3, 4, 8, 5, 6, 7, 4, 5, 7, 3, 8, 2], "vertices": [-5.39, 147.58, 128.59, 71.85, 134.39, -39.88, 134.39, -75.42, 114.18, -75.42, 15.48, -58.06, 20.63, -41.13, 103.09, -64.2, 122.45, -37.98, -0.69, 126.56, -101.17, 93.16, -120.1, -30.28, -131.61, -6.86, -131.61, -0.33, -123.14, 100.27, -11.36, 147.58], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 266, "height": 223}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Capy_ear": {"Capy_ear": {"name": "Capy/ear_1", "x": 20.52, "y": 1.39, "rotation": -54.97, "width": 69, "height": 60}}, "Capy_ear2": {"Capy_ear2": {"name": "Capy/ear_2", "x": 39.82, "y": 140.36, "rotation": -0.33, "width": 56, "height": 51}}, "Horn": {"Horn": {"name": "Capy/horn", "x": 35.9, "y": -3.16, "rotation": -96.45, "width": 54, "height": 106}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.48, "y": -3.89, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Capy/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Capy/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Capy/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Capy/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/eye_black": {"eye": {"name": "Capy/eye-2", "x": -7.49, "y": 10.11, "rotation": -0.33, "width": 29, "height": 24}, "eye_cross2": {"name": "eye_cross3", "path": "v3/eye_cross2", "x": -18.86, "y": 3.88, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"name": "Capy/eye-1", "x": 22.07, "y": 9.62, "rotation": -0.33, "width": 40, "height": 27}, "eye_cross": {"name": "eye_cross2", "path": "v3/eye_cross", "x": 25.84, "y": 3.62, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "Capy/mouth", "x": 33.34, "y": -53.35, "width": 122, "height": 108}}}}, {"name": "Chainsaw", "bones": ["chain", "Chainsaw_Eye_L", "Chainsaw_Eye_R", "chainsaw_part", "chainsaw_part2", "chainsaw_part3", "chainsaw_part4", "chainsaw_part5", "chainsaw_part6", "chainsaw_part7", "chainsaw_part8"], "transform": ["eye_chainsaw"], "attachments": {"Body": {"Body": {"name": "Chainsaw/body", "x": 2.58, "y": 21.99, "width": 230, "height": 207}}, "Body_features": {"Body_features": {"name": "Chainsaw/handle", "x": -8.93, "y": 148.51, "width": 153, "height": 112}}, "Body_outline_big": {"Body_outline_big": {"name": "Chainsaw/body_outline", "x": 2.58, "y": 22.25, "width": 261, "height": 237}}, "Chainsaw_Eye_L": {"Chainsaw_Eye_L": {"name": "Chainsaw/eye_2", "width": 66, "height": 66}}, "Chainsaw_Eye_R": {"Chainsaw_Eye_L": {"name": "Chainsaw/eye_1", "x": 6.62, "width": 29, "height": 61}}, "chainsaw_part": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part2": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part3": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part4": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part5": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part6": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part7": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "chainsaw_part8": {"chainsaw_part": {"name": "Chainsaw/chainsaw_part", "x": 4.41, "y": -4.68, "rotation": -123.46, "width": 32, "height": 32}}, "Horn_outline_big": {"Horn_outline": {"name": "Chainsaw/chainsaw", "x": 45.1, "y": 16.71, "rotation": -96.45, "width": 161, "height": 169}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Chainsaw/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Chainsaw/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Chainsaw/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Chainsaw/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Chainsaw/tail", "x": 55.72, "y": 30.54, "rotation": 3.35, "width": 68, "height": 84}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "x": 18.09, "y": 33.75, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "Diver", "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "Diver/mask", "x": -9.82, "y": 78.12, "rotation": -0.33, "width": 259, "height": 226}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Tail2", "type": "<PERSON><PERSON><PERSON>", "path": "v3/Tail2", "skin": "Scared", "parent": "Tail", "width": 102, "height": 135}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "<PERSON>", "bones": ["Duck_wing"], "attachments": {"Body": {"Body": {"name": "Duck/body", "x": 16.64, "y": 21.99, "width": 254, "height": 212}}, "Body_features": {"Body_features": {"name": "Duck/wing", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 68, 52.72, -34.87, 1, 1, 68, -4.05, -29.66, 1, 1, 68, 1.99, 36.06, 1, 1, 68, 58.75, 30.85, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 66}}, "Body_outline_big": {"Body_outline_big": {"name": "Duck/body_outline", "x": 16.64, "y": 21.99, "width": 280, "height": 238}}, "Horn_outline_big": {"Horn_outline": {"name": "Duck/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 76, "height": 132}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Duck/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Duck/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Duck/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Duck/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "x": -7.95, "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -16.82, "y": 0.1, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "x": -2.98, "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "Duck/mouth", "x": -8.14, "y": -48.89, "width": 73, "height": 53}}}}, {"name": "Evil", "attachments": {"Body": {"Body": {"name": "Evil/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 207}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Evil/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Evil/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Evil/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Evil/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail_front": {"Tail_front": {"name": "Evil/tail", "type": "mesh", "uvs": [0.67006, 0, 0.96653, 0.25729, 1, 0.42039, 1, 0.55864, 0.41363, 0.84119, 0.0086, 0.81963, 0.00638, 0.71203, 0.51179, 0], "triangles": [2, 7, 1, 4, 5, 6, 7, 4, 6, 7, 0, 1, 2, 4, 7, 3, 4, 2], "vertices": [19.97, 24.39, 55.84, -11.37, 59.89, -34.04, 59.89, -53.26, -11.06, -92.53, -60.07, -89.54, -60.34, -74.58, 0.82, 24.39], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 121, "height": 139}}, "Wing": {"Wing": {"name": "Evil/wing", "type": "mesh", "uvs": [0.88814, 0, 1, 0.31152, 1, 0.3871, 0.73026, 0.78817, 0.22975, 1, 0.20125, 1, 0, 0.89885, 0, 0.83512, 0.1578, 0.2627, 0.74784, 0], "triangles": [1, 9, 0, 1, 3, 8, 9, 1, 8, 2, 3, 1, 7, 8, 3, 5, 6, 7, 7, 4, 5, 3, 4, 7], "vertices": [92.9, 184.87, 105.09, 147.8, 105.09, 138.8, 75.69, 91.08, 21.13, 65.87, 18.02, 65.87, -3.91, 77.9, -3.91, 85.49, 13.29, 153.61, 77.6, 184.87], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 109, "height": 119}}, "Wing2": {"Wing": {"name": "Evil/wing", "type": "mesh", "uvs": [0.88902, 0, 1, 0.31904, 1, 0.38774, 0.72928, 0.79011, 0.2319, 1, 0.20101, 1, 0, 0.89804, 0, 0.83448, 0.16043, 0.24769, 0.76311, 0], "triangles": [1, 9, 0, 1, 3, 8, 9, 1, 8, 2, 3, 1, 7, 8, 3, 5, 6, 7, 7, 4, 5, 3, 4, 7], "vertices": [63.91, 202.06, 76, 164.1, 76, 155.92, 46.49, 108.04, -7.72, 83.06, -11.09, 83.06, -33, 95.19, -33, 102.76, -15.51, 172.59, 50.18, 202.06], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 109, "height": 119}}, "Face/eye_black": {"eye": {"name": "Evil/eye-1", "x": -2.62, "y": 4.99, "width": 27, "height": 47}, "eye_cross2": {"name": "eye_cross3", "path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"name": "Evil/eye-2", "x": 5.4, "y": 3.47, "rotation": -0.33, "width": 34, "height": 44}, "eye_cross": {"name": "eye_cross2", "path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Face": {"Face": {"name": "Evil/mouth", "x": 8.76, "y": -46.19, "rotation": -0.33, "width": 40, "height": 26}}}}, {"name": "<PERSON><PERSON><PERSON>", "bones": ["Forelock_hair1", "Forelock_hair2"], "physics": ["Forelock_hair1", "Forelock_hair2"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "Forelock/hair", "type": "mesh", "uvs": [0.48434, 0, 0.70919, 0.03535, 0.82891, 0.15016, 0.91991, 0.3307, 0.96649, 0.5542, 1, 0.80712, 1, 0.98049, 0.9599, 1, 0.86039, 1, 0.76087, 1, 0.72427, 0.8133, 0.70597, 1, 0.63758, 0.99797, 0.56919, 0.99594, 0.53937, 0.7363, 0.53234, 0.50688, 0.49885, 0.64332, 0.4784, 0.81315, 0.4779, 0.99101, 0.3705, 0.98957, 0.26311, 0.98813, 0.15572, 0.98668, 0.04832, 0.98524, 0, 0.98597, 0, 0.70942, 0.01164, 0.49516, 0.04755, 0.30665, 0.11126, 0.18347, 0.19923, 0.08604, 0.29435, 0.00614, 0.70014, 0.55673, 0.26248, 0.57759, 0.86083, 0.70816, 0.823, 0.27781, 0.64754, 0.15055, 0.38241, 0.12966], "triangles": [6, 7, 32, 32, 7, 8, 8, 9, 32, 9, 10, 32, 11, 12, 10, 10, 12, 14, 12, 13, 14, 17, 18, 31, 31, 18, 19, 31, 19, 20, 20, 21, 31, 21, 22, 31, 22, 23, 31, 31, 23, 24, 32, 5, 6, 14, 30, 10, 10, 30, 32, 17, 31, 16, 32, 4, 5, 14, 15, 30, 24, 25, 31, 30, 33, 32, 32, 3, 4, 32, 33, 3, 16, 31, 15, 25, 26, 31, 26, 27, 31, 27, 28, 31, 31, 35, 15, 35, 28, 29, 35, 31, 28, 15, 34, 30, 30, 34, 33, 15, 35, 34, 33, 2, 3, 34, 1, 33, 33, 1, 2, 35, 0, 34, 34, 0, 1, 35, 29, 0], "vertices": [1, 17, -3.28, 146.86, 1, 1, 17, 56.05, 141.78, 1, 1, 17, 87.57, 126.21, 1, 1, 17, 111.45, 101.88, 1, 1, 17, 123.57, 71.86, 1, 1, 17, 132.22, 37.91, 1, 1, 17, 132.09, 14.68, 1, 2, 17, 121.49, 12.13, 0.97171, 59, 114.68, 78.59, 0.02829, 2, 17, 95.22, 12.28, 0.93086, 59, 109.72, 52.79, 0.06914, 2, 17, 68.95, 12.44, 0.85213, 59, 104.77, 26.99, 0.14787, 2, 17, 59.43, 37.51, 0.82577, 59, 78.38, 22.22, 0.17423, 2, 17, 54.45, 12.52, 0.73236, 59, 102.04, 12.75, 0.26764, 2, 17, 36.4, 12.9, 0.73245, 59, 98.37, -4.93, 0.26755, 2, 17, 18.35, 13.27, 0.77168, 59, 94.69, -22.61, 0.22832, 2, 17, 10.67, 48.11, 0.78973, 59, 59.04, -23.78, 0.21027, 3, 17, 9, 78.86, 0.7045, 59, 28.5, -19.8, 0.06821, 58, 21.68, 23.91, 0.22729, 2, 17, 0.05, 60.63, 0.68432, 58, 41.97, 22.98, 0.31568, 2, 17, -5.48, 37.91, 0.54382, 58, 65.02, 26.98, 0.45618, 2, 17, -5.75, 14.07, 0.38689, 58, 86.97, 36.25, 0.61311, 2, 17, -34.1, 14.43, 0.68487, 58, 97.96, 10.11, 0.31513, 2, 17, -62.45, 14.79, 0.80824, 58, 108.96, -16.02, 0.19176, 2, 17, -90.8, 15.15, 0.80061, 58, 119.95, -42.16, 0.19939, 2, 17, -119.15, 15.51, 0.74194, 58, 130.94, -68.29, 0.25806, 2, 17, -131.91, 15.48, 0.6389, 58, 136.05, -79.98, 0.3611, 2, 17, -131.69, 52.54, 0.70322, 58, 101.99, -94.58, 0.29678, 2, 17, -128.46, 81.23, 0.84743, 58, 74.39, -103.06, 0.15257, 2, 17, -118.83, 106.44, 0.89886, 58, 47.44, -104.3, 0.10114, 2, 17, -101.91, 122.84, 0.92824, 58, 25.64, -95.34, 0.07176, 2, 17, -78.61, 135.77, 0.95097, 58, 4.49, -79.14, 0.04903, 1, 17, -53.44, 146.33, 1, 2, 17, 53.26, 71.93, 0.76587, 59, 43.41, 22.44, 0.23413, 1, 17, -62.3, 69.8, 1, 2, 17, 95.56, 51.39, 0.87025, 59, 71.34, 60.28, 0.12975, 1, 17, 85.91, 109.11, 1, 1, 17, 39.69, 126.43, 1, 1, 17, -30.29, 129.64, 1], "hull": 30, "edges": [0, 58, 0, 2, 10, 12, 12, 14, 44, 46, 46, 48, 18, 20, 20, 22, 26, 28, 28, 30, 34, 36, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 14, 16, 16, 18, 22, 24, 24, 26, 62, 36, 62, 46, 64, 12, 64, 18, 56, 58, 48, 50, 50, 52, 52, 54, 54, 56, 2, 4, 4, 6, 6, 8, 8, 10, 68, 70], "width": 264, "height": 134}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "Forelock/horn", "x": 46.72, "y": -3.91, "rotation": -96.78, "width": 73, "height": 115}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Tail2", "type": "<PERSON><PERSON><PERSON>", "path": "v3/Tail2", "skin": "Scared", "parent": "Tail", "width": 102, "height": 135}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"name": "eye2", "path": "v3/eye", "width": 19, "height": 44}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"name": "eye3", "path": "v3/eye2", "width": 19, "height": 42}}}}, {"name": "Frankenstein", "attachments": {"Body": {"Body": {"name": "Frankenstein/body", "x": 2.58, "y": 21.99, "width": 216, "height": 203}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_feature": {"Leg_feature": {"name": "<PERSON>/scar", "x": 3.13, "y": 0.22, "scaleX": 1.04, "rotation": -90, "width": 41, "height": 32}}, "Leg_feature2": {"Leg_feature2": {"name": "<PERSON>/scar", "x": 0.52, "y": 0.48, "scaleX": 1.04, "rotation": -84.75, "width": 41, "height": 32}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "<PERSON>/tail", "type": "<PERSON><PERSON><PERSON>", "skin": "Scared", "parent": "Tail", "width": 102, "height": 134}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -17.88, "y": 0.1, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 7.61, "y": -0.04, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Face": {"Face": {"name": "<PERSON>/hair", "x": 32.01, "y": 72.71, "rotation": -0.33, "width": 167, "height": 60}}, "Face/Mouth": {"Mouth": {"name": "Frankenstein/mouth", "x": 24.01, "y": -49.35, "rotation": -0.33, "width": 119, "height": 52}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "<PERSON><PERSON><PERSON><PERSON>/scarf", "type": "mesh", "uvs": [0.65442, 0, 1, 0.08111, 1, 0.28259, 0.94704, 0.79036, 0.68181, 0.92122, 0.23494, 1, 0.07267, 1, 0, 0.85513, 0, 0.7748, 0.41276, 0.62427, 0.63771, 0.37338, 0.48232, 0.0377, 0.5325, 0], "triangles": [0, 1, 2, 0, 11, 12, 10, 0, 2, 10, 11, 0, 3, 10, 2, 4, 9, 10, 5, 6, 7, 3, 4, 10, 7, 8, 5, 9, 5, 8, 4, 5, 9], "vertices": [32.31, 147.65, 114.79, 128.67, 114.53, 82.74, 101.2, -32.96, 37.63, -62.43, -69.27, -79.77, -108.05, -79.54, -125.23, -46.41, -125.12, -28.1, -26.27, 5.65, 27.82, 62.54, -8.87, 139.29, 3.17, 147.82], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 239, "height": 228}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "<PERSON><PERSON><PERSON><PERSON>/horn", "x": 36.02, "y": -2.14, "rotation": -96.45, "width": 53, "height": 106}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "<PERSON><PERSON>otter/tail", "type": "mesh", "uvs": [0, 0, 0.47523, 0.14541, 1, 0.76091, 1, 0.85891, 0.52267, 1, 0.4518, 1, 0.09586, 0.87861, 0.08374, 0.56915, 0.07162, 0.25968, 0.20962, 0.7653, 0.16299, 0.74567, 0.23014, 0.47776, 0.13315, 0.27858], "triangles": [12, 8, 0, 1, 12, 0, 11, 12, 1, 7, 8, 12, 7, 12, 11, 10, 7, 11, 11, 1, 2, 9, 10, 11, 2, 9, 11, 4, 5, 9, 6, 7, 10, 6, 10, 9, 9, 2, 4, 6, 9, 5, 3, 4, 2], "vertices": [4.99, 62.9, 50.65, 47.36, 104.39, -26.56, 105.1, -38.79, 61.34, -59.02, 54.69, -59.41, 20.4, -46.21, -12.49, -19.93, -4.36, 34.75, 30.25, -31.45, 12.88, -25.79, 30.08, 4.55, 19.52, 28.87], "hull": 9, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 0, 0, 2, 12, 14, 14, 16], "width": 94, "height": 125}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "x": -3.97, "y": 0.02, "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -18.17, "y": 0.11, "scaleX": 0.7706, "scaleY": 0.7706, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "x": 2.78, "y": -0.02, "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 18.05, "y": -0.1, "scaleX": 0.7706, "scaleY": 0.7706, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "HarryPotter/glasses", "x": 12.43, "y": -12.17, "rotation": -0.33, "width": 148, "height": 94}}}}, {"name": "Indus", "bones": ["Trump_eye_L", "<PERSON>_eye_R"], "attachments": {"Body": {"Body": {"name": "Indus/body", "x": 2.58, "y": 21.99, "width": 238, "height": 208}}, "Body_features": {"Body_features": {"name": "Indus/chain", "x": 10.86, "y": 0.41, "rotation": -0.33, "width": 283, "height": 181}}, "Body_outline_big": {"Body_outline_big": {"name": "Indus/body_outline", "x": 2.58, "y": 22.25, "width": 265, "height": 238}}, "Horn": {"Horn": {"name": "Indus/hornhair", "x": 32.76, "y": -44.3, "rotation": -94.41, "width": 291, "height": 138}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Indus/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Indus/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Indus/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Indus/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "color": "000000ff", "x": -12.83, "y": 19.58, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "color": "000000ff", "x": 21.35, "y": 25.27, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "Indus/mouth", "x": 8.11, "y": -42.21, "rotation": -0.33, "width": 131, "height": 60}}, "Face/Trump_brows": {"Trump_brows": {"name": "Indus/brows", "x": 5.37, "y": 34.37, "width": 130, "height": 52}}, "Face/Trump_eye_L": {"Trump_eye_L": {"name": "Indus/eye_1", "x": 17.56, "y": -3.28, "rotation": -0.33, "width": 56, "height": 38}}, "Face/Trump_eye_R": {"Trump_eye_R": {"name": "Indus/eye_2", "x": 13.55, "y": -7.95, "rotation": -0.33, "width": 56, "height": 38}}}}, {"name": "Kvadr<PERSON><PERSON>", "bones": ["<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_under_face": {"Horn_under_face": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.45, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Kvadrober/tail", "type": "<PERSON><PERSON><PERSON>", "skin": "Scared", "parent": "Tail", "width": 100, "height": 134}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -11.41, "y": 0.07, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 8.67, "y": -0.05, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "Kvadrober/Mask", "type": "mesh", "uvs": [0.83342, 0, 0.86878, 0.00995, 0.90273, 0.21565, 0.8504, 0.383, 0.86473, 0.48383, 0.99466, 0.47227, 1, 0.64327, 0.73152, 1, 0.50013, 1, 0.2843, 0.98617, 0, 0.61992, 0, 0.50194, 0.07629, 0.17003, 0.08072, 0, 0.11281, 0, 0.48337, 0.1834, 0.5884, 0.18448, 0.76127, 0, 0.6957, 0.3452, 0.73096, 0.23067], "triangles": [3, 19, 2, 2, 0, 1, 0, 2, 19, 19, 17, 0, 19, 16, 17, 18, 15, 16, 4, 5, 6, 6, 7, 4, 7, 8, 4, 8, 9, 18, 18, 9, 10, 12, 15, 11, 14, 15, 12, 15, 18, 10, 14, 12, 13, 15, 10, 11, 4, 8, 18, 4, 18, 3, 18, 19, 3, 18, 16, 19], "vertices": [1, 60, 73.41, -3.17, 1, 1, 60, 73.39, -11.16, 1, 1, 60, 28.56, -32.64, 1, 1, 9, 99.45, 22.5, 1, 1, 9, 102.41, -1.62, 1, 1, 9, 130.49, 0.98, 1, 1, 9, 131.4, -39.89, 1, 1, 9, 72.92, -124.82, 1, 1, 9, 22.94, -124.53, 1, 1, 9, -23.66, -120.95, 1, 1, 9, -84.56, -33.06, 1, 1, 9, -84.4, -4.86, 1, 1, 9, -67.46, 74.37, 1, 1, 9, -66.26, 115, 1, 1, 9, -59.33, 114.96, 1, 1, 9, 20.45, 70.66, 1, 1, 9, 43.14, 70.27, 1, 1, 60, 68.83, 11.73, 1, 1, 9, 66.09, 31.72, 1, 2, 9, 73.86, 59.05, 0.00289, 60, 14.21, 1.76, 0.99711], "hull": 18, "edges": [0, 34, 0, 2, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 8, 10, 2, 4, 10, 12, 4, 6, 6, 8], "width": 216, "height": 239}}}}, {"name": "Minecraft", "attachments": {"Body": {"Body": {"name": "Minecraft/body", "x": 2.58, "y": 21.99, "width": 212, "height": 196}}, "Body_outline_big": {"Body_outline_big": {"name": "Minecraft/body_outline", "x": 2.58, "y": 22.25, "width": 240, "height": 224}}, "Horn": {"Horn": {"name": "Minecraft/horn", "x": 35.96, "y": -2.68, "rotation": -96.45, "width": 53, "height": 106}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Minecraft/leg", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Minecraft/leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Minecraft/leg", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Minecraft/leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Minecraft/leg_outline", "x": 0.74, "y": 0.02, "rotation": -90, "width": 69, "height": 114}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Minecraft/leg_outline", "x": 2.02, "rotation": -90, "width": 69, "height": 114}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Minecraft/leg_outline", "x": 1.95, "rotation": -90, "width": 69, "height": 114}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Minecraft/leg_outline", "x": 0.53, "rotation": -90, "width": 69, "height": 114}}, "Tail": {"Tail": {"name": "Minecraft/tail", "type": "mesh", "uvs": [0.13098, 0, 0.31415, 0, 1, 0.23455, 1, 0.68423, 0.99932, 0.76896, 0.31571, 1, 0.12756, 1, 0, 1, 0, 0.66179, 0, 0.32357, 0, 0, 0.13269, 0.24406, 0.14124, 0.64888, 0.13782, 0.89949, 0.08993, 0.1055, 0.09165, 0.24044, 0.08993, 0.48141, 0.09165, 0.64888, 0.08993, 0.8031], "triangles": [14, 10, 0, 11, 15, 14, 1, 14, 0, 1, 11, 14, 9, 10, 14, 9, 14, 15, 11, 16, 9, 11, 9, 15, 16, 11, 12, 2, 11, 1, 2, 12, 11, 17, 16, 12, 8, 9, 16, 8, 16, 17, 12, 2, 3, 4, 12, 3, 18, 8, 17, 12, 18, 17, 5, 13, 12, 13, 18, 12, 7, 8, 18, 7, 18, 13, 6, 7, 13, 4, 5, 12, 6, 13, 5], "vertices": [12.75, 66.86, 29.75, 67.85, 95.24, 40.67, 98.71, -18.58, 99.3, -29.75, 37.61, -63.91, 20.14, -64.93, 8.3, -65.63, -27.31, -31.44, -36.61, 18.47, 0.59, 66.15, 14.79, 34.71, 18.71, -18.59, 20.32, -51.63, 9.75, 52.73, 0.17, 31.3, -16.07, 12.19, -11.3, -20.35, -5.34, -32.43], "hull": 11, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 2, 0, 0, 20, 10, 12, 12, 14, 18, 20, 14, 16, 16, 18, 22, 24], "width": 93, "height": 132}}, "Face/EyeBrow_L": {"EyeBrow_L": {"name": "Minecraft/brow-2", "x": -1.01, "y": 4.36, "width": 38, "height": 14}}, "Face/EyeBrow_R": {"EyeBrow_R": {"name": "Minecraft/brow-1", "x": 2.37, "y": 3.24, "width": 26, "height": 14}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "x": -10.98, "y": 0.06, "rotation": -0.33, "width": 52, "height": 43}, "eye": {"name": "Minecraft/eye-1", "width": 18, "height": 43}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "x": 13.87, "y": -0.08, "rotation": -0.33, "width": 47, "height": 47}, "eye2": {"name": "Minecraft/eye-1", "x": 3.89, "y": -0.02, "width": 18, "height": 43}}, "Face/Face": {"Face": {"name": "Minecraft/cheeks", "x": 16.94, "y": -43.89, "rotation": -0.33, "width": 82, "height": 17}}, "Face/Mouth": {"Mouth": {"name": "Minecraft/mouth", "x": 11.38, "y": -51.81, "width": 42, "height": 33}}}}, {"name": "Neo", "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "Neo/coat", "x": 1.57, "y": -34.64, "rotation": -0.33, "width": 248, "height": 125}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.62, "y": 29.24, "scaleY": 0.9424, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "Neo/horn", "x": 35.9, "y": -3.16, "rotation": -96.45, "width": 54, "height": 108}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.23, "y": -3.71, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Neo/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Neo/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Neo/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Neo/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "929292ff", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "929292ff", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "929292ff", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "929292ff", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Neo/tail", "type": "<PERSON><PERSON><PERSON>", "skin": "Scared", "parent": "Tail", "width": 102, "height": 134}}, "Face/eye_black": {"eye_cross2": {"name": "Neo/eye-2", "x": -15.88, "y": 0.09, "rotation": -0.33, "width": 35, "height": 34}}, "Face/eye_black2": {"eye_cross": {"name": "Neo/eye-1", "x": 12.15, "y": -0.07, "rotation": -0.33, "width": 38, "height": 39}}, "Face/Mouth": {"Mouth": {"name": "Neo/glasses", "x": 34.89, "y": -13.42, "rotation": -0.33, "width": 167, "height": 76}}}}, {"name": "<PERSON><PERSON><PERSON>", "bones": ["P<PERSON>e_eye", "Pepe_eye2"], "transform": ["eyes_pepe"], "attachments": {"Body": {"Body": {"name": "Pepe/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 210}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "Pepe/horn", "x": 35.9, "y": -3.16, "rotation": -96.45, "width": 54, "height": 106}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.23, "y": -3.71, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Pepe/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Pepe/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Pepe/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Pepe/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail": {"Tail": {"name": "Tail2", "type": "mesh", "path": "v3/Tail2", "uvs": [0.15153, 0, 0.99979, 0.27514, 1, 0.8041, 0.27266, 1, 0, 0.99815, 0.02626, 0.76947, 0.05252, 0.54079, 0.03026, 0.29745, 0.008, 0.05411, 0.02974, 0, 0.22552, 0.85104, 0.24978, 0.58527, 0.26433, 0.37266, 0.17943, 0.13988], "triangles": [1, 13, 0, 8, 0, 13, 0, 8, 9, 7, 8, 13, 12, 13, 1, 7, 13, 12, 6, 7, 12, 11, 6, 12, 5, 6, 11, 11, 12, 1, 2, 11, 1, 10, 5, 11, 10, 11, 2, 4, 5, 10, 3, 10, 2, 4, 10, 3], "vertices": [11.73, 68.46, 100.46, 36.95, 105.07, -34.31, 32.73, -65.47, 4.96, -67.01, -19, -42.48, -23.39, 0.56, -16.22, 30.9, -2.41, 60.23, -0.67, 67.66, 26.64, -45.71, 26.81, -9.75, 26.44, 18.99, 15.78, 49.79], "hull": 10, "edges": [0, 18, 0, 2, 4, 6, 16, 18, 6, 8, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16], "width": 102, "height": 135}}, "Face/Face": {"Face": {"name": "<PERSON><PERSON><PERSON>/Face", "type": "mesh", "uvs": [0.63874, 0, 0.73248, 0.02549, 0.76409, 0.04418, 0.81467, 0.1085, 0.90384, 0.22186, 0.90323, 0.49946, 0.89817, 0.58701, 1, 0.69977, 1, 0.87173, 0.94144, 0.9364, 0.87743, 0.94735, 0.44267, 0.99329, 0.26122, 0.99301, 0.17337, 0.97236, 0.102, 0.89067, 0.00017, 0.41664, 0, 0.23912, 0.03615, 0.12723, 0.15712, 0, 0.21675, 0, 0.31137, 0.02469, 0.40599, 0.04938, 0.45785, 0.04017, 0.56769, 0, 0.08486, 0.23499, 0.1661, 0.14037, 0.29587, 0.10533, 0.4594, 0.1392, 0.56385, 0.10299, 0.67568, 0.11584, 0.76536, 0.13803, 0.07958, 0.29923, 0.20724, 0.2747, 0.32013, 0.28288, 0.41931, 0.28054, 0.54908, 0.27353, 0.73055, 0.27236, 0.82761, 0.27587], "triangles": [28, 22, 23, 26, 19, 20, 26, 20, 21, 29, 0, 1, 30, 2, 3, 27, 22, 28, 21, 22, 27, 25, 18, 19, 25, 19, 26, 17, 18, 25, 24, 17, 25, 16, 17, 24, 29, 30, 36, 2, 29, 1, 30, 29, 2, 35, 27, 28, 28, 23, 0, 29, 28, 0, 32, 25, 26, 24, 25, 32, 37, 3, 4, 30, 3, 37, 36, 30, 37, 27, 26, 21, 34, 27, 35, 27, 33, 26, 33, 32, 26, 27, 34, 33, 31, 16, 24, 31, 24, 32, 15, 16, 31, 5, 37, 4, 5, 36, 37, 7, 10, 6, 32, 15, 31, 33, 14, 15, 33, 15, 32, 7, 8, 10, 6, 36, 5, 8, 9, 10, 12, 13, 14, 35, 28, 29, 35, 29, 36, 33, 12, 14, 6, 35, 36, 6, 34, 35, 34, 11, 12, 12, 33, 34, 11, 34, 6, 10, 11, 6], "vertices": [2, 9, 34.03, 30.5, 0.78555, 53, 6.38, 48.19, 0.21445, 2, 9, 48.56, 26.94, 0.79249, 53, 20.91, 44.62, 0.20751, 2, 9, 53.46, 24.32, 0.76615, 53, 25.81, 42, 0.23385, 2, 9, 61.3, 15.31, 0.81379, 53, 33.65, 33, 0.18621, 1, 9, 75.12, -0.56, 1, 1, 9, 75.03, -39.42, 1, 1, 9, 74.25, -51.68, 1, 1, 9, 90.03, -67.46, 1, 1, 9, 90.03, -91.54, 1, 1, 9, 80.95, -100.59, 1, 1, 9, 71.03, -102.12, 1, 1, 9, 3.64, -108.56, 1, 1, 9, -24.48, -108.52, 1, 1, 9, -38.1, -105.63, 1, 1, 9, -49.16, -94.19, 1, 1, 9, -64.94, -27.83, 1, 2, 9, -64.97, -2.97, 0.91158, 52, -32.4, 16.25, 0.08842, 2, 9, -59.37, 12.69, 0.8608, 52, -26.8, 31.92, 0.1392, 2, 9, -40.62, 30.5, 0.81512, 52, -8.05, 49.73, 0.18488, 2, 9, -31.37, 30.5, 0.82829, 52, 1.2, 49.73, 0.17171, 2, 9, -16.71, 27.05, 0.85565, 52, 15.86, 46.27, 0.14435, 3, 9, -2.04, 23.59, 0.75533, 52, 30.53, 42.82, 0.12324, 53, -29.7, 41.27, 0.12143, 2, 9, 6, 24.88, 0.79354, 53, -21.66, 42.56, 0.20646, 2, 9, 23.02, 30.5, 0.78265, 53, -4.63, 48.19, 0.21735, 2, 9, -51.82, -2.39, 0.77453, 52, -19.25, 16.83, 0.22547, 2, 9, -39.23, 10.85, 0.7408, 52, -6.66, 30.08, 0.2592, 2, 9, -19.11, 15.76, 0.77635, 52, 13.46, 34.98, 0.22365, 2, 9, 6.24, 11.02, 0.76029, 53, -21.42, 28.7, 0.23971, 2, 9, 22.43, 16.09, 0.72563, 53, -5.23, 33.77, 0.27437, 2, 9, 39.76, 14.29, 0.72244, 53, 12.11, 31.97, 0.27756, 2, 9, 53.66, 11.18, 0.76002, 53, 26.01, 28.86, 0.23998, 1, 9, -52.64, -11.39, 1, 1, 9, -32.85, -7.95, 1, 1, 9, -15.35, -9.1, 1, 1, 9, 0.02, -8.77, 1, 1, 9, 20.14, -7.79, 1, 1, 9, 48.26, -7.63, 1, 1, 9, 63.31, -8.12, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 38, 40, 40, 42, 4, 6, 6, 8, 30, 32], "width": 155, "height": 140}}, "Face/Pepe_Eye1": {"Pepe_Eye1": {"name": "Pepe/Eye1", "width": 63, "height": 56}, "Pepe_Eye_dead": {"name": "Pepe/eye_cross2", "x": 4.38, "y": 3.79, "rotation": -0.33, "width": 70, "height": 57}}, "Face/Pepe_Eye2": {"Pepe_Eye2": {"name": "Pepe/Eye2", "width": 64, "height": 57}, "Pepe_Eye2_dead": {"name": "Pepe/eye_cross", "x": 5.2, "y": 4.6, "scaleX": 1.1, "scaleY": 1.1, "rotation": -0.33, "width": 61, "height": 53}}}}, {"name": "Plunger", "bones": ["plunger"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "plunger": {"plunger": {"name": "Plunger/plunger", "type": "mesh", "uvs": [0.87806, 0, 1, 0.02342, 1, 0.16996, 0.69843, 0.47842, 0.75159, 0.60827, 0.76995, 0.87066, 0.73049, 0.99559, 0.52516, 0.9955, 0.11706, 0.80455, 0, 0.60134, 0, 0.49847, 0.09969, 0.43715, 0.36079, 0.35787, 0.525, 0.36124, 0.79027, 0], "triangles": [0, 1, 2, 3, 13, 14, 14, 0, 2, 3, 14, 2, 9, 10, 11, 8, 11, 12, 9, 11, 8, 13, 4, 12, 4, 13, 3, 7, 4, 5, 4, 8, 12, 7, 8, 4, 6, 7, 5], "vertices": [61.19, 98.1, 83.12, 93.52, 82.95, 65.68, 28.33, 7.39, 37.76, -17.34, 40.77, -67.21, 33.53, -90.91, -3.43, -90.67, -76.67, -53.97, -97.52, -15.24, -97.41, 4.31, -79.4, 15.86, -32.31, 30.64, -2.76, 29.83, 45.39, 98.19], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 10, 12, 4, 6, 6, 8, 8, 10, 26, 28, 22, 24, 24, 26], "width": 180, "height": 190}}, "Tail": {"Tail": {"name": "Tail2", "type": "<PERSON><PERSON><PERSON>", "path": "v3/Tail2", "skin": "Scared", "parent": "Tail", "width": 102, "height": 135}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "<PERSON><PERSON>", "bones": ["Poop_propeller", "Poop_jetpack"], "transform": ["Poop_jetpack", "Poop_propeller"], "attachments": {"Body": {"Body": {"name": "Poop/body", "x": 2.58, "y": 54.66, "width": 304, "height": 299}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "color": "a86427ff", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "color": "a86427ff", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "color": "a86427ff", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "color": "a86427ff", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -15.5, "y": 0.09, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "Potato_fri", "bones": ["potato1", "potato2", "potato3"], "physics": ["potato1", "potato2", "potato3"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "Potato_fri/box", "x": 3.23, "y": -35.16, "rotation": -0.33, "width": 285, "height": 125}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "potato1": {"potato1": {"name": "Potato_fri/potato1", "type": "mesh", "uvs": [0.89142, 0, 1, 0.10913, 1, 0.20595, 0.50858, 0.94282, 0.30897, 1, 0.12778, 1, 0, 0.9216, 0, 0.8313, 0.4194, 0.01275, 0.62791, 0], "triangles": [9, 0, 1, 7, 5, 6, 8, 2, 7, 2, 3, 7, 9, 2, 8, 1, 2, 9, 7, 4, 5, 3, 4, 7], "vertices": [166.88, -8.85, 155, -27.4, 140.14, -33.77, 4.97, -30.73, -12.77, -13.57, -20.9, 5.42, -14.61, 23.96, -0.74, 29.9, 143.74, 39.78, 155.05, 18.77], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 114, "height": 167}}, "potato2": {"potato2": {"name": "Potato_fri/potato2", "type": "mesh", "uvs": [0, 0.04432, 0.40249, 0, 0.75986, 0, 1, 0.85914, 1, 0.99335, 0.28228, 0.99235, 0, 0.23106], "triangles": [6, 0, 1, 3, 5, 6, 6, 1, 2, 2, 3, 6, 5, 3, 4], "vertices": [112.82, 27.23, 111.67, -6.64, 105.71, -35.7, -6.02, -33.13, -22.85, -29.68, -10.76, 28.65, 89.4, 32.03], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 83, "height": 128}}, "potato3": {"potato2": {"name": "Potato_fri/potato_back", "type": "mesh", "uvs": [0.46996, 0, 0.62379, 0.07036, 1, 0.46736, 1, 0.66932, 0.88661, 0.96579, 0.74492, 1, 0.59966, 1, 0, 0.39375, 0, 0.2454, 0.32151, 0], "triangles": [1, 7, 8, 2, 7, 1, 7, 2, 3, 1, 9, 0, 3, 5, 6, 9, 1, 8, 7, 3, 6, 4, 5, 3], "vertices": [165.14, -27.6, 150.51, -32.46, 94.65, -27.73, 76.6, -12.79, 56.26, 16.56, 60.88, 28.37, 68.75, 37.88, 155.43, 32.3, 168.68, 21.33, 173.18, -17.88], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 85, "height": 116}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "ResistanceDog", "attachments": {"Body": {"Body": {"name": "ResistanceDog/body", "x": 2.58, "y": 32.59, "width": 240, "height": 230}}, "Body_outline_big": {"Body_outline_big": {"name": "ResistanceDog/body_outline", "x": 2.58, "y": 32.86, "width": 268, "height": 258}}, "Horn": {"Horn": {"name": "ResistanceDog/horn", "x": 41.84, "y": -14.21, "rotation": -96.78, "width": 74, "height": 109}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "color": "2c2b2bff", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "color": "2c2b2bff", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "color": "2c2b2bff", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "color": "2c2b2bff", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "color": "000000ff", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "color": "393939ff", "x": -10.82, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "color": "393939ff", "x": -8.29, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Mouth": {"Mouth": {"name": "ResistanceDog/nose", "x": -35.86, "y": -84.94, "width": 46, "height": 36}}}}, {"name": "Scared", "bones": ["Scared_eye1", "Scared_eye2"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_features": {"Body_features": {"name": "Scared/Hairs", "type": "mesh", "uvs": [0.69588, 0, 0.77381, 0.08877, 0.75692, 0.14838, 0.93488, 0.33474, 0.97497, 0.31791, 1, 0.34642, 1, 0.39125, 0.94737, 0.43495, 0.91606, 0.3996, 0.93528, 0.3351, 0.74269, 0.16096, 0.71762, 0.1747, 0.66969, 0.12138, 0.62997, 0.15628, 0.58981, 0.17005, 0.57307, 0.03312, 0.06263, 0.35088, 0.10727, 0.43407, 0.07357, 0.46476, 0.07989, 1, 0.03502, 1, 0.01537, 0.97024, 0, 0.63051, 0, 0.38565, 0.61834, 0], "triangles": [12, 24, 0, 12, 0, 1, 1, 10, 12, 13, 24, 12, 15, 24, 13, 2, 10, 1, 14, 15, 13, 11, 12, 10, 9, 3, 4, 10, 2, 3, 10, 3, 9, 23, 24, 16, 24, 15, 16, 4, 5, 6, 6, 7, 9, 6, 9, 4, 8, 9, 7, 18, 16, 17, 23, 16, 18, 22, 23, 18, 19, 20, 21, 21, 22, 19, 18, 19, 22], "vertices": [43.75, 155.02, 63.38, 140.26, 59.05, 130.44, 103.89, 99.43, 114.05, 102.15, 120.36, 97.41, 120.32, 90.01, 106.96, 82.88, 99.07, 88.76, 104, 99.37, 55.44, 128.39, 49.08, 126.16, 37.01, 135.03, 26.92, 129.33, 16.75, 127.11, 12.65, 149.73, -116.8, 98.05, -105.58, 84.26, -114.14, 79.25, -113.05, -9.07, -124.4, -9.01, -129.35, -4.07, -132.91, 52.01, -132.67, 92.41, 24.13, 155.13], "hull": 25, "edges": [0, 48, 10, 12, 12, 14, 24, 26, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 32, 34, 34, 36, 26, 28, 28, 30, 30, 32, 22, 24, 20, 22, 0, 2, 2, 4, 8, 10, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20], "width": 253, "height": 165}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Scared_eye1": {"Scared_eye1": {"name": "Scared/eye-2", "rotation": 120, "width": 56, "height": 55}}, "Scared_eye2": {"Scared_eye2": {"name": "Scared/eye-2", "width": 56, "height": 55}}, "Tail": {"Tail": {"name": "Tail2", "type": "mesh", "path": "v3/Tail2", "uvs": [0.15153, 0, 0.99979, 0.27514, 1, 0.8041, 0.27266, 1, 0, 0.99815, 0.04377, 0.6736, 0.04851, 0.36511, 0.008, 0.05411, 0.02974, 0, 0.13754, 0.89622, 0.23077, 0.88213, 0.24941, 0.68069, 0.26246, 0.4877, 0.21399, 0.14961, 0.13381, 0.1313, 0.15619, 0.42008, 0.16365, 0.67364], "triangles": [0, 7, 8, 14, 7, 0, 1, 13, 0, 14, 0, 13, 6, 7, 14, 6, 14, 13, 15, 6, 13, 12, 15, 13, 1, 12, 13, 5, 6, 15, 16, 5, 15, 12, 16, 15, 11, 16, 12, 12, 1, 2, 11, 12, 2, 10, 16, 11, 10, 11, 2, 9, 5, 16, 9, 16, 10, 4, 5, 9, 3, 10, 2, 9, 10, 3, 4, 9, 3], "vertices": [11.73, 68.46, 100.46, 36.95, 105.07, -34.31, 32.73, -65.47, 4.96, -67.01, -24.19, -30.71, -33.86, 18.26, -11.21, 60.85, -0.67, 67.66, 18.08, -52.38, 27.45, -49.87, 27.6, -22.61, 27.25, 3.48, 19.38, 48.71, 3.27, 50.38, -15.26, 19.4, -7.49, -25.28], "hull": 9, "edges": [0, 16, 0, 2, 4, 6, 14, 16, 6, 8, 2, 4, 8, 10, 10, 12, 12, 14], "width": 102, "height": 135}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "x": -9.4, "y": 0.05, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "x": 12.17, "y": -0.07, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "<PERSON><PERSON><PERSON>", "bones": ["Shinobi_1", "Shinobi_1b", "Shinobi_2", "Shinobi_2b"], "physics": ["Shinobi_1", "Shinobi_1b", "Shinobi_2", "Shinobi_2b"], "attachments": {"Body": {"Body": {"name": "Body2", "path": "v3/Body2", "x": 2.58, "y": 21.99, "width": 219, "height": 206}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Capy_ear2": {"Capy_ear2": {"name": "Shinobi/bandage", "type": "mesh", "uvs": [0.74978, 0.46545, 0.77524, 0.62652, 0.77594, 0.91909, 0.37114, 1, 0.06459, 1, 0, 0.94922, 0, 0.5982, 0.01191, 0.15077, 0.32847, 0.14458], "triangles": [4, 5, 6, 4, 7, 8, 4, 6, 7, 3, 8, 0, 4, 8, 3, 2, 3, 0, 2, 0, 1], "vertices": [117.18, 63.67, 125.64, 50.79, 124.06, 27.12, -8.53, 20.91, -110.3, 20.91, -131.74, 24.97, -131.74, 53.05, -127.79, 88.85, -22.69, 89.34], "hull": 9, "edges": [0, 16, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 2, 2, 4], "width": 332, "height": 80}}, "Horn_outline_big": {"Horn_outline": {"name": "horn", "path": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Leg2", "path": "v3/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Leg2", "path": "v3/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Leg2", "path": "v3/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Shinobi_1": {"Shinobi_1": {"name": "Shinobi/2", "type": "mesh", "uvs": [0.74404, 0, 0.9988, 0.06136, 1, 0.1691, 1, 0.37392, 0.88398, 0.57662, 0.6862, 0.77272, 0.51898, 0.92523, 0.39663, 0.99024, 0.20601, 0.9904, 0.0776, 0.87522, 0.01871, 0.7798, 0.01774, 0.53134, 0.13184, 0.34916, 0.42338, 0.11816], "triangles": [11, 12, 4, 12, 13, 4, 4, 13, 2, 3, 4, 2, 0, 2, 13, 2, 0, 1, 8, 9, 7, 7, 9, 6, 5, 6, 10, 6, 9, 10, 10, 11, 5, 5, 11, 4], "vertices": [1, 64, 51.39, 5.8, 1, 1, 64, 50.68, -9.02, 1, 1, 64, 40.69, -13.05, 1, 2, 62, 58.96, -20.61, 0.00392, 64, 21.65, -20.61, 0.99608, 2, 62, 37.85, -22.38, 0.41227, 64, 0.54, -22.38, 0.58773, 2, 62, 15.76, -19.88, 0.97916, 64, -21.55, -19.88, 0.02084, 1, 62, -1.69, -17.28, 1, 1, 62, -10.12, -13.65, 1, 1, 62, -13.87, -4.27, 1, 1, 62, -5.67, 6.31, 1, 1, 62, 2.04, 12.74, 1, 2, 62, 25.11, 21.96, 0.8157, 64, -12.2, 21.96, 0.1843, 2, 62, 44.28, 23.06, 0.17861, 64, 6.96, 23.06, 0.82139, 1, 64, 34.14, 17.23, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 8, 10, 10, 12, 26, 0], "width": 53, "height": 100}}, "Shinobi_2": {"Shinobi_2": {"name": "Shinobi/1", "type": "mesh", "uvs": [0.88339, 0, 0.96068, 0.0403, 1, 0.12292, 1, 0.22607, 0.98486, 0.33011, 0.88816, 0.56043, 0.73151, 0.75017, 0.44559, 0.94744, 0.10743, 1, 0.03096, 1, 0.01149, 0.93888, 0.01062, 0.78792, 0.04052, 0.66623, 0.17111, 0.45333, 0.37803, 0.23076, 0.59867, 0.06689, 0.84864, 0], "triangles": [4, 5, 15, 4, 15, 16, 13, 14, 6, 6, 14, 5, 15, 5, 14, 3, 4, 0, 0, 4, 16, 2, 3, 0, 0, 1, 2, 11, 7, 8, 8, 9, 10, 7, 11, 12, 12, 13, 7, 7, 13, 6, 11, 8, 10], "vertices": [1, 63, 58.44, 3.54, 1, 1, 63, 61.01, -3.39, 1, 1, 63, 58.88, -10.5, 1, 1, 63, 53.17, -16.45, 1, 1, 63, 46.47, -21.56, 1, 2, 61, 70.04, -29.11, 0.0423, 63, 27.71, -29.11, 0.9577, 2, 61, 49.81, -30.74, 0.32678, 63, 7.48, -30.74, 0.67322, 2, 61, 21.14, -25.12, 0.95248, 63, -21.19, -25.12, 0.04752, 1, 61, -2.76, -8.03, 1, 1, 61, -7.51, -3.48, 1, 1, 61, -5.33, 1.21, 1, 1, 61, 2.97, 9.97, 1, 1, 61, 11.56, 15.22, 1, 2, 61, 31.45, 19.75, 0.91019, 63, -10.88, 19.75, 0.08981, 2, 61, 56.62, 20.29, 0.07811, 63, 14.29, 20.29, 0.92189, 1, 63, 37.06, 16.62, 1, 1, 63, 56.28, 5.61, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 86, "height": 80}}, "Tail": {"Tail": {"name": "Tail2", "type": "<PERSON><PERSON><PERSON>", "path": "v3/Tail2", "skin": "Scared", "parent": "Tail", "width": 102, "height": 135}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "<PERSON>", "attachments": {"Body": {"Body": {"name": "<PERSON>/body", "x": -2.61, "y": 22.02, "width": 236, "height": 212}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn": {"Horn": {"name": "<PERSON>/horn", "x": 35.9, "y": -3.16, "rotation": -96.45, "width": 55, "height": 108}}, "Horn_outline_big": {"Horn_outline": {"name": "v3/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 73, "height": 130}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Jetpack2": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Superman/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Superman/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Superman/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Superman/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail_front": {"Tail_front": {"name": "Superman/coat", "type": "mesh", "uvs": [0.21456, 0, 0.50521, 0.01964, 0.96654, 0.28821, 1, 0.93163, 1, 1, 0.87588, 1, 0.69127, 0.94812, 0.05072, 0.43685, 0, 0.21561, 0, 0.14318, 0.14324, 0], "triangles": [8, 9, 10, 0, 7, 8, 6, 1, 2, 6, 2, 3, 7, 0, 1, 0, 8, 10, 1, 6, 7, 5, 6, 3, 5, 3, 4], "vertices": [-52.78, 53.99, 5.93, 51.04, 99.12, 10.76, 105.88, -85.76, 105.88, -96.01, 80.81, -96.01, 43.52, -88.23, -85.87, -11.54, -96.12, 21.65, -96.12, 32.51, -67.18, 53.99], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 202, "height": 150}}, "Face/EyeBrow_L": {"EyeBrow_L": {"path": "v3/EyeBrow_L", "x": -1.06, "y": -4.24, "width": 58, "height": 31}}, "Face/EyeBrow_R": {"EyeBrow_R": {"path": "v3/EyeBrow_R", "x": 2.34, "y": -3.31, "width": 41, "height": 31}}, "Face/eye_black": {"eye": {"path": "v3/eye", "width": 19, "height": 44}, "eye_cross2": {"path": "v3/eye_cross2", "x": -1.9, "y": 0.01, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye2": {"path": "v3/eye2", "width": 19, "height": 42}, "eye_cross": {"path": "v3/eye_cross", "x": 3.17, "y": -0.02, "rotation": -0.33, "width": 47, "height": 47}}}}, {"name": "<PERSON>", "bones": ["Trump_eye_L", "<PERSON>_eye_R", "Trump_Hat"], "transform": ["eyes_trump"], "attachments": {"Body": {"Body": {"name": "Trump/Body", "x": -8.12, "y": 23.37, "width": 247, "height": 214}}, "Body_features": {"Body_features": {"name": "Trump/hair", "x": -32.61, "y": 114.05, "rotation": -0.33, "width": 306, "height": 95}}, "Body_outline_big": {"Body_outline_big": {"name": "Trump/body_outline", "x": -8.83, "y": 22.32, "width": 277, "height": 238}}, "Hat": {"Hat": {"name": "Trump/hat", "x": -20.59, "y": 3.8, "width": 203, "height": 101}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Trump/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Trump/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Trump/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Trump/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "x": -42.66, "y": 22.76, "rotation": -0.33, "width": 52, "height": 43}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "x": -4.95, "y": 24.84, "rotation": -0.33, "width": 47, "height": 47}}, "Face/Trump_brows": {"Trump_brows": {"name": "Trump/brows", "x": -5.97, "y": 35.74, "width": 145, "height": 52}}, "Face/Trump_eye_L": {"Trump_eye_L": {"name": "Trump/eye_1", "width": 51, "height": 45}}, "Face/Trump_eye_R": {"Trump_eye_R": {"name": "Trump/eye_1", "scaleX": 0.8871, "scaleY": 0.9275, "width": 51, "height": 45}}}}, {"name": "Uni-Inu", "attachments": {"Body": {"Body": {"name": "Uni-Inu/body", "x": 2.58, "y": 21.99, "width": 222, "height": 207}}, "Body_outline_big": {"Body_outline_big": {"name": "Body2_outline_big", "path": "v3/Body2_outline_big", "x": 2.58, "y": 22.25, "width": 249, "height": 234}}, "Horn_outline_big": {"Horn_outline": {"name": "Uni-Inu/horn", "x": 38.28, "y": -3.23, "rotation": -96.45, "width": 75, "height": 132}}, "Horn_under_face": {"Horn_under_face": {"name": "Uni-Inu/ear1", "x": 42.34, "y": -97.08, "rotation": -96.78, "width": 98, "height": 100}}, "Jetpack": {"Jetpack": {"path": "v3/Jetpack", "x": 1.29, "y": 4.03, "width": 95, "height": 191}}, "Leg_color": {"Leg_color": {"name": "Uni-Inu/Leg2", "x": 5.95, "rotation": 90, "width": 45, "height": 69}}, "Leg_color2": {"Leg_color2": {"name": "Uni-Inu/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color3": {"Leg_color3": {"name": "Uni-Inu/Leg2", "x": 7.75, "rotation": 90, "width": 45, "height": 69}}, "Leg_color4": {"Leg_color4": {"name": "Uni-Inu/Leg2", "x": 4.68, "rotation": 90, "width": 45, "height": 69}}, "Leg_Outline_big": {"Leg_Outline_big": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.77, "y": -0.95, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big2": {"Leg_Outline_big2": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.37, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big3": {"Leg_Outline_big3": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 13.31, "rotation": 90, "width": 69, "height": 93}}, "Leg_Outline_big4": {"Leg_Outline_big4": {"name": "Leg2_outline_big", "path": "v3/Leg2_outline_big", "x": 11.56, "rotation": 90, "width": 69, "height": 93}}, "Tail_front": {"Tail_front": {"name": "Uni-Inu/tail", "x": 14.73, "y": 18.7, "rotation": 3.35, "width": 87, "height": 100}}, "Wing2": {"Wing": {"name": "Uni-Inu/ear2", "x": -20.25, "y": 158.41, "rotation": -0.33, "width": 84, "height": 100}}, "Face/eye_black": {"eye_cross2": {"path": "v3/eye_cross2", "x": -6.9, "y": 6.76, "scaleX": 0.8141, "scaleY": 0.8141, "rotation": -0.33, "width": 52, "height": 43}, "eye": {"name": "Uni-Inu/eye1", "x": -2.35, "y": 5.72, "width": 26, "height": 30}}, "Face/eye_black2": {"eye_cross": {"path": "v3/eye_cross", "x": 9.26, "y": 6.33, "scaleX": 0.8816, "scaleY": 0.8816, "rotation": -0.33, "width": 47, "height": 47}, "eye2": {"name": "Uni-Inu/eye2", "x": 7.37, "y": 3.69, "width": 30, "height": 24}}, "Face/Mouth": {"Mouth": {"name": "Uni-Inu/nose", "x": 11.52, "y": -41.07, "width": 65, "height": 46}}, "Face/Trump_brows": {"Trump_brows": {"name": "Uni-Inu/brows", "x": 12.15, "y": 8, "rotation": -0.33, "width": 82, "height": 34}}}}], "animations": {"death": {"slots": {"Chainsaw_Eye_L": {"attachment": [{}]}, "Chainsaw_Eye_R": {"attachment": [{}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{}]}, "Face/EyeBrow_R": {"attachment": [{}]}, "Face/eye_black": {"attachment": [{"name": "eye_cross2"}]}, "Face/eye_black2": {"attachment": [{"name": "eye_cross"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_<PERSON>_dead"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye2_dead"}]}, "Face/Trump_brows": {"attachment": [{}]}, "Face/Trump_eye_L": {"attachment": [{}]}, "Face/Trump_eye_R": {"attachment": [{}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{"value": 6.09}], "translate": [{"x": 3.3, "y": -0.49}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{"value": -2.5}], "translate": [{"x": -6.6, "y": 0.51}], "scale": [{}]}, "Low_cntr": {"translate": [{"x": 8.12, "y": 2.54, "curve": [0.056, 8.12, 0.111, -4.15, 0.057, 4.47, 0.112, 4.18]}, {"time": 0.1667, "x": -4.15, "y": 1.9, "curve": [0.223, -4.15, 0.277, 8.12, 0.22, -0.31, 0.278, 0.67]}, {"time": 0.3333, "x": 8.12, "y": 2.54, "curve": [0.389, 8.12, 0.444, -4.15, 0.39, 4.47, 0.445, 4.18]}, {"time": 0.5, "x": -4.15, "y": 1.9, "curve": [0.556, -4.15, 0.611, 8.12, 0.553, -0.31, 0.611, 0.67]}, {"time": 0.6667, "x": 8.12, "y": 2.54, "curve": [0.723, 8.12, 0.777, -4.15, 0.724, 4.47, 0.778, 4.18]}, {"time": 0.8333, "x": -4.15, "y": 1.9, "curve": [0.889, -4.15, 0.944, 8.12, 0.887, -0.31, 0.945, 0.61]}, {"time": 1, "x": 8.12, "y": 2.54}], "scale": [{}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{}], "scale": [{}]}, "Body_parts": {"translate": [{}], "scale": [{"x": 1.012, "y": 1.012}]}, "Horn": {"translate": [{"x": -0.41, "y": 0.74}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -12.04}], "translate": [{"x": -2.02, "y": -3.49}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translatex": [{"value": -157.74, "curve": [0.167, -157.74, 0.333, 9.01]}, {"time": 0.5, "value": 9.01, "curve": [0.667, 9.01, 0.833, -157.74]}, {"time": 1, "value": -157.74}], "translatey": [{"value": -75.2, "curve": [0.077, -63.42, 0.157, -29.72]}, {"time": 0.2333, "value": -8.39, "curve": [0.323, 16.56, 0.412, 11.78]}, {"time": 0.5, "curve": [0.577, -10.35, 0.656, -45.03]}, {"time": 0.7333, "value": -62.5, "curve": [0.822, -82.47, 0.912, -88.78]}, {"time": 1, "value": -75.2}], "scale": [{"x": 1.026, "y": 1.026, "curve": [0.079, 0.908, 0.156, 0.795, 0.079, 0.908, 0.156, 0.795]}, {"time": 0.2333, "x": 0.795, "y": 0.795, "curve": [0.322, 0.795, 0.411, 0.909, 0.322, 0.795, 0.411, 0.909]}, {"time": 0.5, "curve": [0.578, 1.08, 0.656, 1.307, 0.578, 1.08, 0.656, 1.307]}, {"time": 0.7333, "x": 1.307, "y": 1.307, "curve": [0.823, 1.307, 0.912, 1.162, 0.823, 1.307, 0.912, 1.162]}, {"time": 1, "x": 1.026, "y": 1.026}]}, "star2": {"translatex": [{"value": 9.01, "curve": [0.167, 9.01, 0.333, -157.74]}, {"time": 0.5, "value": -157.74, "curve": [0.667, -157.74, 0.833, 9.01]}, {"time": 1, "value": 9.01}], "translatey": [{"curve": [0.077, -10.35, 0.156, -45.03]}, {"time": 0.2333, "value": -62.5, "curve": [0.322, -82.47, 0.412, -88.78]}, {"time": 0.5, "value": -75.2, "curve": [0.577, -63.42, 0.657, -29.72]}, {"time": 0.7333, "value": -8.39, "curve": [0.823, 16.56, 0.912, 11.78]}, {"time": 1}], "scale": [{"curve": [0.078, 1.08, 0.156, 1.307, 0.078, 1.08, 0.156, 1.307]}, {"time": 0.2333, "x": 1.307, "y": 1.307, "curve": [0.4, 1.307, 0.567, 0.795, 0.4, 1.307, 0.567, 0.795]}, {"time": 0.7333, "x": 0.795, "y": 0.795, "curve": [0.822, 0.795, 0.911, 0.909, 0.822, 0.795, 0.911, 0.909]}, {"time": 1}]}, "Fire_scale": {"scale": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}], "translate": [{}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}, "drawOrder": [{"offsets": [{"slot": "star1", "offset": -42}]}, {"time": 0.5, "offsets": [{"slot": "star2", "offset": -43}]}, {"time": 1, "offsets": [{"slot": "star1", "offset": -42}]}]}, "IDLE": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{}]}, "Face/EyeBrow_R": {"attachment": [{}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Low_cntr": {"translate": [{}], "scale": [{}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{}], "scale": [{}]}, "Body_parts": {"translate": [{"curve": [0.278, 0, 0.556, 0, 0.278, 0, 0.556, 3.3]}, {"time": 0.8333, "y": 3.3, "curve": [1.111, 0, 1.389, 0, 1.111, 3.3, 1.389, 0]}, {"time": 1.6667, "curve": [1.944, 0, 2.222, 0, 1.944, 0, 2.222, 3.3]}, {"time": 2.5, "y": 3.3, "curve": [2.778, 0, 3.056, 0, 2.778, 3.3, 3.056, 0]}, {"time": 3.3333}], "scale": [{"x": 1.012, "y": 1.012, "curve": [0.113, 1.005, 0.223, 1, 0.113, 1.005, 0.223, 1]}, {"time": 0.3333, "curve": [0.611, 1, 0.889, 1.033, 0.611, 1, 0.889, 1.033]}, {"time": 1.1667, "x": 1.033, "y": 1.033, "curve": [1.334, 1.033, 1.5, 1.021, 1.334, 1.033, 1.5, 1.021]}, {"time": 1.6667, "x": 1.012, "y": 1.012, "curve": [1.78, 1.005, 1.89, 1, 1.78, 1.005, 1.89, 1]}, {"time": 2, "curve": [2.278, 1, 2.556, 1.033, 2.278, 1, 2.556, 1.033]}, {"time": 2.8333, "x": 1.033, "y": 1.033, "curve": [3.001, 1.033, 3.169, 1.021, 3.001, 1.033, 3.169, 1.021]}, {"time": 3.3333, "x": 1.012, "y": 1.012}]}, "Horn": {"translate": [{"x": -0.41, "y": 0.74, "curve": [0.069, -0.17, 0.134, 0, 0.069, 0.31, 0.134, 0]}, {"time": 0.2, "curve": [0.478, 0, 0.756, -2.75, 0.478, 0, 0.756, 4.95]}, {"time": 1.0333, "x": -2.75, "y": 4.95, "curve": [1.245, -2.75, 1.456, -1.13, 1.245, 4.95, 1.456, 2.04]}, {"time": 1.6667, "x": -0.41, "y": 0.74, "curve": [1.735, -0.17, 1.801, 0, 1.735, 0.31, 1.801, 0]}, {"time": 1.8667, "curve": [2.144, 0, 2.422, -2.75, 2.144, 0, 2.422, 4.95]}, {"time": 2.7, "x": -2.75, "y": 4.95, "curve": [2.912, -2.75, 3.124, -1.17, 2.912, 4.95, 3.124, 2.1]}, {"time": 3.3333, "x": -0.41, "y": 0.74}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": [0.069, -0.21, 0.134, 0, 0.069, 0.38, 0.134, 0]}, {"time": 0.2, "curve": [0.478, 0, 0.756, -3.3, 0.478, 0, 0.756, 6.05]}, {"time": 1.0333, "x": -3.3, "y": 6.05, "curve": [1.245, -3.3, 1.456, -1.36, 1.245, 6.05, 1.456, 2.49]}, {"time": 1.6667, "x": -0.49, "y": 0.9, "curve": [1.735, -0.21, 1.801, 0, 1.735, 0.38, 1.801, 0]}, {"time": 1.8667, "curve": [2.144, 0, 2.422, -3.3, 2.144, 0, 2.422, 6.05]}, {"time": 2.7, "x": -3.3, "y": 6.05, "curve": [2.912, -3.3, 3.124, -1.4, 2.912, 6.05, 3.124, 2.57]}, {"time": 3.3333, "x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{}], "translate": [{}]}, "eye_black2": {"scale": [{"curve": "stepped"}, {"time": 0.4, "curve": [0.422, 1, 0.444, 1.744, 0.422, 1, 0.444, 0.153]}, {"time": 0.4667, "x": 1.744, "y": 0.153, "curve": "stepped"}, {"time": 0.5, "x": 1.744, "y": 0.153, "curve": [0.544, 1.744, 0.589, 1, 0.544, 0.153, 0.589, 1]}, {"time": 0.6333}]}, "eye_black": {"scale": [{"curve": "stepped"}, {"time": 0.4, "curve": [0.422, 1, 0.444, 1.744, 0.422, 1, 0.444, 0.153]}, {"time": 0.4667, "x": 1.744, "y": 0.153, "curve": "stepped"}, {"time": 0.5, "x": 1.744, "y": 0.153, "curve": [0.544, 1.744, 0.589, 1, 0.544, 0.153, 0.589, 1]}, {"time": 0.6333}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{}]}, "EyeBrow_L": {"translate": [{"curve": "stepped"}, {"time": 0.4, "curve": [0.422, 0, 0.444, 0, 0.422, 0, 0.444, -13.62]}, {"time": 0.4667, "y": -13.62, "curve": "stepped"}, {"time": 0.5, "y": -13.62, "curve": [0.544, 0, 0.589, 0, 0.544, -13.62, 0.589, 0]}, {"time": 0.6333}]}, "EyeBrow_R": {"translate": [{"curve": "stepped"}, {"time": 0.4, "curve": [0.422, 0, 0.444, 3.11, 0.422, 0, 0.444, -13.5]}, {"time": 0.4667, "x": 3.11, "y": -13.5, "curve": "stepped"}, {"time": 0.5, "x": 3.11, "y": -13.5, "curve": [0.544, 3.11, 0.589, 0, 0.544, -13.5, 0.589, 0]}, {"time": 0.6333}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{}]}, "Capy_ear": {"rotate": [{"curve": "stepped"}, {"time": 2.6, "curve": [2.622, 0, 2.644, -26.39]}, {"time": 2.6667, "value": -26.39, "curve": "stepped"}, {"time": 2.7, "value": -26.39, "curve": [2.727, -26.39, 2.74, 11.41]}, {"time": 2.7667, "value": 11.41, "curve": "stepped"}, {"time": 2.8, "value": 11.41, "curve": [2.831, 11.41, 2.836, -13.79]}, {"time": 2.8667, "value": -13.79, "curve": "stepped"}, {"time": 2.9, "value": -13.79, "curve": [2.933, -13.79, 2.961, 11.41]}, {"time": 3, "value": 11.41, "curve": [3.044, 11.41, 3.089, 0]}, {"time": 3.1333}]}, "plunger": {"rotate": [{"value": -2.04, "curve": [0.111, -0.93, 0.222, 0]}, {"time": 0.3333, "curve": [0.611, 0, 0.889, -5.8]}, {"time": 1.1667, "value": -5.8, "curve": [1.444, -5.8, 1.722, 0]}, {"time": 2, "curve": [2.278, 0, 2.556, -5.8]}, {"time": 2.8333, "value": -5.8, "curve": [3, -5.8, 3.167, -3.71]}, {"time": 3.3333, "value": -2.04}], "translate": [{"x": 4.16, "y": -4.32, "curve": [0.111, 1.89, 0.222, 0, 0.111, -1.97, 0.222, 0]}, {"time": 0.3333, "curve": [0.611, 0, 0.889, 11.81, 0.611, 0, 0.889, -12.28]}, {"time": 1.1667, "x": 11.81, "y": -12.28, "curve": [1.444, 11.81, 1.722, 0, 1.444, -12.28, 1.722, 0]}, {"time": 2, "curve": [2.278, 0, 2.556, 11.81, 2.278, 0, 2.556, -12.28]}, {"time": 2.8333, "x": 11.81, "y": -12.28, "curve": [3, 11.81, 3.167, 7.56, 3, -12.28, 3.167, -7.86]}, {"time": 3.3333, "x": 4.16, "y": -4.32}]}, "Kvadrober_ear": {"rotate": [{"curve": "stepped"}, {"time": 2.6, "curve": [2.622, 0, 2.644, -26.39]}, {"time": 2.6667, "value": -26.39, "curve": "stepped"}, {"time": 2.7, "value": -26.39, "curve": [2.727, -26.39, 2.74, 11.41]}, {"time": 2.7667, "value": 11.41, "curve": "stepped"}, {"time": 2.8, "value": 11.41, "curve": [2.831, 11.41, 2.836, -13.79]}, {"time": 2.8667, "value": -13.79, "curve": "stepped"}, {"time": 2.9, "value": -13.79, "curve": [2.933, -13.79, 2.961, 11.41]}, {"time": 3, "value": 11.41, "curve": [3.044, 11.41, 3.089, 0]}, {"time": 3.1333}]}, "Shinobi_2": {"rotate": [{"curve": [0.078, 0, 0.156, -6.2]}, {"time": 0.2333, "value": -6.2, "curve": [0.322, -6.2, 0.411, 5.26]}, {"time": 0.5, "value": 5.26, "curve": [0.589, 5.26, 0.678, -13.14]}, {"time": 0.7667, "value": -13.14, "curve": [0.844, -13.14, 0.922, -4.52]}, {"time": 1, "value": -4.52, "curve": [1.1, -4.52, 1.2, -9.73]}, {"time": 1.3, "value": -9.73, "curve": [1.344, -9.73, 1.389, 0]}, {"time": 1.4333, "curve": [1.578, 0, 1.722, -15.79]}, {"time": 1.8667, "value": -15.79, "curve": [1.967, -15.79, 2.067, 7.45]}, {"time": 2.1667, "value": 7.45, "curve": [2.233, 7.45, 2.3, -3.89]}, {"time": 2.3667, "value": -3.89, "curve": [2.467, -3.89, 2.567, 2.84]}, {"time": 2.6667, "value": 2.84, "curve": [2.744, 2.84, 2.822, -13.08]}, {"time": 2.9, "value": -13.08, "curve": [3.044, -13.08, 3.189, 0]}, {"time": 3.3333}]}, "Shinobi_1": {"rotate": [{"value": 1.87, "curve": [0.067, -1.47, 0.133, -13.08]}, {"time": 0.2, "value": -13.08, "curve": [0.35, -13.08, 0.499, 0]}, {"time": 0.6333, "curve": [0.711, 0, 0.789, -6.2]}, {"time": 0.8667, "value": -6.2, "curve": [0.956, -6.2, 1.044, 5.26]}, {"time": 1.1333, "value": 5.26, "curve": [1.222, 5.26, 1.311, -13.14]}, {"time": 1.4, "value": -13.14, "curve": [1.478, -13.14, 1.556, -4.52]}, {"time": 1.6333, "value": -4.52, "curve": [1.733, -4.52, 1.833, -9.73]}, {"time": 1.9333, "value": -9.73, "curve": [1.978, -9.73, 2.022, 0]}, {"time": 2.0667, "curve": [2.211, 0, 2.356, -15.79]}, {"time": 2.5, "value": -15.79, "curve": [2.6, -15.79, 2.7, 7.45]}, {"time": 2.8, "value": 7.45, "curve": [2.867, 7.45, 2.933, -3.89]}, {"time": 3, "value": -3.89, "curve": [3.1, -3.89, 3.2, 2.84]}, {"time": 3.3, "value": 2.84, "curve": [3.311, 2.84, 3.322, 2.43]}, {"time": 3.3333, "value": 1.87}]}, "Shinobi_2b": {"rotate": [{"value": -2.96, "curve": [0.045, -1.26, 0.089, 0]}, {"time": 0.1333, "curve": [0.211, 0, 0.289, -6.2]}, {"time": 0.3667, "value": -6.2, "curve": [0.456, -6.2, 0.544, 5.26]}, {"time": 0.6333, "value": 5.26, "curve": [0.722, 5.26, 0.811, -13.14]}, {"time": 0.9, "value": -13.14, "curve": [0.978, -13.14, 1.056, -4.52]}, {"time": 1.1333, "value": -4.52, "curve": [1.233, -4.52, 1.333, -9.73]}, {"time": 1.4333, "value": -9.73, "curve": [1.478, -9.73, 1.522, 0]}, {"time": 1.5667, "curve": [1.711, 0, 1.856, -15.79]}, {"time": 2, "value": -15.79, "curve": [2.1, -15.79, 2.2, 7.45]}, {"time": 2.3, "value": 7.45, "curve": [2.367, 7.45, 2.433, -3.89]}, {"time": 2.5, "value": -3.89, "curve": [2.6, -3.89, 2.7, 2.84]}, {"time": 2.8, "value": 2.84, "curve": [2.878, 2.84, 2.956, -13.08]}, {"time": 3.0333, "value": -13.08, "curve": [3.133, -13.08, 3.234, -6.82]}, {"time": 3.3333, "value": -2.96}]}, "Shinobi_1b": {"rotate": [{"value": -1.08, "curve": [0.056, 0.76, 0.111, 2.84]}, {"time": 0.1667, "value": 2.84, "curve": [0.178, 2.84, 0.189, 2.43]}, {"time": 0.2, "value": 1.87, "curve": [0.267, -1.47, 0.333, -13.08]}, {"time": 0.4, "value": -13.08, "curve": [0.55, -13.08, 0.699, 0]}, {"time": 0.8333, "curve": [0.911, 0, 0.989, -6.2]}, {"time": 1.0667, "value": -6.2, "curve": [1.156, -6.2, 1.244, 5.26]}, {"time": 1.3333, "value": 5.26, "curve": [1.422, 5.26, 1.511, -13.14]}, {"time": 1.6, "value": -13.14, "curve": [1.678, -13.14, 1.756, -4.52]}, {"time": 1.8333, "value": -4.52, "curve": [1.933, -4.52, 2.033, -9.73]}, {"time": 2.1333, "value": -9.73, "curve": [2.178, -9.73, 2.222, 0]}, {"time": 2.2667, "curve": [2.411, 0, 2.556, -15.79]}, {"time": 2.7, "value": -15.79, "curve": [2.8, -15.79, 2.9, 7.45]}, {"time": 3, "value": 7.45, "curve": [3.067, 7.45, 3.133, -3.89]}, {"time": 3.2, "value": -3.89, "curve": [3.245, -3.89, 3.289, -2.56]}, {"time": 3.3333, "value": -1.08}]}, "Duck_wing": {"rotate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.711, 0, 0.756, 11.22]}, {"time": 0.8, "value": 11.22, "curve": "stepped"}, {"time": 0.8333, "value": 11.22, "curve": [0.9, 11.22, 0.933, -20.43]}, {"time": 1, "value": -20.43, "curve": "stepped"}, {"time": 1.0333, "value": -20.43, "curve": [1.122, -20.43, 1.178, 19.26]}, {"time": 1.2667, "value": 19.26, "curve": "stepped"}, {"time": 1.3, "value": 19.26, "curve": [1.389, 19.26, 1.444, -23.3]}, {"time": 1.5333, "value": -23.3, "curve": "stepped"}, {"time": 1.5667, "value": -23.3, "curve": [1.658, -23.3, 1.675, 10.52]}, {"time": 1.7667, "value": 10.52, "curve": "stepped"}, {"time": 1.8, "value": 10.52, "curve": [1.911, 10.52, 1.989, 0]}, {"time": 2.1}], "translate": [{"curve": "stepped"}, {"time": 0.6333, "curve": [0.678, 0, 0.722, 0, 0.678, 0, 0.722, 13.15]}, {"time": 0.7667, "y": 13.15, "curve": [0.833, 0, 0.9, 0, 0.833, 13.15, 0.9, -7.21]}, {"time": 0.9667, "y": -7.21, "curve": [1.056, 0, 1.144, 0, 1.056, -7.21, 1.144, 13.15]}, {"time": 1.2333, "y": 13.15, "curve": [1.322, 0, 1.411, 0, 1.322, 13.15, 1.411, -7.21]}, {"time": 1.5, "y": -7.21, "curve": [1.592, 0, 1.656, 0, 1.592, -7.21, 1.656, 3.7]}, {"time": 1.7333, "y": 3.7, "curve": [1.844, 0, 1.956, 0, 1.844, 3.7, 1.956, 0]}, {"time": 2.0667}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}}, "Jump_basic_start": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{}]}, "Face/EyeBrow_R": {"attachment": [{}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0.83, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 0.83, "y": -8.25, "curve": [0.244, 0.83, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0.43, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 0.43, "y": -8.25, "curve": [0.244, 0.43, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}], "translate": [{"curve": [0.011, 0, 0.022, 0.13, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 0.13, "y": -8.25, "curve": [0.244, 0.13, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{"y": 58.76}], "scale": [{}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{}], "translate": [{"curve": [0.022, 0, 0.044, -6.45, 0.022, 0, 0.044, -29.6]}, {"time": 0.0667, "x": -6.45, "y": -29.6, "curve": [0.256, -6.45, 0.444, 0, 0.256, -29.6, 0.444, 0]}, {"time": 0.6333}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667}]}, "Low_cntr": {"translate": [{}], "scale": [{"x": 0.9, "y": 1.1, "curve": [0.044, 0.9, 0.089, 1, 0.044, 1.1, 0.089, 1]}, {"time": 0.1333}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{"curve": [0.022, 0, 0.044, -0.17, 0.022, 0, 0.044, -29.64]}, {"time": 0.0667, "x": -0.17, "y": -29.64, "curve": [0.256, -0.17, 0.444, 0, 0.256, -29.64, 0.444, 0]}, {"time": 0.6333}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}], "translate": [{}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.133, 0, 0.2, 0, 0.133, 0, 0.2, 33.13]}, {"time": 0.2667, "y": 33.13, "curve": [0.378, 0, 0.489, 0, 0.378, 33.13, 0.508, 10.52]}, {"time": 0.6}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}}, "Jump_end": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{}]}, "Face/EyeBrow_R": {"attachment": [{}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{}, {"time": 0.0667, "x": -0.06, "y": -9.88}], "scale": [{}, {"time": 0.0667, "x": 0.802}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}, {"time": 0.0667, "x": -0.06, "y": -9.88}], "scale": [{}, {"time": 0.0667, "x": 0.802}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}, {"time": 0.0667, "x": -0.06, "y": -9.88}], "scale": [{}, {"time": 0.0667, "x": 0.802}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}, {"time": 0.0667, "x": -0.06, "y": -9.88}], "scale": [{}, {"time": 0.0667, "x": 0.802}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{}], "scale": [{}]}, "Body_parts": {"translate": [{}, {"time": 0.0667, "x": -0.07, "y": -11.2}], "scale": [{}]}, "Horn": {"translate": [{"x": 0.04, "y": 6.07}, {"time": 0.0667, "x": -0.03, "y": -5.14}]}, "Face": {"translate": [{"x": -0.46, "y": 6.97}, {"time": 0.0667, "x": -0.52, "y": -4.24}]}, "Tail": {"rotate": [{}], "translate": [{}, {"time": 0.0667, "x": -0.07, "y": -11.2}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "Low_cntr": {"translate": [{}], "scale": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}, {"time": 0.0667, "x": 3.75}]}, "Leg_Outline_big3": {"translate": [{}, {"time": 0.0667, "x": 3.75}]}, "Leg_Outline_big": {"translate": [{}, {"time": 0.0667, "x": 3.75}]}, "Leg_Outline_big4": {"translate": [{}, {"time": 0.0667, "x": 3.75}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}, {"time": 0.0667, "x": -0.07, "y": -11.2}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}], "translate": [{}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}}, "Jump_jet_end": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{"name": "Jetpack"}, {"time": 1.6667}]}, "Jetpack2": {"attachment": [{"name": "Jetpack"}, {"time": 1.6667}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff", "curve": [1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 0]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"name": "Rainbow4"}, {"time": 1.6667}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}, {"time": 1.1667}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}, {"time": 1.1667}]}, "Face/eye_black": {"attachment": [{"name": "eye"}, {"time": 1.1667, "name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}, {"time": 1.1667, "name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{"curve": "stepped"}, {"time": 1.3, "curve": [1.422, 0, 1.544, -32.42]}, {"time": 1.6667, "value": -32.42}], "translate": [{"curve": "stepped"}, {"time": 1.3, "curve": [1.422, 0, 1.544, 249.96, 1.422, 0, 1.603, -302.94]}, {"time": 1.6667, "x": 249.96, "y": -1057.38}], "scale": [{"x": 110, "y": 110, "curve": "stepped"}, {"time": 0.0667, "x": 110, "y": 110, "curve": [0.1, 110, 0.144, 100, 0.1, 110, 0.144, 100]}, {"time": 0.1667, "x": 100, "y": 100, "curve": [0.2, 100, 0.233, 110, 0.2, 100, 0.233, 110]}, {"time": 0.2667, "x": 110, "y": 110, "curve": [0.3, 110, 0.344, 100, 0.3, 110, 0.344, 100]}, {"time": 0.3667, "x": 100, "y": 100, "curve": [0.4, 100, 0.433, 110, 0.4, 100, 0.433, 110]}, {"time": 0.4667, "x": 110, "y": 110, "curve": [0.479, 110, 0.686, 100, 0.479, 110, 0.686, 100]}, {"time": 0.7, "x": 100, "y": 100}]}, "Fire_scale": {"scale": [{"x": 300, "y": 100.99, "curve": [0.156, 300, 0.311, 70, 0.156, 100.99, 0.311, 70]}, {"time": 0.4667, "x": 70, "y": 70}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8, "curve": [0.022, 1.2, 0.044, 0.8, 0.022, 0.8, 0.044, 1.2]}, {"time": 0.0667, "x": 0.8, "y": 1.2, "curve": [0.089, 0.8, 0.111, 1.2, 0.089, 1.2, 0.111, 0.8]}, {"time": 0.1333, "x": 1.2, "y": 0.8, "curve": [0.156, 1.2, 0.178, 0.8, 0.156, 0.8, 0.178, 1.2]}, {"time": 0.2, "x": 0.8, "y": 1.2, "curve": [0.222, 0.8, 0.244, 1.2, 0.222, 1.2, 0.244, 0.8]}, {"time": 0.2667, "x": 1.2, "y": 0.8, "curve": [0.289, 1.2, 0.311, 0.8, 0.289, 0.8, 0.311, 1.2]}, {"time": 0.3333, "x": 0.8, "y": 1.2, "curve": [0.356, 0.8, 0.378, 1.2, 0.356, 1.2, 0.378, 0.8]}, {"time": 0.4, "x": 1.2, "y": 0.8, "curve": [0.422, 1.2, 0.444, 0.694, 0.422, 0.8, 0.444, 1.094]}, {"time": 0.4667, "x": 0.694, "y": 1.094, "curve": [0.903, 0.694, 0.511, 0.8, 0.903, 1.094, 0.511, 1.2]}, {"time": 0.5333, "x": 0.8, "y": 1.2, "curve": [0.556, 0.8, 0.578, 1.2, 0.556, 1.2, 0.578, 0.8]}, {"time": 0.6, "x": 1.2, "y": 0.8, "curve": [0.622, 1.2, 0.644, 0.8, 0.622, 0.8, 0.644, 1.2]}, {"time": 0.6667, "x": 0.8, "y": 1.2, "curve": [0.689, 0.8, 0.711, 1.2, 0.689, 1.2, 0.711, 0.8]}, {"time": 0.7333, "x": 1.2, "y": 0.8, "curve": [0.756, 1.2, 0.778, 0.8, 0.756, 0.8, 0.778, 1.2]}, {"time": 0.8, "x": 0.8, "y": 1.2, "curve": [0.822, 0.8, 0.844, 1.2, 0.822, 1.2, 0.844, 0.8]}, {"time": 0.8667, "x": 1.2, "y": 0.8, "curve": [0.868, 1.2, 0.917, -0.337, 0.868, 0.8, 0.917, 0.063]}, {"time": 0.9333, "x": -0.337, "y": 0.063}], "shear": [{"y": -6.29, "curve": [0.004, 0, 0.026, 0, 0.004, -8.51, 0.026, 6.41]}, {"time": 0.0333, "y": 6.41, "curve": [0.055, 0, 0.078, 0, 0.055, 6.47, 0.078, -19.04]}, {"time": 0.1, "y": -19.04, "curve": [0.122, 0, 0.145, 0, 0.122, -18.86, 0.145, 6.41]}, {"time": 0.1667, "y": 6.41, "curve": [0.189, 0, 0.211, 0, 0.189, 6.47, 0.211, -19.04]}, {"time": 0.2333, "y": -19.04, "curve": [0.255, 0, 0.278, 0, 0.255, -18.86, 0.278, 6.41]}, {"time": 0.3, "y": 6.41, "curve": [0.322, 0, 0.345, 0, 0.322, 6.47, 0.345, -19.04]}, {"time": 0.3667, "y": -19.04, "curve": [0.389, 0, 0.411, 0, 0.389, -18.86, 0.411, 6.41]}, {"time": 0.4333, "y": 6.41, "curve": [0.455, 0, 0.478, 0, 0.455, 6.47, 0.478, -19.04]}, {"time": 0.5, "y": -19.04, "curve": [0.522, 0, 0.545, 0, 0.522, -18.86, 0.545, 6.41]}, {"time": 0.5667, "y": 6.41, "curve": [0.571, 0, 0.629, 0, 0.571, 6.42, 0.629, -17.34]}, {"time": 0.6333, "y": -19.04, "curve": [0.656, 0, 0.678, 0, 0.656, -27.6, 0.678, 6.41]}, {"time": 0.7, "y": 6.41, "curve": [0.722, 0, 0.745, 0, 0.722, 6.47, 0.745, -19.04]}, {"time": 0.7667, "y": -19.04, "curve": [0.789, 0, 0.811, 0, 0.789, -18.86, 0.811, 6.41]}, {"time": 0.8333, "y": 6.41, "curve": [0.855, 0, 0.878, 0, 0.855, 6.47, 0.878, -19.04]}, {"time": 0.9, "y": -19.04, "curve": [0.922, 0, 0.945, 0, 0.922, -18.86, 0.945, 6.41]}, {"time": 0.9667, "y": 6.41}]}, "Jetpack2": {"rotate": [{"curve": "stepped"}, {"time": 1.3, "curve": [1.422, 0, 1.544, -31.2]}, {"time": 1.6667, "value": -31.2}], "translate": [{"curve": "stepped"}, {"time": 1.3, "curve": [1.422, 0, 1.56, 260.96, 1.387, 0, 1.613, -199.44]}, {"time": 1.6667, "x": 268.33, "y": -1059.99}], "scale": [{"x": 110, "y": 110, "curve": "stepped"}, {"time": 0.0667, "x": 110, "y": 110, "curve": [0.1, 110, 0.144, 100, 0.1, 110, 0.144, 100]}, {"time": 0.1667, "x": 100, "y": 100, "curve": [0.2, 100, 0.233, 110, 0.2, 100, 0.233, 110]}, {"time": 0.2667, "x": 110, "y": 110, "curve": [0.3, 110, 0.344, 100, 0.3, 110, 0.344, 100]}, {"time": 0.3667, "x": 100, "y": 100, "curve": [0.4, 100, 0.433, 110, 0.4, 100, 0.433, 110]}, {"time": 0.4667, "x": 110, "y": 110, "curve": [0.479, 110, 0.686, 100, 0.479, 110, 0.686, 100]}, {"time": 0.7, "x": 100, "y": 100}]}, "Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Low_cntr": {"translate": [{"x": 8.12, "y": 2.54, "curve": [0.016, 8.12, 0.051, 8.12, 0.019, 3.53, 0.051, 1.86]}, {"time": 0.0667, "x": 8.12, "y": 2.54, "curve": [0.111, 8.12, 0.156, -4.15, 0.112, 4.47, 0.156, 4.18]}, {"time": 0.2, "x": -4.15, "y": 1.9, "curve": [0.244, -4.15, 0.289, 8.12, 0.242, -0.31, 0.289, 0.61]}, {"time": 0.3333, "x": 8.12, "y": 2.54, "curve": [0.378, 8.12, 0.422, -4.15, 0.384, 4.75, 0.423, 4.03]}, {"time": 0.4667, "x": -4.15, "y": 1.9, "curve": [0.511, -4.15, 0.556, 8.12, 0.512, -0.31, 0.556, 0.27]}, {"time": 0.6, "x": 8.12, "y": 2.54, "curve": [0.644, 8.12, 0.689, -4.15, 0.653, 5.3, 0.689, 3.81]}, {"time": 0.7333, "x": -4.15, "y": 1.9, "curve": [0.778, -4.15, 0.822, 8.12, 0.778, -0.03, 0.823, 1.03]}, {"time": 0.8667, "x": 8.12, "y": 2.54, "curve": [0.911, 8.12, 0.956, -4.15, 0.907, 3.92, 0.956, 1.9]}, {"time": 1, "x": -4.15, "y": 1.9, "curve": [1.373, -4.15, -0.013, 0, 1.373, 1.9, -0.013, 0]}, {"time": 1.6667}], "scale": [{}]}, "Rainbow": {"translate": [{"y": 24.83}], "scale": [{"y": 200, "curve": [0.489, 1, 0.978, 1, 0.489, 200, 0.978, 60]}, {"time": 1.4667, "y": 60}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25, "curve": "stepped"}, {"time": 1.3333, "value": -121.25, "curve": [1.402, 2.7, 1.556, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.8333}], "translate": [{"x": -33.11, "y": -0.73, "curve": "stepped"}, {"time": 1.3333, "x": -33.11, "y": -0.73, "curve": [1.444, -33.11, 1.556, 0, 1.444, -0.73, 1.556, 0]}, {"time": 1.6667}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": [1.356, 0, 1.411, 1, 1.356, 0, 1.411, 1]}, {"time": 1.4667}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{"value": 19.88, "curve": "stepped"}, {"time": 1.2667, "value": 19.88, "curve": [1.356, 19.88, 1.444, 0]}, {"time": 1.5333}], "translate": [{"x": -26.7, "y": 23.9, "curve": "stepped"}, {"time": 1.3, "x": -26.7, "y": 23.9, "curve": [1.389, -26.7, 1.478, 0, 1.389, 23.9, 1.478, 0]}, {"time": 1.5667}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"value": 1}]}, "Rainbow_v2b": {"mix": [{"value": 1}]}, "Rainbow_v2c": {"mix": [{"value": 1}]}, "Rainbow_v2d": {"mix": [{"value": 1}]}, "Rainbow_v2e": {"mix": [{"value": 1}]}, "Rainbow_v2f": {"mix": [{"value": 1}]}, "Rainbow_v2g": {"mix": [{"value": 1}]}, "Rainbow_v2h": {"mix": [{"value": 1}]}, "Rainbow_v2i": {"mix": [{"value": 1}]}, "Rainbow_v2j": {"mix": [{"value": 1}]}, "Rainbow_v2k": {"mix": [{"value": 1}]}, "Rainbow_v2l": {"mix": [{"value": 1}]}, "Rainbow_v2m": {"mix": [{"value": 1}]}, "Rainbow_v2n": {"mix": [{"value": 1}]}, "Rainbow_v2o": {"mix": [{"value": 1}]}, "Rainbow_v2p": {"mix": [{"value": 1}]}, "Rainbow_v2q": {"mix": [{"value": 1}]}, "Rainbow_v2r": {"mix": [{"value": 1}]}, "Rainbow_v2s": {"mix": [{"value": 1}]}, "Rainbow_v2t": {"mix": [{"value": 1}]}, "Rainbow_v2u": {"mix": [{"value": 1}]}, "Rainbow_v2v": {"mix": [{"value": 1}]}, "Rainbow_v2w": {"mix": [{"value": 1}]}, "Rainbow_v2x": {"mix": [{"value": 1}]}}}, "Jump_jet_idle": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{"name": "Jetpack"}]}, "Jetpack2": {"attachment": [{"name": "Jetpack"}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Rainbow4"}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{}], "translate": [{}], "scale": [{"x": 110, "y": 110, "curve": [0.022, 110, 0.052, 100, 0.022, 110, 0.052, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": [0.089, 100, 0.111, 110, 0.089, 100, 0.111, 110]}, {"time": 0.1333, "x": 110, "y": 110, "curve": [0.156, 110, 0.185, 100, 0.156, 110, 0.185, 100]}, {"time": 0.2, "x": 100, "y": 100, "curve": [0.222, 100, 0.244, 110, 0.222, 100, 0.244, 110]}, {"time": 0.2667, "x": 110, "y": 110}]}, "Fire_scale": {"scale": [{"x": 300, "y": 100.99}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8, "curve": [0.022, 1.2, 0.044, 0.8, 0.022, 0.8, 0.044, 1.2]}, {"time": 0.0667, "x": 0.8, "y": 1.2, "curve": [0.089, 0.8, 0.111, 1.2, 0.089, 1.2, 0.111, 0.8]}, {"time": 0.1333, "x": 1.2, "y": 0.8, "curve": [0.156, 1.2, 0.178, 0.8, 0.156, 0.8, 0.178, 1.2]}, {"time": 0.2, "x": 0.8, "y": 1.2, "curve": [0.222, 0.8, 0.244, 1.2, 0.222, 1.2, 0.244, 0.8]}, {"time": 0.2667, "x": 1.2, "y": 0.8}], "shear": [{"y": -6.29, "curve": [0.011, 0, 0.022, 0, 0.011, -12.66, 0.022, -19.04]}, {"time": 0.0333, "y": -19.04, "curve": [0.055, 0, 0.078, 0, 0.055, -18.86, 0.078, 6.41]}, {"time": 0.1, "y": 6.41, "curve": [0.122, 0, 0.145, 0, 0.122, 6.47, 0.145, -19.04]}, {"time": 0.1667, "y": -19.04, "curve": [0.189, 0, 0.211, 0, 0.189, -18.86, 0.211, 6.41]}, {"time": 0.2333, "y": 6.41, "curve": [0.244, 0, 0.256, 0, 0.244, 6.41, 0.256, 0.05]}, {"time": 0.2667, "y": -6.29}]}, "Jetpack2": {"rotate": [{}], "translate": [{}], "scale": [{"x": 110, "y": 110, "curve": [0.022, 110, 0.052, 100, 0.022, 110, 0.052, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": [0.089, 100, 0.111, 110, 0.089, 100, 0.111, 110]}, {"time": 0.1333, "x": 110, "y": 110, "curve": [0.156, 110, 0.185, 100, 0.156, 110, 0.185, 100]}, {"time": 0.2, "x": 100, "y": 100, "curve": [0.222, 100, 0.244, 110, 0.222, 100, 0.244, 110]}, {"time": 0.2667, "x": 110, "y": 110}]}, "Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Low_cntr": {"translate": [{"x": 8.12, "y": 2.54, "curve": [0.044, 8.12, 0.089, -4.15, 0.053, 5.3, 0.089, 3.81]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.178, -0.03, 0.223, 1.03]}, {"time": 0.2667, "x": 8.12, "y": 2.54}], "scale": [{}]}, "Rainbow": {"translate": [{"y": 24.83}], "scale": [{"y": 200}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25}], "translate": [{"x": -33.11, "y": -0.73}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{"value": 19.88}], "translate": [{"x": -26.7, "y": 23.9}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"value": 1}]}, "Rainbow_v2b": {"mix": [{"value": 1}]}, "Rainbow_v2c": {"mix": [{"value": 1}]}, "Rainbow_v2d": {"mix": [{"value": 1}]}, "Rainbow_v2e": {"mix": [{"value": 1}]}, "Rainbow_v2f": {"mix": [{"value": 1}]}, "Rainbow_v2g": {"mix": [{"value": 1}]}, "Rainbow_v2h": {"mix": [{"value": 1}]}, "Rainbow_v2i": {"mix": [{"value": 1}]}, "Rainbow_v2j": {"mix": [{"value": 1}]}, "Rainbow_v2k": {"mix": [{"value": 1}]}, "Rainbow_v2l": {"mix": [{"value": 1}]}, "Rainbow_v2m": {"mix": [{"value": 1}]}, "Rainbow_v2n": {"mix": [{"value": 1}]}, "Rainbow_v2o": {"mix": [{"value": 1}]}, "Rainbow_v2p": {"mix": [{"value": 1}]}, "Rainbow_v2q": {"mix": [{"value": 1}]}, "Rainbow_v2r": {"mix": [{"value": 1}]}, "Rainbow_v2s": {"mix": [{"value": 1}]}, "Rainbow_v2t": {"mix": [{"value": 1}]}, "Rainbow_v2u": {"mix": [{"value": 1}]}, "Rainbow_v2v": {"mix": [{"value": 1}]}, "Rainbow_v2w": {"mix": [{"value": 1}]}, "Rainbow_v2x": {"mix": [{"value": 1}]}}}, "Jump_jet_start": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{"name": "Jetpack"}]}, "Jetpack2": {"attachment": [{"name": "Jetpack"}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Rainbow4"}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}, {"time": 0.8, "name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}, {"time": 0.8, "name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}, {"time": 0.8, "name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}, {"time": 0.8, "name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.5, "x": 100, "y": 100, "curve": [0.533, 100, 0.567, 110, 0.533, 100, 0.567, 110]}, {"time": 0.6, "x": 110, "y": 110, "curve": [0.633, 110, 0.678, 100, 0.633, 110, 0.678, 100]}, {"time": 0.7, "x": 100, "y": 100, "curve": [0.733, 100, 0.767, 110, 0.733, 100, 0.767, 110]}, {"time": 0.8, "x": 110, "y": 110}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70, "curve": "stepped"}, {"time": 0.5, "x": 70, "y": 70, "curve": [0.6, 70, 0.7, 300, 0.6, 70, 0.7, 100.99]}, {"time": 0.8, "x": 300, "y": 100.99}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8, "curve": [0.022, 1.2, 0.044, 0.8, 0.022, 0.8, 0.044, 1.2]}, {"time": 0.0667, "x": 0.8, "y": 1.2, "curve": [0.089, 0.8, 0.111, 1.2, 0.089, 1.2, 0.111, 0.8]}, {"time": 0.1333, "x": 1.2, "y": 0.8, "curve": [0.156, 1.2, 0.178, 0.8, 0.156, 0.8, 0.178, 1.2]}, {"time": 0.2, "x": 0.8, "y": 1.2, "curve": [0.222, 0.8, 0.244, 1.2, 0.222, 1.2, 0.244, 0.8]}, {"time": 0.2667, "x": 1.2, "y": 0.8, "curve": [0.289, 1.2, 0.311, 0.8, 0.289, 0.8, 0.311, 1.2]}, {"time": 0.3333, "x": 0.8, "y": 1.2, "curve": [0.356, 0.8, 0.378, 1.2, 0.356, 1.2, 0.378, 0.8]}, {"time": 0.4, "x": 1.2, "y": 0.8, "curve": [0.422, 1.2, 0.444, 0.8, 0.422, 0.8, 0.444, 1.2]}, {"time": 0.4667, "x": 0.8, "y": 1.2, "curve": [0.489, 0.8, 0.511, 1.2, 0.489, 1.2, 0.511, 0.8]}, {"time": 0.5333, "x": 1.2, "y": 0.8, "curve": [0.556, 1.2, 0.578, 0.8, 0.556, 0.8, 0.578, 1.2]}, {"time": 0.6, "x": 0.8, "y": 1.2, "curve": [0.622, 0.8, 0.644, 1.2, 0.622, 1.2, 0.644, 0.8]}, {"time": 0.6667, "x": 1.2, "y": 0.8, "curve": [0.689, 1.2, 0.711, 0.8, 0.689, 0.8, 0.711, 1.2]}, {"time": 0.7333, "x": 0.8, "y": 1.2, "curve": [0.756, 0.8, 0.778, 1.2, 0.756, 1.2, 0.778, 0.8]}, {"time": 0.8, "x": 1.2, "y": 0.8}], "shear": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -23.32]}, {"time": 0.0333, "y": -19.04, "curve": [0.055, 0, 0.078, 0, 0.055, -10.56, 0.078, 6.41]}, {"time": 0.1, "y": 6.41, "curve": [0.122, 0, 0.145, 0, 0.122, 6.47, 0.145, -19.04]}, {"time": 0.1667, "y": -19.04, "curve": [0.189, 0, 0.211, 0, 0.189, -18.86, 0.211, 6.41]}, {"time": 0.2333, "y": 6.41, "curve": [0.255, 0, 0.278, 0, 0.255, 6.47, 0.278, -19.04]}, {"time": 0.3, "y": -19.04, "curve": [0.322, 0, 0.345, 0, 0.322, -18.86, 0.345, 6.41]}, {"time": 0.3667, "y": 6.41, "curve": [0.389, 0, 0.411, 0, 0.389, 6.47, 0.411, -19.04]}, {"time": 0.4333, "y": -19.04, "curve": [0.455, 0, 0.478, 0, 0.455, -18.86, 0.478, 6.41]}, {"time": 0.5, "y": 6.41, "curve": [0.522, 0, 0.545, 0, 0.522, 6.47, 0.545, -19.04]}, {"time": 0.5667, "y": -19.04, "curve": [0.589, 0, 0.611, 0, 0.589, -18.86, 0.611, 6.41]}, {"time": 0.6333, "y": 6.41, "curve": [0.655, 0, 0.678, 0, 0.655, 6.47, 0.678, -19.04]}, {"time": 0.7, "y": -19.04, "curve": [0.722, 0, 0.745, 0, 0.722, -18.86, 0.745, 6.41]}, {"time": 0.7667, "y": 6.41, "curve": [0.778, 0, 0.789, 0, 0.778, 6.44, 0.789, 0.08]}, {"time": 0.8, "y": -6.29}]}, "Jetpack2": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.5, "x": 100, "y": 100, "curve": [0.533, 100, 0.567, 110, 0.533, 100, 0.567, 110]}, {"time": 0.6, "x": 110, "y": 110, "curve": [0.633, 110, 0.678, 100, 0.633, 110, 0.678, 100]}, {"time": 0.7, "x": 100, "y": 100, "curve": [0.733, 100, 0.767, 110, 0.733, 100, 0.767, 110]}, {"time": 0.8, "x": 110, "y": 110}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Low_cntr": {"translate": [{"curve": [0.044, 0, 0.089, -4.15, 0.044, 0, 0.089, 3.11]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.174, 0.8, 0.223, 0.67]}, {"time": 0.2667, "x": 8.12, "y": 2.54, "curve": [0.311, 8.12, 0.356, -4.15, 0.312, 4.47, 0.356, 4.18]}, {"time": 0.4, "x": -4.15, "y": 1.9, "curve": [0.444, -4.15, 0.489, 8.12, 0.442, -0.31, 0.489, 0.61]}, {"time": 0.5333, "x": 8.12, "y": 2.54, "curve": [0.578, 8.12, 0.622, -4.15, 0.584, 4.75, 0.623, 4.03]}, {"time": 0.6667, "x": -4.15, "y": 1.9, "curve": [0.711, -4.15, 0.756, 8.12, 0.712, -0.31, 0.756, 0.27]}, {"time": 0.8, "x": 8.12, "y": 2.54}], "scale": [{"x": 0.9, "y": 1.1, "curve": [0.044, 0.9, 0.089, 1, 0.044, 1.1, 0.089, 1]}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8}]}, "Rainbow": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.009, 20.31, 0.044, 68.22]}, {"time": 0.0667, "y": 68.22, "curve": [0.267, 0, 0.467, 0, 0.267, 68.22, 0.467, 24.83]}, {"time": 0.6667, "y": 24.83}], "scale": [{"curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 200]}, {"time": 0.1, "y": 200}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": "stepped"}, {"time": 0.8}]}, "Horn": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.8, "x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25}], "translate": [{"x": -33.11, "y": -0.73}]}, "eye_black2": {"scale": [{"curve": "stepped"}, {"time": 0.8}]}, "eye_black": {"scale": [{"curve": "stepped"}, {"time": 0.8}]}, "Body_cntr": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}]}, "star1": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "star2": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "EyeBrow_R": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}, {"time": 0.1333, "value": 19.88}], "translate": [{}, {"time": 0.1333, "x": -26.7, "y": 23.9}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2b": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2c": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2d": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2e": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2f": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2g": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2h": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2i": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2j": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2k": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2l": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2m": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2n": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2o": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2p": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2q": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2r": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2s": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2t": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2u": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2v": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2w": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2x": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}}}, "Jump_Propeller_end": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{"name": "st_6"}, {"time": 0.0333, "name": "st_6"}, {"time": 0.0667, "name": "st_66"}, {"time": 0.1, "name": "st_66"}, {"time": 0.1333, "name": "st_6"}, {"time": 0.1667, "name": "st_6"}, {"time": 0.2, "name": "st_66"}, {"time": 0.2333, "name": "st_66"}, {"time": 0.2667, "name": "st_6"}, {"time": 0.3, "name": "st_6"}, {"time": 0.3333, "name": "st_66"}, {"time": 0.3667, "name": "st_66"}, {"time": 0.4, "name": "st_6"}, {"time": 0.4333, "name": "st_6"}, {"time": 0.4667, "name": "st_66"}, {"time": 0.5, "name": "st_66"}, {"time": 0.5333, "name": "st_6"}, {"time": 0.5667, "name": "st_6"}, {"time": 0.6, "name": "st_66"}, {"time": 0.6333, "name": "st_66"}, {"time": 0.6667, "name": "st_6"}, {"time": 0.7, "name": "st_6"}, {"time": 0.7333, "name": "st_66"}, {"time": 0.7667, "name": "st_66"}, {"time": 0.8, "name": "st_5"}, {"time": 0.8333, "name": "st_4"}, {"time": 0.8667, "name": "st_3"}, {"time": 0.9333, "name": "st_1"}, {"time": 1, "name": "st_2"}, {"time": 1.0667, "name": "st_1"}, {"time": 1.1333, "name": "st_2"}, {"time": 1.2, "name": "st_1"}, {"time": 1.2667, "name": "st_2"}, {"time": 1.3333, "name": "st_1"}, {"time": 1.4, "name": "st_2"}, {"time": 1.4667, "name": "st_1"}, {"time": 1.5333, "name": "st_2"}]}, "Rainbow2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff", "curve": [1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 0]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"name": "Rainbow4"}, {"time": 1.6667}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}, {"time": 1.1667}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}, {"time": 1.1667}]}, "Face/eye_black": {"attachment": [{"name": "eye"}, {"time": 1.1667, "name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}, {"time": 1.1667, "name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8}], "shear": [{}]}, "Jetpack2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Low_cntr": {"translate": [{"x": 8.12, "y": 2.54, "curve": [0.016, 8.12, 0.051, 8.12, 0.019, 3.53, 0.051, 1.86]}, {"time": 0.0667, "x": 8.12, "y": 2.54, "curve": [0.111, 8.12, 0.156, -4.15, 0.112, 4.47, 0.156, 4.18]}, {"time": 0.2, "x": -4.15, "y": 1.9, "curve": [0.244, -4.15, 0.289, 8.12, 0.242, -0.31, 0.289, 0.61]}, {"time": 0.3333, "x": 8.12, "y": 2.54, "curve": [0.378, 8.12, 0.422, -4.15, 0.384, 4.75, 0.423, 4.03]}, {"time": 0.4667, "x": -4.15, "y": 1.9, "curve": [0.511, -4.15, 0.556, 8.12, 0.512, -0.31, 0.556, 0.27]}, {"time": 0.6, "x": 8.12, "y": 2.54, "curve": [0.644, 8.12, 0.689, -4.15, 0.653, 5.3, 0.689, 3.81]}, {"time": 0.7333, "x": -4.15, "y": 1.9, "curve": [0.778, -4.15, 0.822, 8.12, 0.778, -0.03, 0.823, 1.03]}, {"time": 0.8667, "x": 8.12, "y": 2.54, "curve": [0.911, 8.12, 0.956, -4.15, 0.907, 3.92, 0.956, 1.9]}, {"time": 1, "x": -4.15, "y": 1.9, "curve": [1.373, -4.15, -0.013, 0, 1.373, 1.9, -0.013, 0]}, {"time": 1.6667}], "scale": [{}]}, "Rainbow": {"translate": [{"y": 24.83}], "scale": [{"y": 200, "curve": [0.489, 1, 0.978, 1, 0.489, 200, 0.978, 60]}, {"time": 1.4667, "y": 60}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25, "curve": "stepped"}, {"time": 1.1667, "value": -121.25, "curve": [1.235, 2.7, 1.371, 0]}, {"time": 1.6667}], "translate": [{"x": -22.6, "y": 2.92, "curve": "stepped"}, {"time": 1.1667, "x": -22.6, "y": 2.92}, {"time": 1.6667}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{"curve": "stepped"}, {"time": 1.1333, "curve": [1.311, 0, 1.489, -58.44]}, {"time": 1.6667, "value": -58.44}], "translate": [{"curve": "stepped"}, {"time": 1.1333, "curve": [1.311, 0, 1.489, 265.32, 1.311, 0, 1.555, -334.39]}, {"time": 1.6667, "x": 265.32, "y": -903}], "scale": [{}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": [1.189, 0, 1.244, 1, 1.189, 0, 1.244, 1]}, {"time": 1.3}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{"value": 19.88, "curve": "stepped"}, {"time": 1.1333, "value": 19.88, "curve": [1.222, 19.88, 1.311, 0]}, {"time": 1.4}], "translate": [{"x": -26.7, "y": 23.9, "curve": "stepped"}, {"time": 1.1667, "x": -26.7, "y": 23.9, "curve": [1.256, -26.7, 1.344, 0, 1.256, 23.9, 1.344, 0]}, {"time": 1.4333}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"value": 1}]}, "Rainbow_v2b": {"mix": [{"value": 1}]}, "Rainbow_v2c": {"mix": [{"value": 1}]}, "Rainbow_v2d": {"mix": [{"value": 1}]}, "Rainbow_v2e": {"mix": [{"value": 1}]}, "Rainbow_v2f": {"mix": [{"value": 1}]}, "Rainbow_v2g": {"mix": [{"value": 1}]}, "Rainbow_v2h": {"mix": [{"value": 1}]}, "Rainbow_v2i": {"mix": [{"value": 1}]}, "Rainbow_v2j": {"mix": [{"value": 1}]}, "Rainbow_v2k": {"mix": [{"value": 1}]}, "Rainbow_v2l": {"mix": [{"value": 1}]}, "Rainbow_v2m": {"mix": [{"value": 1}]}, "Rainbow_v2n": {"mix": [{"value": 1}]}, "Rainbow_v2o": {"mix": [{"value": 1}]}, "Rainbow_v2p": {"mix": [{"value": 1}]}, "Rainbow_v2q": {"mix": [{"value": 1}]}, "Rainbow_v2r": {"mix": [{"value": 1}]}, "Rainbow_v2s": {"mix": [{"value": 1}]}, "Rainbow_v2t": {"mix": [{"value": 1}]}, "Rainbow_v2u": {"mix": [{"value": 1}]}, "Rainbow_v2v": {"mix": [{"value": 1}]}, "Rainbow_v2w": {"mix": [{"value": 1}]}, "Rainbow_v2x": {"mix": [{"value": 1}]}}}, "Jump_propeller_idle": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{"name": "st_6"}, {"time": 0.0333, "name": "st_66"}, {"time": 0.0667, "name": "st_66"}, {"time": 0.1, "name": "st_6"}, {"time": 0.1333, "name": "st_6"}, {"time": 0.1667, "name": "st_66"}, {"time": 0.2, "name": "st_66"}, {"time": 0.2333, "name": "st_6"}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Rainbow4"}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8}], "shear": [{}]}, "Jetpack2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Low_cntr": {"translate": [{"x": 8.12, "y": 2.54, "curve": [0.044, 8.12, 0.089, -4.15, 0.053, 5.3, 0.089, 3.81]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.178, -0.03, 0.223, 1.03]}, {"time": 0.2667, "x": 8.12, "y": 2.54}], "scale": [{}]}, "Rainbow": {"translate": [{"y": 24.83}], "scale": [{"y": 200}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25}], "translate": [{"x": -22.6, "y": 2.92}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{"value": 19.88}], "translate": [{"x": -26.7, "y": 23.9}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"value": 1}]}, "Rainbow_v2b": {"mix": [{"value": 1}]}, "Rainbow_v2c": {"mix": [{"value": 1}]}, "Rainbow_v2d": {"mix": [{"value": 1}]}, "Rainbow_v2e": {"mix": [{"value": 1}]}, "Rainbow_v2f": {"mix": [{"value": 1}]}, "Rainbow_v2g": {"mix": [{"value": 1}]}, "Rainbow_v2h": {"mix": [{"value": 1}]}, "Rainbow_v2i": {"mix": [{"value": 1}]}, "Rainbow_v2j": {"mix": [{"value": 1}]}, "Rainbow_v2k": {"mix": [{"value": 1}]}, "Rainbow_v2l": {"mix": [{"value": 1}]}, "Rainbow_v2m": {"mix": [{"value": 1}]}, "Rainbow_v2n": {"mix": [{"value": 1}]}, "Rainbow_v2o": {"mix": [{"value": 1}]}, "Rainbow_v2p": {"mix": [{"value": 1}]}, "Rainbow_v2q": {"mix": [{"value": 1}]}, "Rainbow_v2r": {"mix": [{"value": 1}]}, "Rainbow_v2s": {"mix": [{"value": 1}]}, "Rainbow_v2t": {"mix": [{"value": 1}]}, "Rainbow_v2u": {"mix": [{"value": 1}]}, "Rainbow_v2v": {"mix": [{"value": 1}]}, "Rainbow_v2w": {"mix": [{"value": 1}]}, "Rainbow_v2x": {"mix": [{"value": 1}]}}}, "Jump_propeller_start": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{"name": "st_6"}, {"time": 0.0333, "name": "st_6"}, {"time": 0.0667, "name": "st_66"}, {"time": 0.1, "name": "st_66"}, {"time": 0.1333, "name": "st_6"}, {"time": 0.1667, "name": "st_6"}, {"time": 0.2, "name": "st_66"}, {"time": 0.2333, "name": "st_66"}, {"time": 0.2667, "name": "st_6"}, {"time": 0.3, "name": "st_6"}, {"time": 0.3333, "name": "st_66"}, {"time": 0.3667, "name": "st_66"}, {"time": 0.4, "name": "st_6"}, {"time": 0.4333, "name": "st_6"}, {"time": 0.4667, "name": "st_66"}, {"time": 0.5, "name": "st_66"}, {"time": 0.5333, "name": "st_6"}, {"time": 0.5667, "name": "st_6"}, {"time": 0.6, "name": "st_66"}, {"time": 0.6333, "name": "st_66"}, {"time": 0.6667, "name": "st_6"}, {"time": 0.7, "name": "st_6"}, {"time": 0.7333, "name": "st_66"}, {"time": 0.7667, "name": "st_66"}, {"time": 0.8, "name": "st_6"}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Rainbow4"}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}, {"time": 0.8, "name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}, {"time": 0.8, "name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}, {"time": 0.8, "name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}, {"time": 0.8, "name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Jetpack": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8}], "shear": [{}]}, "Jetpack2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8}]}, "Low_cntr": {"translate": [{"curve": [0.044, 0, 0.089, -4.15, 0.044, 0, 0.089, 3.11]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.174, 0.8, 0.223, 0.67]}, {"time": 0.2667, "x": 8.12, "y": 2.54, "curve": [0.311, 8.12, 0.356, -4.15, 0.312, 4.47, 0.356, 4.18]}, {"time": 0.4, "x": -4.15, "y": 1.9, "curve": [0.444, -4.15, 0.489, 8.12, 0.442, -0.31, 0.489, 0.61]}, {"time": 0.5333, "x": 8.12, "y": 2.54, "curve": [0.578, 8.12, 0.622, -4.15, 0.584, 4.75, 0.623, 4.03]}, {"time": 0.6667, "x": -4.15, "y": 1.9, "curve": [0.711, -4.15, 0.756, 8.12, 0.712, -0.31, 0.756, 0.27]}, {"time": 0.8, "x": 8.12, "y": 2.54}], "scale": [{"x": 0.9, "y": 1.1, "curve": [0.044, 0.9, 0.089, 1, 0.044, 1.1, 0.089, 1]}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8}]}, "Rainbow": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.009, 20.31, 0.044, 68.22]}, {"time": 0.0667, "y": 68.22, "curve": [0.267, 0, 0.467, 0, 0.267, 68.22, 0.467, 24.83]}, {"time": 0.6667, "y": 24.83}], "scale": [{"curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 200]}, {"time": 0.1, "y": 200}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"curve": "stepped"}, {"time": 0.8}]}, "Horn": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.8, "x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25}], "translate": [{"x": -33.11, "y": -0.73}]}, "eye_black2": {"scale": [{"curve": "stepped"}, {"time": 0.8}]}, "eye_black": {"scale": [{"curve": "stepped"}, {"time": 0.8}]}, "Body_cntr": {"rotate": [{"curve": "stepped"}, {"time": 0.8}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8}]}, "star1": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "star2": {"translate": [{"curve": "stepped"}, {"time": 0.8}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "EyeBrow_L": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "EyeBrow_R": {"translate": [{"curve": "stepped"}, {"time": 0.8}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": [0.467, 5.67, 0.633, 0, 0.467, 0, 0.633, 0]}, {"time": 0.8}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01, "curve": [0.044, 0.407, 0.089, 1.5, 0.044, 0.407, 0.089, 1.5]}, {"time": 0.1333, "x": 1.5, "y": 1.5, "curve": [0.211, 1.5, 0.289, 1.194, 0.211, 1.5, 0.289, 1.194]}, {"time": 0.3667}]}, "Tail_front": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{"curve": [0.044, 0, 0.089, 19.88]}, {"time": 0.1333, "value": 19.88}], "translate": [{"curve": [0.044, 0, 0.089, -26.7, 0.044, 0, 0.089, 23.9]}, {"time": 0.1333, "x": -26.7, "y": 23.9}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2b": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2c": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2d": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2e": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2f": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2g": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2h": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2i": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2j": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2k": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2l": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2m": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2n": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2o": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2p": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2q": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2r": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2s": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2t": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2u": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2v": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2w": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}, "Rainbow_v2x": {"mix": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.378, 0, 0.522, 1]}, {"time": 0.6667, "value": 1}]}}}, "Jump_spring_start": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{}], "scale": [{}]}, "Body_cntr": {"rotate": [{"curve": [0.136, -136.2, 0.644, -720]}, {"time": 0.9667, "value": -720, "curve": "stepped"}, {"time": 1}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"curve": [0.022, 0, 0.044, 73.56]}, {"time": 0.0667, "value": 73.56, "curve": [0.322, 73.56, 0.578, 50.26]}, {"time": 0.8333, "value": 50.26, "curve": [0.889, 50.26, 0.944, 0]}, {"time": 1}], "translate": [{}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Low_cntr": {"translate": [{}], "scale": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{}]}, "Leg_Outline_big3": {"translate": [{}]}, "Leg_Outline_big": {"translate": [{}]}, "Leg_Outline_big4": {"translate": [{}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{}], "scale": [{}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}], "translate": [{}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}}, "Jump_trampoline_start": {"slots": {"Chainsaw_Eye_L": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Chainsaw_Eye_R": {"attachment": [{"name": "Chainsaw_Eye_L"}]}, "Jetpack": {"attachment": [{}]}, "Jetpack2": {"attachment": [{}]}, "Propeller": {"attachment": [{}]}, "Rainbow2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}, "Face/Pepe_Eye1": {"attachment": [{"name": "Pepe_Eye1"}]}, "Face/Pepe_Eye2": {"attachment": [{"name": "Pepe_Eye2"}]}, "Face/Trump_eye_L": {"attachment": [{"name": "Trump_eye_L"}]}, "Face/Trump_eye_R": {"attachment": [{"name": "<PERSON>_eye_R"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Jetpack": {"scale": [{}]}, "Jetpack2": {"scale": [{}]}, "Rainbow": {"translate": [{"y": 58.76}], "scale": [{}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{}], "translate": [{"curve": [0.022, 0, 0.044, -7.5, 0.022, 0, 0.044, -29.59]}, {"time": 0.0667, "x": -7.5, "y": -29.59, "curve": [0.256, -7.5, 0.444, 0, 0.256, -29.59, 0.444, 0]}, {"time": 0.6333}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667}]}, "Low_cntr": {"translate": [{}], "scale": [{"x": 0.9, "y": 1.1, "curve": [0.044, 0.9, 0.089, 1, 0.044, 1.1, 0.089, 1]}, {"time": 0.1333}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Propeller": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0.01, "y": 0.01}]}, "Tail_front": {"translate": [{"curve": [0.022, 0, 0.044, -0.17, 0.022, 0, 0.044, -29.64]}, {"time": 0.0667, "x": -0.17, "y": -29.64, "curve": [0.256, -0.17, 0.444, 0, 0.256, -29.64, 0.444, 0]}, {"time": 0.6333}], "scale": [{}]}, "Capy_ear": {"rotate": [{}]}, "plunger": {"rotate": [{}], "translate": [{}]}, "Duck_wing": {"rotate": [{}], "translate": [{}]}, "Trump_Hat": {"translate": [{}]}}, "physics": {"Rainbow_v2": {"mix": [{}]}, "Rainbow_v2b": {"mix": [{}]}, "Rainbow_v2c": {"mix": [{}]}, "Rainbow_v2d": {"mix": [{}]}, "Rainbow_v2e": {"mix": [{}]}, "Rainbow_v2f": {"mix": [{}]}, "Rainbow_v2g": {"mix": [{}]}, "Rainbow_v2h": {"mix": [{}]}, "Rainbow_v2i": {"mix": [{}]}, "Rainbow_v2j": {"mix": [{}]}, "Rainbow_v2k": {"mix": [{}]}, "Rainbow_v2l": {"mix": [{}]}, "Rainbow_v2m": {"mix": [{}]}, "Rainbow_v2n": {"mix": [{}]}, "Rainbow_v2o": {"mix": [{}]}, "Rainbow_v2p": {"mix": [{}]}, "Rainbow_v2q": {"mix": [{}]}, "Rainbow_v2r": {"mix": [{}]}, "Rainbow_v2s": {"mix": [{}]}, "Rainbow_v2t": {"mix": [{}]}, "Rainbow_v2u": {"mix": [{}]}, "Rainbow_v2v": {"mix": [{}]}, "Rainbow_v2w": {"mix": [{}]}, "Rainbow_v2x": {"mix": [{}]}}}, "t0_idle": {"bones": {"Scared_eye2": {"rotate": [{}, {"time": 1.3333, "value": 360}]}, "Scared_eye1": {"rotate": [{}, {"time": 1.3333, "value": 360}]}, "chainsaw_part": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "value": -13.65}, {"time": 1, "value": -61.8}, {"time": 1.3333, "value": -115.38}], "translate": [{}, {"time": 0.3333, "x": 44.2, "y": -0.41}, {"time": 0.6667, "x": 89.86, "y": -11.14}, {"time": 1, "x": 126.43, "y": -39.68}, {"time": 1.3333, "x": 128.94, "y": -78.65}]}, "chainsaw_part2": {"rotate": [{}, {"time": 0.3333, "value": -13.65}, {"time": 0.6667, "value": -61.8}, {"time": 1, "value": -115.38}, {"time": 1.3333, "value": -157.09}], "translate": [{}, {"time": 0.3333, "x": 45.66, "y": -10.73}, {"time": 0.6667, "x": 82.23, "y": -39.27}, {"time": 1, "x": 84.74, "y": -78.24}, {"time": 1.3333, "x": 52.47, "y": -108.36}]}, "chainsaw_part3": {"rotate": [{}, {"time": 0.3333, "value": -48.16}, {"time": 0.6667, "value": -101.73}, {"time": 1, "value": -143.45}, {"time": 1.3333, "value": -158.74}], "translate": [{}, {"time": 0.3333, "x": 36.57, "y": -28.54}, {"time": 0.6667, "x": 39.08, "y": -67.51}, {"time": 1, "x": 6.81, "y": -97.63}, {"time": 1.3333, "x": -34.74, "y": -108.99}]}, "chainsaw_part4": {"rotate": [{}, {"time": 0.3333, "value": -53.57}, {"time": 0.6667, "value": -95.29}, {"time": 1, "value": -110.59}, {"time": 1.3333, "value": -120.36}], "translate": [{}, {"time": 0.3333, "x": 2.51, "y": -38.97}, {"time": 0.6667, "x": -29.76, "y": -69.08}, {"time": 1, "x": -71.31, "y": -80.45}, {"time": 1.3333, "x": -117.06, "y": -83.06}]}, "chainsaw_part5": {"rotate": [{}, {"time": 0.3333, "value": -41.72}, {"time": 0.6667, "value": -57.01}, {"time": 1, "value": -66.79, "curve": "stepped"}, {"time": 1.2, "value": -66.79, "curve": "stepped"}, {"time": 1.2333, "value": 132.88}, {"time": 1.3333, "value": 115.38}], "translate": [{}, {"time": 0.3333, "x": -32.27, "y": -30.12}, {"time": 0.6667, "x": -73.82, "y": -41.48}, {"time": 1, "x": -119.57, "y": -44.09}, {"time": 1.2, "x": -143.38, "y": -36.96, "curve": "stepped"}, {"time": 1.2333, "x": -138.99, "y": 64.6}, {"time": 1.3333, "x": -128.94, "y": 78.65}]}, "chainsaw_part6": {"rotate": [{}, {"time": 0.3333, "value": -15.29}, {"time": 0.6667, "value": -25.07, "curve": "stepped"}, {"time": 0.8667, "value": -25.07, "curve": "stepped"}, {"time": 0.9, "value": 174.6}, {"time": 1, "value": 157.09}], "translate": [{}, {"time": 0.3333, "x": -41.55, "y": -11.37}, {"time": 0.6667, "x": -87.3, "y": -13.97}, {"time": 0.8667, "x": -110.47, "y": -7.37, "curve": "stepped"}, {"time": 0.9, "x": -106.72, "y": 94.72}, {"time": 1, "x": -96.67, "y": 108.77}, {"time": 1.3333, "x": -52.47, "y": 108.36}]}, "chainsaw_part7": {"rotate": [{}, {"time": 0.3333, "value": -9.77, "curve": "stepped"}, {"time": 0.5333, "value": -9.77, "curve": "stepped"}, {"time": 0.5667, "value": 189.89}, {"time": 0.6667, "value": 172.39}, {"time": 1.3333, "value": 158.74}], "translate": [{}, {"time": 0.3333, "x": -45.75, "y": -2.6}, {"time": 0.5333, "x": -67.67, "y": 3.31, "curve": "stepped"}, {"time": 0.5667, "x": -65.17, "y": 106.09}, {"time": 0.6667, "x": -55.12, "y": 120.13}, {"time": 1, "x": -10.92, "y": 119.72}, {"time": 1.3333, "x": 34.74, "y": 108.99}]}, "chainsaw_part8": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "value": 199.67}, {"time": 0.3333, "value": 182.16}, {"time": 1, "value": 168.52}, {"time": 1.3333, "value": 120.36}], "translate": [{}, {"time": 0.2, "x": -26.7, "y": 4.1, "curve": "stepped"}, {"time": 0.2333, "x": -19.42, "y": 108.69}, {"time": 0.3333, "x": -9.36, "y": 122.74}, {"time": 0.6667, "x": 34.83, "y": 122.33}, {"time": 1, "x": 80.49, "y": 111.6}, {"time": 1.3333, "x": 117.06, "y": 83.06}]}}}, "Blend_move/t1_IDLE": {"bones": {"Leg_F_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Horn": {"rotate": [{}]}, "Body_cntr": {"rotate": [{}]}}}, "Blend_move/t1_jump_1": {"bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"y": -8.25}], "scale": [{}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"y": -8.25}], "scale": [{}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"y": -8.25}], "scale": [{}]}, "Leg_F_R": {"rotate": [{}], "translate": [{"y": -8.25}], "scale": [{}]}, "Horn": {"rotate": [{}]}, "Body_cntr": {"rotate": [{}]}}}, "Blend_move/t1_jump_2": {"bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"y": -0.86}], "scale": [{"x": 0.577}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"y": -0.86}], "scale": [{"x": 0.577}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"y": -0.86}], "scale": [{"x": 0.577}]}, "Leg_F_R": {"rotate": [{}], "translate": [{"y": -0.86}], "scale": [{"x": 0.577}]}, "Horn": {"rotate": [{}]}, "Body_cntr": {"rotate": [{}]}}}, "Blend_move/t2_side_move_1": {"bones": {"Body_cntr": {"rotate": [{"value": -28.78}]}, "Horn": {"rotate": [{"value": 25.69}]}}}, "Blend_move/t2_side_move_2": {"bones": {"Body_cntr": {"rotate": [{}]}, "Horn": {"rotate": [{}]}}}, "Examples/Jump_side": {"bones": {"Low_cntr": {"translate": [{"curve": [0.189, 0, 0.349, -603.85, 0.236, 1627.53, 0.446, 1386.13]}, {"time": 0.5667, "x": -603.85, "y": 827.3, "curve": [0.6, -603.85, 0.633, -603.85, 0.6, 827.3, 0.633, 827.3]}, {"time": 0.6667, "x": -603.85, "y": 827.3, "curve": [0.856, -603.85, 1.044, 0, 0.779, 1525.75, 1.117, 1424.96]}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3333}], "scale": [{"x": 0.6, "y": 1.5, "curve": [0.078, 0.6, 0.156, 1, 0.078, 1.5, 0.156, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5333, "curve": [0.556, 1, 0.578, 1.4, 0.556, 1, 0.578, 0.6]}, {"time": 0.6, "x": 1.4, "y": 0.6, "curve": [0.611, 1.4, 0.622, 1.201, 0.611, 0.6, 0.622, 0.824]}, {"time": 0.6333, "y": 1.05, "curve": "stepped"}, {"time": 0.6667, "x": -0.6, "y": 1.5, "curve": [0.733, -0.6, 0.8, -1, 0.733, 1.5, 0.8, 1]}, {"time": 0.8667, "x": -1, "curve": "stepped"}, {"time": 1.2, "x": -1, "curve": [1.222, -1, 1.244, -1.4, 1.222, 1, 1.244, 0.6]}, {"time": 1.2667, "x": -1.4, "y": 0.6, "curve": [1.289, -1.4, 1.311, -0.6, 1.289, 0.6, 1.311, 1.5]}, {"time": 1.3333, "x": -0.6, "y": 1.5}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, -22.04]}, {"time": 0.3333, "value": -22.04, "curve": [0.411, -22.04, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, -22.04]}, {"time": 1, "value": -22.04, "curve": [1.078, -22.04, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, -36.79, 0.222, 0, 0.278, -3.45]}, {"time": 0.3333, "x": -36.79, "y": -3.45, "curve": [0.444, -36.79, 0.556, 0, 0.444, -3.45, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, 59.89]}, {"time": 0.3333, "value": 59.89, "curve": [0.411, 59.89, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, 59.89]}, {"time": 1, "value": 59.89, "curve": [1.078, 59.89, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, 29.89, 0.222, 0, 0.278, 14.95]}, {"time": 0.3333, "x": 29.89, "y": 14.95, "curve": [0.444, 29.89, 0.556, 0, 0.444, 14.95, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, 31.56]}, {"time": 0.3333, "value": 31.56, "curve": [0.411, 31.56, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, 31.56]}, {"time": 1, "value": 31.56, "curve": [1.078, 31.56, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, 36.22, 0.222, 0, 0.278, 2.3]}, {"time": 0.3333, "x": 36.22, "y": 2.3, "curve": [0.444, 36.22, 0.556, 0, 0.444, 2.3, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, -52.92]}, {"time": 0.3333, "value": -52.92, "curve": [0.411, -52.92, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, -52.92]}, {"time": 1, "value": -52.92, "curve": [1.078, -52.92, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, -36.22, 0.222, 0, 0.278, 11.5]}, {"time": 0.3333, "x": -36.22, "y": 11.5, "curve": [0.444, -36.22, 0.556, 0, 0.444, 11.5, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Body_cntr": {"rotate": [{"curve": [0.044, -7.53, 0.089, -28.78]}, {"time": 0.1333, "value": -28.78, "curve": [0.278, -28.78, 0.422, -7.53]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667, "curve": [0.711, -7.53, 0.756, -35.95]}, {"time": 0.8, "value": -35.95, "curve": [0.944, -35.95, 1.089, -7.53]}, {"time": 1.2333}]}, "Horn": {"rotate": [{"curve": [0.044, 0, 0.089, 25.69]}, {"time": 0.1333, "value": 25.69, "curve": [0.278, 25.69, 0.422, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667, "curve": [0.711, 0, 0.756, 25.69]}, {"time": 0.8, "value": 25.69, "curve": [0.944, 25.69, 1.089, 0]}, {"time": 1.2333}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up": {"bones": {"Low_cntr": {"translate": [{"curve": [0.189, 0, 0.349, 0, 0.236, 1627.53, 0.446, 1386.13]}, {"time": 0.5667, "y": 827.3, "curve": [0.6, 0, 0.633, 0, 0.6, 827.3, 0.633, 827.3]}, {"time": 0.6667, "y": 827.3, "curve": [0.856, 0, 1.044, 0, 0.779, 1525.75, 1.117, 1424.96]}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3333}], "scale": [{"x": 0.6, "y": 1.5, "curve": [0.078, 0.6, 0.156, 1, 0.078, 1.5, 0.156, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5333, "curve": [0.556, 1, 0.578, 1.4, 0.556, 1, 0.578, 0.6]}, {"time": 0.6, "x": 1.4, "y": 0.6, "curve": [0.622, 1.4, 0.644, 0.6, 0.622, 0.6, 0.644, 1.5]}, {"time": 0.6667, "x": 0.6, "y": 1.5, "curve": [0.733, 0.6, 0.8, 1, 0.733, 1.5, 0.8, 1]}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2, "curve": [1.222, 1, 1.244, 1.4, 1.222, 1, 1.244, 0.6]}, {"time": 1.2667, "x": 1.4, "y": 0.6, "curve": [1.289, 1.4, 1.311, 0.6, 1.289, 0.6, 1.311, 1.5]}, {"time": 1.3333, "x": 0.6, "y": 1.5}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, -22.04]}, {"time": 0.3333, "value": -22.04, "curve": [0.411, -22.04, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, -22.04]}, {"time": 1, "value": -22.04, "curve": [1.078, -22.04, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, -36.79, 0.222, 0, 0.278, -3.45]}, {"time": 0.3333, "x": -36.79, "y": -3.45, "curve": [0.444, -36.79, 0.556, 0, 0.444, -3.45, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, 59.89]}, {"time": 0.3333, "value": 59.89, "curve": [0.411, 59.89, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, 59.89]}, {"time": 1, "value": 59.89, "curve": [1.078, 59.89, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, 29.89, 0.222, 0, 0.278, 14.95]}, {"time": 0.3333, "x": 29.89, "y": 14.95, "curve": [0.444, 29.89, 0.556, 0, 0.444, 14.95, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, 31.56]}, {"time": 0.3333, "value": 31.56, "curve": [0.411, 31.56, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, 31.56]}, {"time": 1, "value": 31.56, "curve": [1.078, 31.56, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, 36.22, 0.222, 0, 0.278, 2.3]}, {"time": 0.3333, "x": 36.22, "y": 2.3, "curve": [0.444, 36.22, 0.556, 0, 0.444, 2.3, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.2, 0, 0.267, -52.92]}, {"time": 0.3333, "value": -52.92, "curve": [0.411, -52.92, 0.489, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "curve": [0.867, 0, 0.933, -52.92]}, {"time": 1, "value": -52.92, "curve": [1.078, -52.92, 1.156, 0]}, {"time": 1.2333}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.078, 0, 0.122, 0, 0.078, -8.25, 0.122, 0]}, {"time": 0.1667, "curve": [0.222, 0, 0.278, -36.22, 0.222, 0, 0.278, 11.5]}, {"time": 0.3333, "x": -36.22, "y": 11.5, "curve": [0.444, -36.22, 0.556, 0, 0.444, 11.5, 0.556, 0]}, {"time": 0.6667, "curve": [0.678, 0, 0.689, 0, 0.678, 0, 0.689, -8.25]}, {"time": 0.7, "y": -8.25, "curve": [0.744, 0, 0.789, 0, 0.744, -8.25, 0.789, 0]}, {"time": 0.8333}]}, "Body_cntr": {"rotate": [{}]}, "Horn": {"rotate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up2": {"bones": {"Low_cntr": {"translate": [{"curve": [0.2, 0, 0.67, 0, 0.25, 1723.26, 0.772, 1419]}, {"time": 0.9, "y": 827.3, "curve": [0.933, 0, 0.967, 0, 0.933, 827.3, 0.967, 827.3]}, {"time": 1, "y": 827.3, "curve": [1.198, 0, 1.702, 0, 1.118, 1557.7, 1.779, 1490.15]}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"x": 0.6, "y": 1.5, "curve": [0.105, 0.6, 0.109, 1, 0.105, 1.5, 0.109, 1]}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 0.9, "x": 1.4, "y": 0.6, "curve": [0.922, 1.4, 0.944, 1.237, 0.922, 0.6, 0.944, 0.733]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "x": 0.6, "y": 1.5, "curve": [1.105, 0.6, 1.109, 1, 1.105, 1.5, 1.109, 1]}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.8667, "curve": "stepped"}, {"time": 1.9, "x": 1.4, "y": 0.6, "curve": [1.922, 1.4, 1.944, 1.237, 1.922, 0.6, 1.944, 0.733]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "x": 0.6, "y": 1.5}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -6.09]}, {"time": 0.2667, "value": -6.09, "curve": [0.344, -6.09, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -6.09]}, {"time": 1.2667, "value": -6.09, "curve": [1.344, -6.09, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.1, 14.15, 0.167, -7.13, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -7.13, "curve": [0.378, -7.13, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 14.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 14.15, "y": -8.25, "curve": [1.1, 14.15, 1.167, -7.13, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -7.13, "curve": [1.378, -7.13, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 10.62]}, {"time": 0.2667, "value": 10.62, "curve": [0.344, 10.62, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 10.62]}, {"time": 1.2667, "value": 10.62, "curve": [1.344, 10.62, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.1, -13.32, 0.167, 6.04, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 6.04, "curve": [0.378, 6.04, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, -13.32, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": -13.32, "y": -8.25, "curve": [1.1, -13.32, 1.167, 6.04, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 6.04, "curve": [1.378, 6.04, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 5.54]}, {"time": 0.2667, "value": 5.54, "curve": [0.344, 5.54, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 5.54]}, {"time": 1.2667, "value": 5.54, "curve": [1.344, 5.54, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.1, 0, 0.167, 5.49, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 5.49, "curve": [0.378, 5.49, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 0, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "y": -8.25, "curve": [1.1, 0, 1.167, 5.49, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 5.49, "curve": [1.378, 5.49, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -16.85]}, {"time": 0.2667, "value": -16.85, "curve": [0.344, -16.85, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -16.85]}, {"time": 1.2667, "value": -16.85, "curve": [1.344, -16.85, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.1, 9.15, 0.167, -13.72, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -13.72, "curve": [0.245, -13.72, 0.256, -14.12, 0.245, 0, 0.256, 2.53]}, {"time": 0.2667, "x": -13.96, "y": 2.53, "curve": [0.401, -12.1, 0.534, 0, 0.401, 2.53, 0.534, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 9.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 9.15, "y": -8.25, "curve": [1.1, 9.15, 1.167, -13.72, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -13.72, "curve": [1.245, -13.72, 1.256, -14.12, 1.245, 0, 1.256, 2.53]}, {"time": 1.2667, "x": -13.96, "y": 2.53, "curve": [1.401, -12.1, 1.534, 0, 1.401, 2.53, 1.534, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Body_cntr": {"rotate": [{}]}, "Horn": {"rotate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up3": {"bones": {"Low_cntr": {"translate": [{"curve": [0.2, 0, 0.67, 0, 0.25, 1723.26, 0.772, 1419]}, {"time": 0.9, "y": 827.3, "curve": [0.933, 0, 0.967, 0, 0.933, 827.3, 0.967, 827.3]}, {"time": 1, "y": 827.3, "curve": [1.198, 0, 1.702, 0, 1.118, 1557.7, 1.779, 1490.15]}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -6.09]}, {"time": 0.2667, "value": -6.09, "curve": [0.344, -6.09, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -6.09]}, {"time": 1.2667, "value": -6.09, "curve": [1.344, -6.09, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.1, 14.15, 0.167, -7.13, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -7.13, "curve": [0.378, -7.13, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 14.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 14.15, "y": -8.25, "curve": [1.1, 14.15, 1.167, -7.13, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -7.13, "curve": [1.378, -7.13, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 10.62]}, {"time": 0.2667, "value": 10.62, "curve": [0.344, 10.62, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 10.62]}, {"time": 1.2667, "value": 10.62, "curve": [1.344, 10.62, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.1, -13.32, 0.167, 6.04, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 6.04, "curve": [0.378, 6.04, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, -13.32, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": -13.32, "y": -8.25, "curve": [1.1, -13.32, 1.167, 6.04, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 6.04, "curve": [1.378, 6.04, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 5.54]}, {"time": 0.2667, "value": 5.54, "curve": [0.344, 5.54, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 5.54]}, {"time": 1.2667, "value": 5.54, "curve": [1.344, 5.54, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.1, 0, 0.167, 5.49, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 5.49, "curve": [0.378, 5.49, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 0, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "y": -8.25, "curve": [1.1, 0, 1.167, 5.49, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 5.49, "curve": [1.378, 5.49, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -16.85]}, {"time": 0.2667, "value": -16.85, "curve": [0.344, -16.85, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -16.85]}, {"time": 1.2667, "value": -16.85, "curve": [1.344, -16.85, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.1, 9.15, 0.167, -13.72, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -13.72, "curve": [0.245, -13.72, 0.256, -14.12, 0.245, 0, 0.256, 2.53]}, {"time": 0.2667, "x": -13.96, "y": 2.53, "curve": [0.401, -12.1, 0.534, 0, 0.401, 2.53, 0.534, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 9.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 9.15, "y": -8.25, "curve": [1.1, 9.15, 1.167, -13.72, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -13.72, "curve": [1.245, -13.72, 1.256, -14.12, 1.245, 0, 1.256, 2.53]}, {"time": 1.2667, "x": -13.96, "y": 2.53, "curve": [1.401, -12.1, 1.534, 0, 1.401, 2.53, 1.534, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Body_cntr": {"rotate": [{}]}, "Horn": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "Tail": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.9, "x": -0.49, "y": 0.9, "curve": [0.922, -0.49, 0.944, -0.49, 0.922, 0.9, 0.944, -10.7]}, {"time": 0.9667, "x": -0.49, "y": -10.7, "curve": "stepped"}, {"time": 1, "x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 1.9, "x": -0.49, "y": 0.9, "curve": [1.922, -0.49, 1.944, -0.49, 1.922, 0.9, 1.944, -10.7]}, {"time": 1.9667, "x": -0.49, "y": -10.7, "curve": "stepped"}, {"time": 2, "x": -0.49, "y": 0.9}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up4": {"bones": {"Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -6.09]}, {"time": 0.2667, "value": -6.09, "curve": [0.344, -6.09, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -6.09]}, {"time": 1.2667, "value": -6.09, "curve": [1.344, -6.09, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.1, 14.15, 0.167, -7.13, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -7.13, "curve": [0.378, -7.13, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 14.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 14.15, "y": -8.25, "curve": [1.1, 14.15, 1.167, -7.13, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -7.13, "curve": [1.378, -7.13, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 10.62]}, {"time": 0.2667, "value": 10.62, "curve": [0.344, 10.62, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 10.62]}, {"time": 1.2667, "value": 10.62, "curve": [1.344, 10.62, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.1, -13.32, 0.167, 6.04, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 6.04, "curve": [0.378, 6.04, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, -13.32, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": -13.32, "y": -8.25, "curve": [1.1, -13.32, 1.167, 6.04, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 6.04, "curve": [1.378, 6.04, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, 5.54]}, {"time": 0.2667, "value": 5.54, "curve": [0.344, 5.54, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, 5.54]}, {"time": 1.2667, "value": 5.54, "curve": [1.344, 5.54, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.1, 0, 0.167, 5.49, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": 5.49, "curve": [0.378, 5.49, 0.522, 0, 0.378, 0, 0.522, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 0, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "y": -8.25, "curve": [1.1, 0, 1.167, 5.49, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": 5.49, "curve": [1.378, 5.49, 1.522, 0, 1.378, 0, 1.522, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.111, 0, 0.189, -16.85]}, {"time": 0.2667, "value": -16.85, "curve": [0.344, -16.85, 0.422, 0]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": [1.111, 0, 1.189, -16.85]}, {"time": 1.2667, "value": -16.85, "curve": [1.344, -16.85, 1.422, 0]}, {"time": 1.5}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.1, 9.15, 0.167, -13.72, 0.1, -8.25, 0.167, 0]}, {"time": 0.2333, "x": -13.72, "curve": [0.245, -13.72, 0.256, -14.12, 0.245, 0, 0.256, 2.53]}, {"time": 0.2667, "x": -13.96, "y": 2.53, "curve": [0.401, -12.1, 0.534, 0, 0.401, 2.53, 0.534, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 9.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 9.15, "y": -8.25, "curve": [1.1, 9.15, 1.167, -13.72, 1.1, -8.25, 1.167, 0]}, {"time": 1.2333, "x": -13.72, "curve": [1.245, -13.72, 1.256, -14.12, 1.245, 0, 1.256, 2.53]}, {"time": 1.2667, "x": -13.96, "y": 2.53, "curve": [1.401, -12.1, 1.534, 0, 1.401, 2.53, 1.534, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.022, 1, 0.044, 1.226, 0.022, 1, 0.044, 1]}, {"time": 0.0667, "x": 1.226, "curve": [0.133, 1.226, 0.2, 1, 0.133, 1, 0.2, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1, "curve": [1.022, 1, 1.044, 1.226, 1.022, 1, 1.044, 1]}, {"time": 1.0667, "x": 1.226, "curve": [1.133, 1.226, 1.2, 1, 1.133, 1, 1.2, 1]}, {"time": 1.2667}]}, "Horn": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -25.89]}, {"time": 0.9667, "y": -25.89, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -20.85]}, {"time": 1.9667, "y": -20.85, "curve": "stepped"}, {"time": 2}]}, "Tail": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -25.89]}, {"time": 0.9667, "y": -25.89, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -20.85]}, {"time": 1.9667, "y": -20.85, "curve": "stepped"}, {"time": 2}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.9, "x": -0.49, "y": 0.9, "curve": [0.922, -0.49, 0.944, -0.49, 0.922, 0.9, 0.944, -24.99]}, {"time": 0.9667, "x": -0.49, "y": -24.99, "curve": "stepped"}, {"time": 1, "x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 1.9, "x": -0.49, "y": 0.9, "curve": [1.922, -0.49, 1.944, -0.49, 1.922, 0.9, 1.944, -19.95]}, {"time": 1.9667, "x": -0.49, "y": -19.95, "curve": "stepped"}, {"time": 2, "x": -0.49, "y": 0.9}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -25.89]}, {"time": 0.9667, "y": -25.89, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -20.85]}, {"time": 1.9667, "y": -20.85, "curve": "stepped"}, {"time": 2}]}, "Low_cntr": {"translate": [{"curve": [0.2, 0, 0.67, 0, 0.25, 1723.26, 0.772, 1419]}, {"time": 0.9, "y": 827.3, "curve": [0.933, 0, 0.967, 0, 0.933, 827.3, 0.967, 827.3]}, {"time": 1, "y": 827.3, "curve": [1.198, 0, 1.702, 0, 1.118, 1557.7, 1.779, 1490.15]}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"x": 0.8, "y": 1.2, "curve": [0.105, 0.8, 0.109, 1, 0.105, 1.2, 0.109, 1]}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 0.9, "x": 1.2, "y": 0.8, "curve": [0.922, 1.2, 0.944, 1.237, 0.922, 0.8, 0.944, 0.733]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "x": 0.8, "y": 1.2, "curve": [1.105, 0.8, 1.109, 1, 1.105, 1.2, 1.109, 1]}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.8667, "curve": "stepped"}, {"time": 1.9, "x": 1.2, "y": 0.8, "curve": [1.922, 1.2, 1.944, 1.237, 1.922, 0.8, 1.944, 0.733]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "x": 0.8, "y": 1.2}]}, "Body_cntr": {"rotate": [{}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up5": {"bones": {"Low_cntr": {"translate": [{"curve": [0.2, 0, 0.67, 0, 0.25, 1723.26, 0.772, 1419]}, {"time": 0.9, "y": 827.3, "curve": [0.933, 0, 0.967, 0, 0.933, 827.3, 0.967, 827.3]}, {"time": 1, "y": 827.3, "curve": [1.198, 0, 1.702, 0, 1.118, 1557.7, 1.779, 1490.15]}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 14.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 14.15, "y": -8.25, "curve": [1.244, 14.15, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.063, 1, 0.07, 2, 0.063, 1, 0.07, 1]}, {"time": 0.1, "x": 2, "curve": [0.167, 2, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0.833, 0.978, 0.5, 0.922, 1, 0.978, 1]}, {"time": 1, "x": 0.5, "curve": [1.011, 0.5, 1.022, 1, 1.011, 1, 1.022, 1]}, {"time": 1.0333, "curve": [1.063, 1, 1.104, 2, 1.063, 1, 1.104, 1]}, {"time": 1.1333, "x": 2, "curve": [1.2, 2, 1.267, 1, 1.2, 1, 1.267, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 1}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, -13.32, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": -13.32, "y": -8.25, "curve": [1.244, -13.32, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.063, 1, 0.07, 2, 0.063, 1, 0.07, 1]}, {"time": 0.1, "x": 2, "curve": [0.167, 2, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0.833, 0.978, 0.5, 0.922, 1, 0.978, 1]}, {"time": 1, "x": 0.5, "curve": [1.011, 0.5, 1.022, 1, 1.011, 1, 1.022, 1]}, {"time": 1.0333, "curve": [1.063, 1, 1.104, 2, 1.063, 1, 1.104, 1]}, {"time": 1.1333, "x": 2, "curve": [1.2, 2, 1.267, 1, 1.2, 1, 1.267, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 0, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "y": -8.25, "curve": [1.244, 0, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.063, 1, 0.07, 2, 0.063, 1, 0.07, 1]}, {"time": 0.1, "x": 2, "curve": [0.167, 2, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0.833, 0.978, 0.5, 0.922, 1, 0.978, 1]}, {"time": 1, "x": 0.5, "curve": [1.011, 0.5, 1.022, 1, 1.011, 1, 1.022, 1]}, {"time": 1.0333, "curve": [1.063, 1, 1.104, 2, 1.063, 1, 1.104, 1]}, {"time": 1.1333, "x": 2, "curve": [1.2, 2, 1.267, 1, 1.2, 1, 1.267, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.9}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": [1.011, 0, 1.022, 9.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 9.15, "y": -8.25, "curve": [1.244, 9.15, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.063, 1, 0.07, 2, 0.063, 1, 0.07, 1]}, {"time": 0.1, "x": 2, "curve": [0.167, 2, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.933, 0.75, 0.967, 0.5, 0.933, 1, 0.967, 1]}, {"time": 1, "x": 0.5, "curve": [1.011, 0.5, 1.022, 1, 1.011, 1, 1.022, 1]}, {"time": 1.0333, "curve": [1.063, 1, 1.104, 2, 1.063, 1, 1.104, 1]}, {"time": 1.1333, "x": 2, "curve": [1.2, 2, 1.267, 1, 1.2, 1, 1.267, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5}]}, "Body_cntr": {"rotate": [{}], "translate": [{"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -27.32]}, {"time": 0.9667, "y": -27.32, "curve": [1.278, 0, 1.589, 0, 1.278, -27.32, 1.589, 0]}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -27.32]}, {"time": 1.9667, "y": -27.32}]}, "Horn": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "Tail": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.9, "x": -0.49, "y": 0.9, "curve": [0.922, -0.49, 0.944, -0.49, 0.922, 0.9, 0.944, -10.7]}, {"time": 0.9667, "x": -0.49, "y": -10.7, "curve": "stepped"}, {"time": 1, "x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 1.9, "x": -0.49, "y": 0.9, "curve": [1.922, -0.49, 1.944, -0.49, 1.922, 0.9, 1.944, -10.7]}, {"time": 1.9667, "x": -0.49, "y": -10.7, "curve": "stepped"}, {"time": 2, "x": -0.49, "y": 0.9}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -11.6]}, {"time": 0.9667, "y": -11.6, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -11.6]}, {"time": 1.9667, "y": -11.6, "curve": "stepped"}, {"time": 2}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "Examples/Jump_up6": {"slots": {"Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}, {"time": 0.9, "name": "EyeBrow_L"}, {"time": 1, "name": "EyeBrow_L"}, {"time": 1.9, "name": "EyeBrow_L"}, {"time": 2, "name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}, {"time": 0.9, "name": "EyeBrow_R"}, {"time": 1, "name": "EyeBrow_R"}, {"time": 1.9, "name": "EyeBrow_R"}, {"time": 2, "name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}, {"time": 0.9, "name": "eye"}, {"time": 1, "name": "eye"}, {"time": 1.9, "name": "eye"}, {"time": 2, "name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}, {"time": 0.9, "name": "eye2"}, {"time": 1, "name": "eye2"}, {"time": 1.9, "name": "eye2"}, {"time": 2, "name": "eye2"}]}}, "bones": {"Low_cntr": {"translate": [{"curve": [0.2, 0, 0.67, 0, 0.25, 1723.26, 0.772, 1419]}, {"time": 0.9, "y": 827.3, "curve": [0.933, 0, 0.967, 0, 0.933, 827.3, 0.967, 827.3]}, {"time": 1, "y": 827.3, "curve": [1.198, 0, 1.702, 0, 1.118, 1557.7, 1.779, 1490.15]}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"x": 0.8, "y": 1.2, "curve": [0.133, 0.8, 0.267, 1, 0.133, 1.2, 0.267, 1]}, {"time": 0.4, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 0.9, "x": 1.2, "y": 0.8, "curve": [0.985, 1.2, 0.926, 1.087, 0.985, 0.8, 0.926, 0.913]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "x": 0.8, "y": 1.2, "curve": [1.133, 0.8, 1.267, 1, 1.133, 1.2, 1.267, 1]}, {"time": 1.4, "curve": "stepped"}, {"time": 1.8667, "curve": "stepped"}, {"time": 1.9, "x": 1.2, "y": 0.8, "curve": [1.985, 1.2, 1.926, 1.087, 1.985, 0.8, 1.926, 0.913]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "x": 0.8, "y": 1.2}]}, "Leg_F_L": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.12]}, {"time": 0.9667, "x": 0.04, "y": 6.12, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.12, 0.989, 0]}, {"time": 1, "curve": [1.011, 0, 1.022, 14.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 14.15, "y": -8.25, "curve": [1.244, 14.15, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.12]}, {"time": 1.9667, "x": 0.04, "y": 6.12, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.12, 1.989, 0]}, {"time": 2}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 1, 0.944, 0.5, 0.922, 1, 0.944, 1]}, {"time": 0.9667, "x": 0.5, "curve": [0.978, 0.5, 0.989, 1, 0.978, 1, 0.989, 1]}, {"time": 1, "curve": [1.03, 1, 1.07, 1.1, 1.03, 1, 1.07, 1]}, {"time": 1.1, "x": 1.1, "curve": [1.167, 1.1, 1.233, 1, 1.167, 1, 1.233, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5, "curve": [1.978, 0.5, 1.989, 1, 1.978, 1, 1.989, 1]}, {"time": 2}]}, "Leg_B_L": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.12]}, {"time": 0.9667, "x": 0.04, "y": 6.12, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.12, 0.989, 0]}, {"time": 1, "curve": [1.011, 0, 1.022, -13.32, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": -13.32, "y": -8.25, "curve": [1.244, -13.32, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.12]}, {"time": 1.9667, "x": 0.04, "y": 6.12, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.12, 1.989, 0]}, {"time": 2}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 1, 0.944, 0.5, 0.922, 1, 0.944, 1]}, {"time": 0.9667, "x": 0.5, "curve": [0.978, 0.5, 0.989, 1, 0.978, 1, 0.989, 1]}, {"time": 1, "curve": [1.03, 1, 1.07, 1.1, 1.03, 1, 1.07, 1]}, {"time": 1.1, "x": 1.1, "curve": [1.167, 1.1, 1.233, 1, 1.167, 1, 1.233, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5, "curve": [1.978, 0.5, 1.989, 1, 1.978, 1, 1.989, 1]}, {"time": 2}]}, "Leg_B_R": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.12]}, {"time": 0.9667, "x": 0.04, "y": 6.12, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.12, 0.989, 0]}, {"time": 1, "curve": [1.011, 0, 1.022, 0, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "y": -8.25, "curve": [1.244, 0, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.12]}, {"time": 1.9667, "x": 0.04, "y": 6.12, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.12, 1.989, 0]}, {"time": 2}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 1, 0.944, 0.5, 0.922, 1, 0.944, 1]}, {"time": 0.9667, "x": 0.5, "curve": [0.978, 0.5, 0.989, 1, 0.978, 1, 0.989, 1]}, {"time": 1, "curve": [1.03, 1, 1.07, 1.1, 1.03, 1, 1.07, 1]}, {"time": 1.1, "x": 1.1, "curve": [1.167, 1.1, 1.233, 1, 1.167, 1, 1.233, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5, "curve": [1.978, 0.5, 1.989, 1, 1.978, 1, 1.989, 1]}, {"time": 2}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.12]}, {"time": 0.9667, "x": 0.04, "y": 6.12, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.12, 0.989, 0]}, {"time": 1, "curve": [1.011, 0, 1.022, 9.15, 1.011, 0, 1.022, -8.25]}, {"time": 1.0333, "x": 9.15, "y": -8.25, "curve": [1.244, 9.15, 1.456, 0, 1.244, -8.25, 1.456, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.12]}, {"time": 1.9667, "x": 0.04, "y": 6.12, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.12, 1.989, 0]}, {"time": 2}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 1, 0.944, 0.5, 0.922, 1, 0.944, 1]}, {"time": 0.9667, "x": 0.5, "curve": [0.978, 0.5, 0.989, 1, 0.978, 1, 0.989, 1]}, {"time": 1, "curve": [1.03, 1, 1.07, 1.1, 1.03, 1, 1.07, 1]}, {"time": 1.1, "x": 1.1, "curve": [1.167, 1.1, 1.233, 1, 1.167, 1, 1.233, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 1, 1.944, 0.5, 1.922, 1, 1.944, 1]}, {"time": 1.9667, "x": 0.5, "curve": [1.978, 0.5, 1.989, 1, 1.978, 1, 1.989, 1]}, {"time": 2}]}, "Jetpack": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Jetpack2": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Rainbow": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Body_parts": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.07]}, {"time": 0.9667, "x": 0.04, "y": 6.07, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.07, 0.989, 0]}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.07]}, {"time": 1.9667, "x": 0.04, "y": 6.07, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.07, 1.989, 0]}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Horn": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0.04, 0.922, 0, 0.944, 6.07]}, {"time": 0.9667, "x": 0.04, "y": 6.07, "curve": [0.978, 0.04, 0.989, 0, 0.978, 6.07, 0.989, 0]}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0.04, 1.922, 0, 1.944, 6.07]}, {"time": 1.9667, "x": 0.04, "y": 6.07, "curve": [1.978, 0.04, 1.989, 0, 1.978, 6.07, 1.989, 0]}, {"time": 2}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 0.9, "x": -0.49, "y": 0.9, "curve": [0.922, -0.49, 0.944, -0.46, 0.922, 0.9, 0.944, 6.97]}, {"time": 0.9667, "x": -0.46, "y": 6.97, "curve": [0.978, -0.46, 0.989, -0.49, 0.978, 6.97, 0.989, 0.9]}, {"time": 1, "x": -0.49, "y": 0.9, "curve": "stepped"}, {"time": 1.9, "x": -0.49, "y": 0.9, "curve": [1.922, -0.49, 1.944, -0.46, 1.922, 0.9, 1.944, 6.97]}, {"time": 1.9667, "x": -0.46, "y": 6.97, "curve": [1.978, -0.46, 1.989, -0.49, 1.978, 6.97, 1.989, 0.9]}, {"time": 2, "x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "eye_black2": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "eye_black": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Body_cntr": {"rotate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 0, 0.922, 0, 0.944, -27.32]}, {"time": 0.9667, "y": -27.32, "curve": [0.978, 0, 0.989, 0, 0.978, -27.32, 0.989, 0]}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 0, 1.922, 0, 1.944, -27.32]}, {"time": 1.9667, "y": -27.32, "curve": [1.978, 0, 1.989, 0, 1.978, -27.32, 1.989, 0]}, {"time": 2}]}, "star1": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "star2": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "Fire_scale": {"scale": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "EyeBrow_L": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "EyeBrow_R": {"translate": [{"curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 11.79, 0.922, 0, 0.944, 0]}, {"time": 0.9667, "x": 11.79, "curve": [0.978, 11.79, 0.989, 0.76, 0.978, 0, 0.989, 0]}, {"time": 1, "curve": [1.033, -2.27, 1.067, -1.04, 1.033, 0, 1.067, 0]}, {"time": 1.1, "x": -1.04, "curve": [1.167, -1.04, 1.233, -2.27, 1.167, 0, 1.233, 0]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 11.79, 1.922, 0, 1.944, 0]}, {"time": 1.9667, "x": 11.79, "curve": [1.978, 11.79, 1.989, 0.76, 1.978, 0, 1.989, 0]}, {"time": 2}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 11.79, 0.922, 0, 0.944, 0]}, {"time": 0.9667, "x": 11.79, "curve": [0.978, 11.79, 0.989, 0.76, 0.978, 0, 0.989, 0]}, {"time": 1, "curve": [1.033, -2.27, 1.067, -1.04, 1.033, 0, 1.067, 0]}, {"time": 1.1, "x": -1.04, "curve": [1.167, -1.04, 1.233, -2.27, 1.167, 0, 1.233, 0]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 11.79, 1.922, 0, 1.944, 0]}, {"time": 1.9667, "x": 11.79, "curve": [1.978, 11.79, 1.989, 0.76, 1.978, 0, 1.989, 0]}, {"time": 2}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 11.79, 0.922, 0, 0.944, 0]}, {"time": 0.9667, "x": 11.79, "curve": [0.978, 11.79, 0.989, 0.76, 0.978, 0, 0.989, 0]}, {"time": 1, "curve": [1.033, -2.27, 1.067, -1.04, 1.033, 0, 1.067, 0]}, {"time": 1.1, "x": -1.04, "curve": [1.167, -1.04, 1.233, -2.27, 1.167, 0, 1.233, 0]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 11.79, 1.922, 0, 1.944, 0]}, {"time": 1.9667, "x": 11.79, "curve": [1.978, 11.79, 1.989, 0.76, 1.978, 0, 1.989, 0]}, {"time": 2}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.9, "curve": [0.922, 0, 0.944, 11.79, 0.922, 0, 0.944, 0]}, {"time": 0.9667, "x": 11.79, "curve": [0.978, 11.79, 0.989, 0.76, 0.978, 0, 0.989, 0]}, {"time": 1, "curve": [1.033, -2.27, 1.067, -1.04, 1.033, 0, 1.067, 0]}, {"time": 1.1, "x": -1.04, "curve": [1.167, -1.04, 1.233, -2.27, 1.167, 0, 1.233, 0]}, {"time": 1.3, "curve": "stepped"}, {"time": 1.9, "curve": [1.922, 0, 1.944, 11.79, 1.922, 0, 1.944, 0]}, {"time": 1.9667, "x": 11.79, "curve": [1.978, 11.79, 1.989, 0.76, 1.978, 0, 1.989, 0]}, {"time": 2}]}}}, "Old/Jump_jet_start4s": {"slots": {"Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Low_cntr": {"translate": [{"curve": [0.044, 0, 0.089, -4.15, 0.044, 0, 0.089, 3.11]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.174, 0.8, 0.223, 0.67]}, {"time": 0.2667, "x": 8.12, "y": 2.54, "curve": [0.311, 8.12, 0.356, -4.15, 0.312, 4.47, 0.356, 4.18]}, {"time": 0.4, "x": -4.15, "y": 1.9, "curve": [0.444, -4.15, 0.489, 8.12, 0.442, -0.31, 0.489, 0.61]}, {"time": 0.5333, "x": 8.12, "y": 2.54, "curve": [0.578, 8.12, 0.622, -4.15, 0.584, 4.75, 0.623, 4.03]}, {"time": 0.6667, "x": -4.15, "y": 1.9, "curve": [0.711, -4.15, 0.756, 8.12, 0.712, -0.31, 0.756, 0.27]}, {"time": 0.8, "x": 8.12, "y": 2.54, "curve": [0.844, 8.12, 0.889, -4.15, 0.853, 5.3, 0.889, 3.81]}, {"time": 0.9333, "x": -4.15, "y": 1.9, "curve": [0.978, -4.15, 1.022, 8.12, 0.978, -0.03, 1.023, 1.03]}, {"time": 1.0667, "x": 8.12, "y": 2.54, "curve": [1.111, 8.12, 1.156, -4.15, 1.107, 3.92, 1.156, 3.11]}, {"time": 1.2, "x": -4.15, "y": 1.9, "curve": [1.244, -4.15, 1.289, 8.12, 1.241, 0.8, 1.289, 0.67]}, {"time": 1.3333, "x": 8.12, "y": 2.54, "curve": [1.378, 8.12, 1.422, -4.15, 1.379, 4.47, 1.423, 4.18]}, {"time": 1.4667, "x": -4.15, "y": 1.9, "curve": [1.511, -4.15, 1.556, 8.12, 1.509, -0.31, 1.556, 0.61]}, {"time": 1.6, "x": 8.12, "y": 2.54, "curve": [1.644, 8.12, 1.689, -4.15, 1.65, 4.75, 1.69, 4.03]}, {"time": 1.7333, "x": -4.15, "y": 1.9, "curve": [1.778, -4.15, 1.822, 8.12, 1.779, -0.31, 1.823, 0.27]}, {"time": 1.8667, "x": 8.12, "y": 2.54, "curve": [1.911, 8.12, 1.956, -4.15, 1.92, 5.3, 1.956, 3.81]}, {"time": 2, "x": -4.15, "y": 1.9, "curve": [2.044, -4.15, 2.089, 8.12, 2.044, -0.03, 2.089, 1.03]}, {"time": 2.1333, "x": 8.12, "y": 2.54, "curve": [2.178, 8.12, 2.222, -4.15, 2.174, 3.92, 2.222, 3.11]}, {"time": 2.2667, "x": -4.15, "y": 1.9, "curve": [2.311, -4.15, 2.356, 8.12, 2.307, 0.8, 2.356, 0.67]}, {"time": 2.4, "x": 8.12, "y": 2.54, "curve": [2.444, 8.12, 2.489, -4.15, 2.445, 4.47, 2.49, 4.18]}, {"time": 2.5333, "x": -4.15, "y": 1.9, "curve": [2.578, -4.15, 2.622, 8.12, 2.576, -0.31, 2.623, 0.61]}, {"time": 2.6667, "x": 8.12, "y": 2.54, "curve": [2.711, 8.12, 2.756, -4.15, 2.717, 4.75, 2.756, 4.03]}, {"time": 2.8, "x": -4.15, "y": 1.9, "curve": [2.844, -4.15, 2.889, 8.12, 2.845, -0.31, 2.89, 0.27]}, {"time": 2.9333, "x": 8.12, "y": 2.54, "curve": [2.978, 8.12, 3.022, -4.15, 2.987, 5.3, 3.023, 3.81]}, {"time": 3.0667, "x": -4.15, "y": 1.9, "curve": [3.111, -4.15, 3.156, 8.12, 3.111, -0.03, 3.156, 1.03]}, {"time": 3.2, "x": 8.12, "y": 2.54, "curve": [3.244, 8.12, 3.289, -4.15, 3.241, 3.92, 3.289, 1.9]}, {"time": 3.3333, "x": -4.15, "y": 1.9, "curve": [3.707, -4.15, 2.32, 0, 3.707, 1.9, 2.32, 0]}, {"time": 4}], "scale": [{"x": 0.8, "y": 1.2, "curve": [0.133, 0.8, 0.267, 1, 0.133, 1.2, 0.267, 1]}, {"time": 0.4}]}, "Jetpack": {"rotate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, -32.42]}, {"time": 4, "value": -32.42}], "translate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, 249.96, 3.756, 0, 3.936, -302.94]}, {"time": 4, "x": 249.96, "y": -1057.38}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.4667, "x": 100, "y": 100, "curve": [0.5, 100, 0.533, 110, 0.5, 100, 0.533, 110]}, {"time": 0.5667, "x": 110, "y": 110, "curve": [0.6, 110, 0.644, 100, 0.6, 110, 0.644, 100]}, {"time": 0.6667, "x": 100, "y": 100, "curve": [0.7, 100, 0.733, 110, 0.7, 100, 0.733, 110]}, {"time": 0.7667, "x": 110, "y": 110, "curve": [0.8, 110, 0.844, 100, 0.8, 110, 0.844, 100]}, {"time": 0.8667, "x": 100, "y": 100, "curve": [0.9, 100, 0.933, 110, 0.9, 100, 0.933, 110]}, {"time": 0.9667, "x": 110, "y": 110, "curve": [1, 110, 1.044, 100, 1, 110, 1.044, 100]}, {"time": 1.0667, "x": 100, "y": 100, "curve": [1.1, 100, 1.133, 110, 1.1, 100, 1.133, 110]}, {"time": 1.1667, "x": 110, "y": 110, "curve": [1.2, 110, 1.244, 100, 1.2, 110, 1.244, 100]}, {"time": 1.2667, "x": 100, "y": 100, "curve": [1.3, 100, 1.333, 110, 1.3, 100, 1.333, 110]}, {"time": 1.3667, "x": 110, "y": 110, "curve": [1.4, 110, 1.444, 100, 1.4, 110, 1.444, 100]}, {"time": 1.4667, "x": 100, "y": 100, "curve": [1.5, 100, 1.533, 110, 1.5, 100, 1.533, 110]}, {"time": 1.5667, "x": 110, "y": 110, "curve": [1.6, 110, 1.644, 100, 1.6, 110, 1.644, 100]}, {"time": 1.6667, "x": 100, "y": 100, "curve": [1.7, 100, 1.733, 110, 1.7, 100, 1.733, 110]}, {"time": 1.7667, "x": 110, "y": 110, "curve": [1.8, 110, 1.844, 100, 1.8, 110, 1.844, 100]}, {"time": 1.8667, "x": 100, "y": 100, "curve": [1.9, 100, 1.933, 110, 1.9, 100, 1.933, 110]}, {"time": 1.9667, "x": 110, "y": 110, "curve": [2, 110, 2.044, 100, 2, 110, 2.044, 100]}, {"time": 2.0667, "x": 100, "y": 100, "curve": [2.1, 100, 2.133, 110, 2.1, 100, 2.133, 110]}, {"time": 2.1667, "x": 110, "y": 110, "curve": [2.2, 110, 2.244, 100, 2.2, 110, 2.244, 100]}, {"time": 2.2667, "x": 100, "y": 100, "curve": [2.3, 100, 2.333, 110, 2.3, 100, 2.333, 110]}, {"time": 2.3667, "x": 110, "y": 110, "curve": [2.4, 110, 2.444, 100, 2.4, 110, 2.444, 100]}, {"time": 2.4667, "x": 100, "y": 100, "curve": [2.5, 100, 2.533, 110, 2.5, 100, 2.533, 110]}, {"time": 2.5667, "x": 110, "y": 110, "curve": [2.6, 110, 2.644, 100, 2.6, 110, 2.644, 100]}, {"time": 2.6667, "x": 100, "y": 100, "curve": [2.7, 100, 2.733, 110, 2.7, 100, 2.733, 110]}, {"time": 2.7667, "x": 110, "y": 110, "curve": [2.779, 110, 2.986, 100, 2.779, 110, 2.986, 100]}, {"time": 3, "x": 100, "y": 100}]}, "Jetpack2": {"rotate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, -31.2]}, {"time": 4, "value": -31.2}], "translate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.893, 260.96, 3.72, 0, 3.947, -199.44]}, {"time": 4, "x": 268.33, "y": -1059.99}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.4667, "x": 100, "y": 100, "curve": [0.5, 100, 0.533, 110, 0.5, 100, 0.533, 110]}, {"time": 0.5667, "x": 110, "y": 110, "curve": [0.6, 110, 0.644, 100, 0.6, 110, 0.644, 100]}, {"time": 0.6667, "x": 100, "y": 100, "curve": [0.7, 100, 0.733, 110, 0.7, 100, 0.733, 110]}, {"time": 0.7667, "x": 110, "y": 110, "curve": [0.8, 110, 0.844, 100, 0.8, 110, 0.844, 100]}, {"time": 0.8667, "x": 100, "y": 100, "curve": [0.9, 100, 0.933, 110, 0.9, 100, 0.933, 110]}, {"time": 0.9667, "x": 110, "y": 110, "curve": [1, 110, 1.044, 100, 1, 110, 1.044, 100]}, {"time": 1.0667, "x": 100, "y": 100, "curve": [1.1, 100, 1.133, 110, 1.1, 100, 1.133, 110]}, {"time": 1.1667, "x": 110, "y": 110, "curve": [1.2, 110, 1.244, 100, 1.2, 110, 1.244, 100]}, {"time": 1.2667, "x": 100, "y": 100, "curve": [1.3, 100, 1.333, 110, 1.3, 100, 1.333, 110]}, {"time": 1.3667, "x": 110, "y": 110, "curve": [1.4, 110, 1.444, 100, 1.4, 110, 1.444, 100]}, {"time": 1.4667, "x": 100, "y": 100, "curve": [1.5, 100, 1.533, 110, 1.5, 100, 1.533, 110]}, {"time": 1.5667, "x": 110, "y": 110, "curve": [1.6, 110, 1.644, 100, 1.6, 110, 1.644, 100]}, {"time": 1.6667, "x": 100, "y": 100, "curve": [1.7, 100, 1.733, 110, 1.7, 100, 1.733, 110]}, {"time": 1.7667, "x": 110, "y": 110, "curve": [1.8, 110, 1.844, 100, 1.8, 110, 1.844, 100]}, {"time": 1.8667, "x": 100, "y": 100, "curve": [1.9, 100, 1.933, 110, 1.9, 100, 1.933, 110]}, {"time": 1.9667, "x": 110, "y": 110, "curve": [2, 110, 2.044, 100, 2, 110, 2.044, 100]}, {"time": 2.0667, "x": 100, "y": 100, "curve": [2.1, 100, 2.133, 110, 2.1, 100, 2.133, 110]}, {"time": 2.1667, "x": 110, "y": 110, "curve": [2.2, 110, 2.244, 100, 2.2, 110, 2.244, 100]}, {"time": 2.2667, "x": 100, "y": 100, "curve": [2.3, 100, 2.333, 110, 2.3, 100, 2.333, 110]}, {"time": 2.3667, "x": 110, "y": 110, "curve": [2.4, 110, 2.444, 100, 2.4, 110, 2.444, 100]}, {"time": 2.4667, "x": 100, "y": 100, "curve": [2.5, 100, 2.533, 110, 2.5, 100, 2.533, 110]}, {"time": 2.5667, "x": 110, "y": 110, "curve": [2.6, 110, 2.644, 100, 2.6, 110, 2.644, 100]}, {"time": 2.6667, "x": 100, "y": 100, "curve": [2.7, 100, 2.733, 110, 2.7, 100, 2.733, 110]}, {"time": 2.7667, "x": 110, "y": 110, "curve": [2.779, 110, 2.986, 100, 2.779, 110, 2.986, 100]}, {"time": 3, "x": 100, "y": 100}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8, "curve": [0.022, 1.2, 0.044, 0.8, 0.022, 0.8, 0.044, 1.2]}, {"time": 0.0667, "x": 0.8, "y": 1.2, "curve": [0.089, 0.8, 0.111, 1.2, 0.089, 1.2, 0.111, 0.8]}, {"time": 0.1333, "x": 1.2, "y": 0.8, "curve": [0.156, 1.2, 0.178, 0.8, 0.156, 0.8, 0.178, 1.2]}, {"time": 0.2, "x": 0.8, "y": 1.2, "curve": [0.222, 0.8, 0.244, 1.2, 0.222, 1.2, 0.244, 0.8]}, {"time": 0.2667, "x": 1.2, "y": 0.8, "curve": [0.289, 1.2, 0.311, 0.8, 0.289, 0.8, 0.311, 1.2]}, {"time": 0.3333, "x": 0.8, "y": 1.2, "curve": [0.356, 0.8, 0.378, 1.2, 0.356, 1.2, 0.378, 0.8]}, {"time": 0.4, "x": 1.2, "y": 0.8, "curve": [0.422, 1.2, 0.444, 0.8, 0.422, 0.8, 0.444, 1.2]}, {"time": 0.4667, "x": 0.8, "y": 1.2, "curve": [0.489, 0.8, 0.511, 1.2, 0.489, 1.2, 0.511, 0.8]}, {"time": 0.5333, "x": 1.2, "y": 0.8, "curve": [0.556, 1.2, 0.578, 0.8, 0.556, 0.8, 0.578, 1.2]}, {"time": 0.6, "x": 0.8, "y": 1.2, "curve": [0.622, 0.8, 0.644, 1.2, 0.622, 1.2, 0.644, 0.8]}, {"time": 0.6667, "x": 1.2, "y": 0.8, "curve": [0.689, 1.2, 0.711, 0.8, 0.689, 0.8, 0.711, 1.2]}, {"time": 0.7333, "x": 0.8, "y": 1.2, "curve": [0.756, 0.8, 0.778, 1.2, 0.756, 1.2, 0.778, 0.8]}, {"time": 0.8, "x": 1.2, "y": 0.8, "curve": [0.822, 1.2, 0.844, 0.8, 0.822, 0.8, 0.844, 1.2]}, {"time": 0.8667, "x": 0.8, "y": 1.2, "curve": [0.889, 0.8, 0.911, 1.2, 0.889, 1.2, 0.911, 0.8]}, {"time": 0.9333, "x": 1.2, "y": 0.8, "curve": [0.956, 1.2, 0.978, 0.8, 0.956, 0.8, 0.978, 1.2]}, {"time": 1, "x": 0.8, "y": 1.2, "curve": [1.022, 0.8, 1.044, 1.2, 1.022, 1.2, 1.044, 0.8]}, {"time": 1.0667, "x": 1.2, "y": 0.8, "curve": [1.089, 1.2, 1.111, 0.8, 1.089, 0.8, 1.111, 1.2]}, {"time": 1.1333, "x": 0.8, "y": 1.2, "curve": [1.156, 0.8, 1.178, 1.2, 1.156, 1.2, 1.178, 0.8]}, {"time": 1.2, "x": 1.2, "y": 0.8, "curve": [1.222, 1.2, 1.244, 0.8, 1.222, 0.8, 1.244, 1.2]}, {"time": 1.2667, "x": 0.8, "y": 1.2, "curve": [1.289, 0.8, 1.311, 1.2, 1.289, 1.2, 1.311, 0.8]}, {"time": 1.3333, "x": 1.2, "y": 0.8, "curve": [1.356, 1.2, 1.378, 0.694, 1.356, 0.8, 1.378, 1.094]}, {"time": 1.4, "x": 0.694, "y": 1.094, "curve": [1.507, 0.694, 1.444, 0.8, 1.507, 1.094, 1.444, 1.2]}, {"time": 1.4667, "x": 0.8, "y": 1.2, "curve": [1.489, 0.8, 1.511, 1.2, 1.489, 1.2, 1.511, 0.8]}, {"time": 1.5333, "x": 1.2, "y": 0.8, "curve": [1.556, 1.2, 1.578, 0.8, 1.556, 0.8, 1.578, 1.2]}, {"time": 1.6, "x": 0.8, "y": 1.2, "curve": [1.622, 0.8, 1.644, 1.2, 1.622, 1.2, 1.644, 0.8]}, {"time": 1.6667, "x": 1.2, "y": 0.8, "curve": [1.689, 1.2, 1.711, 0.8, 1.689, 0.8, 1.711, 1.2]}, {"time": 1.7333, "x": 0.8, "y": 1.2, "curve": [1.756, 0.8, 1.778, 1.2, 1.756, 1.2, 1.778, 0.8]}, {"time": 1.8, "x": 1.2, "y": 0.8, "curve": [1.822, 1.2, 1.844, 0.8, 1.822, 0.8, 1.844, 1.2]}, {"time": 1.8667, "x": 0.8, "y": 1.2, "curve": [1.889, 0.8, 1.911, 1.2, 1.889, 1.2, 1.911, 0.8]}, {"time": 1.9333, "x": 1.2, "y": 0.8, "curve": [1.956, 1.2, 1.978, 0.8, 1.956, 0.8, 1.978, 1.2]}, {"time": 2, "x": 0.8, "y": 1.2, "curve": [2.022, 0.8, 2.044, 1.2, 2.022, 1.2, 2.044, 0.8]}, {"time": 2.0667, "x": 1.2, "y": 0.8, "curve": [2.089, 1.2, 2.111, 0.8, 2.089, 0.8, 2.111, 1.2]}, {"time": 2.1333, "x": 0.8, "y": 1.2, "curve": [2.156, 0.8, 2.178, 1.2, 2.156, 1.2, 2.178, 0.8]}, {"time": 2.2, "x": 1.2, "y": 0.8, "curve": [2.222, 1.2, 2.244, 0.8, 2.222, 0.8, 2.244, 1.2]}, {"time": 2.2667, "x": 0.8, "y": 1.2, "curve": [2.289, 0.8, 2.311, 1.2, 2.289, 1.2, 2.311, 0.8]}, {"time": 2.3333, "x": 1.2, "y": 0.8, "curve": [2.356, 1.2, 2.378, 0.8, 2.356, 0.8, 2.378, 1.2]}, {"time": 2.4, "x": 0.8, "y": 1.2, "curve": [2.422, 0.8, 2.444, 1.2, 2.422, 1.2, 2.444, 0.8]}, {"time": 2.4667, "x": 1.2, "y": 0.8, "curve": [2.489, 1.2, 2.511, 0.8, 2.489, 0.8, 2.511, 1.2]}, {"time": 2.5333, "x": 0.8, "y": 1.2, "curve": [2.556, 0.8, 2.578, 1.2, 2.556, 1.2, 2.578, 0.8]}, {"time": 2.6, "x": 1.2, "y": 0.8, "curve": [2.622, 1.2, 2.644, 0.8, 2.622, 0.8, 2.644, 1.2]}, {"time": 2.6667, "x": 0.8, "y": 1.2, "curve": [2.689, 0.8, 2.711, 1.2, 2.689, 1.2, 2.711, 0.8]}, {"time": 2.7333, "x": 1.2, "y": 0.8, "curve": [2.756, 1.2, 2.778, 0.694, 2.756, 0.8, 2.778, 1.094]}, {"time": 2.8, "x": 0.694, "y": 1.094, "curve": [3.236, 0.694, 2.844, 0.8, 3.236, 1.094, 2.844, 1.2]}, {"time": 2.8667, "x": 0.8, "y": 1.2, "curve": [2.889, 0.8, 2.911, 1.2, 2.889, 1.2, 2.911, 0.8]}, {"time": 2.9333, "x": 1.2, "y": 0.8, "curve": [2.956, 1.2, 2.978, 0.8, 2.956, 0.8, 2.978, 1.2]}, {"time": 3, "x": 0.8, "y": 1.2, "curve": [3.022, 0.8, 3.044, 1.2, 3.022, 1.2, 3.044, 0.8]}, {"time": 3.0667, "x": 1.2, "y": 0.8, "curve": [3.089, 1.2, 3.111, 0.8, 3.089, 0.8, 3.111, 1.2]}, {"time": 3.1333, "x": 0.8, "y": 1.2, "curve": [3.156, 0.8, 3.178, 1.2, 3.156, 1.2, 3.178, 0.8]}, {"time": 3.2, "x": 1.2, "y": 0.8, "curve": [3.201, 1.2, 3.25, -0.337, 3.201, 0.8, 3.25, 0.063]}, {"time": 3.2667, "x": -0.337, "y": 0.063}], "shear": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -23.32]}, {"time": 0.0333, "y": -19.04, "curve": [0.055, 0, 0.078, 0, 0.055, -10.56, 0.078, 6.41]}, {"time": 0.1, "y": 6.41, "curve": [0.122, 0, 0.145, 0, 0.122, 6.47, 0.145, -19.04]}, {"time": 0.1667, "y": -19.04, "curve": [0.189, 0, 0.211, 0, 0.189, -18.86, 0.211, 6.41]}, {"time": 0.2333, "y": 6.41, "curve": [0.255, 0, 0.278, 0, 0.255, 6.47, 0.278, -19.04]}, {"time": 0.3, "y": -19.04, "curve": [0.322, 0, 0.345, 0, 0.322, -18.86, 0.345, 6.41]}, {"time": 0.3667, "y": 6.41, "curve": [0.389, 0, 0.411, 0, 0.389, 6.47, 0.411, -19.04]}, {"time": 0.4333, "y": -19.04, "curve": [0.455, 0, 0.478, 0, 0.455, -18.86, 0.478, 6.41]}, {"time": 0.5, "y": 6.41, "curve": [0.522, 0, 0.545, 0, 0.522, 6.47, 0.545, -19.04]}, {"time": 0.5667, "y": -19.04, "curve": [0.589, 0, 0.611, 0, 0.589, -18.86, 0.611, 6.41]}, {"time": 0.6333, "y": 6.41, "curve": [0.655, 0, 0.678, 0, 0.655, 6.47, 0.678, -19.04]}, {"time": 0.7, "y": -19.04, "curve": [0.722, 0, 0.745, 0, 0.722, -18.86, 0.745, 6.41]}, {"time": 0.7667, "y": 6.41, "curve": [0.789, 0, 0.811, 0, 0.789, 6.47, 0.811, -19.04]}, {"time": 0.8333, "y": -19.04, "curve": [0.855, 0, 0.878, 0, 0.855, -18.86, 0.878, 6.41]}, {"time": 0.9, "y": 6.41, "curve": [0.922, 0, 0.945, 0, 0.922, 6.47, 0.945, -19.04]}, {"time": 0.9667, "y": -19.04, "curve": [0.989, 0, 1.011, 0, 0.989, -18.86, 1.011, 6.41]}, {"time": 1.0333, "y": 6.41, "curve": [1.055, 0, 1.078, 0, 1.055, 6.47, 1.078, -19.04]}, {"time": 1.1, "y": -19.04, "curve": [1.122, 0, 1.145, 0, 1.122, -18.86, 1.145, 6.41]}, {"time": 1.1667, "y": 6.41, "curve": [1.189, 0, 1.211, 0, 1.189, 6.47, 1.211, -19.04]}, {"time": 1.2333, "y": -19.04, "curve": [1.255, 0, 1.278, 0, 1.255, -18.86, 1.278, 6.41]}, {"time": 1.3, "y": 6.41, "curve": [1.322, 0, 1.345, 0, 1.322, 6.47, 1.345, -19.04]}, {"time": 1.3667, "y": -19.04, "curve": [1.389, 0, 1.411, 0, 1.389, -18.86, 1.411, 6.41]}, {"time": 1.4333, "y": 6.41, "curve": [1.438, 0, 1.496, 0, 1.438, 6.42, 1.496, -17.34]}, {"time": 1.5, "y": -19.04, "curve": [1.522, 0, 1.545, 0, 1.522, -27.6, 1.545, 6.41]}, {"time": 1.5667, "y": 6.41, "curve": [1.589, 0, 1.611, 0, 1.589, 6.47, 1.611, -19.04]}, {"time": 1.6333, "y": -19.04, "curve": [1.655, 0, 1.678, 0, 1.655, -18.86, 1.678, 6.41]}, {"time": 1.7, "y": 6.41, "curve": [1.722, 0, 1.745, 0, 1.722, 6.47, 1.745, -19.04]}, {"time": 1.7667, "y": -19.04, "curve": [1.789, 0, 1.811, 0, 1.789, -18.86, 1.811, 6.41]}, {"time": 1.8333, "y": 6.41, "curve": [1.855, 0, 1.878, 0, 1.855, 6.47, 1.878, -19.04]}, {"time": 1.9, "y": -19.04, "curve": [1.922, 0, 1.945, 0, 1.922, -18.86, 1.945, 6.41]}, {"time": 1.9667, "y": 6.41, "curve": [1.989, 0, 2.011, 0, 1.989, 6.47, 2.011, -19.04]}, {"time": 2.0333, "y": -19.04, "curve": [2.055, 0, 2.078, 0, 2.055, -18.86, 2.078, 6.41]}, {"time": 2.1, "y": 6.41, "curve": [2.122, 0, 2.145, 0, 2.122, 6.47, 2.145, -19.04]}, {"time": 2.1667, "y": -19.04, "curve": [2.189, 0, 2.211, 0, 2.189, -18.86, 2.211, 6.41]}, {"time": 2.2333, "y": 6.41, "curve": [2.255, 0, 2.278, 0, 2.255, 6.47, 2.278, -19.04]}, {"time": 2.3, "y": -19.04, "curve": [2.322, 0, 2.345, 0, 2.322, -18.86, 2.345, 6.41]}, {"time": 2.3667, "y": 6.41, "curve": [2.389, 0, 2.411, 0, 2.389, 6.47, 2.411, -19.04]}, {"time": 2.4333, "y": -19.04, "curve": [2.455, 0, 2.478, 0, 2.455, -18.86, 2.478, 6.41]}, {"time": 2.5, "y": 6.41, "curve": [2.522, 0, 2.545, 0, 2.522, 6.47, 2.545, -19.04]}, {"time": 2.5667, "y": -19.04, "curve": [2.589, 0, 2.611, 0, 2.589, -18.86, 2.611, 6.41]}, {"time": 2.6333, "y": 6.41, "curve": [2.655, 0, 2.678, 0, 2.655, 6.47, 2.678, -19.04]}, {"time": 2.7, "y": -19.04, "curve": [2.722, 0, 2.745, 0, 2.722, -18.86, 2.745, 6.41]}, {"time": 2.7667, "y": 6.41, "curve": [2.789, 0, 2.811, 0, 2.789, 6.47, 2.811, -19.04]}, {"time": 2.8333, "y": -19.04, "curve": [2.855, 0, 2.878, 0, 2.855, -18.86, 2.878, 6.41]}, {"time": 2.9, "y": 6.41, "curve": [2.904, 0, 2.962, 0, 2.904, 6.42, 2.962, -17.34]}, {"time": 2.9667, "y": -19.04, "curve": [2.989, 0, 3.011, 0, 2.989, -27.6, 3.011, 6.41]}, {"time": 3.0333, "y": 6.41, "curve": [3.055, 0, 3.078, 0, 3.055, 6.47, 3.078, -19.04]}, {"time": 3.1, "y": -19.04, "curve": [3.122, 0, 3.145, 0, 3.122, -18.86, 3.145, 6.41]}, {"time": 3.1667, "y": 6.41, "curve": [3.189, 0, 3.211, 0, 3.189, 6.47, 3.211, -19.04]}, {"time": 3.2333, "y": -19.04, "curve": [3.255, 0, 3.278, 0, 3.255, -18.86, 3.278, 6.41]}, {"time": 3.3, "y": 6.41}]}, "Rainbow": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.009, 20.31, 0.044, 52.14]}, {"time": 0.0667, "y": 52.14, "curve": [0.267, 0, 0.467, 0, 0.267, 52.14, 0.467, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 100]}, {"time": 0.1, "y": 100}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25, "curve": "stepped"}, {"time": 3.6667, "value": -121.25, "curve": [3.715, -63.42, 3.889, 0]}, {"time": 4}], "translate": [{}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70, "curve": "stepped"}, {"time": 0.5, "x": 70, "y": 70, "curve": [0.6, 70, 0.7, 300, 0.6, 70, 0.7, 100.99]}, {"time": 0.8, "x": 300, "y": 100.99, "curve": "stepped"}, {"time": 2.3333, "x": 300, "y": 100.99, "curve": [2.489, 300, 2.644, 70, 2.489, 100.99, 2.644, 70]}, {"time": 2.8, "x": 70, "y": 70}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}}}, "Old/Jump_jet_start4s_r": {"slots": {"Face/EyeBrow_L": {"attachment": [{"name": "EyeBrow_L"}]}, "Face/EyeBrow_R": {"attachment": [{"name": "EyeBrow_R"}]}, "Face/eye_black": {"attachment": [{"name": "eye"}]}, "Face/eye_black2": {"attachment": [{"name": "eye2"}]}}, "bones": {"Leg_F_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 14.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 14.15, "y": -8.25, "curve": [0.244, 14.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_L": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, -13.32, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": -13.32, "y": -8.25, "curve": [0.244, -13.32, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_B_R": {"rotate": [{}], "translate": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "y": -8.25, "curve": [0.244, 0, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Leg_F_R": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}], "translate": [{"curve": [0.011, 0, 0.022, 9.15, 0.011, 0, 0.022, -8.25]}, {"time": 0.0333, "x": 9.15, "y": -8.25, "curve": [0.244, 9.15, 0.456, 0, 0.244, -8.25, 0.456, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.03, 1, 0.07, 1.1, 0.03, 1, 0.07, 1]}, {"time": 0.1, "x": 1.1, "curve": [0.167, 1.1, 0.233, 1, 0.167, 1, 0.233, 1]}, {"time": 0.3}]}, "Low_cntr": {"translate": [{"curve": [0.044, 0, 0.089, -4.15, 0.044, 0, 0.089, 3.11]}, {"time": 0.1333, "x": -4.15, "y": 1.9, "curve": [0.178, -4.15, 0.222, 8.12, 0.174, 0.8, 0.223, 0.67]}, {"time": 0.2667, "x": 8.12, "y": 2.54, "curve": [0.311, 8.12, 0.356, -4.15, 0.312, 4.47, 0.356, 4.18]}, {"time": 0.4, "x": -4.15, "y": 1.9, "curve": [0.444, -4.15, 0.489, 8.12, 0.442, -0.31, 0.489, 0.61]}, {"time": 0.5333, "x": 8.12, "y": 2.54, "curve": [0.578, 8.12, 0.622, -4.15, 0.584, 4.75, 0.623, 4.03]}, {"time": 0.6667, "x": -4.15, "y": 1.9, "curve": [0.711, -4.15, 0.756, 8.12, 0.712, -0.31, 0.756, 0.27]}, {"time": 0.8, "x": 8.12, "y": 2.54, "curve": [0.844, 8.12, 0.889, -4.15, 0.853, 5.3, 0.889, 3.81]}, {"time": 0.9333, "x": -4.15, "y": 1.9, "curve": [0.978, -4.15, 1.022, 8.12, 0.978, -0.03, 1.023, 1.03]}, {"time": 1.0667, "x": 8.12, "y": 2.54, "curve": [1.111, 8.12, 1.156, -4.15, 1.107, 3.92, 1.156, 3.11]}, {"time": 1.2, "x": -4.15, "y": 1.9, "curve": [1.244, -4.15, 1.289, 8.12, 1.241, 0.8, 1.289, 0.67]}, {"time": 1.3333, "x": 8.12, "y": 2.54, "curve": [1.378, 8.12, 1.422, -4.15, 1.379, 4.47, 1.423, 4.18]}, {"time": 1.4667, "x": -4.15, "y": 1.9, "curve": [1.511, -4.15, 1.556, 8.12, 1.509, -0.31, 1.556, 0.61]}, {"time": 1.6, "x": 8.12, "y": 2.54, "curve": [1.644, 8.12, 1.689, -4.15, 1.65, 4.75, 1.69, 4.03]}, {"time": 1.7333, "x": -4.15, "y": 1.9, "curve": [1.778, -4.15, 1.822, 8.12, 1.779, -0.31, 1.823, 0.27]}, {"time": 1.8667, "x": 8.12, "y": 2.54, "curve": [1.911, 8.12, 1.956, -4.15, 1.92, 5.3, 1.956, 3.81]}, {"time": 2, "x": -4.15, "y": 1.9, "curve": [2.044, -4.15, 2.089, 8.12, 2.044, -0.03, 2.089, 1.03]}, {"time": 2.1333, "x": 8.12, "y": 2.54, "curve": [2.178, 8.12, 2.222, -4.15, 2.174, 3.92, 2.222, 3.11]}, {"time": 2.2667, "x": -4.15, "y": 1.9, "curve": [2.311, -4.15, 2.356, 8.12, 2.307, 0.8, 2.356, 0.67]}, {"time": 2.4, "x": 8.12, "y": 2.54, "curve": [2.444, 8.12, 2.489, -4.15, 2.445, 4.47, 2.49, 4.18]}, {"time": 2.5333, "x": -4.15, "y": 1.9, "curve": [2.578, -4.15, 2.622, 8.12, 2.576, -0.31, 2.623, 0.61]}, {"time": 2.6667, "x": 8.12, "y": 2.54, "curve": [2.711, 8.12, 2.756, -4.15, 2.717, 4.75, 2.756, 4.03]}, {"time": 2.8, "x": -4.15, "y": 1.9, "curve": [2.844, -4.15, 2.889, 8.12, 2.845, -0.31, 2.89, 0.27]}, {"time": 2.9333, "x": 8.12, "y": 2.54, "curve": [2.978, 8.12, 3.022, -4.15, 2.987, 5.3, 3.023, 3.81]}, {"time": 3.0667, "x": -4.15, "y": 1.9, "curve": [3.111, -4.15, 3.156, 8.12, 3.111, -0.03, 3.156, 1.03]}, {"time": 3.2, "x": 8.12, "y": 2.54, "curve": [3.244, 8.12, 3.289, -4.15, 3.241, 3.92, 3.289, 1.9]}, {"time": 3.3333, "x": -4.15, "y": 1.9, "curve": [3.707, -4.15, 2.32, 0, 3.707, 1.9, 2.32, 0]}, {"time": 4}], "scale": [{"x": 0.8, "y": 1.2, "curve": [0.133, 0.8, 0.267, 1, 0.133, 1.2, 0.267, 1]}, {"time": 0.4}]}, "Jetpack": {"rotate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, -32.42]}, {"time": 4, "value": -32.42}], "translate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, 249.96, 3.756, 0, 3.936, -302.94]}, {"time": 4, "x": 249.96, "y": -1057.38}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.4667, "x": 100, "y": 100, "curve": [0.5, 100, 0.533, 110, 0.5, 100, 0.533, 110]}, {"time": 0.5667, "x": 110, "y": 110, "curve": [0.6, 110, 0.644, 100, 0.6, 110, 0.644, 100]}, {"time": 0.6667, "x": 100, "y": 100, "curve": [0.7, 100, 0.733, 110, 0.7, 100, 0.733, 110]}, {"time": 0.7667, "x": 110, "y": 110, "curve": [0.8, 110, 0.844, 100, 0.8, 110, 0.844, 100]}, {"time": 0.8667, "x": 100, "y": 100, "curve": [0.9, 100, 0.933, 110, 0.9, 100, 0.933, 110]}, {"time": 0.9667, "x": 110, "y": 110, "curve": [1, 110, 1.044, 100, 1, 110, 1.044, 100]}, {"time": 1.0667, "x": 100, "y": 100, "curve": [1.1, 100, 1.133, 110, 1.1, 100, 1.133, 110]}, {"time": 1.1667, "x": 110, "y": 110, "curve": [1.2, 110, 1.244, 100, 1.2, 110, 1.244, 100]}, {"time": 1.2667, "x": 100, "y": 100, "curve": [1.3, 100, 1.333, 110, 1.3, 100, 1.333, 110]}, {"time": 1.3667, "x": 110, "y": 110, "curve": [1.4, 110, 1.444, 100, 1.4, 110, 1.444, 100]}, {"time": 1.4667, "x": 100, "y": 100, "curve": [1.5, 100, 1.533, 110, 1.5, 100, 1.533, 110]}, {"time": 1.5667, "x": 110, "y": 110, "curve": [1.6, 110, 1.644, 100, 1.6, 110, 1.644, 100]}, {"time": 1.6667, "x": 100, "y": 100, "curve": [1.7, 100, 1.733, 110, 1.7, 100, 1.733, 110]}, {"time": 1.7667, "x": 110, "y": 110, "curve": [1.8, 110, 1.844, 100, 1.8, 110, 1.844, 100]}, {"time": 1.8667, "x": 100, "y": 100, "curve": [1.9, 100, 1.933, 110, 1.9, 100, 1.933, 110]}, {"time": 1.9667, "x": 110, "y": 110, "curve": [2, 110, 2.044, 100, 2, 110, 2.044, 100]}, {"time": 2.0667, "x": 100, "y": 100, "curve": [2.1, 100, 2.133, 110, 2.1, 100, 2.133, 110]}, {"time": 2.1667, "x": 110, "y": 110, "curve": [2.2, 110, 2.244, 100, 2.2, 110, 2.244, 100]}, {"time": 2.2667, "x": 100, "y": 100, "curve": [2.3, 100, 2.333, 110, 2.3, 100, 2.333, 110]}, {"time": 2.3667, "x": 110, "y": 110, "curve": [2.4, 110, 2.444, 100, 2.4, 110, 2.444, 100]}, {"time": 2.4667, "x": 100, "y": 100, "curve": [2.5, 100, 2.533, 110, 2.5, 100, 2.533, 110]}, {"time": 2.5667, "x": 110, "y": 110, "curve": [2.6, 110, 2.644, 100, 2.6, 110, 2.644, 100]}, {"time": 2.6667, "x": 100, "y": 100, "curve": [2.7, 100, 2.733, 110, 2.7, 100, 2.733, 110]}, {"time": 2.7667, "x": 110, "y": 110, "curve": [2.779, 110, 2.986, 100, 2.779, 110, 2.986, 100]}, {"time": 3, "x": 100, "y": 100}]}, "Jetpack2": {"rotate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.878, -31.2]}, {"time": 4, "value": -31.2}], "translate": [{"curve": "stepped"}, {"time": 3.6333, "curve": [3.756, 0, 3.893, 260.96, 3.72, 0, 3.947, -199.44]}, {"time": 4, "x": 268.33, "y": -1059.99}], "scale": [{"curve": [0.022, 1, 0.044, 100, 0.022, 1, 0.044, 100]}, {"time": 0.0667, "x": 100, "y": 100, "curve": "stepped"}, {"time": 0.4667, "x": 100, "y": 100, "curve": [0.5, 100, 0.533, 110, 0.5, 100, 0.533, 110]}, {"time": 0.5667, "x": 110, "y": 110, "curve": [0.6, 110, 0.644, 100, 0.6, 110, 0.644, 100]}, {"time": 0.6667, "x": 100, "y": 100, "curve": [0.7, 100, 0.733, 110, 0.7, 100, 0.733, 110]}, {"time": 0.7667, "x": 110, "y": 110, "curve": [0.8, 110, 0.844, 100, 0.8, 110, 0.844, 100]}, {"time": 0.8667, "x": 100, "y": 100, "curve": [0.9, 100, 0.933, 110, 0.9, 100, 0.933, 110]}, {"time": 0.9667, "x": 110, "y": 110, "curve": [1, 110, 1.044, 100, 1, 110, 1.044, 100]}, {"time": 1.0667, "x": 100, "y": 100, "curve": [1.1, 100, 1.133, 110, 1.1, 100, 1.133, 110]}, {"time": 1.1667, "x": 110, "y": 110, "curve": [1.2, 110, 1.244, 100, 1.2, 110, 1.244, 100]}, {"time": 1.2667, "x": 100, "y": 100, "curve": [1.3, 100, 1.333, 110, 1.3, 100, 1.333, 110]}, {"time": 1.3667, "x": 110, "y": 110, "curve": [1.4, 110, 1.444, 100, 1.4, 110, 1.444, 100]}, {"time": 1.4667, "x": 100, "y": 100, "curve": [1.5, 100, 1.533, 110, 1.5, 100, 1.533, 110]}, {"time": 1.5667, "x": 110, "y": 110, "curve": [1.6, 110, 1.644, 100, 1.6, 110, 1.644, 100]}, {"time": 1.6667, "x": 100, "y": 100, "curve": [1.7, 100, 1.733, 110, 1.7, 100, 1.733, 110]}, {"time": 1.7667, "x": 110, "y": 110, "curve": [1.8, 110, 1.844, 100, 1.8, 110, 1.844, 100]}, {"time": 1.8667, "x": 100, "y": 100, "curve": [1.9, 100, 1.933, 110, 1.9, 100, 1.933, 110]}, {"time": 1.9667, "x": 110, "y": 110, "curve": [2, 110, 2.044, 100, 2, 110, 2.044, 100]}, {"time": 2.0667, "x": 100, "y": 100, "curve": [2.1, 100, 2.133, 110, 2.1, 100, 2.133, 110]}, {"time": 2.1667, "x": 110, "y": 110, "curve": [2.2, 110, 2.244, 100, 2.2, 110, 2.244, 100]}, {"time": 2.2667, "x": 100, "y": 100, "curve": [2.3, 100, 2.333, 110, 2.3, 100, 2.333, 110]}, {"time": 2.3667, "x": 110, "y": 110, "curve": [2.4, 110, 2.444, 100, 2.4, 110, 2.444, 100]}, {"time": 2.4667, "x": 100, "y": 100, "curve": [2.5, 100, 2.533, 110, 2.5, 100, 2.533, 110]}, {"time": 2.5667, "x": 110, "y": 110, "curve": [2.6, 110, 2.644, 100, 2.6, 110, 2.644, 100]}, {"time": 2.6667, "x": 100, "y": 100, "curve": [2.7, 100, 2.733, 110, 2.7, 100, 2.733, 110]}, {"time": 2.7667, "x": 110, "y": 110, "curve": [2.779, 110, 2.986, 100, 2.779, 110, 2.986, 100]}, {"time": 3, "x": 100, "y": 100}]}, "fire": {"scale": [{"x": 1.2, "y": 0.8, "curve": [0.022, 1.2, 0.044, 0.8, 0.022, 0.8, 0.044, 1.2]}, {"time": 0.0667, "x": 0.8, "y": 1.2, "curve": [0.089, 0.8, 0.111, 1.2, 0.089, 1.2, 0.111, 0.8]}, {"time": 0.1333, "x": 1.2, "y": 0.8, "curve": [0.156, 1.2, 0.178, 0.8, 0.156, 0.8, 0.178, 1.2]}, {"time": 0.2, "x": 0.8, "y": 1.2, "curve": [0.222, 0.8, 0.244, 1.2, 0.222, 1.2, 0.244, 0.8]}, {"time": 0.2667, "x": 1.2, "y": 0.8, "curve": [0.289, 1.2, 0.311, 0.8, 0.289, 0.8, 0.311, 1.2]}, {"time": 0.3333, "x": 0.8, "y": 1.2, "curve": [0.356, 0.8, 0.378, 1.2, 0.356, 1.2, 0.378, 0.8]}, {"time": 0.4, "x": 1.2, "y": 0.8, "curve": [0.422, 1.2, 0.444, 0.8, 0.422, 0.8, 0.444, 1.2]}, {"time": 0.4667, "x": 0.8, "y": 1.2, "curve": [0.489, 0.8, 0.511, 1.2, 0.489, 1.2, 0.511, 0.8]}, {"time": 0.5333, "x": 1.2, "y": 0.8, "curve": [0.556, 1.2, 0.578, 0.8, 0.556, 0.8, 0.578, 1.2]}, {"time": 0.6, "x": 0.8, "y": 1.2, "curve": [0.622, 0.8, 0.644, 1.2, 0.622, 1.2, 0.644, 0.8]}, {"time": 0.6667, "x": 1.2, "y": 0.8, "curve": [0.689, 1.2, 0.711, 0.8, 0.689, 0.8, 0.711, 1.2]}, {"time": 0.7333, "x": 0.8, "y": 1.2, "curve": [0.756, 0.8, 0.778, 1.2, 0.756, 1.2, 0.778, 0.8]}, {"time": 0.8, "x": 1.2, "y": 0.8, "curve": [0.822, 1.2, 0.844, 0.8, 0.822, 0.8, 0.844, 1.2]}, {"time": 0.8667, "x": 0.8, "y": 1.2, "curve": [0.889, 0.8, 0.911, 1.2, 0.889, 1.2, 0.911, 0.8]}, {"time": 0.9333, "x": 1.2, "y": 0.8, "curve": [0.956, 1.2, 0.978, 0.8, 0.956, 0.8, 0.978, 1.2]}, {"time": 1, "x": 0.8, "y": 1.2, "curve": [1.022, 0.8, 1.044, 1.2, 1.022, 1.2, 1.044, 0.8]}, {"time": 1.0667, "x": 1.2, "y": 0.8, "curve": [1.089, 1.2, 1.111, 0.8, 1.089, 0.8, 1.111, 1.2]}, {"time": 1.1333, "x": 0.8, "y": 1.2, "curve": [1.156, 0.8, 1.178, 1.2, 1.156, 1.2, 1.178, 0.8]}, {"time": 1.2, "x": 1.2, "y": 0.8, "curve": [1.222, 1.2, 1.244, 0.8, 1.222, 0.8, 1.244, 1.2]}, {"time": 1.2667, "x": 0.8, "y": 1.2, "curve": [1.289, 0.8, 1.311, 1.2, 1.289, 1.2, 1.311, 0.8]}, {"time": 1.3333, "x": 1.2, "y": 0.8, "curve": [1.356, 1.2, 1.378, 0.694, 1.356, 0.8, 1.378, 1.094]}, {"time": 1.4, "x": 0.694, "y": 1.094, "curve": [1.507, 0.694, 1.444, 0.8, 1.507, 1.094, 1.444, 1.2]}, {"time": 1.4667, "x": 0.8, "y": 1.2, "curve": [1.489, 0.8, 1.511, 1.2, 1.489, 1.2, 1.511, 0.8]}, {"time": 1.5333, "x": 1.2, "y": 0.8, "curve": [1.556, 1.2, 1.578, 0.8, 1.556, 0.8, 1.578, 1.2]}, {"time": 1.6, "x": 0.8, "y": 1.2, "curve": [1.622, 0.8, 1.644, 1.2, 1.622, 1.2, 1.644, 0.8]}, {"time": 1.6667, "x": 1.2, "y": 0.8, "curve": [1.689, 1.2, 1.711, 0.8, 1.689, 0.8, 1.711, 1.2]}, {"time": 1.7333, "x": 0.8, "y": 1.2, "curve": [1.756, 0.8, 1.778, 1.2, 1.756, 1.2, 1.778, 0.8]}, {"time": 1.8, "x": 1.2, "y": 0.8, "curve": [1.822, 1.2, 1.844, 0.8, 1.822, 0.8, 1.844, 1.2]}, {"time": 1.8667, "x": 0.8, "y": 1.2, "curve": [1.889, 0.8, 1.911, 1.2, 1.889, 1.2, 1.911, 0.8]}, {"time": 1.9333, "x": 1.2, "y": 0.8, "curve": [1.956, 1.2, 1.978, 0.8, 1.956, 0.8, 1.978, 1.2]}, {"time": 2, "x": 0.8, "y": 1.2, "curve": [2.022, 0.8, 2.044, 1.2, 2.022, 1.2, 2.044, 0.8]}, {"time": 2.0667, "x": 1.2, "y": 0.8, "curve": [2.089, 1.2, 2.111, 0.8, 2.089, 0.8, 2.111, 1.2]}, {"time": 2.1333, "x": 0.8, "y": 1.2, "curve": [2.156, 0.8, 2.178, 1.2, 2.156, 1.2, 2.178, 0.8]}, {"time": 2.2, "x": 1.2, "y": 0.8, "curve": [2.222, 1.2, 2.244, 0.8, 2.222, 0.8, 2.244, 1.2]}, {"time": 2.2667, "x": 0.8, "y": 1.2, "curve": [2.289, 0.8, 2.311, 1.2, 2.289, 1.2, 2.311, 0.8]}, {"time": 2.3333, "x": 1.2, "y": 0.8, "curve": [2.356, 1.2, 2.378, 0.8, 2.356, 0.8, 2.378, 1.2]}, {"time": 2.4, "x": 0.8, "y": 1.2, "curve": [2.422, 0.8, 2.444, 1.2, 2.422, 1.2, 2.444, 0.8]}, {"time": 2.4667, "x": 1.2, "y": 0.8, "curve": [2.489, 1.2, 2.511, 0.8, 2.489, 0.8, 2.511, 1.2]}, {"time": 2.5333, "x": 0.8, "y": 1.2, "curve": [2.556, 0.8, 2.578, 1.2, 2.556, 1.2, 2.578, 0.8]}, {"time": 2.6, "x": 1.2, "y": 0.8, "curve": [2.622, 1.2, 2.644, 0.8, 2.622, 0.8, 2.644, 1.2]}, {"time": 2.6667, "x": 0.8, "y": 1.2, "curve": [2.689, 0.8, 2.711, 1.2, 2.689, 1.2, 2.711, 0.8]}, {"time": 2.7333, "x": 1.2, "y": 0.8, "curve": [2.756, 1.2, 2.778, 0.694, 2.756, 0.8, 2.778, 1.094]}, {"time": 2.8, "x": 0.694, "y": 1.094, "curve": [3.236, 0.694, 2.844, 0.8, 3.236, 1.094, 2.844, 1.2]}, {"time": 2.8667, "x": 0.8, "y": 1.2, "curve": [2.889, 0.8, 2.911, 1.2, 2.889, 1.2, 2.911, 0.8]}, {"time": 2.9333, "x": 1.2, "y": 0.8, "curve": [2.956, 1.2, 2.978, 0.8, 2.956, 0.8, 2.978, 1.2]}, {"time": 3, "x": 0.8, "y": 1.2, "curve": [3.022, 0.8, 3.044, 1.2, 3.022, 1.2, 3.044, 0.8]}, {"time": 3.0667, "x": 1.2, "y": 0.8, "curve": [3.089, 1.2, 3.111, 0.8, 3.089, 0.8, 3.111, 1.2]}, {"time": 3.1333, "x": 0.8, "y": 1.2, "curve": [3.156, 0.8, 3.178, 1.2, 3.156, 1.2, 3.178, 0.8]}, {"time": 3.2, "x": 1.2, "y": 0.8, "curve": [3.201, 1.2, 3.25, -0.337, 3.201, 0.8, 3.25, 0.063]}, {"time": 3.2667, "x": -0.337, "y": 0.063}], "shear": [{"curve": [0.011, 0, 0.022, 0, 0.011, 0, 0.022, -23.32]}, {"time": 0.0333, "y": -19.04, "curve": [0.055, 0, 0.078, 0, 0.055, -10.56, 0.078, 6.41]}, {"time": 0.1, "y": 6.41, "curve": [0.122, 0, 0.145, 0, 0.122, 6.47, 0.145, -19.04]}, {"time": 0.1667, "y": -19.04, "curve": [0.189, 0, 0.211, 0, 0.189, -18.86, 0.211, 6.41]}, {"time": 0.2333, "y": 6.41, "curve": [0.255, 0, 0.278, 0, 0.255, 6.47, 0.278, -19.04]}, {"time": 0.3, "y": -19.04, "curve": [0.322, 0, 0.345, 0, 0.322, -18.86, 0.345, 6.41]}, {"time": 0.3667, "y": 6.41, "curve": [0.389, 0, 0.411, 0, 0.389, 6.47, 0.411, -19.04]}, {"time": 0.4333, "y": -19.04, "curve": [0.455, 0, 0.478, 0, 0.455, -18.86, 0.478, 6.41]}, {"time": 0.5, "y": 6.41, "curve": [0.522, 0, 0.545, 0, 0.522, 6.47, 0.545, -19.04]}, {"time": 0.5667, "y": -19.04, "curve": [0.589, 0, 0.611, 0, 0.589, -18.86, 0.611, 6.41]}, {"time": 0.6333, "y": 6.41, "curve": [0.655, 0, 0.678, 0, 0.655, 6.47, 0.678, -19.04]}, {"time": 0.7, "y": -19.04, "curve": [0.722, 0, 0.745, 0, 0.722, -18.86, 0.745, 6.41]}, {"time": 0.7667, "y": 6.41, "curve": [0.789, 0, 0.811, 0, 0.789, 6.47, 0.811, -19.04]}, {"time": 0.8333, "y": -19.04, "curve": [0.855, 0, 0.878, 0, 0.855, -18.86, 0.878, 6.41]}, {"time": 0.9, "y": 6.41, "curve": [0.922, 0, 0.945, 0, 0.922, 6.47, 0.945, -19.04]}, {"time": 0.9667, "y": -19.04, "curve": [0.989, 0, 1.011, 0, 0.989, -18.86, 1.011, 6.41]}, {"time": 1.0333, "y": 6.41, "curve": [1.055, 0, 1.078, 0, 1.055, 6.47, 1.078, -19.04]}, {"time": 1.1, "y": -19.04, "curve": [1.122, 0, 1.145, 0, 1.122, -18.86, 1.145, 6.41]}, {"time": 1.1667, "y": 6.41, "curve": [1.189, 0, 1.211, 0, 1.189, 6.47, 1.211, -19.04]}, {"time": 1.2333, "y": -19.04, "curve": [1.255, 0, 1.278, 0, 1.255, -18.86, 1.278, 6.41]}, {"time": 1.3, "y": 6.41, "curve": [1.322, 0, 1.345, 0, 1.322, 6.47, 1.345, -19.04]}, {"time": 1.3667, "y": -19.04, "curve": [1.389, 0, 1.411, 0, 1.389, -18.86, 1.411, 6.41]}, {"time": 1.4333, "y": 6.41, "curve": [1.438, 0, 1.496, 0, 1.438, 6.42, 1.496, -17.34]}, {"time": 1.5, "y": -19.04, "curve": [1.522, 0, 1.545, 0, 1.522, -27.6, 1.545, 6.41]}, {"time": 1.5667, "y": 6.41, "curve": [1.589, 0, 1.611, 0, 1.589, 6.47, 1.611, -19.04]}, {"time": 1.6333, "y": -19.04, "curve": [1.655, 0, 1.678, 0, 1.655, -18.86, 1.678, 6.41]}, {"time": 1.7, "y": 6.41, "curve": [1.722, 0, 1.745, 0, 1.722, 6.47, 1.745, -19.04]}, {"time": 1.7667, "y": -19.04, "curve": [1.789, 0, 1.811, 0, 1.789, -18.86, 1.811, 6.41]}, {"time": 1.8333, "y": 6.41, "curve": [1.855, 0, 1.878, 0, 1.855, 6.47, 1.878, -19.04]}, {"time": 1.9, "y": -19.04, "curve": [1.922, 0, 1.945, 0, 1.922, -18.86, 1.945, 6.41]}, {"time": 1.9667, "y": 6.41, "curve": [1.989, 0, 2.011, 0, 1.989, 6.47, 2.011, -19.04]}, {"time": 2.0333, "y": -19.04, "curve": [2.055, 0, 2.078, 0, 2.055, -18.86, 2.078, 6.41]}, {"time": 2.1, "y": 6.41, "curve": [2.122, 0, 2.145, 0, 2.122, 6.47, 2.145, -19.04]}, {"time": 2.1667, "y": -19.04, "curve": [2.189, 0, 2.211, 0, 2.189, -18.86, 2.211, 6.41]}, {"time": 2.2333, "y": 6.41, "curve": [2.255, 0, 2.278, 0, 2.255, 6.47, 2.278, -19.04]}, {"time": 2.3, "y": -19.04, "curve": [2.322, 0, 2.345, 0, 2.322, -18.86, 2.345, 6.41]}, {"time": 2.3667, "y": 6.41, "curve": [2.389, 0, 2.411, 0, 2.389, 6.47, 2.411, -19.04]}, {"time": 2.4333, "y": -19.04, "curve": [2.455, 0, 2.478, 0, 2.455, -18.86, 2.478, 6.41]}, {"time": 2.5, "y": 6.41, "curve": [2.522, 0, 2.545, 0, 2.522, 6.47, 2.545, -19.04]}, {"time": 2.5667, "y": -19.04, "curve": [2.589, 0, 2.611, 0, 2.589, -18.86, 2.611, 6.41]}, {"time": 2.6333, "y": 6.41, "curve": [2.655, 0, 2.678, 0, 2.655, 6.47, 2.678, -19.04]}, {"time": 2.7, "y": -19.04, "curve": [2.722, 0, 2.745, 0, 2.722, -18.86, 2.745, 6.41]}, {"time": 2.7667, "y": 6.41, "curve": [2.789, 0, 2.811, 0, 2.789, 6.47, 2.811, -19.04]}, {"time": 2.8333, "y": -19.04, "curve": [2.855, 0, 2.878, 0, 2.855, -18.86, 2.878, 6.41]}, {"time": 2.9, "y": 6.41, "curve": [2.904, 0, 2.962, 0, 2.904, 6.42, 2.962, -17.34]}, {"time": 2.9667, "y": -19.04, "curve": [2.989, 0, 3.011, 0, 2.989, -27.6, 3.011, 6.41]}, {"time": 3.0333, "y": 6.41, "curve": [3.055, 0, 3.078, 0, 3.055, 6.47, 3.078, -19.04]}, {"time": 3.1, "y": -19.04, "curve": [3.122, 0, 3.145, 0, 3.122, -18.86, 3.145, 6.41]}, {"time": 3.1667, "y": 6.41, "curve": [3.189, 0, 3.211, 0, 3.189, 6.47, 3.211, -19.04]}, {"time": 3.2333, "y": -19.04, "curve": [3.255, 0, 3.278, 0, 3.255, -18.86, 3.278, 6.41]}, {"time": 3.3, "y": 6.41}]}, "Rainbow": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.009, 20.31, 0.044, 52.14]}, {"time": 0.0667, "y": 52.14, "curve": [0.267, 0, 0.467, 0, 0.267, 52.14, 0.467, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.033, 1, 0.067, 1, 0.033, 1, 0.067, 100]}, {"time": 0.1, "y": 100}]}, "Body_parts": {"translate": [{}], "scale": [{}]}, "Horn": {"translate": [{}]}, "Face": {"translate": [{"x": -0.49, "y": 0.9}]}, "Tail": {"rotate": [{"value": -121.25, "curve": "stepped"}, {"time": 3.6667, "value": -121.25, "curve": [3.715, -63.42, 3.889, 0]}, {"time": 4}], "translate": [{}]}, "eye_black2": {"scale": [{}]}, "eye_black": {"scale": [{}]}, "Body_cntr": {"rotate": [{"curve": [0.033, 0, 0.067, -32.08]}, {"time": 0.1, "value": -32.08, "curve": "stepped"}, {"time": 3.3333, "value": -32.08, "curve": [3.4, -32.08, 3.667, 0]}, {"time": 3.8333}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.007, 26.16, 0.044, 54.39]}, {"time": 0.0667, "y": 54.39, "curve": [0.267, 0, 0.467, 0, 0.267, 54.39, 0.467, 0]}, {"time": 0.6667}]}, "star1": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "star2": {"translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Fire_scale": {"scale": [{"x": 70, "y": 70, "curve": "stepped"}, {"time": 0.5, "x": 70, "y": 70, "curve": [0.6, 70, 0.7, 300, 0.6, 70, 0.7, 100.99]}, {"time": 0.8, "x": 300, "y": 100.99, "curve": "stepped"}, {"time": 2.3333, "x": 300, "y": 100.99, "curve": [2.489, 300, 2.644, 70, 2.489, 100.99, 2.644, 70]}, {"time": 2.8, "x": 70, "y": 70}]}, "EyeBrow_L": {"translate": [{}]}, "EyeBrow_R": {"translate": [{}]}, "Leg_Outline_big2": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big3": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}, "Leg_Outline_big4": {"translate": [{"curve": [0.033, -2.27, 0.067, -1.04, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": -1.04, "curve": [0.167, -1.04, 0.233, -2.27, 0.167, 0, 0.233, 0]}, {"time": 0.3}]}}}}}