{"skeleton": {"hash": "D2GtSpYIOPI", "spine": "4.2.38", "x": -165.5, "y": -151, "width": 330, "height": 304, "images": "./Images/Monster_3/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "cntr", "parent": "root", "x": -1.48, "y": 10.99, "icon": "arrows"}, {"name": "Body", "parent": "cntr", "icon": "square"}, {"name": "leg 1", "parent": "Body", "length": 34.37, "rotation": 50.91, "x": 62.79, "y": 97.8}, {"name": "leg 6", "parent": "Body", "length": 51.47, "rotation": 135.44, "x": -60.01, "y": 84.46}, {"name": "leg 5", "parent": "Body", "length": 37.79, "rotation": -151.93, "x": -113.91, "y": -15}, {"name": "leg 4", "parent": "Body", "length": 41.3, "rotation": -113.81, "x": -30.01, "y": -93.35}, {"name": "leg 3", "parent": "Body", "length": 22.13, "rotation": -61.5, "x": 51.12, "y": -100.58}, {"name": "leg 2", "parent": "Body", "length": 56.76, "rotation": -23.05, "x": 98.91, "y": -30.01}, {"name": "eye1_blink", "parent": "Body", "x": -48.9, "y": -8.89}, {"name": "eye 1", "parent": "eye1_blink", "icon": "eye"}, {"name": "eye2_blink", "parent": "Body", "x": 36.12, "y": -28.34}, {"name": "eye 2", "parent": "eye2_blink", "icon": "eye"}, {"name": "pupil", "parent": "eye 1", "x": -11.55, "y": 8.19}, {"name": "pupil2", "parent": "eye 2", "x": 15.36, "y": -4.15}, {"name": "Explosion", "parent": "root", "x": -7.01, "y": -10.41, "icon": "asterisk"}, {"name": "Smoke_1", "parent": "Explosion"}, {"name": "Smoke_2", "parent": "Explosion"}, {"name": "Smoke_3", "parent": "Explosion"}, {"name": "Smoke_4", "parent": "Explosion"}, {"name": "Smoke_5", "parent": "Explosion"}, {"name": "Smoke_6", "parent": "Explosion"}, {"name": "Smoke_7", "parent": "Explosion"}, {"name": "Smoke_8", "parent": "Explosion"}, {"name": "Smoke_9", "parent": "Explosion"}, {"name": "Smoke_10", "parent": "Explosion"}, {"name": "Smoke_11", "parent": "Explosion"}, {"name": "Smoke_12", "parent": "Explosion"}], "slots": [{"name": "leg 1", "bone": "leg 1", "attachment": "leg 1"}, {"name": "leg 2", "bone": "leg 2", "attachment": "leg 2"}, {"name": "leg 3", "bone": "leg 3", "attachment": "leg 3"}, {"name": "leg 4", "bone": "leg 4", "attachment": "leg 4"}, {"name": "leg 5", "bone": "leg 5", "attachment": "leg 5"}, {"name": "leg 6", "bone": "leg 6", "attachment": "leg 6"}, {"name": "pimples", "bone": "Body", "attachment": "pimples"}, {"name": "eye 1", "bone": "eye 1", "attachment": "eye 1"}, {"name": "eye 2", "bone": "eye 2", "attachment": "eye 2"}, {"name": "pupil", "bone": "pupil", "attachment": "pupil"}, {"name": "pupil2", "bone": "pupil2", "attachment": "pupil"}, {"name": "Smoke_1", "bone": "Smoke_1", "color": "56687eff"}, {"name": "Smoke_2", "bone": "Smoke_2", "color": "56687eff"}, {"name": "Smoke_3", "bone": "Smoke_3", "color": "56687eff"}, {"name": "Smoke_4", "bone": "Smoke_4", "color": "56687eff"}, {"name": "Smoke_5", "bone": "Smoke_5", "color": "56687eff"}, {"name": "Smoke_6", "bone": "Smoke_6", "color": "56687eff"}, {"name": "Smoke_7", "bone": "Smoke_7", "color": "56687eff"}, {"name": "Smoke_8", "bone": "Smoke_8", "color": "56687eff"}, {"name": "Smoke_9", "bone": "Smoke_9", "color": "56687eff"}, {"name": "Smoke_10", "bone": "Smoke_10", "color": "56687eff"}, {"name": "Smoke_11", "bone": "Smoke_11", "color": "56687eff"}, {"name": "Smoke_12", "bone": "Smoke_12", "color": "56687eff"}], "skins": [{"name": "default", "attachments": {"eye 1": {"eye 1": {"x": -0.13, "y": -10.6, "width": 88, "height": 97}}, "eye 2": {"eye 2": {"x": 0.86, "y": -6.65, "width": 72, "height": 88}}, "leg 1": {"leg 1": {"x": 21.21, "y": -0.4, "rotation": -50.91, "width": 63, "height": 56}}, "leg 2": {"leg 2": {"x": 31.82, "y": -4.37, "rotation": 23.05, "width": 79, "height": 77}}, "leg 3": {"leg 3": {"x": 14.86, "y": 0.3, "rotation": 61.5, "width": 59, "height": 49}}, "leg 4": {"leg 4": {"x": 34.85, "y": -4.32, "rotation": 113.81, "width": 72, "height": 77}}, "leg 5": {"leg 5": {"x": 25.06, "y": 5.32, "rotation": 151.93, "width": 61, "height": 67}}, "leg 6": {"leg 6": {"x": 34.72, "y": 3.95, "rotation": -135.44, "width": 75, "height": 68}}, "pimples": {"pimples": {"type": "mesh", "uvs": [0.52586, 0.01228, 0.64825, 0.01398, 0.73904, 0.03722, 0.82779, 0.09907, 0.87685, 0.16725, 0.91798, 0.28124, 0.93462, 0.34411, 0.99349, 0.39102, 0.99607, 0.46994, 0.95682, 0.52, 0.94404, 0.63924, 0.9048, 0.7433, 0.8527, 0.82721, 0.78136, 0.89593, 0.67883, 0.95988, 0.59027, 0.97559, 0.50172, 0.99131, 0.39516, 0.99252, 0.27839, 0.94768, 0.18448, 0.86474, 0.09938, 0.77674, 0.04866, 0.6749, 0.00676, 0.568, 0.0038, 0.4368, 0.01286, 0.29969, 0.06318, 0.23493, 0.12552, 0.16426, 0.28133, 0.08236, 0.44996, 0, 0.4965, 0.49148], "triangles": [29, 5, 6, 29, 6, 7, 29, 7, 8, 9, 29, 8, 10, 29, 9, 11, 29, 10, 12, 29, 11, 12, 13, 29, 13, 14, 29, 14, 15, 29, 29, 18, 19, 29, 17, 18, 15, 16, 29, 16, 17, 29, 29, 23, 24, 22, 23, 29, 21, 22, 29, 20, 21, 29, 19, 20, 29, 28, 29, 27, 0, 29, 28, 26, 27, 29, 25, 26, 29, 25, 29, 24, 29, 0, 1, 29, 1, 2, 29, 2, 3, 29, 3, 4, 29, 4, 5], "vertices": [4, 2, 7.23, 110.19, 0.12141, 3, -25.42, 50.93, 0.51347, 4, -29.86, -65.51, 0.34397, 8, -139.25, 93.1, 0.02115, 4, 2, 36.85, 109.8, 0.07885, 3, -7.04, 27.7, 0.69364, 4, -51.24, -86.02, 0.21229, 8, -111.84, 104.34, 0.01522, 3, 3, 2.66, 7.27, 0.83628, 4, -70.64, -97.63, 0.10862, 8, -89.53, 108.03, 0.0551, 2, 3, 5.17, -18.37, 0.86042, 8, -64.2, 103.35, 0.13958, 3, 2, 92.17, 74.54, 0.05753, 3, 0.48, -37.47, 0.693, 8, -47.13, 93.56, 0.24947, 3, 2, 102.13, 48.32, 0.09557, 3, -13.59, -61.73, 0.53028, 8, -27.71, 73.34, 0.37415, 3, 2, 106.15, 33.87, 0.15346, 3, -22.27, -73.97, 0.37502, 8, -18.34, 61.61, 0.47152, 4, 2, 120.4, 23.08, 0.22692, 3, -21.66, -91.83, 0.21017, 5, -224.67, 76.67, 0.10062, 8, -1.01, 57.26, 0.46229, 4, 2, 121.03, 4.92, 0.21625, 3, -35.36, -103.76, 0.13869, 5, -216.67, 92.98, 0.10136, 8, 6.67, 40.8, 0.54369, 4, 2, 111.53, -6.59, 0.11274, 3, -50.28, -103.65, 0.09167, 7, -53.78, 97.93, 0.04873, 8, 2.44, 26.49, 0.74687, 2, 7, -31.15, 82.12, 0.1589, 8, 10.33, 0.04, 0.8411, 2, 7, -14.65, 62.36, 0.31913, 8, 10.97, -25.7, 0.68087, 2, 7, -3.7, 42.07, 0.52198, 8, 6.92, -48.39, 0.47802, 3, 6, -40.26, 90.52, 0.07642, 7, 1.95, 19.36, 0.65862, 8, -2.77, -69.7, 0.26496, 3, 6, -16.79, 73.76, 0.20151, 7, 3.04, -9.47, 0.68355, 8, -19.85, -92.95, 0.11495, 3, 6, -4.83, 55.61, 0.39272, 7, -4.01, -30.02, 0.57221, 8, -38.15, -104.67, 0.03508, 3, 6, 7.12, 37.46, 0.60933, 7, -11.06, -50.58, 0.3845, 8, -56.45, -116.38, 0.00616, 3, 5, -31.8, 130.6, 0.05827, 6, 17.79, 13.98, 0.74579, 7, -23.11, -73.38, 0.19593, 4, 5, -11.72, 108.2, 0.14729, 6, 19.76, -16.04, 0.77659, 7, -45.66, -93.29, 0.0761, 8, -110.11, -128.31, 1e-05, 4, 5, -0.64, 80.67, 0.30207, 6, 11.48, -44.53, 0.65751, 7, -73.27, -104.17, 0.02049, 8, -138.5, -119.66, 0.01993, 4, 2, -95.97, -65.64, 0.17077, 5, 8, 53.12, 0.43395, 6, 1.27, -71.54, 0.32933, 8, -165.37, -109.1, 0.06595, 4, 2, -108.25, -42.22, 0.12249, 5, 7.81, 26.68, 0.64506, 6, -15.2, -92.23, 0.21367, 8, -185.83, -92.35, 0.01878, 4, 2, -118.39, -17.63, 0.1454, 5, 5.19, 0.21, 0.71805, 6, -33.6, -111.43, 0.11682, 8, -204.79, -73.7, 0.01973, 4, 2, -119.11, 12.55, 0.19782, 4, -8.36, 92.7, 0.19972, 5, -8.38, -26.75, 0.52228, 8, -217.27, -46.21, 0.08019, 4, 2, -116.91, 44.08, 0.19122, 4, 12.21, 68.7, 0.32845, 5, -25.16, -53.55, 0.40193, 8, -227.6, -16.33, 0.0784, 4, 2, -104.74, 58.98, 0.10026, 4, 13.98, 49.54, 0.55685, 5, -42.91, -60.96, 0.32457, 8, -222.23, 2.14, 0.01831, 4, 2, -89.65, 75.23, 0.08229, 4, 14.64, 27.37, 0.69916, 5, -63.87, -68.2, 0.21852, 8, -214.71, 23, 3e-05, 4, 3, -75.24, 86.69, 0.15615, 4, 0.99, -12.51, 0.68604, 5, -106.01, -67.08, 0.13808, 8, -187.39, 55.1, 0.01974, 4, 2, -11.13, 113.01, 0.1939, 3, -34.81, 66.97, 0.27586, 4, -14.79, -54.64, 0.46199, 8, -157.25, 88.51, 0.06826, 4, 2, 0.13, -0.03, 0.22818, 4, -102.14, 18, 0.21305, 5, -107.67, 40.46, 0.31386, 8, -102.63, -11.1, 0.24492], "hull": 29, "edges": [2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 26, 28, 32, 34, 34, 36, 12, 14, 8, 10, 10, 12, 52, 54, 54, 56, 2, 0, 0, 56, 18, 20, 20, 22, 22, 24, 24, 26, 36, 38, 38, 40, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 46, 58, 48, 58, 56, 58, 40, 58, 14, 58, 16, 58], "width": 242, "height": 230}}, "pupil": {"pupil": {"width": 13, "height": 17}}, "pupil2": {"pupil": {"width": 13, "height": 17}}, "Smoke_1": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_2": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_3": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_4": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_5": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_6": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_7": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_8": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_9": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_10": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_11": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}, "Smoke_12": {"Explosion0001": {"width": 100, "height": 100}, "Explosion0002": {"width": 100, "height": 100}, "Explosion0003": {"width": 100, "height": 100}}}}], "animations": {"t1_Death": {"slots": {"eye 1": {"rgba": [{"color": "ffffffff", "curve": [0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "eye 2": {"rgba": [{"color": "ffffffff", "curve": [0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "leg 1": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "leg 2": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "leg 3": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "leg 4": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "leg 5": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "leg 6": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "pimples": {"rgba": [{"color": "ffffffff", "curve": [0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 1, 0.078, 1, 0.156, 0]}, {"time": 0.2333, "color": "ffffff00"}]}, "pupil": {"rgba": [{"color": "ffffffff", "curve": [0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "pupil2": {"rgba": [{"color": "ffffffff", "curve": [0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 1, 0.1, 1, 0.2, 0]}, {"time": 0.3, "color": "ffffff00"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_3": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_4": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6333}]}, "Smoke_5": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}, "Smoke_6": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_9": {"attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0001"}, {"time": 0.6}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0003"}, {"time": 0.6}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}, {"time": 0.0333, "name": "Explosion0002"}, {"time": 0.6}]}}, "bones": {"cntr": {"rotate": [{"value": 2.93, "curve": [0.011, 2.93, 0.023, 1.6]}, {"time": 0.0333, "value": 1, "curve": [0.067, -0.76, 0.1, -3.32]}, {"time": 0.1333, "value": -3.32, "curve": [0.178, -3.32, 0.222, 2.93]}, {"time": 0.2667, "value": 2.93, "curve": [0.311, 2.93, 0.356, -3.32]}, {"time": 0.4, "value": -3.32, "curve": [0.444, -3.32, 0.489, 2.93]}, {"time": 0.5333, "value": 2.93, "curve": [0.578, 2.93, 0.622, -3.32]}, {"time": 0.6667, "value": -3.32, "curve": [0.711, -3.32, 0.756, 2.93]}, {"time": 0.8, "value": 2.93}], "translate": [{"x": 4.49, "y": 1.5, "curve": [0.022, 4.49, 0.044, -3.59, 0.022, 2.21, 0.044, 2.39]}, {"time": 0.0667, "x": -3.59, "y": 2.39, "curve": [0.1, -3.59, 0.133, 2.99, 0.1, 2.39, 0.133, 1.32]}, {"time": 0.1667, "x": 2.99, "y": 0.3, "curve": [0.189, 2.99, 0.211, -2.69, 0.189, -0.38, 0.211, -2.69]}, {"time": 0.2333, "x": -2.69, "y": -2.69, "curve": [0.289, -2.69, 0.344, -0.68, 0.289, -2.69, 0.344, 3.89]}, {"time": 0.4, "x": 0.6, "y": 3.89, "curve": [0.422, 1.11, 0.444, 2.69, 0.422, 3.89, 0.444, 0]}, {"time": 0.4667, "x": 2.69, "y": -1.2, "curve": [0.478, 2.69, 0.489, -4.19, 0.478, -1.8, 0.489, -1.8]}, {"time": 0.5, "x": -4.19, "y": -1.8, "curve": [0.533, -4.19, 0.567, -0.2, 0.533, -1.8, 0.567, 3.59]}, {"time": 0.6, "x": 0.9, "y": 3.59, "curve": [0.633, 1.99, 0.667, 1.8, 0.633, 3.59, 0.667, -2.99]}, {"time": 0.7, "x": 2.39, "y": -2.99, "curve": [0.733, 2.99, 0.767, 4.49, 0.733, -2.99, 0.767, 0.42]}, {"time": 0.8, "x": 4.49, "y": 1.5}], "scale": [{"curve": [0.133, 1, 0.267, 1.078, 0.133, 1, 0.267, 1.078]}, {"time": 0.4, "x": 1.078, "y": 1.078, "curve": [0.533, 1.078, 0.667, 1, 0.533, 1.078, 0.667, 1]}, {"time": 0.8}]}, "eye 1": {"scale": [{"x": 0.976, "y": 0.976, "curve": [0.033, 0.976, 0.068, 1.044, 0.033, 0.976, 0.068, 1.044]}, {"time": 0.1, "x": 1.044, "y": 1.044, "curve": [0.122, 1.044, 0.145, 0.976, 0.122, 1.044, 0.145, 0.976]}, {"time": 0.1667, "x": 0.976, "y": 0.976, "curve": [0.199, 0.976, 0.234, 1.044, 0.199, 0.976, 0.234, 1.044]}, {"time": 0.2667, "x": 1.044, "y": 1.044, "curve": [0.288, 1.044, 0.312, 0.976, 0.288, 1.044, 0.312, 0.976]}, {"time": 0.3333, "x": 0.976, "y": 0.976, "curve": [0.366, 0.976, 0.401, 1.044, 0.366, 0.976, 0.401, 1.044]}, {"time": 0.4333, "x": 1.044, "y": 1.044, "curve": [0.455, 1.044, 0.478, 0.976, 0.455, 1.044, 0.478, 0.976]}, {"time": 0.5, "x": 0.976, "y": 0.976, "curve": [0.532, 0.976, 0.534, 1.044, 0.532, 0.976, 0.534, 1.044]}, {"time": 0.5667, "x": 1.044, "y": 1.044, "curve": [0.588, 1.044, 0.612, 0.976, 0.588, 1.044, 0.612, 0.976]}, {"time": 0.6333, "x": 0.976, "y": 0.976, "curve": [0.667, 0.976, 0.7, 1.044, 0.667, 0.976, 0.7, 1.044]}, {"time": 0.7333, "x": 1.044, "y": 1.044, "curve": [0.756, 1.044, 0.778, 0.976, 0.756, 1.044, 0.778, 0.976]}, {"time": 0.8, "x": 0.976, "y": 0.976}]}, "eye 2": {"scale": [{"x": 1.026, "y": 1.026, "curve": [0.011, 1.036, 0.022, 1.044, 0.011, 1.036, 0.022, 1.044]}, {"time": 0.0333, "x": 1.044, "y": 1.044, "curve": [0.055, 1.044, 0.078, 0.976, 0.055, 1.044, 0.078, 0.976]}, {"time": 0.1, "x": 0.976, "y": 0.976, "curve": [0.132, 0.976, 0.168, 1.044, 0.132, 0.976, 0.168, 1.044]}, {"time": 0.2, "x": 1.044, "y": 1.044, "curve": [0.222, 1.044, 0.245, 0.976, 0.222, 1.044, 0.245, 0.976]}, {"time": 0.2667, "x": 0.976, "y": 0.976, "curve": [0.299, 0.976, 0.334, 1.044, 0.299, 0.976, 0.334, 1.044]}, {"time": 0.3667, "x": 1.044, "y": 1.044, "curve": [0.388, 1.044, 0.412, 0.976, 0.388, 1.044, 0.412, 0.976]}, {"time": 0.4333, "x": 0.976, "y": 0.976, "curve": [0.466, 0.976, 0.468, 1.044, 0.466, 0.976, 0.468, 1.044]}, {"time": 0.5, "x": 1.044, "y": 1.044, "curve": [0.522, 1.044, 0.545, 0.976, 0.522, 1.044, 0.545, 0.976]}, {"time": 0.5667, "x": 0.976, "y": 0.976, "curve": [0.6, 0.976, 0.633, 1.044, 0.6, 0.976, 0.633, 1.044]}, {"time": 0.6667, "x": 1.044, "y": 1.044, "curve": [0.689, 1.044, 0.711, 0.976, 0.689, 1.044, 0.711, 0.976]}, {"time": 0.7333, "x": 0.976, "y": 0.976, "curve": [0.755, 0.976, 0.778, 1.006, 0.755, 0.976, 0.778, 1.006]}, {"time": 0.8, "x": 1.026, "y": 1.026}]}, "pupil2": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.1, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.1667, "x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.2333, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.3, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.3333, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.4, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.4333, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.5667, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.6, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.6667, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.7333, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.8}], "scale": [{}, {"time": 0.0667, "x": 1.488, "y": 1.488}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.4, "x": 1.209, "y": 1.209}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7333, "x": 1.209, "y": 1.209}, {"time": 0.7667}]}, "pupil": {"translate": [{"x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.0667, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.1333, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.1667, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.2333, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.2667, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.4333, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.5, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.5667, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.7333, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.8, "x": 0.09, "y": -2.27}], "scale": [{"curve": "stepped"}, {"time": 0.1}, {"time": 0.1667, "x": 1.209, "y": 1.209}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5667, "x": 1.488, "y": 1.488}, {"time": 0.6333}]}, "Smoke_9": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -54.71}], "translate": [{}, {"time": 0.0333, "x": 34.77, "y": 7.93, "curve": [0.211, 220.21, 0.389, 117.02, 0.211, 50.22, 0.389, 28.14]}, {"time": 0.5667, "x": 117.02, "y": 28.14}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_1": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 56.04}], "translate": [{}, {"time": 0.0333, "x": -12.81, "y": 15.25, "curve": [0.167, -64.05, 0.3, -129.93, 0.167, 76.25, 0.3, 120.78]}, {"time": 0.4333, "x": -129.93, "y": 120.78}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.189, 2, 0.344, 0, 0.189, 2, 0.344, 0]}, {"time": 0.5, "x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -57.38}], "translate": [{}, {"time": 0.0333, "x": 51.24, "y": 40.87, "curve": [0.211, 125.25, 0.389, 106.75, 0.211, 170.19, 0.389, 137.86]}, {"time": 0.5667, "x": 106.75, "y": 137.86}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_3": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{}, {"time": 0.0333, "x": 12.2, "y": 1.83, "curve": [0.211, 96.79, 0.389, 108.58, 0.211, -63.24, 0.389, -62.83]}, {"time": 0.5667, "x": 108.58, "y": -62.83}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -28.45}], "translate": [{}, {"time": 0.0333, "x": -25.62, "y": -27.45, "curve": [0.211, -73.67, 0.389, -59.49, 0.211, -65.72, 0.389, -112.82]}, {"time": 0.5667, "x": -58.74, "y": -117.91, "curve": [0.589, -58.65, 0.611, -71.98, 0.589, -118.54, 0.611, -96.99]}, {"time": 0.6333, "x": -71.98, "y": -96.99}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.222, 2, 0.411, 0, 0.222, 2, 0.411, 0]}, {"time": 0.6, "x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 24.28}], "translate": [{}, {"time": 0.0333, "x": -28.67, "y": 32.94, "curve": [0.211, -149.04, 0.389, -132.98, 0.211, 111.02, 0.389, 111.02]}, {"time": 0.5667, "x": -132.98, "y": 111.02}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.189, 2, 0.344, 0, 0.189, 2, 0.344, 0]}, {"time": 0.5, "x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -5.43}], "translate": [{}, {"time": 0.0333, "x": 4.88, "y": 54.29, "curve": [0.211, 4.88, 0.389, -26.84, 0.211, 189.3, 0.389, 155.55]}, {"time": 0.5667, "x": -26.84, "y": 155.55}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -32.21}], "translate": [{}, {"time": 0.0333, "x": -44.53, "y": 6.71, "curve": [0.211, -243.8, 0.389, -116.35, 0.211, 6.71, 0.389, 21.36]}, {"time": 0.5667, "x": -116.35, "y": 21.36}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 50.3}], "translate": [{}, {"time": 0.0333, "x": 11.59, "y": -22.57, "curve": [0.211, 11.59, 0.389, -3.05, 0.211, -112.04, 0.389, -152.5]}, {"time": 0.5667, "x": -3.05, "y": -152.5}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.2, 2, 0.367, 0, 0.2, 2, 0.367, 0]}, {"time": 0.5333, "x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": 12.37}], "translate": [{}, {"time": 0.0333, "x": -32.94, "y": -12.2, "curve": [0.178, -158.5, 0.322, -133.4, 0.178, -42.6, 0.322, 11.46]}, {"time": 0.4667, "x": -133.4, "y": 11.46}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{}, {"time": 0.0333, "x": -5.49, "y": 13.42, "curve": [0.156, -25.62, 0.278, -43.31, 0.156, 13.42, 0.278, -123.83]}, {"time": 0.4, "x": -43.31, "y": -123.83}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.178, 2, 0.322, 0, 0.178, 2, 0.322, 0]}, {"time": 0.4667, "x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{"curve": "stepped"}, {"time": 0.0333}, {"time": 0.5667, "value": -58.46}], "translate": [{}, {"time": 0.0333, "x": 42.09, "y": -1.22, "curve": [0.156, 42.09, 0.278, 125.05, 0.156, -5.69, 0.278, 86.62]}, {"time": 0.4, "x": 125.05, "y": 86.62}], "scale": [{"x": 0, "y": 0}, {"time": 0.0333, "x": 2, "y": 2, "curve": [0.211, 2, 0.389, 0, 0.211, 2, 0.389, 0]}, {"time": 0.5667, "x": 0, "y": 0}]}}}, "t1_IDLE": {"slots": {"eye 1": {"rgba": [{"color": "ffffffff"}]}, "eye 2": {"rgba": [{"color": "ffffffff"}]}, "leg 1": {"rgba": [{"color": "ffffffff"}]}, "leg 2": {"rgba": [{"color": "ffffffff"}]}, "leg 3": {"rgba": [{"color": "ffffffff"}]}, "leg 4": {"rgba": [{"color": "ffffffff"}]}, "leg 5": {"rgba": [{"color": "ffffffff"}]}, "leg 6": {"rgba": [{"color": "ffffffff"}]}, "pimples": {"rgba": [{"color": "ffffffff"}]}, "pupil": {"rgba": [{"color": "ffffffff"}]}, "pupil2": {"rgba": [{"color": "ffffffff"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_3": {"attachment": [{}]}, "Smoke_4": {"attachment": [{}]}, "Smoke_5": {"attachment": [{}]}, "Smoke_6": {"attachment": [{}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_9": {"attachment": [{}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}}, "bones": {"cntr": {"rotate": [{"value": 2.93, "curve": [0.011, 2.93, 0.023, 1.6]}, {"time": 0.0333, "value": 1, "curve": [0.067, -0.76, 0.1, -3.32]}, {"time": 0.1333, "value": -3.32, "curve": [0.178, -3.32, 0.222, 2.93]}, {"time": 0.2667, "value": 2.93, "curve": [0.311, 2.93, 0.356, -3.32]}, {"time": 0.4, "value": -3.32, "curve": [0.444, -3.32, 0.489, 2.93]}, {"time": 0.5333, "value": 2.93, "curve": [0.578, 2.93, 0.622, -3.32]}, {"time": 0.6667, "value": -3.32, "curve": [0.711, -3.32, 0.756, 2.93]}, {"time": 0.8, "value": 2.93}], "translate": [{"x": 4.49, "y": 1.5, "curve": [0.022, 4.49, 0.044, -3.59, 0.022, 2.21, 0.044, 2.39]}, {"time": 0.0667, "x": -3.59, "y": 2.39, "curve": [0.1, -3.59, 0.133, 2.99, 0.1, 2.39, 0.133, 1.32]}, {"time": 0.1667, "x": 2.99, "y": 0.3, "curve": [0.189, 2.99, 0.211, -2.69, 0.189, -0.38, 0.211, -2.69]}, {"time": 0.2333, "x": -2.69, "y": -2.69, "curve": [0.289, -2.69, 0.344, -0.68, 0.289, -2.69, 0.344, 3.89]}, {"time": 0.4, "x": 0.6, "y": 3.89, "curve": [0.422, 1.11, 0.444, 2.69, 0.422, 3.89, 0.444, 0]}, {"time": 0.4667, "x": 2.69, "y": -1.2, "curve": [0.478, 2.69, 0.489, -4.19, 0.478, -1.8, 0.489, -1.8]}, {"time": 0.5, "x": -4.19, "y": -1.8, "curve": [0.533, -4.19, 0.567, -0.2, 0.533, -1.8, 0.567, 3.59]}, {"time": 0.6, "x": 0.9, "y": 3.59, "curve": [0.633, 1.99, 0.667, 1.8, 0.633, 3.59, 0.667, -2.99]}, {"time": 0.7, "x": 2.39, "y": -2.99, "curve": [0.733, 2.99, 0.767, 4.49, 0.733, -2.99, 0.767, 0.42]}, {"time": 0.8, "x": 4.49, "y": 1.5}], "scale": [{"curve": [0.133, 1, 0.267, 1.078, 0.133, 1, 0.267, 1.078]}, {"time": 0.4, "x": 1.078, "y": 1.078, "curve": [0.533, 1.078, 0.667, 1, 0.533, 1.078, 0.667, 1]}, {"time": 0.8}]}, "eye 1": {"scale": [{"x": 0.976, "y": 0.976, "curve": [0.033, 0.976, 0.068, 1.044, 0.033, 0.976, 0.068, 1.044]}, {"time": 0.1, "x": 1.044, "y": 1.044, "curve": [0.122, 1.044, 0.145, 0.976, 0.122, 1.044, 0.145, 0.976]}, {"time": 0.1667, "x": 0.976, "y": 0.976, "curve": [0.199, 0.976, 0.234, 1.044, 0.199, 0.976, 0.234, 1.044]}, {"time": 0.2667, "x": 1.044, "y": 1.044, "curve": [0.288, 1.044, 0.312, 0.976, 0.288, 1.044, 0.312, 0.976]}, {"time": 0.3333, "x": 0.976, "y": 0.976, "curve": [0.366, 0.976, 0.401, 1.044, 0.366, 0.976, 0.401, 1.044]}, {"time": 0.4333, "x": 1.044, "y": 1.044, "curve": [0.455, 1.044, 0.478, 0.976, 0.455, 1.044, 0.478, 0.976]}, {"time": 0.5, "x": 0.976, "y": 0.976, "curve": [0.532, 0.976, 0.534, 1.044, 0.532, 0.976, 0.534, 1.044]}, {"time": 0.5667, "x": 1.044, "y": 1.044, "curve": [0.588, 1.044, 0.612, 0.976, 0.588, 1.044, 0.612, 0.976]}, {"time": 0.6333, "x": 0.976, "y": 0.976, "curve": [0.667, 0.976, 0.7, 1.044, 0.667, 0.976, 0.7, 1.044]}, {"time": 0.7333, "x": 1.044, "y": 1.044, "curve": [0.756, 1.044, 0.778, 0.976, 0.756, 1.044, 0.778, 0.976]}, {"time": 0.8, "x": 0.976, "y": 0.976}]}, "eye 2": {"scale": [{"x": 1.026, "y": 1.026, "curve": [0.011, 1.036, 0.022, 1.044, 0.011, 1.036, 0.022, 1.044]}, {"time": 0.0333, "x": 1.044, "y": 1.044, "curve": [0.055, 1.044, 0.078, 0.976, 0.055, 1.044, 0.078, 0.976]}, {"time": 0.1, "x": 0.976, "y": 0.976, "curve": [0.132, 0.976, 0.168, 1.044, 0.132, 0.976, 0.168, 1.044]}, {"time": 0.2, "x": 1.044, "y": 1.044, "curve": [0.222, 1.044, 0.245, 0.976, 0.222, 1.044, 0.245, 0.976]}, {"time": 0.2667, "x": 0.976, "y": 0.976, "curve": [0.299, 0.976, 0.334, 1.044, 0.299, 0.976, 0.334, 1.044]}, {"time": 0.3667, "x": 1.044, "y": 1.044, "curve": [0.388, 1.044, 0.412, 0.976, 0.388, 1.044, 0.412, 0.976]}, {"time": 0.4333, "x": 0.976, "y": 0.976, "curve": [0.466, 0.976, 0.468, 1.044, 0.466, 0.976, 0.468, 1.044]}, {"time": 0.5, "x": 1.044, "y": 1.044, "curve": [0.522, 1.044, 0.545, 0.976, 0.522, 1.044, 0.545, 0.976]}, {"time": 0.5667, "x": 0.976, "y": 0.976, "curve": [0.6, 0.976, 0.633, 1.044, 0.6, 0.976, 0.633, 1.044]}, {"time": 0.6667, "x": 1.044, "y": 1.044, "curve": [0.689, 1.044, 0.711, 0.976, 0.689, 1.044, 0.711, 0.976]}, {"time": 0.7333, "x": 0.976, "y": 0.976, "curve": [0.755, 0.976, 0.778, 1.006, 0.755, 0.976, 0.778, 1.006]}, {"time": 0.8, "x": 1.026, "y": 1.026}]}, "pupil2": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.1, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.1667, "x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.2333, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.3, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.3333, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.4, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.4333, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.5667, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.6, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.6667, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.7333, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.8}], "scale": [{}, {"time": 0.0667, "x": 1.488, "y": 1.488}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.4, "x": 1.209, "y": 1.209}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7333, "x": 1.209, "y": 1.209}, {"time": 0.7667}]}, "pupil": {"translate": [{"x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.0667, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.1333, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.1667, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.2333, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.2667, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.4333, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.5, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.5667, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.7333, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.8, "x": 0.09, "y": -2.27}], "scale": [{"curve": "stepped"}, {"time": 0.1}, {"time": 0.1667, "x": 1.209, "y": 1.209}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5667, "x": 1.488, "y": 1.488}, {"time": 0.6333}]}, "Smoke_9": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_1": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_3": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "t1_IDLE2": {"slots": {"eye 1": {"rgba": [{"color": "ffffffff"}]}, "eye 2": {"rgba": [{"color": "ffffffff"}]}, "leg 1": {"rgba": [{"color": "ffffffff"}]}, "leg 2": {"rgba": [{"color": "ffffffff"}]}, "leg 3": {"rgba": [{"color": "ffffffff"}]}, "leg 4": {"rgba": [{"color": "ffffffff"}]}, "leg 5": {"rgba": [{"color": "ffffffff"}]}, "leg 6": {"rgba": [{"color": "ffffffff"}]}, "pimples": {"rgba": [{"color": "ffffffff"}]}, "pupil": {"rgba": [{"color": "ffffffff"}]}, "pupil2": {"rgba": [{"color": "ffffffff"}]}, "Smoke_1": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_2": {"rgba": [{"color": "475971ff"}], "attachment": [{}]}, "Smoke_3": {"attachment": [{}]}, "Smoke_4": {"attachment": [{}]}, "Smoke_5": {"attachment": [{}]}, "Smoke_6": {"attachment": [{}]}, "Smoke_7": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_8": {"rgba": [{"color": "4b5d75ff"}], "attachment": [{}]}, "Smoke_9": {"attachment": [{}]}, "Smoke_10": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_11": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}, "Smoke_12": {"rgba": [{"color": "5b6d83ff"}], "attachment": [{}]}}, "bones": {"cntr": {"translate": [{"x": 4.49, "y": 1.5, "curve": [0.022, 4.49, 0.044, -3.59, 0.022, 2.21, 0.044, 2.39]}, {"time": 0.0667, "x": -3.59, "y": 2.39, "curve": [0.1, -3.59, 0.133, 2.99, 0.1, 2.39, 0.133, 1.32]}, {"time": 0.1667, "x": 2.99, "y": 0.3, "curve": [0.189, 2.99, 0.211, -2.69, 0.189, -0.38, 0.211, -2.69]}, {"time": 0.2333, "x": -2.69, "y": -2.69, "curve": [0.289, -2.69, 0.344, -0.68, 0.289, -2.69, 0.344, 3.89]}, {"time": 0.4, "x": 0.6, "y": 3.89, "curve": [0.422, 1.11, 0.444, 2.69, 0.422, 3.89, 0.444, 0]}, {"time": 0.4667, "x": 2.69, "y": -1.2, "curve": [0.478, 2.69, 0.489, -4.19, 0.478, -1.8, 0.489, -1.8]}, {"time": 0.5, "x": -4.19, "y": -1.8, "curve": [0.533, -4.19, 0.567, -0.2, 0.533, -1.8, 0.567, 3.59]}, {"time": 0.6, "x": 0.9, "y": 3.59, "curve": [0.633, 1.99, 0.667, 1.8, 0.633, 3.59, 0.667, -2.99]}, {"time": 0.7, "x": 2.39, "y": -2.99, "curve": [0.733, 2.99, 0.767, 4.49, 0.733, -2.99, 0.767, 0.42]}, {"time": 0.8, "x": 4.49, "y": 1.5}]}, "eye 1": {"scale": [{"x": 0.976, "y": 0.976, "curve": [0.033, 0.976, 0.068, 1.044, 0.033, 0.976, 0.068, 1.044]}, {"time": 0.1, "x": 1.044, "y": 1.044, "curve": [0.122, 1.044, 0.145, 0.976, 0.122, 1.044, 0.145, 0.976]}, {"time": 0.1667, "x": 0.976, "y": 0.976, "curve": [0.199, 0.976, 0.234, 1.044, 0.199, 0.976, 0.234, 1.044]}, {"time": 0.2667, "x": 1.044, "y": 1.044, "curve": [0.288, 1.044, 0.312, 0.976, 0.288, 1.044, 0.312, 0.976]}, {"time": 0.3333, "x": 0.976, "y": 0.976, "curve": [0.366, 0.976, 0.401, 1.044, 0.366, 0.976, 0.401, 1.044]}, {"time": 0.4333, "x": 1.044, "y": 1.044, "curve": [0.455, 1.044, 0.478, 0.976, 0.455, 1.044, 0.478, 0.976]}, {"time": 0.5, "x": 0.976, "y": 0.976, "curve": [0.532, 0.976, 0.534, 1.044, 0.532, 0.976, 0.534, 1.044]}, {"time": 0.5667, "x": 1.044, "y": 1.044, "curve": [0.588, 1.044, 0.612, 0.976, 0.588, 1.044, 0.612, 0.976]}, {"time": 0.6333, "x": 0.976, "y": 0.976, "curve": [0.667, 0.976, 0.7, 1.044, 0.667, 0.976, 0.7, 1.044]}, {"time": 0.7333, "x": 1.044, "y": 1.044, "curve": [0.756, 1.044, 0.778, 0.976, 0.756, 1.044, 0.778, 0.976]}, {"time": 0.8, "x": 0.976, "y": 0.976}]}, "eye 2": {"scale": [{"x": 1.026, "y": 1.026, "curve": [0.011, 1.036, 0.022, 1.044, 0.011, 1.036, 0.022, 1.044]}, {"time": 0.0333, "x": 1.044, "y": 1.044, "curve": [0.055, 1.044, 0.078, 0.976, 0.055, 1.044, 0.078, 0.976]}, {"time": 0.1, "x": 0.976, "y": 0.976, "curve": [0.132, 0.976, 0.168, 1.044, 0.132, 0.976, 0.168, 1.044]}, {"time": 0.2, "x": 1.044, "y": 1.044, "curve": [0.222, 1.044, 0.245, 0.976, 0.222, 1.044, 0.245, 0.976]}, {"time": 0.2667, "x": 0.976, "y": 0.976, "curve": [0.299, 0.976, 0.334, 1.044, 0.299, 0.976, 0.334, 1.044]}, {"time": 0.3667, "x": 1.044, "y": 1.044, "curve": [0.388, 1.044, 0.412, 0.976, 0.388, 1.044, 0.412, 0.976]}, {"time": 0.4333, "x": 0.976, "y": 0.976, "curve": [0.466, 0.976, 0.468, 1.044, 0.466, 0.976, 0.468, 1.044]}, {"time": 0.5, "x": 1.044, "y": 1.044, "curve": [0.522, 1.044, 0.545, 0.976, 0.522, 1.044, 0.545, 0.976]}, {"time": 0.5667, "x": 0.976, "y": 0.976, "curve": [0.6, 0.976, 0.633, 1.044, 0.6, 0.976, 0.633, 1.044]}, {"time": 0.6667, "x": 1.044, "y": 1.044, "curve": [0.689, 1.044, 0.711, 0.976, 0.689, 1.044, 0.711, 0.976]}, {"time": 0.7333, "x": 0.976, "y": 0.976, "curve": [0.755, 0.976, 0.778, 1.006, 0.755, 0.976, 0.778, 1.006]}, {"time": 0.8, "x": 1.026, "y": 1.026}]}, "pupil2": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.1, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.1667, "x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.2333, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.3, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.3333, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.4, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.4333, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.5667, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.6, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.6667, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.7333, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.8}], "scale": [{}, {"time": 0.0667, "x": 1.488, "y": 1.488}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.4, "x": 1.209, "y": 1.209}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7333, "x": 1.209, "y": 1.209}, {"time": 0.7667}]}, "pupil": {"translate": [{"x": 0.09, "y": -2.27, "curve": "stepped"}, {"time": 0.0667, "x": -3.11, "y": 0.1, "curve": "stepped"}, {"time": 0.1333, "x": -3.16, "y": -2.38, "curve": "stepped"}, {"time": 0.1667, "x": -0.33, "y": -2.4, "curve": "stepped"}, {"time": 0.2333, "x": -1.8, "y": -0.41, "curve": "stepped"}, {"time": 0.2667, "x": -1.43, "y": -2.57, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4, "x": -1.35, "y": -3.5, "curve": "stepped"}, {"time": 0.4333, "x": -3.49, "y": -1.29, "curve": "stepped"}, {"time": 0.5, "x": 0.49, "y": -2.45, "curve": "stepped"}, {"time": 0.5667, "x": -2.02, "y": -0.34, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7, "x": 2.6, "y": 1.99, "curve": "stepped"}, {"time": 0.7333, "x": -2.99, "y": -0.47, "curve": "stepped"}, {"time": 0.8, "x": 0.09, "y": -2.27}], "scale": [{"curve": "stepped"}, {"time": 0.1}, {"time": 0.1667, "x": 1.209, "y": 1.209}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5667, "x": 1.488, "y": 1.488}, {"time": 0.6333}]}, "Smoke_9": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_1": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_2": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_3": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_4": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_5": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_6": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_7": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_8": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_10": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_11": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}, "Smoke_12": {"rotate": [{}], "translate": [{}], "scale": [{"x": 0, "y": 0}]}}}, "t2_IDLE_legs": {"bones": {"leg 4": {"translate": [{"curve": [0.155, 0, 0.612, -14.94, 0.155, 0, 0.612, -21.28]}, {"time": 0.7667, "x": -14.94, "y": -21.28, "curve": [1.015, -14.94, 1.752, 0, 1.015, -21.28, 1.752, 0]}, {"time": 2}]}, "leg 1": {"translate": [{"x": 7.9, "y": 5.8, "curve": [0.172, 8.94, 0.31, 10.67, 0.172, 6.64, 0.31, 7.49]}, {"time": 0.4, "x": 10.67, "y": 7.49, "curve": [0.545, 10.67, 0.989, 0, 0.545, 7.49, 0.989, 0]}, {"time": 1.1333, "curve": [1.302, 0, 1.68, 5.95, 1.302, 0, 1.68, 4.21]}, {"time": 2, "x": 7.9, "y": 5.8}]}, "leg 2": {"translate": [{"x": 3.67, "y": -0.37, "curve": [0.211, 1.78, 0.429, 0, 0.211, -0.18, 0.429, 0]}, {"time": 0.5333, "curve": [0.709, 0, 1.224, 16.49, 0.709, 0, 1.224, -6.41]}, {"time": 1.4, "x": 16.49, "y": -6.41, "curve": [1.523, 16.49, 1.758, 5.88, 1.523, -6.41, 1.758, -0.59]}, {"time": 2, "x": 3.67, "y": -0.37}]}, "leg 3": {"translate": [{"x": 3.81, "y": -5.02, "curve": [0.263, 6.3, 0.535, 16.1, 0.263, -8.3, 0.535, -15.84]}, {"time": 0.6667, "x": 16.1, "y": -15.84, "curve": [0.832, 16.1, 1.335, 0, 0.832, -15.84, 1.335, 0]}, {"time": 1.5, "curve": [1.606, 0, 1.79, 1.8, 1.606, 0, 1.79, -2.38]}, {"time": 2, "x": 3.81, "y": -5.02}]}, "leg 5": {"translate": [{"x": -16.43, "y": -9.48, "curve": [0.4, -13.69, 0.973, 0, 0.4, -8.71, 0.973, 0]}, {"time": 1.2, "curve": [1.334, 0, 1.732, -17.11, 1.334, 0, 1.732, -9.67]}, {"time": 1.8667, "x": -17.11, "y": -9.67, "curve": [1.908, -17.11, 1.94, -16.84, 1.908, -9.67, 1.94, -9.59]}, {"time": 2, "x": -16.43, "y": -9.48}]}, "leg 6": {"translate": [{"x": -3.48, "y": 17.77, "curve": [0.17, -4.51, 0.313, -8.16, 0.17, 19.44, 0.313, 23.05]}, {"time": 0.4, "x": -8.16, "y": 23.05, "curve": [0.586, -8.16, 1.147, 0, 0.586, 23.05, 1.147, 0]}, {"time": 1.3333, "curve": [1.464, 0, 1.745, -1.93, 1.464, 0, 1.745, 15.27]}, {"time": 2, "x": -3.48, "y": 17.77}]}}}, "t2_IDLE_legs2": {"bones": {"leg 4": {"translate": [{"curve": [0.078, 0, 0.322, -14.94, 0.078, 0, 0.322, -21.28]}, {"time": 0.4, "x": -14.94, "y": -21.28, "curve": [0.524, -14.94, 0.876, 0, 0.524, -21.28, 0.876, 0]}, {"time": 1}]}, "leg 1": {"translate": [{"x": 7.9, "y": 5.8, "curve": [0.086, 8.94, 0.155, 10.67, 0.086, 6.64, 0.155, 7.49]}, {"time": 0.2, "x": 10.67, "y": 7.49, "curve": [0.272, 10.67, 0.494, 0, 0.272, 7.49, 0.494, 0]}, {"time": 0.5667, "curve": [0.651, 0, 0.84, 5.95, 0.651, 0, 0.84, 4.21]}, {"time": 1, "x": 7.9, "y": 5.8}]}, "leg 2": {"translate": [{"x": 3.67, "y": -0.37, "curve": [0.106, 1.78, 0.214, 0, 0.106, -0.18, 0.214, 0]}, {"time": 0.2667, "curve": [0.355, 0, 0.612, 16.49, 0.355, 0, 0.612, -6.41]}, {"time": 0.7, "x": 16.49, "y": -6.41, "curve": [0.761, 16.49, 0.879, 5.88, 0.761, -6.41, 0.879, -0.59]}, {"time": 1, "x": 3.67, "y": -0.37}]}, "leg 3": {"translate": [{"x": 3.81, "y": -5.02, "curve": [0.132, 6.3, 0.268, 16.1, 0.132, -8.3, 0.268, -15.84]}, {"time": 0.3333, "x": 16.1, "y": -15.84, "curve": [0.416, 16.1, 0.684, 0, 0.416, -15.84, 0.684, 0]}, {"time": 0.7667, "curve": [0.82, 0, 0.895, 1.8, 0.82, 0, 0.895, -2.38]}, {"time": 1, "x": 3.81, "y": -5.02}]}, "leg 5": {"translate": [{"x": -16.43, "y": -9.48, "curve": [0.2, -13.69, 0.486, 0, 0.2, -8.71, 0.486, 0]}, {"time": 0.6, "curve": [0.667, 0, 0.866, -17.11, 0.667, 0, 0.866, -9.67]}, {"time": 0.9333, "x": -17.11, "y": -9.67, "curve": [0.954, -17.11, 0.97, -16.84, 0.954, -9.67, 0.97, -9.59]}, {"time": 1, "x": -16.43, "y": -9.48}]}, "leg 6": {"translate": [{"x": -3.48, "y": 17.77, "curve": [0.085, -4.51, 0.157, -8.16, 0.085, 19.44, 0.157, 23.05]}, {"time": 0.2, "x": -8.16, "y": 23.05, "curve": [0.293, -8.16, 0.574, 0, 0.293, 23.05, 0.574, 0]}, {"time": 0.6667, "curve": [0.732, 0, 0.872, -1.93, 0.732, 0, 0.872, 15.27]}, {"time": 1, "x": -3.48, "y": 17.77}]}}}, "t3_eyes": {"bones": {"eye1_blink": {"scale": [{"curve": "stepped"}, {"time": 0.7, "curve": [0.722, 1, 0.755, 1.355, 0.722, 1, 0.754, 0.427]}, {"time": 0.7667, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 1.488, "y": 0, "curve": [0.83, 1.319, 0.889, 1, 0.816, 0.46, 0.889, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.3667, "curve": [2.389, 1, 2.421, 1.355, 2.389, 1, 2.42, 0.427]}, {"time": 2.4333, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 1.488, "y": 0, "curve": [2.497, 1.319, 2.556, 1, 2.482, 0.46, 2.556, 1]}, {"time": 2.6, "curve": "stepped"}, {"time": 2.7, "curve": [2.722, 1, 2.755, 1.355, 2.722, 1, 2.754, 0.427]}, {"time": 2.7667, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 1.488, "y": 0, "curve": [2.83, 1.319, 2.889, 1, 2.816, 0.46, 2.889, 1]}, {"time": 2.9333, "curve": "stepped"}, {"time": 5.6667}]}, "eye2_blink": {"scale": [{"curve": "stepped"}, {"time": 0.7667, "curve": [0.789, 1, 0.821, 1.355, 0.789, 1, 0.82, 0.427]}, {"time": 0.8333, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 1.488, "y": 0, "curve": [0.897, 1.319, 0.956, 1, 0.882, 0.46, 0.956, 1]}, {"time": 1, "curve": "stepped"}, {"time": 2.3333, "curve": [2.356, 1, 2.388, 1.355, 2.356, 1, 2.387, 0.427]}, {"time": 2.4, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 2.4333, "x": 1.488, "y": 0, "curve": [2.464, 1.319, 2.522, 1, 2.449, 0.46, 2.522, 1]}, {"time": 2.5667, "curve": "stepped"}, {"time": 2.7, "curve": [2.722, 1, 2.755, 1.355, 2.722, 1, 2.754, 0.427]}, {"time": 2.7667, "x": 1.488, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 1.488, "y": 0, "curve": [2.83, 1.319, 2.889, 1, 2.816, 0.46, 2.889, 1]}, {"time": 2.9333, "curve": "stepped"}, {"time": 5.6667}]}}}}}