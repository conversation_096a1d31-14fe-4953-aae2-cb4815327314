import fs from 'fs';
import path from 'path';
import { compileRaw } from 'pbf/compile';
import schema from 'protocol-buffers-schema';

// Path to your .proto files
const protoDir = './protos';
const outputDir = './src/protos';

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to compile .proto files
function compileProtoFile(protoFile) {
  // Read the .proto file
  const protoPath = path.join(protoDir, protoFile);
  const protoContent = fs.readFileSync(protoPath, 'utf8');

  // Parse the schema
  const parsedSchema = schema.parse(protoContent);

  // Compile the schema to JavaScript code
  let compiledCode = "/* eslint-disable */\n// @ts-nocheck\n\n" + compileRaw(parsedSchema).toString();

  compiledCode = compiledCode.replace(/end\)/g, 'end = undefined)');

  // Write the compiled JavaScript code to the output directory
  const outputFilePath = path.join(outputDir, `${path.basename(protoFile, '.proto')}.ts`);

  fs.writeFileSync(outputFilePath, compiledCode, 'utf8');

  console.log(`Compiled ${protoFile} -> ${outputFilePath}`);
}

// Compile all .proto files in the protoDir
fs.readdirSync(protoDir).forEach((file) => {
  if (path.extname(file) === '.proto') {
    compileProtoFile(file);
  }
});
