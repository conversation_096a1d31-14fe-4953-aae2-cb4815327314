import vue from '@vitejs/plugin-vue'
import axios from 'axios'
import fs from 'fs'
import { fileURLToPath, URL } from 'node:url'
import path from 'path'
import { obfuscator } from 'rollup-obfuscator'
import { visualizer } from 'rollup-plugin-visualizer'
import { defineConfig, loadEnv } from 'vite'

export function getRandomNumber(): number {
  const high = Math.floor(Math.random() * 0xfffff) // Higher 20 bits (keeping within 53-bit limit)
  const low = Math.floor(Math.random() * 0xffffffff) // Lower 32 bits

  return high * 0x100000000 + low // Shifting by 32 bits and adding the lower part
}

const env = loadEnv(process.env.NODE_ENV!, process.cwd())
const __DEV__ = env.VITE_APP_ENV === 'dev'
const mapGenConfig: { weights: any[]; procedural: any[]; composition: any[] } = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, `./mapgen/config-main.json`), 'utf-8')
)
const tutorialConfig: { weights: any[]; procedural: any[]; composition: any[] } = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, `./mapgen/config-tutorial.json`), 'utf-8')
)
const mobsTutorialConfig: { weights: any[]; procedural: any[]; composition: any[] } = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, `./mapgen/config-tutorial-mobs.json`), 'utf-8')
)
const league3Config: { weights: any[]; procedural: any[]; composition: any[] } = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, `./mapgen/config-league-3.json`), 'utf-8')
)
const chunkNames: Record<number, string | undefined> = {}

if (!__DEV__) {
  for (const config of mapGenConfig.composition) {
    config.stringName = undefined
  }
} else {
  for (const config of mapGenConfig.composition) {
    chunkNames[config.id as number] = config.stringName
  }
}

if (!__DEV__) {
  for (const config of tutorialConfig.composition) {
    config.stringName = undefined
  }
} else {
  for (const config of tutorialConfig.composition) {
    chunkNames[config.id as number] = config.stringName
  }
}

if (!__DEV__) {
  for (const config of mobsTutorialConfig.composition) {
    config.stringName = undefined
  }
} else {
  for (const config of mobsTutorialConfig.composition) {
    chunkNames[config.id as number] = config.stringName
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    __DEV__,
    __GTAG__: JSON.stringify(env.VITE_GTM_ID),
    __IS_TEST__: process.env.NODE_ENV === 'test',
    __GAME_CONFIG_DATA__: mapGenConfig,
    __GAME_CHUNK_NAMES__: chunkNames,
    __TUTORIAL_CONFIG_DATA__: tutorialConfig,
    __MOBS_TUTORIAL_CONFIG_DATA__: mobsTutorialConfig,
    __LEAGUE_3_CONFIG_DATA__: league3Config,
    __ENV__: JSON.stringify(env.VITE_APP_ENV),
    __VERSION__: JSON.stringify(env.VITE_CLIENT_VERSION)
  },
  plugins: [
    vue(),
    visualizer({
      filename: './dist/stats.html',
      open: true
    }),
    {
      name: 'fetch-data-plugin',
      async configResolved() {
        const leaguesUrl = env.VITE_PRELOAD_SERVER_URL + '/api/v1/leagues/list'
        fs.writeFileSync(
          './src/preload/leagues-list.json',
          JSON.stringify((await axios.get(leaguesUrl)).data)
        )
      }
    },
    !__DEV__ &&
      obfuscator({
        compact: true, // Compresses the code by removing unnecessary whitespace and line breaks
        controlFlowFlattening: false, // Disabled for performance (this is the most performance-heavy obfuscation feature)
        controlFlowFlatteningThreshold: 0, // Ensure control flow flattening doesn't run (even if accidentally enabled)

        deadCodeInjection: false, // Disabled to avoid bloating the code with non-functional logic
        deadCodeInjectionThreshold: 0, // No dead code insertion for better performance

        debugProtection: false, // Disabled for performance; enables anti-debugging if security requires it
        debugProtectionInterval: 0, // Ensure debug protection is off

        disableConsoleOutput: true, // Obfuscates and disables `console` functions (good for security)

        forceTransformStrings: [], // No specific strings forced to transform

        identifierNamesCache: null, // No identifier cache to speed up the obfuscation process
        identifierNamesGenerator: 'mangled', // Use 'mangled' for identifier names, better for performance than 'hexadecimal'

        identifiersDictionary: [], // Let the obfuscator handle identifier naming automatically
        identifiersPrefix: 'obf_', // Prefix to avoid name collisions

        ignoreImports: true, // Skip obfuscation of imported libraries to improve performance

        inputFileName: '', // Optional file name, usually auto-handled by the tool

        log: false, // Disable logging for faster builds
        numbersToExpressions: false, // Disable number-to-expression obfuscation for better performance

        optionsPreset: 'low-obfuscation', // Use a low-obfuscation preset for a good balance between speed and security

        renameGlobals: false, // Avoid renaming global variables for compatibility and performance
        renameProperties: false, // Avoid renaming properties to prevent potential performance issues

        reservedNames: [],
        reservedStrings: [], // No reserved strings

        seed: getRandomNumber(), // Fixed seed for consistent obfuscation results
        selfDefending: false, // Self-defending is performance-heavy, so disable it for better performance

        simplify: true, // Simplify the control flow for faster execution

        sourceMap: false, // Disable source maps to avoid exposing original code
        sourceMapBaseUrl: '',
        sourceMapFileName: '',
        sourceMapMode: 'separate',
        sourceMapSourcesMode: 'sources',

        splitStrings: false, // Disabled for performance; splitting strings increases complexity
        splitStringsChunkLength: 0,

        stringArray: true, // Obfuscate strings by moving them to a separate array
        stringArrayCallsTransform: false, // Disable for performance, as it adds complexity to string calls
        stringArrayCallsTransformThreshold: 0, // Ensure no string call transformation

        stringArrayEncoding: ['rc4'], // Use RC4 for lightweight encoding (better than base64 for performance)
        stringArrayIndexesType: ['hexadecimal-numeric-string'], // Hexadecimal indexing for arrays
        stringArrayIndexShift: false, // Disable index shifting for performance
        stringArrayRotate: false, // Disable array rotation for better performance
        stringArrayShuffle: true, // Enable shuffling to protect the string array without heavy performance hit
        stringArrayWrappersChainedCalls: false, // Disable for performance
        stringArrayWrappersCount: 1, // Minimal wrappers to reduce performance overhead
        stringArrayWrappersParametersMaxCount: 2, // Minimal parameters for wrappers to boost performance
        stringArrayWrappersType: 'function', // Simplest wrapper type for performance

        stringArrayThreshold: 0.75, // Obfuscate 75% of the strings for a balance between security and performance

        target: 'browser', // Target environment is the browser (adjust based on deployment)
        transformObjectKeys: false, // Avoid transforming object keys for performance
        unicodeEscapeSequence: false, // Disable Unicode escape sequences for better performance
        include: [
          'src/game/core/MapGen/MapGenerator.ts',
          'src/game/core/MapGen/GameSessionManager.ts'
        ]
      })
  ],
  build: {
    rollupOptions: {
      output: {
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name].[hash].js',
        entryFileNames: 'assets/[name].[hash].js',
        manualChunks(id) {
          if (id.includes('src/services/auth.ts') || id.includes('node_modules/@telegram-apps')) {
            return 'auth'
          }
          if (id.includes('phaser')) {
            return 'phaser'
          }
          if (id.includes('esotericsoftware')) {
            return 'spine'
          }
          if (id.includes('datadog')) {
            return 'datadog'
          }
          if (id.includes('node_modules')) {
            return 'vendor'
          }
          if (id.includes('src')) {
            return 'app'
          }
        }
      }
    },
    assetsInlineLimit: 0
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  preview: {
    port: 3000
  },
  server: {
    host: '127.0.0.1',
    port: 3000,
    proxy: {
      '/api': {
        target: env.VITE_PROXY_SERVER_URL!,
        changeOrigin: true
      }
    },
    hmr: {
      protocol: 'wss',
      path: 'hrm'
    },
    allowedHosts: true
  },
  optimizeDeps: {
    exclude: ['@tanstack/vue-query']
  }
})
