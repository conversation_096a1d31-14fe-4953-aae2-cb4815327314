/**
 * should be same or more than the value
 * of the transition animation delay in the css
 * FadeTransition component
 **/
export const MIN_TRANSITION_ANIMATION_DELAY = 600

export const MAIN_CHANNEL_LINK = 'https://t.me/+xFacQq4heG8zNjQ8'
export const SUPPORT_LINK = 'https://t.me/unijump_support'
export const FAQ_LINK = 'https://t.me/unijump/71'
export const COMMUNITY_CHAT_LINK = __DEV__
  ? 'https://t.me/+7hET4UYMhspiMjQy'
  : 'https://t.me/+P6LWSN46rOtiNmMy'
export const BOOST_LINK = 'https://t.me/boost?c=2266027272'
export const X_LINK = 'https://x.com/uni_jump'

export const TON_WALLET_ADDRESS = import.meta.env.VITE_MAIN_WALLET_ADDR

export const CLOUD_STORAGE_KEYS = {
  isControlMethodGyroscope: 'selectedControlMethod'
}
