import tonImage from '@/assets/images/temp/currency/ton.png'

export const CURRENCY_IMAGES: Record<string, string> = {
  ton: tonImage
}

export const CURRENCY_NAMES: Record<string, string> = {
  ton: 'toncoin'
}

export const CURRENCY_MULTIPLIER: Record<string, number> = {
  ton: 1e9
}

export const TON_TRUNCATE_INDEX = 100000

export function getCurrencyRealAmount(amount: number, currency: string) {
  return amount / (CURRENCY_MULTIPLIER[currency] || 1)
}

export function getCurrencyFormattedAmount(amount: number, currency: string) {
  return Math.floor(amount * (CURRENCY_MULTIPLIER[currency] || 1))
}
