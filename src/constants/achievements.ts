import boosterHigh from '@/assets/images/temp/achievements/booster-high.png'
import booster from '@/assets/images/temp/achievements/booster.png'
import brokePlatforms from '@/assets/images/temp/achievements/broke-platforms.png'
import getHeight from '@/assets/images/temp/achievements/get-height.png'
import inviteReferrals from '@/assets/images/temp/achievements/invite-referrals.png'
import jumpOnMonsters from '@/assets/images/temp/achievements/jump-on-monsters.png'
import jumpOnSpring from '@/assets/images/temp/achievements/jump-on-spring.png'
import jumpOnTrampoline from '@/assets/images/temp/achievements/jump-on-trampoline.png'
import jump from '@/assets/images/temp/achievements/jump.png'
import killMonsters from '@/assets/images/temp/achievements/kill-monsters.png'
import levelFrame1 from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-1.png'
import levelFrame2 from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-2.png'
import levelFrame3 from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-3.png'
import levelFrame4 from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-4.png'
import levelFrame5 from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-5.png'
import propeller from '@/assets/images/temp/achievements/propeller.png'
import revive from '@/assets/images/temp/achievements/revive.png'
import score from '@/assets/images/temp/achievements/score.png'
import shootMonsters from '@/assets/images/temp/achievements/shoot-monsters.png'
import spendSoft from '@/assets/images/temp/achievements/spend-soft.png'
import unlockSkins from '@/assets/images/temp/achievements/unlock-skins.png'

export type AchievementName = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16

export const ACHIEVEMENTS_ORDER: Array<AchievementName> = [
  13, 3, 1, 2, 12, 4, 8, 5, 6, 7, 9, 10, 11, 14, 15, 16
]

export const ACHIEVEMENTS_IMAGES: Record<AchievementName, string> = {
  1: inviteReferrals,
  2: revive,
  3: score,
  4: spendSoft,
  5: jumpOnMonsters,
  6: shootMonsters,
  7: boosterHigh,
  8: killMonsters,
  9: brokePlatforms,
  10: booster,
  11: propeller,
  12: unlockSkins,
  13: getHeight,
  14: jump,
  15: jumpOnTrampoline,
  16: jumpOnSpring
}

export const ACHIEVEMENTS_LEVELS_FRAMES: Array<string> = [
  levelFrame1,
  levelFrame2,
  levelFrame3,
  levelFrame4,
  levelFrame5
]
