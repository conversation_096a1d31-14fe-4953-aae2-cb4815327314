export {}

declare global {
  declare const __DEV__: boolean
  declare const __SHOP_ITEMS__: any
  declare const __LEAGUES_LIST__: any
  declare const __GTAG__: string
  declare const __IS_TEST__: boolean
  declare const __GAME_CONFIG_DATA__: any
  declare const __TUTORIAL_CONFIG_DATA__: any
  declare const __MOBS_TUTORIAL_CONFIG_DATA__: any
  declare const __LEAGUE_3_CONFIG_DATA__: any
  declare let __GAME_CHUNK_NAMES__: Record<number, string | undefined>
  declare const __VERSION__: string
  declare const __ENV__: string

  interface Window {
    __AUTH__: true | undefined
    gtag: (string, string, object) => void
  }

  namespace NodeJS {
    interface ProcessEnv {
      VITE_APP_ENV: 'dev' | 'production'
    }
  }
}
