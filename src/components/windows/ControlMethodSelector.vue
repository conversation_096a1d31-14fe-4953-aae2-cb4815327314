<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import VButton from '@/components/UI/VButton.vue'
import { computed, ref } from 'vue'
import phoneImage from '@/assets/images/temp/tutorial/move-tutorial.png'
import swipeImage from '@/assets/images/temp/tutorial/swipe-tutorial.png'
import { useControlMethodStore } from '@/stores/controlMethodStore'
import platform from '@/assets/sprites/environment/platforms/static.png'
import { SKIN_ID_TO_IMAGE, DEFAULT_SKIN_ID } from '@/constants/skins'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useI18n } from 'vue-i18n'

const emit = defineEmits(['close', 'already-selected'])
const { t } = useI18n()

const { playerState } = usePlayerState()
const store = useControlMethodStore()
const isOpen = ref(false)
const userSkin = computed(() => playerState.value?.skin ?? DEFAULT_SKIN_ID)

const openWindow = () => {
  isOpen.value = true
}

const openWindowWithCheck = () => {
  if (store.isMethodSelected) {
    emit('already-selected')
    return
  }
  isOpen.value = true
}

const closeWindow = () => {
  if (store.isMethodSelected) {
    isOpen.value = false
    emit('close')
  }
}

defineExpose({
  openWindowWithCheck,
  openWindow
})
</script>

<template>
  <ModalWindow
    class="control-method-dialog"
    :is-open="isOpen"
    :hide-close="!store.isMethodSelected"
    @close="closeWindow"
  >
    <p class="text-center text-[16px] mb-1">
      <span v-text="t('controlMethod.selectMsg')"></span>
    </p>
    <div class="control-method-dialog__animation relative w-full h-[187px] rounded-[5px] mb-3">
      <img class="control-method-dialog__platform" :src="platform" alt="platform" />
      <img class="control-method-dialog__platform" :src="platform" alt="platform" />
      <img class="control-method-dialog__uni" :src="SKIN_ID_TO_IMAGE[userSkin]" alt="uni" />
    </div>
    <div class="w-full h-[143px] flex gap-x-3">
      <div class="control-method-dialog__method">
        <p class="text-[16px] text-shadow text-shadow">
          <span v-text="t('controlMethod.gyroscope')"></span>
        </p>
        <img class="control-method-dialog__image control-method-dialog__image_rotate" :src="phoneImage" alt="phone image" />
        <VButton
          class="!w-full"
          type="success"
          size="medium"
          :text="store.isGyroscope ? t('controlMethod.selected') : t('controlMethod.select')"
          :disabled="store.isGyroscope"
          @click="() => store.selectMethod(true)"
        />
      </div>
      <div class="control-method-dialog__method">
        <p class="text-[16px] text-shadow">
          <span v-text="t('controlMethod.swipe')"></span>
        </p>
        <img class="control-method-dialog__image control-method-dialog__image_swipe" :src="swipeImage" alt="hand image" />
        <VButton
          class="!w-full"
          type="success"
          size="medium"
          :text="store.isSwipe ? t('controlMethod.selected') : t('controlMethod.select')"
          :disabled="store.isSwipe"
          @click="() => store.selectMethod(false)"
        />
      </div>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.control-method-dialog {
  padding: 12px;
  border: 5px solid #FFD634;
  border-radius: 18px;
  background: linear-gradient(360deg, #0C3E9F 0%, #2F9BFF 92.65%);

  .modal-window__title {
    font-size: 32px;
  }

  .close-button {
    top: 8px;
    right: 8px;
  }

  &__method {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    background-color: #00EEFF66;
    border-radius: 5px;
    padding: 8px;
  }

  &__image {
    height: 55px;

    &_rotate {
      animation: rotate 2s infinite;
    }

    &_swipe {
      animation: swipe 2s infinite;
    }
  }

  &__animation {
    background: url('@/assets/images/temp/background.png') no-repeat center / cover;
  }

  @keyframes control-method_y-motion {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(-100px);
    }
  }

  @keyframes control-method_x-motion {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(130px);
    }
  }

  @keyframes control-method_scale {
    0%, 100% {
      transform: scaleX(-1);
    }
    50% {
      transform: scaleX(1);
    }
  }

  &__uni {
    width: 50px;
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-90px);

    animation:
      control-method_x-motion 1s ease-in-out 0s infinite alternate,
      control-method_y-motion 0.5s ease 0s infinite alternate,
      control-method_scale 2s step-start 0s infinite;
    animation-composition: add;
  }

  &__platform {
    position: absolute;
    width: 50px;
    bottom: 10px;
    left: 50%;

    &:nth-child(1) {
      transform: translateX(-90px);
    }
    &:nth-child(2) {
      transform: translateX(40px);
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(35deg);
    }
    50% {
      transform: rotate(-35deg);
    }
    100% {
      transform: rotate(35deg);
    }
  }

  @keyframes swipe {
    0% {
      transform: translateX(50%);
    }
    50% {
      transform: translateX(-50%);
    }
    100% {
      transform: translateX(50%);
    }
  }
}
</style>
