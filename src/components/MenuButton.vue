<script setup lang="ts">
import VButton from '@/components/UI/VButton.vue'
const props = defineProps<{
  text: string
}>()
</script>

<template>
  <VButton :text="props.text" class="menu-button">
    <slot></slot>
    <template #content>
      <slot name="content"></slot>
    </template>
  </VButton>
</template>

<style lang="scss">
.menu-button {
  position: relative;
  z-index: 0;
  flex: 1 1 0%;
  height: 57px;
  padding: 0 0 12px;
  font-size: 16px;

  .button__content {
    position: absolute;
    top: auto;
    bottom: 13px;
  }

  &::before {
    transform: skew(-8deg);
  }

  img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    max-width: none;
  }
}
</style>
