<script setup lang="ts">
import tapIcon from '@/assets/images/temp/tap.svg'
import type { Language } from '@/i18n'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n({ useScope: 'global' })

const localeToClass = {
  en: '',
  uk: 'tap-to-wrapper__text_small',
  ru: 'tap-to-wrapper__text_small'
} as const

const { text } = defineProps<{
  text: string
}>()
</script>

<template>
  <div class="tap-to-wrapper">
    <div class="tap-to-wrapper__text" :class="localeToClass[locale as Language]">{{ text }}</div>
    <img :src="tapIcon" class="tap-to-wrapper__icon" alt="tap icon" />
  </div>
</template>

<style lang="scss" scoped>
.tap-to-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  &__text {
    font-size: 44px;
    font-weight: 600;
    text-shadow:
      #0000001f 0 4px 10px,
      #0000001a 0 17px 17px,
      #0000000f 0 39px 24px,
      #00000005 0 70px 28px,
      #00000000 0 109px 31px;

    white-space: pre-wrap;
    transform: rotate(-9deg) translateX(-10px);

    &_small {
      font-size: 35px;
    }
  }

  &__icon {
    transform: translateX(30px);
  }
}
</style>
