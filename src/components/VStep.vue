<script setup lang="ts">

const props = defineProps<{
  step: number
  text: string
  isDone?: boolean
}>()

</script>

<template>
  <div class="step" :class="{ 'step__done': props.isDone }">
    <div class="step__progress">
      <span class="text-shadow text-shadow_black">
        {{ props.text }}
      </span>
    </div>
    <div class="step__circle">
      <p class="text-shadow text-shadow_black">
        {{ props.step }}
      </p>
    </div>
    <div v-if="props.isDone" class="step__check check-icon"></div>
  </div>
</template>

<style lang="scss">
.step {
  --step-height: 43px;
  --step-bg: #0F4589;
  --step-active-start: #FEA801;
  --step-active-end: #FF6C00;
  --step-progress-active: linear-gradient(90deg, var(--step-active-start) 0%, var(--step-active-end) 100%);

  position: relative;
  height: var(--step-height);

  &__circle {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-50%, -50%);

    display: flex;
    justify-content: center;
    align-items: center;

    width: 56px;
    height: 56px;
    background-color: var(--step-bg);
    border-radius: 50%;

    font-size: 24px;
    line-height: 32px;

    &::before {
      content: '';
      position: absolute;
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background-color: var(--step-active-start);
      z-index: -1;
    }
  }

  &__progress {
    transform: translate(0, 0);
    
    display: flex;
    align-items: center;
    height: var(--step-height);
    padding: 0 16px 0 28px;
    
    background-color: var(--step-bg);
    border-radius: 5px;
    font-size: 17px;
    line-height: 23px;
  }

  &__done {
    .step__circle {
      &::after {
        content: '';
        position: absolute;
        width: 50%;
        height: calc(var(--step-height) - 12px);
        right: 0;
        background-color: var(--step-active-start);
        z-index: -1;
      }
    }

    .step__progress {
      &::before {
        content: '';
        position: absolute;
        width: calc(100% - 12px);
        height: calc(var(--step-height) - 12px);
        left: 6px;
        border-radius: 3px;
        background: var(--step-progress-active);
        z-index: -1;
      }
    }
  }

  &__check {
    position: absolute;
    right: -8px;
    top: -8px;
  }
}
</style>
