<script setup lang="ts">

const emit = defineEmits(['clickSelf', 'click'])
defineProps<{
  isOpen: boolean
  short?: boolean
}>()
</script>

<template>
  <Transition name="overlay">
    <div
      v-if="isOpen"
      class="overlay"
      :class="{ 'overlay_short': short }"
      @click.stop.self="() => emit('clickSelf')"
      @click="() => emit('click')"
    >
      <slot></slot>
    </div>
  </Transition>
</template>
