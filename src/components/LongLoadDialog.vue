<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import VOverlay from './VOverlay.vue'

const LONG_LOADING_TIME = 5000

const { t } = useI18n()

const isLongLoad = ref(false)

onMounted(() => {
  setTimeout(() => {
    isLongLoad.value = true
  }, LONG_LOADING_TIME)
})
</script>

<template>
  <VOverlay
    :isOpen="isLongLoad"
    class="flex items-center justify-center !bg-[#0000007F] !z-[9999] px-[16px]"
  >
    <div class="long-load-dialog">
      <div class="close-button absolute top-[10px] right-[10px] z-[9999]" @click="isLongLoad = false" />
      <p
        class="text-[36px] leading-[40px] text-shadow text-shadow_black text-shadow_thin text-center mb-3"
      >
        {{ t('longLoad.title') }}
      </p>
      <div class="long-load-dialog__description">
        <p class="text-[20px] font-[800] text-[#275A9D] text-center mb-6">
          {{ t('longLoad.shortDescription') }}
        </p>
        <p class="text-[18px] font-[800] text-[#275A9D] text-center">
          {{ t('longLoad.fullDescription') }}
        </p>
      </div>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.long-load-dialog {
  position: relative;
  border-radius: 12px;
  padding: 16px 11px 20px;
  background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
  box-shadow:
    0 2px #00000033,
    inset 0 4px #e1f6ff;

  &__description {
    background: #2397d54d;
    border-radius: 12px;
    padding: 40px 10px;
  }

  .close-button {
    --close-btn-background-color: #275a9d;
  }
}

@keyframes appear {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
