<script lang="ts" setup>
import { onMounted, ref, useTemplateRef } from 'vue';
import { useI18n } from 'vue-i18n';
import type { Language } from '@/i18n'
import { LOCALE_STORAGE_KEY, LOCALE_TO_LANGUAGE } from '@/i18n/index';
import ModalWindow from '@/components/UI/ModalWindow.vue';
import VSwitch from '@/components/UI/VSwitch.vue';
import VButton from '@/components/UI/VButton.vue';
import TextDivider from '@/components/UI/TextDivider.vue';
import { useSettings } from '@/composables/useSettings';
import instructionButtonImage from '@/assets/images/temp/question-mark.png'

import telegramImage from '@/assets/images/temp/socials/telegram.png'
import xImage from '@/assets/images/temp/socials/x.png'
// import instagramImage from '@/assets/images/temp/socials/instagram.png'
import checkIcon from '@/assets/images/temp/check-icon.png'
import { MAIN_CHANNEL_LINK, SUPPORT_LINK, FAQ_LINK, X_LINK } from '@/constants';
import { openTelegramLink, openLink } from '@telegram-apps/sdk';
import { localStorageService } from '@/shared/storage/localStorageService';
import { useCompleteUnverifiedTask } from '@/services/client/useCompleteUnverifiedTask';
import ControlMethodSelector from '@/components/windows/ControlMethodSelector.vue';
import VRadio from '@/components/UI/VRadio.vue';
import { useControlMethodStore } from '@/stores/controlMethodStore';

const { t, locale } = useI18n({ useScope: 'global' })

const {
  isHapticsEnabled,
  // isMusicEnabled,
  // isSoundEnabled,
  toggleHapticsEnabled,
} = useSettings()
const controlStore = useControlMethodStore()
const { completeUnverifiedTask } = useCompleteUnverifiedTask()

const isLanguageOpen = ref(false);
const controlSelectorRef = useTemplateRef('controlSelectorRef');

const selectLanguage = (newLocale: Language) => {
  locale.value = newLocale
  localStorageService.save(LOCALE_STORAGE_KEY, newLocale)
}

const X_TASK_ID = 7
const onXClick = async () => {
  openLink(X_LINK)
  await completeUnverifiedTask(X_TASK_ID)
}

const appVersion = 'Beta v' + import.meta.env.VITE_CLIENT_VERSION

const isOpen = ref(false)
onMounted(() => {
  setTimeout(() => {
    isOpen.value = true
  })
})

const isDev = __DEV__
</script>

<template>
  <div class="flex-1">
    <div class="absolute top-[13px] left-[12px] text-[18px] text-[#0065A6]">
      {{ appVersion }}
    </div>
    <div class="settings-block switchers mb-[18px]">
      <!-- <div class="switchers__item">
        <div class="text-[16px] leading-[22px] text-shadow text-shadow_thin">
          {{ t('settings.music') }}
        </div>
        <VSwitch :enabled="isMusicEnabled" @on-change="value => isMusicEnabled = value" />
      </div> -->
      <div class="switchers__item">
        <div class="text-[16px] leading-[22px] text-shadow text-shadow_thin">
          {{ t('settings.haptic') }}
        </div>
        <VSwitch :enabled="isHapticsEnabled" @on-change="toggleHapticsEnabled" />
      </div>
      <!-- <div class="switchers__item">
        <div class="text-[16px] leading-[22px] text-shadow text-shadow_thin">
          {{ t('settings.sound') }}
        </div>
        <VSwitch :enabled="isSoundEnabled" @on-change="value => isSoundEnabled = value" />
      </div> -->
    </div>
    <div class="settings-block flex items-center justify-between gap-x-2 mb-[18px]">
      <div class="flex items-center gap-x-[25px]">
        <VRadio
          :selected="controlStore.isGyroscope"
          @click="controlStore.selectMethod(true)"
          >
          <p class="text-[16px] text-shadow"><span v-text="t('controlMethod.gyroscope')"></span>
          </p>
        </VRadio>
        <VRadio
          :selected="controlStore.isSwipe"
          @click="controlStore.selectMethod(false)"
        >
          <p class="text-[16px] text-shadow"> <span v-text="t('controlMethod.swipe')"></span>
          </p>
        </VRadio>
      </div>
      <img
        class="instruction-button"
        :src="instructionButtonImage"
        alt="instruction button"
        @click="() => controlSelectorRef?.openWindow()"
      />
    </div>
    <div class="flex gap-x-2 mb-[12px]">
      <VButton
        class="flex-1"
        type="success"
        size="medium"
        :text="t('settings.support')"
        @click="() => openTelegramLink(SUPPORT_LINK)"
      />
      <VButton
        class="flex-1"
        size="medium"
        :text="t('settings.language')"
        @click="isLanguageOpen = true"
      />
    </div>
    <TextDivider class="mb-[8px] !text-[16px] !leading-[22px]" :style="{ '--text-divider-color': '#1E4073' }">
      {{ t('joinUs') }}
    </TextDivider>
    <div class="flex items-center justify-center gap-x-[20px] mb-[18px]">
      <img
        class="w-[55px]"
        :src="telegramImage"
        @click="() => openTelegramLink(MAIN_CHANNEL_LINK)"
      />
      <img
        class="w-[55px]"
        :src="xImage"
        @click="onXClick"
      />
      <!-- <img class="w-[55px]" :src="instagramImage" /> -->
    </div>
    <div class="flex gap-x-[14px]">
      <!-- <button class="button-link flex-1">
        {{ t('termsOfUse') }}
      </button> -->
      <button
        @click="() => openTelegramLink(FAQ_LINK)"
        class="button-link flex-1"
      >
        {{ t('faq') }}
      </button>
    </div>
    <ModalWindow
      :is-open="isLanguageOpen"
      :title="t('settings.language')"
      @close="isLanguageOpen = false"
    >
      <div class="language-list space-y-[8px] mb-[20px]">
        <template
          v-for="language, key in LOCALE_TO_LANGUAGE"
          :key="key"
        >
          <div
            v-if="isDev || key === 'en'"
            class="language-list__item"
            @click="() => selectLanguage(key)"
          >
            {{ language }}
            <img
              v-if="key === locale"
              class="absolute w-[28px] right-[10px]"
              :src="checkIcon"
            />
          </div>
        </template>
      </div>
      <VButton
        class="mx-auto !w-full max-w-[244px] mb-[16px]"
        type="success"
        :text="t('actions.back')"
        @click="isLanguageOpen = false"
      />
    </ModalWindow>
    <ControlMethodSelector
      ref="controlSelectorRef"
    />
  </div>
</template>

<style lang="scss">
.settings-block {
  background-color: #00EEFF4D;
  border-radius: 9px;
  padding: 12px 12px 17px;
}

.switchers {
  display: grid;
  grid-template-columns: 1fr;

  &__item {
    display: flex;
    // flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 11px;
  }
}

.language-list {
  position: relative;
  height: 294px;
  width: 100%;
  padding-top: 18px;

  &__item {
    height: 47px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0F4589;
    font-size: 20px;
    border-radius: 5px;
  }

  // &::before {
  //   content: '';
  //   display: block;
  //   position: sticky;
  //   top: 0;
  //   width: 100%;
  //   height: 28px;
  //   margin-bottom: -7px;
  //   background: linear-gradient(180deg, #0091D4 14.82%, rgba(0, 145, 212, 0) 68.56%);
  // }

  // &::after {
  //   content: '';
  //   display: block;
  //   position: sticky;
  //   bottom: 0;
  //   width: 100%;
  //   height: 28px;
  //   margin-top: -7px;
  //   background: linear-gradient(360deg, #0273BB 14.82%, rgba(2, 115, 187, 0) 68.56%);
  // }
}
</style>
