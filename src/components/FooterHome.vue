<script setup lang="ts">
import { hapticsService } from '@/shared/haptics/hapticsService.ts'

const onBackClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}
</script>

<template>
  <div class="home-button-container">
    <div class="w-[45%] absolute h-full left-0">
      <div class="home-button-container__uni home-button-container__uni_left"></div>
      <div class="home-button-container__uni home-button-container__uni_center"></div>
      <div class="home-button-container__uni home-button-container__uni_right"></div>
    </div>
    <div class="w-[45%] absolute h-full right-0">
      <div class="home-button-container__uni home-button-container__uni_left"></div>
      <div class="home-button-container__uni home-button-container__uni_center"></div>
      <div class="home-button-container__uni home-button-container__uni_right"></div>
    </div>
    <RouterLink class="home-button" to="/" @click="onBackClick">
      <div class="close-button"></div>
    </RouterLink>
  </div>
</template>

<style lang="scss" scoped>
.home-button-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: var(--footer-top-padding);
  padding-bottom: var(--footer-bottom-padding);

  background: linear-gradient(180deg, #EAF4FF 0%, #4296F8);
  overflow: hidden;
  z-index: 1;

  &__uni {
    background: url('@/assets/images/temp/uni-flat.svg') no-repeat center;
    background-size: contain;
    position: absolute;
    mix-blend-mode: color-burn;

    &_left {
      width: 60px;
      height: 60px;
      bottom: -10px;
      left: 0;
    }

    &_center {
      width: 83px;
      height: 83px;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
    }

    &_right {
      width: 60px;
      height: 60px;
      bottom: -10px;
      right: 0;
    }
  }
}

.home-button {
  width: var(--footer-button-size);
  height: var(--footer-button-size);

  display: flex;
  justify-content: center;
  align-items: center;

  .close-button {
    --close-btn-background-color: #275a9d;
  }
}
</style>
