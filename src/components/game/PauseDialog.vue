<script setup lang="ts">
import TapTo from '@/components/TapTo.vue';
import { useI18n } from 'vue-i18n';
import { formatNumberWithSeparator } from '@/utils/number';
import VOverlay from '../VOverlay.vue';

const { t } = useI18n()

const props = defineProps<{
  open: boolean
  score: string
  highScore: string
}>()

const emit = defineEmits(['continue'])

</script>

<template>
  <VOverlay
    :isOpen="props.open"
    class="overlay_gradient pause-window"
    @click="() => emit('continue')"
  >
    <div class="text-center space-y-[6px]">
      <div>
        <p class="text-[30px] leading-[68px] text-shadow text-shadow_fat">
          {{ t('currentScore') }}
        </p>
      </div>
      <div>
        <p class="text-[50px] text-[#FFDF6F] text-shadow text-shadow_fat text-shadow_black">
          {{ formatNumberWithSeparator(score) }}
        </p>
      </div>
    </div>
    <TapTo :text="t('actions.tapToContinue')" />
  </VOverlay>
</template>

<style lang="scss" scoped>
.pause-window {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 1001;
  padding: 150px 0 183px;
}
</style>
