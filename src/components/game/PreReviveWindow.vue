<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue';
import ReviveBanner from '../banners/ReviveBanner.vue';
import { cloudStorageService } from '@/shared/storage/cloudStorageService';
import { sendAnalyticsEvent } from '@/utils/analytics';

const HAS_SEEN_BANNER_KEY = 'hasSeenReviveBannerFirstTime'

const emit = defineEmits(['close'])

const reviveBannerRef = useTemplateRef('reviveBanner')

onMounted(async () => {
  const hasSeenFirstTime = await cloudStorageService.load(HAS_SEEN_BANNER_KEY)
  if (!hasSeenFirstTime) {
    reviveBannerRef.value?.openBanner()
    await cloudStorageService.save(HAS_SEEN_BANNER_KEY, true)
    sendAnalyticsEvent('new_user', { step: 'revive_banner' })
  } else {
    emit('close')
  }
})
</script>

<template>
  <ReviveBanner
    ref="reviveBanner"
    @close="() => emit('close')"
  />
</template>

<style lang="scss">
</style>
