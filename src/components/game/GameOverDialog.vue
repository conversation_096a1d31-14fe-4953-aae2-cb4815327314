<script setup lang="ts">
import { computed, ref, onUnmounted, onMounted, useTemplateRef } from 'vue'
import VButton from '../UI/VButton.vue'
import { useI18n } from 'vue-i18n'
import { formatNumberToShortString } from '@/utils/number'
import { usePlayerState } from '@/services/client/usePlayerState';
import { onBackButtonClick } from '@telegram-apps/sdk'

import uniImage from '@/assets/images/temp/uni-death.png'
import homeIcon from '@/assets/images/temp/home-icon.svg'
import ticketImage from '@/assets/images/temp/currency/ticket.png'
import softCoinImage from '@/assets/images/temp/currency/soft-coin.png'

import LivesBar from '../lives/LivesBar.vue';
import LivesModal from '../lives/LivesModal.vue';
import { useLifesStore } from '@/stores/livesStore';
import ReviveBanner from '../banners/ReviveBanner.vue';
import TonLimitBanner from '../banners/TonLimitBanner.vue'
import TutorialDialog from '@/components/game/TutorialDialog.vue';
import { useRoute } from 'vue-router';
import { sendAnalyticsEvent } from '@/utils/analytics';
import { NOT_FIRST_VISIT_GAME_OVER } from '@/shared/constants/storedPlayerData';
import { cloudStorageService } from '@/shared/storage/cloudStorageService';

export type Props = {
  score: string
  tickets: string
  isSessionUpdated: boolean
  isTonLimitEnded: boolean
  sessionCount: number
}

const { playerState } = usePlayerState()

const SCORE_TO_COINS_MULTIPLIER = 0.095
const MIN_SCORE_FOR_COINS = 200
const MIN_SESSIONS_FOR_HOME = 3

const { t } = useI18n()

const props = defineProps<Props>()
const emit = defineEmits(['restart', 'restart-tutorial', 'goToMainMenu'])

const livesStore = useLifesStore()
const route = useRoute()
const isFirstTutorial = !!route.query.tutorial
const canGoHome = computed(() => props.sessionCount >= MIN_SESSIONS_FOR_HOME || !isFirstTutorial)

const calculateCoins = (score: string) => {
  const scoreNumber = parseInt(score)
  return scoreNumber > MIN_SCORE_FOR_COINS ? Math.floor(scoreNumber * SCORE_TO_COINS_MULTIPLIER) : 0
}

const isOpenLivesModal = ref(false)
const isOpenShootTutorialDialog = ref(false)

const finalStats = computed(() => [
  { label: t('state.yourScore'), value: props.score },
  { label: t('state.ticketsCollected'), value: props.tickets, image: ticketImage },
  { label: t('state.coinsCollected'), value: calculateCoins(props.score), image: softCoinImage },
])

const reviveBannerRef = useTemplateRef('reviveBanner')
const onReviveBannerClose = ref<Function | null>(null)
const openReviveBanner = (callback: Function) => {
  onReviveBannerClose.value = callback
  reviveBannerRef.value?.openBanner()
}

const tryPlayAgain = () => {
  const isMobsTutorial = playerState.value?.tutorialMobs
  if (isMobsTutorial) {
    isOpenShootTutorialDialog.value = true
  } else if (livesStore.lives > 0) {
    emit('restart')
  } else {
    isOpenLivesModal.value = true
  }
}

const goToHome = () => {
  emit('goToMainMenu')
}

const backButtonRemoveListener = ref<Function | null>(null)

const backButtonClicked = () => {
  if (!canGoHome.value) {
    return
  } else if (onReviveBannerClose.value === null) {
    goToHome()
  } else if (onReviveBannerClose.value === tryPlayAgain) {
    // if user press back button after pressing playAgain and seeing revive banner
    // change callback to goToHome
    onReviveBannerClose.value = goToHome
  }
}

// for opacity animation if you manage open state with v-if in parent
const isOpen = ref(false)
onMounted(async () => {
  const isNotFirstTimeVisit = await cloudStorageService.load(NOT_FIRST_VISIT_GAME_OVER)
  if (!isNotFirstTimeVisit) {
    sendAnalyticsEvent('new_user', { step: 'game_over' })
    await cloudStorageService.save(NOT_FIRST_VISIT_GAME_OVER, true)
  }

  setTimeout(() => {
    isOpen.value = true
  })
  if (onBackButtonClick.isSupported()) {
    try {
      backButtonRemoveListener.value = onBackButtonClick(backButtonClicked)
    } catch(e) {
      backButtonClicked()
      console.log(e)
    }
  }
})
onUnmounted(() => {
  if (backButtonRemoveListener.value !== null) {
    backButtonRemoveListener.value()
  }
})
</script>

<template>
  <div
    class="game-end-window game-over-window"
    :class="{ 'game-end-window_active': isOpen }"
  >
    <p class="text-[42px] text-shadow text-shadow_fat">
      {{ t('gameover') }}
    </p>
    <div class="game-over-window__uni">
      <img :src="uniImage" class="game-over-window__uni-image" alt="Unicorn" />
    </div>
    <table class="game-over-window__table max-w-[234px] mb-10">
      <tbody>
        <tr class="text-shadow text-shadow_thin" v-for="stat in finalStats" :key="stat.label">
          <td>{{ stat.label }}</td>
          <td class="text-end">
            <div class="flex justify-end items-center gap-1">
              <img v-if="stat.image" :src="stat.image" class="relative -top-[1px] w-[18px]" alt="stat image" />
              {{ formatNumberToShortString(+stat.value) }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <div class="w-full max-w-[300px] flex gap-3 mb-[30px]">
      <VButton
        type="success"
        :text="t('actions.playAgain')"
        class="!w-full"
        @click="() => openReviveBanner(tryPlayAgain)"
      />
      <VButton
        v-if="canGoHome"
        :image="homeIcon"
        @click="goToHome"
      />
    </div>
  </div>
  <LivesBar
    class="gameover-lives-bar"
    @add="() => isOpenLivesModal = true"
  />
  <LivesModal
    :isOpen="isOpenLivesModal"
    @close="() => isOpenLivesModal = false"
  />
  <ReviveBanner
    ref="reviveBanner"
    @close="() => onReviveBannerClose !== null && onReviveBannerClose()"
  />
  <TonLimitBanner
    v-if="isTonLimitEnded"
  />
  <TutorialDialog
    :open-shoot="isOpenShootTutorialDialog"
    @continue="() => emit('restart-tutorial')"
  />
</template>

<style lang="scss">
.game-end-window {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;

  padding: 60px 24px;

  pointer-events: none;
  opacity: 0;
  // transform: translateY(10%);
  transition: opacity 0.3s, transform 0.3s;

  &_active {
    transform: translateY(0);
    pointer-events: auto;
    opacity: 1;
  }
}

.game-over-window {
  &__table {
    width: 100%;
    font-size: 18px;

    tr:not(:last-child) {
      border-bottom: 1px solid #002967;
    }

    td {
      padding: 16px 0;
    }
  }

  @keyframes wawing {
    0% {
      transform: rotate(10deg);
    }
    50% {
      transform: rotate(-10deg);
    }
    100% {
      transform: rotate(10deg);
    }
  }

  &__uni {
    width: 170px;
    height: 170px;
    margin: 15px 0 15px;

    &-image {
      width: inherit;
      height: inherit;
      animation: wawing 2s infinite ease-in-out;
    }
  }
}

.gameover-lives-bar {
  position: absolute;
  top: max(var(--inset-top), 16px);
  left: 32px;
  z-index: 10;
  width: fit-content;
}
</style>
