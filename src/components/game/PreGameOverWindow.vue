<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from 'vue';
import LoaderText from '../LoaderText.vue';
import LeagueRewardScreen from '../rewards/LeagueRewardScreen.vue';
import { useLifesStore } from '@/stores/livesStore';

import { usePlayerState } from '@/services/client/usePlayerState';

const props = defineProps<{
  isSessionUpdated: boolean
}>()
const emit = defineEmits(['close'])

const {
  playerState,
  refetchPlayerState,
  isRefetching: isRefetchingPlayerState
} = usePlayerState()
const livesStore = useLifesStore()

const leagueRewardScreenRef = useTemplateRef('leagueRewardScreenRef')

// TODO: Move to GameView
watch(() => props.isSessionUpdated, () => {
  if (props.isSessionUpdated) {
    if (livesStore.isRestoringLife) {
      livesStore.recalculateLifeRestoreTime()
    }
    if (livesStore.isUnlimitedLives) {
      livesStore.recalculateUnlimitedLivesTime()
    }
  }

  refetchPlayerState().then(() => {
    if (playerState.value!.leaguesToClaim?.length) {
      leagueRewardScreenRef.value?.open(playerState.value!.leaguesToClaim)
    } else {
      emit('close')
    }
  })
})

const isOpen = ref(false)
onMounted(() => {
  setTimeout(() => {
    isOpen.value = true
  })
})
</script>

<template>
  <div
    v-if="!isSessionUpdated || isRefetchingPlayerState"
    class="game-end-window-loading"
    :class="{ 'game-end-window-loading_active': isOpen }"
  >
    <LoaderText class="text-white" is-loading />
  </div>
  <LeagueRewardScreen
    ref="leagueRewardScreenRef"
    @close="() => emit('close')"
  />
</template>

<style lang="scss">
.game-end-window-loading {
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  font-size: 24px;
  color: #6DB0ED;
  transition: opacity 0.3s;
  transition-delay: 1s;
  opacity: 0;

  &_active {
    opacity: 1;
  }
}
</style>
