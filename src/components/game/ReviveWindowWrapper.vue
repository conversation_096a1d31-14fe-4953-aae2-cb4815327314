<script setup lang="ts">
import { ref } from 'vue';
import PreReviveWindow from '@/components/game/PreReviveWindow.vue'
import ReviveWindow, { type Props } from '@/components/game/ReviveWindow.vue'

const props = defineProps<Props>()
const emit = defineEmits(['revive', 'game-over'])

const isPreRevive = ref(true)
const isRevive = ref(false)
const openRevive = () => {
  isPreRevive.value = false
  isRevive.value = true
}
</script>

<template>
  <div
    class="absolute inset-0"
  >
    <PreReviveWindow v-if="isPreRevive" @close="openRevive" />
    <ReviveWindow
      v-if="isRevive"
      v-bind="props"
      @revive="() => emit('revive')"
      @game-over="() => emit('game-over')"
    />
  </div>
</template>

<style lang="scss">
</style>
