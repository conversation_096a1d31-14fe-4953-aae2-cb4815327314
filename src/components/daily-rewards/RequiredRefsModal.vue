<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import ModalWindow from '../UI/ModalWindow.vue';
import VButton from '../UI/VButton.vue';
import lockImage from '@/assets/images/temp/lock.svg'
import { formatNumberToShortString } from '@/utils/number';
import storeImage from '@/assets/images/temp/store.png'
import { useReferralLink } from '@/composables/useReferralLink'
import { getCurrencyRealAmount } from '@/constants/currency';

const { t } = useI18n()

defineProps<{
  isOpen: boolean;
  invitedRefs: number;
  requiredRefs: number;
  rewardImage: string;
  rewardAmount: number;
  rewardType: string;
}>();
const emit = defineEmits(['close', 'goToShop'])

const {
  forwardRefLink
} = useReferralLink()
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    class="requirenment-modal"
    @close="() => emit('close')"
  >
    <h1 class="text-[26px] leading-[35px] text-shadow text-shadow_black mb-[5px]">
      {{ t('dailyRewards.almostThere') }}
    </h1>
    <p class="text-[16px] leading-[22px] text-[#0065A6] mb-[24px] tracking-normal">
      {{ t('dailyRewards.youNeedRefs') }}
    </p>
    <div class="requirenment-modal__reward mb-[25px]">
      <div class="flex-1 relative">
        <img
            :src="rewardImage"
            class="requirenment-modal__reward-image"
          />
          <div class="requirenment-modal__reward-amount text-shadow text-shadow_black">
            {{
              rewardType === 'ton'
              ? getCurrencyRealAmount(rewardAmount, 'ton') + ' TON'
              : formatNumberToShortString(rewardAmount)
            }}
          </div>
      </div>
      <div class="requirenment-modal__reward-requirenment">
        <img class="absolute -left-[4px] w-[18px]" :src="lockImage" />
        {{ t('reward.refs') }}: {{ invitedRefs }}/{{ requiredRefs }}
      </div>
    </div>
    <VButton
      class="!w-full max-w-[250px] mb-[12px]"
      type="success"
      :text="t('friends.inviteFriends')"
      @click="forwardRefLink"
    />
    <div class="relative !w-full max-w-[250px]">
      <img class="absolute -top-[7px] -left-[7px] w-[42px] z-10" :src="storeImage" />
      <VButton
        class="!w-full"
        type="accent"
        :text="t('actions.goToShop')"
        @click="() => emit('goToShop')"
      />
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.requirenment-modal {
  padding: 16px 11px 35px;
  background: linear-gradient(360deg, #1EADEA 0%, #98E0FF 92.65%);
  box-shadow: 0 2px #00000033, inset 0 4px #E1F6FF;

  display: flex;
  flex-direction: column;
  align-items: center;

  &__reward {
    display: flex;
    flex-direction: column;
    width: 121px;
    height: 131px;
    background-color: #007BB4B5;
    border-radius: 9px;

    &-image {
      position: absolute;
      width: 123px;
      top: calc(50% - 5px);
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &-amount {
      width: 100%;
      text-align: center;
      position: absolute;
      bottom: 13px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 22px;
      line-height: 30px;
    }

    &-requirenment {
      position: relative;
      width: 100%;
      background-color: #84EDFF;
      border-radius: 0 0 9px 9px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: visible;
      height: 19px;
      font-size: 15px;
      line-height: 20px;
      font-weight: 800;
      color: #0065A6;
    }
  }
}
</style>
