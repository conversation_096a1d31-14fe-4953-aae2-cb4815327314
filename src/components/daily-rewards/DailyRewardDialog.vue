<script setup lang="ts">
import LoaderText from '@/components/LoaderText.vue'
import { usePlayerState } from '@/services/client/usePlayerState'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import ModalWindow from '../UI/ModalWindow.vue'
import ProgressBar from '../UI/ProgressBar.vue'

import checkIconSimple from '@/assets/images/temp/check-icon-simple.svg'
import checkIcon from '@/assets/images/temp/check-icon.png'
import dailyRewardBanner from '@/assets/images/temp/daily-rewards/banner-1.png'
import dailyRewardBanner2 from '@/assets/images/temp/daily-rewards/banner-2.png'
import dailyRewardImage from '@/assets/images/temp/daily-rewards/image.png'

import RewardItem from '@/components/RewardItem.vue'
import { useReward } from '@/composables/useReward'
import { REWARD_TO_IMAGE } from '@/composables/useIconImage'
import { useClaimDailyReward } from '@/services/client/useDailyRewards'
import skinImage from '@/assets/images/temp/skins/2014.png'
import { isLootboxRewardType } from '@/types'
import type { RewardType } from '@/services/openapi'

const MAX_DAYS_TOTAL = 28
const DAYS_PER_WEEK = 7

const { t } = useI18n()
const emit = defineEmits(['close'])

const { playerState } = usePlayerState()
const { claimDailyReward, isPending } = useClaimDailyReward()
const { showRewards } = useReward()
const progressPoints = computed(() => playerState.value!.dailyRewards?.milestones || [])
const isOpenRequiredRefs = ref(false)
const isReceivingRewards = ref(false)

const getProgressPointPosition = (point: number) => {
  if (point === 1) return 0
  return (point / MAX_DAYS_TOTAL) * 100
}

const getProgressImage = (type: RewardType) => {
  if (type === 'skin') {
    return skinImage
  }
  return REWARD_TO_IMAGE[type] ?? ''
}

const dailyRewards = computed(() => {
  return playerState.value!.dailyRewards?.rewards || []
})

const currentDay = computed(() => playerState.value!.dailyRewards?.currentDay || 1)

const currentWeek = computed(() => Math.ceil(currentDay.value / DAYS_PER_WEEK))

const activeDailyReward = computed(() => {
  return dailyRewards.value.find(reward => reward.day === currentDay.value)!
})

const skinId = computed(() => {
  return playerState.value!.dailyRewards?.milestones.find(m => m.reward?.type === 'skin')?.reward
    ?.value
})

const closeDialog = () => {
  emit('close')
}

const claimReward = () => {
  if (activeDailyReward.value.claimed) {
    closeDialog()
    return
  }

  isReceivingRewards.value = true
  claimDailyReward()
    .then(data => {
      isReceivingRewards.value = false
      showRewards(data.rewards).then(closeDialog)
    })
    .catch(() => {
      isReceivingRewards.value = false
      closeDialog()
    })
}

const isOpen = ref(false)
onMounted(() => {
  setTimeout(() => {
    isOpen.value = true
  })
})
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    :class="`daily-reward ${isOpenRequiredRefs ? 'opacity-0' : ''}`"
    @close="claimReward"
  >
    <div>
      <div v-if="skinId" class="mb-6">
        <img
          class="w-full absolute left-0 -top-[15px] pointer-events-none"
          :src="dailyRewardImage"
          alt="daily reward skin banner"
        />
        <img
          class="w-full absolute left-0 -top-[70px] pointer-events-none"
          :src="dailyRewardBanner2"
          alt="daily reward banner"
        />
        <div class="w-full absolute top-[30px] flex items-center justify-center">
          <i18n-t
            class="w-[50%] text-[13px] leading-[16px] text-white text-shadow text-shadow_black text-shadow_thin text-center whitespace-pre-wrap"
            tag="p"
            keypath="dailyRewards.skinInfo"
          >
            <template v-slot:skin>Uni Anon</template>
            <template v-slot:bonus>
              <span class="text-gradient">50X</span>
            </template>
          </i18n-t>
        </div>
      </div>
      <img
        v-else
        class="w-full absolute left-0 -top-[60px] pointer-events-none"
        :src="dailyRewardBanner"
        alt="daily reward banner"
      />

      <div class="mb-8">
        <div class="daily-reward-progress">
          <div
            v-for="point in progressPoints"
            :key="point.day"
            class="daily-reward-progress__point daily-reward-progress__point-bg"
            :style="{ left: `${getProgressPointPosition(point.day)}%` }"
          >
            <RewardItem
              v-if="point.reward && point.day !== 1"
              class="daily-reward-progress__point-reward"
              :class="{
                'daily-reward-progress__point-reward_last': point.day === 28 && point.reward.type === 'skin'
              }"
              valueClass="!text-[16px] translate-y-[3px]"
              :type="point.reward.type"
              :amount="point.reward.value"
              :show-amount="!isLootboxRewardType(point.reward.type)"
              :image="getProgressImage(point.reward.type)"
            />
          </div>
          <div
            v-for="point in progressPoints"
            :key="point.day"
            class="daily-reward-progress__point daily-reward-progress__point-inner"
            :style="{ left: `${getProgressPointPosition(point.day)}%` }"
            :class="{
              'daily-reward-progress__point-inner_active': point.day <= currentDay
            }"
          >
            <span class="text-shadow text-shadow_black">
              <img v-if="point.claimed" class="w-[12px]" :src="checkIconSimple" alt="check icon" />
              <span v-else>
                {{ point.day }}
              </span>
            </span>
          </div>
          <ProgressBar
            class="daily-reward-progress__bar"
            inner-class="daily-reward-progress__bar-inner"
            :goal="MAX_DAYS_TOTAL"
            :progress="currentDay === 1 ? 0 : currentDay"
            :transition-speed="0"
          />
        </div>
      </div>
      <div class="daily-reward-week" :class="['daily-reward-week_' + currentWeek]">
        <div
          v-for="dailyReward in dailyRewards"
          :key="dailyReward.day"
          class="daily-reward-day"
          @click="() => dailyReward.day === currentDay && claimReward()"
          :class="{
            'daily-reward-day_active': dailyReward.day === currentDay && !dailyReward.claimed,
            'daily-reward-day_done': dailyReward.claimed || dailyReward.day < currentDay
          }"
        >
          <img
            v-if="dailyReward.claimed"
            :src="checkIcon"
            class="absolute -top-[3px] -left-[6px] w-[28px]"
            alt="check icon"
          />
          <div class="daily-reward-day__title">
            {{ t('dailyRewards.day') }} {{ dailyReward.day }}
          </div>
          <RewardItem
            v-if="dailyReward.reward"
            class="w-[40%] h-full mx-auto"
            valueClass="!text-[16px] !bottom-[15%]"
            :type="dailyReward.reward!.type"
            :amount="dailyReward.reward!.value"
            :image="REWARD_TO_IMAGE[dailyReward.reward!.type] ?? ''"
          />
          <div v-if="dailyReward.rewards?.length" class="w-full h-full flex justify-evenly">
            <RewardItem
              v-for="reward in dailyReward.rewards"
              :key="reward.type"
              class="w-[13%] h-[80%]"
              valueClass="!text-[16px] translate-y-1/2"
              :type="reward.type"
              :amount="reward.value"
              :image="REWARD_TO_IMAGE[reward.type] ?? ''"
            />
          </div>
        </div>
      </div>
    </div>
    <template #bottom>
      <div class="text-center text-shadow text-shadow_black mt-[11px]">
        <div class="text-[24px] leading-[32px]" @click="claimReward">
          {{ t('actions.tapToCollect') }}
        </div>
      </div>
    </template>
  </ModalWindow>
  <div v-if="isPending || isReceivingRewards" class="daily-loader">
    <LoaderText is-loading class="text-[26px]" />
  </div>
</template>

<style lang="scss">
.daily-reward {
  padding: 95px 18px 18px;

  .close-button {
    top: -5px;
    right: 9px;
    --close-btn-background-color: #4d18bf;
  }
}

.daily-reward-progress {
  position: relative;
  overflow: visible;
  width: 100%;
  max-width: 80%;
  margin: 0 auto;

  &__bar {
    width: 100%;
    height: 17px;
    background: linear-gradient(360deg, #165d9c 0%, #1f1071 100%);
    padding: 3px;
    border-radius: 4px;
    transform: skew(0deg);

    .progress-bar__inner {
      position: relative;
      background:
        linear-gradient(90deg, rgba(255, 255, 255, 0) 0, #ffffff 100%) right no-repeat,
        #ffa201 linear-gradient(90deg, #ffa200 90%, #ffe02f 100%);
      border-radius: 2px;
      background-size:
        4px 11px,
        auto;
    }
  }

  &__point {
    height: 28px;
    width: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;

    top: -6px;
    transform: translateX(-50%);

    &-bg {
      border-radius: 50%;
      background: linear-gradient(180deg, #1f1071 0%, #165d9c 100%);
    }

    &-inner-wrapper {
      border-radius: 50%;
      padding: 3px;
    }

    &-inner {
      border-radius: 50%;
      width: 22px;
      height: 22px;
      z-index: 1;
      top: -3.5px;

      font-size: 14px;

      &_active {
        background: #ffa201;
        // color: #1E4073;
      }
    }

    &-reward {
      position: absolute;
      top: -45px;
      left: 50%;
      transform: translateX(-50%);
      height: 38px;
      width: 38px;

      &_last {
        top: -63px;
        height: 60px;
        width: 60px;
        transform: translateX(-50%) scaleX(-1);
      }
    }

    &-amount {
      position: relative;
      top: -26px;
      font-size: 14px;
      line-height: 19px;
    }
  }
}

.daily-reward-week {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: 100px;
  gap: 11px;

  &_1 {
    --left-reward: url('@/assets/images/temp/daily-rewards/reward-sm-l.png');
    --right-reward: url('@/assets/images/temp/daily-rewards/reward-sm-r.png');
  }
  &_2 {
    --left-reward: url('@/assets/images/temp/daily-rewards/reward-md-l.png');
    --right-reward: url('@/assets/images/temp/daily-rewards/reward-md-r.png');
  }
  &_3 {
    --left-reward: url('@/assets/images/temp/daily-rewards/reward-lg-l.png');
    --right-reward: url('@/assets/images/temp/daily-rewards/reward-lg-r.png');
  }
}

.daily-reward-day {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 9px;
  background: #00eeff66;

  &__title {
    border-radius: 9px 9px 0 0;
    height: 18px;
    font-size: 10px;
    line-height: 18px;
    text-align: center;
    background-color: #00629f;
  }

  &__requirenment {
    position: absolute;
    top: 2px;
    left: 0;
    z-index: 1;
    background-color: #ffec9c;
    border-radius: 17px;
    display: flex;
    align-items: center;
    overflow: visible;
    height: 14px;
    line-height: 16px;
    font-size: 12px;
    font-weight: 800;
    color: #bd764a;
    padding: 0 9px 0 19px;
  }

  &_active {
    outline: 3px solid white;
    z-index: 1;

    &:active {
      box-shadow: inset 0 100px rgba(0, 0, 0, 0.25);
    }

    &::before {
      content: '';
      position: absolute;
      top: -13px;
      left: 0;
      width: 100%;
      height: 20px;
      background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
      z-index: -1;
      opacity: 0.7;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -13px;
      left: 0;
      width: 100%;
      height: 20px;
      background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
      z-index: 1;
      opacity: 0.7;
    }
  }

  &_done {
    opacity: 0.6;
    background: #004c9f59;
  }

  &:last-child {
    grid-column: 1 / -1;
    background: url('@/assets/images/temp/daily-rewards/reward-bg.png') no-repeat center/cover;

    .daily-reward-day__title {
      background-color: #e07400;
    }
  }
}

.tap-to-collect {
  color: #bfbeff;
  font-size: 20px;
  -webkit-text-stroke: unset;
}

.daily-loader {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1001;
  background: rgba(0, 34, 85, 0.56);
}
</style>
