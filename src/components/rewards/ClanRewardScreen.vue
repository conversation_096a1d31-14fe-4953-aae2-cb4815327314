<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import VOverlay from '../VOverlay.vue';

import confetti from '@/assets/images/temp/confetti.png'
import clanImage from '@/assets/images/temp/clan-badge.png'

const { t } = useI18n()

defineProps<{
  isOpen: boolean,
  clanName: string
}>()
const emit = defineEmits(['close'])


const close = () => {
  emit('close')
}
</script>

<template>
  <VOverlay
    :isOpen="isOpen"
    class="reward-screen clan-reward-screen"
    short
    @click="close"
  >
    <div class="relative text-center">
      <h1 class="text-[32px] leading-[43px] text-shadow mb-4">
        You joined!
      </h1>
      <h1 class="text-[32px] leading-[43px] text-shadow text-shadow_black text-[#FFE11D]">
        {{ clanName }}
      </h1>
      <img class="absolute w-[130px] -z-[1] bottom-0 left-1/2 -translate-x-1/2 translate-y-[130%]" :src="clanImage" />
    </div>
    <img class="absolute w-dvw -z-[2] top-[15%] left-1/2 -translate-x-1/2" :src="confetti" />
    <div class="text-[24px] leading-[32px] font-extrabold text-shadow text-shadow_black">
      {{ t('actions.tapToContinue') }}
    </div>
  </VOverlay>
</template>

<style lang="scss">
.clan-reward-screen {
  &__banner {
    width: 100%;
    height: 185px;
    max-width: 306px;
  }
}
</style>
