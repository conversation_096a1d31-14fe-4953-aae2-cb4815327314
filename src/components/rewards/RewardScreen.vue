<script setup lang="ts">
import {
  useRewardStore,
} from '@/stores/rewardStore';
import SingleRewardScreen from '@/components/rewards/simple-rewards/SingleRewardScreen.vue';
import VOverlay from '@/components/VOverlay.vue';
import { useTemplateRef } from 'vue';
import MultipleRewardScreen from '@/components/rewards/simple-rewards/MultipleRewardScreen.vue';

const store = useRewardStore()

const singleRewardScreenRef = useTemplateRef('singleRewardScreenRef')

const collect = () => {
  if (store.reward) {
    singleRewardScreenRef.value?.collect()
  } else {
    store.closeReward()
  }
}
</script>

<template>
  <VOverlay
    :isOpen="store.isOpen"
    class="overlay_shine overlay_not-transparent reward-screen"
    @click="collect"
  >
    <SingleRewardScreen
      v-if="store.reward !== null"
      ref="singleRewardScreenRef"
      :reward="store.reward"
      @close="store.closeReward"
    />
    <MultipleRewardScreen
      v-if="store.rewards && store.rewards.length > 1"
      :rewards="store.rewards"
    />
  </VOverlay>
</template>

<style lang="scss">
</style>
