<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  isNumeralReward,
  isSimpleReward,
  isTimeReward,
  type Reward
} from '@/types'
import RewardItem from '@/components/RewardItem.vue';
import { useIconImage } from '@/composables/useIconImage';

defineProps<{
  rewards: Reward[]
}>()

const { t } = useI18n();

const startAnimation = ref(false)
const { getImage } = useIconImage()

onMounted(() => {
  setTimeout(() => startAnimation.value = true)
})
</script>

<template>
  <div class="text-center tracking-normal space-y-3">
    <p class="text-[32px] leading-[46x] text-shadow">
      {{ t('reward.youGot') }}
    </p>
    <p
      class="text-[40px] leading-[55x] text-[#FFE657] text-shadow"
    >
      {{ t('reward.rewards') }}:
    </p>
  </div>

  <div class="flex justify-center items-center flex-wrap px-5 gap-x-5">
    <RewardItem
      v-for="reward, index in rewards"
      class="multiple-rewards__item"
      :class="{
        'multiple-rewards__item_shown': startAnimation
      }"
      :style="{
        '--reward-index': index
      }"
      :key="index"
      :type="reward.type"
      :image="getImage(reward.type)"
      :amount="
        isNumeralReward(reward)
          ? reward.value
          : isTimeReward(reward)
            ? reward.duration
            : 0
      "
      :show-amount="!isSimpleReward(reward)"
      dontGetCurrencyRealAmount
    />
  </div>

  <div class="flex justify-center">
    <p class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal">
      {{ t('actions.tapToCollect') }}
    </p>
  </div>
</template>

<style lang="scss">
.multiple-rewards {
  &__item {
    width: 60px;
    height: 90px;

    --reward-index: 0;
    &_shown {
      animation: reward-bounce 0.5s ease-in-out;
      opacity: 1;
      animation-delay: calc(var(--reward-index) * 0.1s);
    }
  }
}
</style>
