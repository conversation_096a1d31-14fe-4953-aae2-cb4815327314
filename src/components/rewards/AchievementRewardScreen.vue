<script setup lang="ts">
import achievementShine from '@/assets/images/temp/shine.png'
import AchievementAvatarItem from '@/components/UI/AchievementAvatarItem.vue'
import MultiplierWithPlus from '@/components/UI/MultiplierWithPlus.vue'
import { useRewardCounter } from '@/composables/useRewardCounter'
import {
  ACHIEVEMENTS_IMAGES,
  ACHIEVEMENTS_LEVELS_FRAMES,
  type AchievementName
} from '@/constants/achievements.ts'
import { useAchievementReward } from '@/stores/rewardStore'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import VOverlay from '../VOverlay.vue'

const COLLECT_ANIMATION_DELAY = 1000

const { t } = useI18n()

const achievementStore = useAchievementReward()

const { targetValue, sourceValue, isCollecting, charge, setValues } = useRewardCounter(() => {
  setTimeout(() => {
    achievementStore.closeReward()
  }, COLLECT_ANIMATION_DELAY)
})

watch(
  () => achievementStore.isOpen,
  value => {
    if (!value) return
    if (achievementStore.plusMultiplier) {
      setValues(achievementStore.multiplier, achievementStore.plusMultiplier)
    }
  }
)

const collect = () => {
  charge()
}
</script>

<template>
  <VOverlay
    :isOpen="achievementStore.isOpen"
    class="overlay_shine overlay_not-transparent reward-screen"
    @click="collect"
  >
    <div class="relative z-10 text-center tracking-normal space-y-3">
      <p class="text-[32px] leading-[46x] text-shadow">
        {{ t('achievementRewards.newAchievement') }}
      </p>
      <p
        v-if="achievementStore.achievementId"
        class="text-[40px] leading-[55x] text-[#FFE657] text-shadow"
      >
        {{
          t(
            `achievements.list.${achievementStore.achievementId}.${achievementStore.achievementLevel}.name`
          )
        }}
      </p>
    </div>

    <div class="relative w-full z-0 overflow-visible">
      <img
        class="advanced-reward__shine absolute -bottom-[200px] w-[100%] z-0"
        :src="achievementShine"
        alt="shine"
      />
      <div class="advanced-reward__pedestal z-0">
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-circle"></div>
      </div>
      <AchievementAvatarItem
        class="advanced-reward__item z-10 mx-auto relative"
        :class="{ 'advanced-reward__item_active': achievementStore.isOpen }"
        size="170"
        :frame-src="ACHIEVEMENTS_LEVELS_FRAMES[achievementStore.achievementLevel - 1] ?? ''"
        :src="ACHIEVEMENTS_IMAGES[achievementStore.achievementId as AchievementName] ?? ''"
      />
    </div>

    <div class="relative z-10 flex flex-col items-center gap-y-[70px]">
      <MultiplierWithPlus
        class="advanced-reward__multiplier"
        :class="{ 'advanced-reward__multiplier_active': !isCollecting }"
        :multiplier="targetValue"
        :plus="sourceValue"
      />
      <p
        class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal"
      >
        {{ t('actions.tapToContinue') }}
      </p>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.advanced-reward__item_active {
  --shadow-color-1: #2c548b;
  --shadow-color-2: rgba(11, 101, 164, 0);
  --jump-height: -60px;
  z-index: 1;

  .achievement-avatar-frame {
    animation: reward-appear 0.9s alternate;
  }
}
</style>
