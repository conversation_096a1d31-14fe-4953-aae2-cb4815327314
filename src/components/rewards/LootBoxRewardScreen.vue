<script setup lang="ts">
import stars from '@/assets/images/temp/stars.png'
import {
  LOOTBOX_TYPE_TO_EXPLOSION_IMAGE,
  LOOTBOX_TYPE_TO_IMAGE,
  LOOTBOX_REWARD_ID_TO_TYPE,
  LOOTBOX_REWARD_ID_TO_SKIN_ID,
  type LootboxRewardId
} from '@/constants/lootboxes.ts'
import { useLootboxReward } from '@/stores/rewardStore'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import VOverlay from '../VOverlay.vue'
import { useReward } from '@/composables/useReward'
import type { RewardInfo, RewardType } from '@/services/openapi'

const { t } = useI18n()

const lootBoxStore = useLootboxReward()
const { showRewards } = useReward()

const TRANSITION_TIME = 1000
const isExplosion = ref(false)

const nextLootBox = () => {
  isExplosion.value = false
  lootBoxStore.nextLootBox()
}

const onClick = () => {
  isExplosion.value = true
  setTimeout(() => {
    const rewards: RewardInfo[] = lootBoxStore.rewards.map((item) => {
      const rewardType = LOOTBOX_REWARD_ID_TO_TYPE[item.id as LootboxRewardId]
      return {
        multiplier: item.multiplier,
        type: LOOTBOX_REWARD_ID_TO_TYPE[item.id as LootboxRewardId],
        value: rewardType === 'skin' ? LOOTBOX_REWARD_ID_TO_SKIN_ID[item.id] : parseInt(item.description)
      }
    })
    
    const uniqueRewards = Object.values(
      rewards.reduce((acc: Record<string, RewardInfo>, item) => {
        const { type, value, multiplier } = item;
        if (type === 'skin') {
          acc[value] = { type, value, multiplier };
        } else if (!acc[type] || type === 'fullLives') {
          acc[type] = { type, value, multiplier };
        } else {
          acc[type].value += value;
        }
        return acc;
      }, {} as Record<RewardType, RewardInfo>)
    );
    
    showRewards(uniqueRewards).then(nextLootBox)
  }, TRANSITION_TIME)
}
</script>

<template>
  <VOverlay
    :isOpen="lootBoxStore.isOpen"
    class="overlay_shine overlay_not-transparent reward-screen"
    @click="onClick"
  >
    <div class="text-center tracking-normal space-y-3">
      <p class="text-[32px] leading-[46x] text-shadow">
        {{ t('reward.youGot') }}
      </p>
      <p class="text-[40px] leading-[55x] text-[#FFE657] text-shadow">
        {{ t(`lootboxes.${lootBoxStore.lootboxType}`) }}
      </p>
      <img class="absolute left-0 top-[var(--inset-top)] w-full" :src="stars" alt="stars" />
    </div>

    <div class="lootBox-reward">
      <img
        class="lootBox-reward__explosion"
        :class="{
          'lootBox-reward__explosion_active': isExplosion
        }"
        :src="LOOTBOX_TYPE_TO_EXPLOSION_IMAGE[lootBoxStore.lootboxType!]"
        alt="box"
      />

      <img
        class="w-[164px]"
        :class="{
          'lootBox-reward__box_hide': isExplosion
        }"
        :src="LOOTBOX_TYPE_TO_IMAGE[lootBoxStore.lootboxType!]"
        alt="box"
      />
    </div>

    <p
      class="mx-auto text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal"
    >
      {{ t('actions.tapToOpen') }}
    </p>
  </VOverlay>
</template>

<style lang="scss">
.lootBox-reward {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .lootbox-rewards-grid__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 90px;

    &-shine {
      width: 150px;
    }

    &-image {
      width: 60px;
    }
  }

  &__box {
    @keyframes box-hide-animation {
      0% {
        transform: scale(1);
      }
      8% {
        transform: scale(1);
      }
      100% {
        transform: scale(0);
        opacity: 0;
      }
    }

    &_hide {
      animation: box-hide-animation 1s forwards;
    }
  }

  &__explosion {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100px;
    transform: translate(-50%, -50%) scale(0);
    z-index: 1000;

    @keyframes explosion-animation {
      0% {
        transform: translate(-50%, -50%) scale(0);
      }
      60% {
        opacity: 1;
      }
      70% {
        transform: translate(-50%, -50%) scale(7);
      }
      100% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0;
      }
    }

    &_active {
      animation: explosion-animation 2s forwards;
    }
  }
}
</style>
