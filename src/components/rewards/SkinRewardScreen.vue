<script setup lang="ts">
import { watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRewardCounter } from '@/composables/useRewardCounter';
import MultiplierWithPlus from '@/components/UI/MultiplierWithPlus.vue';
import { SKIN_ID_TO_IMAGE } from '@/constants/skins'
import { useSkinReward } from '@/stores/rewardStore';
import SkinItem from '../skins/SkinItem.vue';
import skinShine from '@/assets/images/temp/shine.png'
import VOverlay from '../VOverlay.vue';

const COLLECT_ANIMATION_DELAY = 1000;
const TIMEOUT_BETWEEN_REWARDS = 200;

const { t } = useI18n();

const skinStore = useSkinReward()

const {
  targetValue,
  sourceValue,
  isCollecting,
  charge,
  setValues
} = useRewardCounter(() => {
  setTimeout(() => {
    skinStore.isCurrentRewardVisible = false
    setTimeout(() => {
      skinStore.nextSkinReward()
    }, TIMEOUT_BETWEEN_REWARDS)
  }, COLLECT_ANIMATION_DELAY)
})

watch(() => skinStore.skin, (value) => {
  if (!value) return
  if (skinStore.skin.plusMultiplier) {
    setValues(skinStore.skin.multiplier, skinStore.skin.plusMultiplier)
  }
})

const collect = () => {
  charge()
}
</script>

<template>
  <VOverlay
    :isOpen="skinStore.isOpen"
    class="overlay_shine overlay_not-transparent reward-screen"
    @click="collect"
  >
    <div class="relative z-10 text-center tracking-normal space-y-3">
      <p class="text-[32px] leading-[46x] text-shadow">
        {{ t('skins.newSkin') }}
      </p>
      <p
        class="text-[40px] leading-[55x] text-[#FFE657] text-shadow"
      >
        {{ t(`skins.list.${skinStore.skin.skinId}.title`) }}
      </p>
    </div>

    <div class="relative w-full z-0 overflow-visible">
      <img class="advanced-reward__shine absolute -bottom-[200px] w-[100%] z-0" :src="skinShine" alt="shine" />
      <div class="advanced-reward__pedestal z-0">
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-trace-wrapper">
          <div class="advanced-reward__pedestal-trace"></div>
        </div>
        <div class="advanced-reward__pedestal-circle"></div>
      </div>
      <SkinItem
        class="advanced-reward__item z-10 w-[200px] mx-auto relative left-[-11px]"
        :class="{ 'advanced-reward__item_active': skinStore.isOpen && skinStore.isCurrentRewardVisible }"
        :src="SKIN_ID_TO_IMAGE[skinStore.skin.skinId]"
      />
    </div>

    <div class="relative z-10 flex flex-col items-center gap-y-[70px]">
      <MultiplierWithPlus
        class="advanced-reward__multiplier"
        :class="{ 'advanced-reward__multiplier_active': !isCollecting }"
        :multiplier="targetValue"
        :plus="sourceValue"
      />
      <p class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal">
        {{ t('actions.tapToContinue') }}
      </p>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.advanced-reward__item_active {
  .skin__image {
    animation: reward-appear 0.9s alternate;
  }
}
</style>
