<script setup lang="ts">
import { useClaimLeagueReward } from '@/services/client/useClaimLeagueReward';
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n';
import { formatNumberToShortString } from '@/utils/number';
import type { RewardInfo } from '@/services/openapi';
import LoaderText from '../LoaderText.vue';
import LeagueBadge from '../leagues/LeagueBadge.vue';
import leaguesList from '@/preload/leagues-list.json'
import { LEAGUE_TO_UNI } from '@/constants/leagues'

import { REWARD_TO_IMAGE } from '@/composables/useIconImage'
import confetti from '@/assets/images/temp/confetti.png'
import hotrecordImage from '@/assets/images/temp/hotrecord/button.png'
import onepercentImage from '@/assets/images/temp/onepercent/button.png'
import tonMiningImage from '@/assets/images/temp/ton-event/button.png'
import dailyRewardImage from '@/assets/images/temp/daily-rewards/button.png'
import farmingImage from '@/assets/images/temp/farming-icon.png'
import withdrawImage from '@/assets/images/temp/withdraw-icon.png'
import customCoinImage from '@/assets/images/temp/custom-coin-event/button.png'
import battleEventImage from '@/assets/images/temp/battle-event/button.png'
import deepDiveMoonImage from '@/assets/images/temp/deep-dive/button-moon.png'
import deepDiveOceanImage from '@/assets/images/temp/deep-dive/button-ocean.png'
import fragmentImage from '@/assets/images/temp/fragment/button.png'

import { useReward } from '@/composables/useReward';
import { useHasUserSeenLeagues } from '@/stores/hasUserSeenStore';
import leaguesService from '@/services/local/leagues';
import type { LeagueFeature } from '@/services/openapi';
import { isTimeRewardType } from '@/types';
import { useWindowQueue } from '@/composables/useWindowQueue';
import VOverlay from '../VOverlay.vue';
import { usePlayerState } from '@/services/client/usePlayerState';

const FEATURE_TO_IMAGE: Record<Exclude<LeagueFeature, 'dynamicCoins'>, string> = {
  farming: farmingImage,
  dailyReward: dailyRewardImage,
  hotRecordEvent: hotrecordImage,
  onePercentEvent: onepercentImage,
  tonMiningEvent: tonMiningImage,
  withdraw: withdrawImage,
  offers: '',
  puzzleCoins: fragmentImage,
  customCoinEvent: customCoinImage,
  battleEvent: battleEventImage,
  clanEvent: onepercentImage,
  lives: REWARD_TO_IMAGE['lives']!
}

const DEEP_DIVE_ID_TO_IMAGE: Record<number, string> = {
  10000: deepDiveMoonImage,
  10001: deepDiveOceanImage
}

const LEAGUES = leaguesList.list
const { t } = useI18n()
const emit = defineEmits(['close'])

const { playerState } = usePlayerState()
const { claimLeagueReward, isClaimingLeagueReward } = useClaimLeagueReward()
const { showRewards } = useReward()
const hasUserSeenLeaguesStore = useHasUserSeenLeagues()

const {
  isOpen,
  openWindowInQueue,
  closeWindowInQueue
} = useWindowQueue('league-reward-screen')

const leaguesToClaimArray = ref<Array<number>>([])
const currentLeague = ref<number>(1)
const leagueRewards = ref<RewardInfo[]>([])
const leagueName = computed(() => LEAGUES.find((league) => league.leagueLevel === currentLeague.value)?.name)

const progressiveOfferId = computed(() => playerState.value?.progressiveOffers.find(offer => offer.usageDynamicCoins)?.id ?? 10000)

const getFeatureImage = (feature: LeagueFeature) => {
  if (feature === 'dynamicCoins') {
    return DEEP_DIVE_ID_TO_IMAGE[progressiveOfferId.value]
  }
  return FEATURE_TO_IMAGE[feature]
}

const getFeatureText = (feature: LeagueFeature) => {
  if (feature === 'dynamicCoins') {
    return progressiveOfferId.value === 10000 ? t('features.dynamicCoins_1') : t('features.dynamicCoins_2')
  }
  return t(`features.${feature}`)
}

const goToNextLeagueReward = () => {
  leaguesToClaimArray.value = leaguesToClaimArray.value.slice(1)
  nextLeague()
}

const claimRewards = () => {
  showRewards(leagueRewards.value).then(goToNextLeagueReward)
}

const nextLeague = async () => {
  if (leaguesToClaimArray.value.length === 0) {
    close()
    return
  }
  currentLeague.value = leaguesToClaimArray.value.at(0)!
  const result = await claimLeagueReward(currentLeague.value)
  leagueRewards.value = result.rewards
}

const close = () => {
  closeWindowInQueue()
  emit('close')
}

const open = (leaguesToClaim: number[]) => {
  if (!leaguesToClaim.length) return
  hasUserSeenLeaguesStore.hasNewLeagueInfo = true
  leaguesToClaimArray.value = leaguesToClaim
  openWindowInQueue()
  nextLeague()
}

defineExpose({ open })
</script>

<template>
  <VOverlay
    :isOpen="isOpen"
    class="reward-screen league-reward-screen"
    @click="claimRewards"
  >
    <h1 class="text-[32px] leading-[43px] text-shadow">
      New league!
    </h1>
    <div v-if="isClaimingLeagueReward" class="absolute inset-0 flex items-center justify-center">
      <LoaderText class="text-white" text="Loading your League reward" is-loading />
    </div>
    <div
      v-else
      class="relative league-reward-screen__banner"
    >
      <img class="absolute w-dvw -z-[2] top-0 left-1/2 -translate-x-1/2 -translate-y-[150px]" :src="confetti" alt="confetti" />
      <img class="absolute w-[150px] -z-[1] top-0 left-1/2 -translate-x-1/2 -translate-y-[90%]" :src="LEAGUE_TO_UNI[currentLeague]" alt="league uni" />
      <div class="league-reward-screen__header mb-[15px]">
        <LeagueBadge :league="currentLeague" />
        <div class="text-[24px] text-[#FFE657] text-shadow text-shadow_black">
          {{ leagueName }}
        </div>
      </div>
      <p class="text-[20px] leading-[27px] text-shadow text-shadow_black text-center">
        Rewards:
      </p>
      <div class="flex items-center justify-center gap-x-3">
        <div
          v-for="reward in leagueRewards"
          :key="reward.type"
          class="league-reward-screen__reward"
        >
          <img
            :src="REWARD_TO_IMAGE[reward.type!]"
            class="league-reward-screen__reward-image"
            alt="reward"
          />
          <div
            v-if="reward.value"
            class="league-reward-screen__reward-amount text-shadow text-shadow_black"
          >
            {{
              isTimeRewardType(reward.type)
                ? (reward.value / 60) + ' min'
                : formatNumberToShortString(reward.value)
            }}
          </div>
        </div>
      </div>

      <div
        v-if="leaguesService.getLeagueFeatures(currentLeague)?.length"
        class="absolute top-full translate-y-[15px] left-1/2 -translate-x-1/2"
      >
        <p class="text-[20px] leading-[27px] text-shadow text-shadow_black text-center mb-[5px]">
          Unlocked:
        </p>
        <div class="flex w-dvw gap-x-[10px] items-center justify-center px-[10px]">
          <div
            v-for="feature in leaguesService.getLeagueFeatures(currentLeague).filter(f => f !== 'offers')"
            :key="feature"
            class="relative bg-[#007BB4B5] rounded-[9px] flex-1 max-w-[90px] h-[98px] flex items-center justify-center"
          >
            <img
              :src="getFeatureImage(feature)"
              class="absolute w-[52px] -translate-y-[5px]"
              alt="feature"
            />
            <span
              v-if="feature === 'lives'"
              class="absolute -translate-y-[5px] text-[20px] text-shadow text-shadow_black"
            >
              5
            </span>
            <p class="text-[#95DCFF] text-[12px] text-center absolute bottom-[6px]">
              {{ getFeatureText(feature) }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="text-[24px] leading-[32px] font-extrabold text-shadow text-shadow_black">
      {{ t('actions.tapToContinue') }}
    </div>
  </VOverlay>
</template>

<style lang="scss">
.league-reward-screen {
  &__banner {
    width: 100%;
    height: 185px;
    max-width: 306px;
    border-radius: 9px;
    background-color: #082A57B8;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, -50%);
      overflow: visible;
      width: 110dvw;
      height: 110dvw;
      background: radial-gradient(50% 50% at 50% 50%, #10D4FF 15.5%, rgba(16, 212, 255, 0) 91%);
      opacity: 0.8;
      z-index: -3;
    }
  }

  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 51px;
    background: linear-gradient(360deg, #65C9FF 0%, #94D8FF 92.65%);
    border: 1px solid #000710;
    border-radius: 9px;
    box-shadow: inset 0 4px #FFFFFF, 0 2px #00000033;
  }

  &__reward {
    flex: 0 0 70px;
    position: relative;
    height: 80px;

    &-image {
      position: absolute;
      width: 50px;
      top: calc(50% - 5px);
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &-amount {
      white-space: nowrap;
      width: 100%;
      text-align: center;
      position: absolute;
      bottom: 13px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 16px;
      line-height: 22px;
    }
  }
}
</style>
