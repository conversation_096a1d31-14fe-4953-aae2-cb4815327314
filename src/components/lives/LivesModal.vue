<script setup lang="ts">
import { useReferralLink } from '@/composables/useReferralLink'
import ModalWindow from '../UI/ModalWindow.vue'
import VButton from '../UI/VButton.vue'

import heart from '@/assets/images/temp/heart.png'
import livesClock from '@/assets/images/temp/lives-clock.png'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useLifesStore } from '@/stores/livesStore'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()

defineProps<{
  isOpen: boolean
}>()
const emit = defineEmits(['close'])

const router = useRouter()
const { forwardRefLink } = useReferralLink()
const lifesStore = useLifesStore()

const hasLives = computed(() => lifesStore.lives > 0)

const goToShop = () => {
  emit('close')
  router.push('/menu/shop')
}
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    :class="`
      lives-modal text-center relative
      ${!hasLives ? 'lives-modal_no-lives' : ''}
    `"
    @close="() => emit('close')"
  >

    <h1 class="text-[26px] leading-[35px] text-shadow text-shadow_black mb-[5px]">
      {{ hasLives ? t('lives.moreLives') : t('lives.noMoreLives') }}
    </h1>
    <div
      class="w-full flex flex-col items-center justify-center rounded-[9px] bg-[#2397D54D] py-[13px]"
    >
      <div class="flex justify-center items-center w-full">
        <img class="w-[120px] h-[110px]" :src="heart" alt="heart" />
        <p class="absolute text-[36px] text-shadow text-shadow_black">
          {{ lifesStore.lives }}
        </p>
      </div>
      <p class="text-[20px] leading-[28px] text-[#1E4073]">
        {{ t('lives.timeToNext') }}
      </p>
      <div class="counter-wrapper">
        <img
          class="absolute left-[-18px] bottom-[-1px] w-[27px] h-[27px]"
          :src="livesClock"
          alt="clock"
        />
        <CountdownTimerManual
          class="text-[12px] text-shadow text-shadow_black text-shadow_thin px-[10px] py-[3px]"
          :days="lifesStore.days"
          :hours="lifesStore.hours"
          :minutes="lifesStore.minutes"
          :seconds="lifesStore.seconds"
          digital
        />
      </div>
    </div>
    <p class="text-[16px] leading-[22px] text-[#0065a6] my-3">{{ t('lives.inviteFriendToGet') }}</p>
    <VButton
      class="!w-full"
      type="success"
      @click="forwardRefLink"
      :text="t('lives.inviteFriend')"
    />
    <div class="w-full relative mt-3">
      <div class="absolute z-10 top-[15px] left-[15px] flex items-center justify-center">
        <div class="absolute -z-10 icon-bg store-bg !w-[45px] !h-[45px]"></div>
      </div>
      <VButton class="!w-full" type="accent" @click="goToShop" :text="t('lives.goToShop')" />
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.lives-modal {
  padding: 16px 11px 20px;

  background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
  box-shadow:
    0 2px #00000033,
    inset 0 4px #e1f6ff;
}

.counter-wrapper {
  background: #043870;
  margin-top: 10px;
  position: relative;
  border-radius: 0 5px 5px 0;
}
</style>
