<script setup lang="ts">
import ticketImage from '@/assets/images/temp/currency/ticket.png';
import { formatNumberToShortString } from '@/utils/number'

</script>

<template>
  <div class="tickets-banner">
    <img
      class="tickets-banner__image"
      :src="ticketImage"
      alt="ticket"
    />
    <div class="tickets-banner__amount text-3xl">
      {{ formatNumberToShortString(99999999) }}
    </div>
  </div>
</template>

<style lang="scss">
.tickets-banner {
  position: relative;
  left: -10px;
  width: 216px;
  height: 48px;

  padding: 20px 0 0 40px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-1000);
    box-shadow: black 4px 4px;
    transform: skew(-25deg);
    clip-path: polygon(10% 50%, 105% 0, 110% 110%, 16% 110%);
  }

  &__image {
    position: absolute;
    top: 10px;
    left: 0;
    width: 50px;
    height: 50px;
  }

  &__amount {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    position: relative;
    transform: rotate(-5deg);
  }
}
</style>
