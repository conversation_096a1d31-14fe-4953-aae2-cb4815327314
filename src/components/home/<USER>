<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

const props = defineProps<{
  secondsToFarm: number
  ticketsFarmed: number
  ticketsToFarm: number
}>();

const MIN_STEP = 1 // 1 ticket
const MIN_STEP_TIME = 1000 // ms

const totalTicketsToFarm = computed(() => props.ticketsFarmed + props.ticketsToFarm)
const totalTicketsToFarmLength = computed(() => String(totalTicketsToFarm.value).length)

const incrementStep = computed(() => {
  if (props.ticketsToFarm <= 0) return 0
  const step = Math.round(props.ticketsToFarm / props.secondsToFarm)
  return Math.max(step, MIN_STEP)
})

const incrementTimeStep = computed(() => {
  if (props.ticketsToFarm <= 0) return 0
  const timeStep = Math.round(props.secondsToFarm / props.ticketsToFarm * 1000)
  return Math.max(MIN_STEP_TIME, timeStep)
})

const currentTicketsFarmedArray = ref<string[]>([])
const newTicketsFarmedArray = ref<string[]>([])

const changedNumbersIndexes = computed(() => {
  return newTicketsFarmedArray.value.map((num, i) => {
    return num !== currentTicketsFarmedArray.value.at(i)
  })
})

/**
 * Animate the counter from the current value to the new value
 * First we change the newTicketsFarmedArray so we see the digits to animate
 * After animation end in {incrementTimeStep * 0.9} (same as animation duration and setTimeot)
 * we update the currentTicketsFarmedArray so animation can reset (rerender) in
 * {incrementTimeStep - incrementTimeStep * 0.9} time
 */

const animate = () => {
  const updateCurrentValue = () => {
    currentTicketsFarmedArray.value = newTicketsFarmedArray.value
  }
  const updateNewValue = (newValue: number) => {
    const digitsArr = Array.from(String(newValue)).reverse()
    const digitsDiff = totalTicketsToFarmLength.value - digitsArr.length
    if (digitsDiff) {
      digitsArr.push(...Array(digitsDiff).fill('0'))
    }
    newTicketsFarmedArray.value = digitsArr
  }
  const nextStep = (onFinish?: Function) => {
    if (props.ticketsToFarm <= 0 || newTicketsFarmed === totalTicketsToFarm.value) {
      if (onFinish) onFinish()
      return
    }
    if (newTicketsFarmed + incrementStep.value > totalTicketsToFarm.value) {
      newTicketsFarmed = totalTicketsToFarm.value
    } else {
      newTicketsFarmed += incrementStep.value
    }
    updateNewValue(newTicketsFarmed)
    setTimeout(() => {
      updateCurrentValue()
    }, incrementTimeStep.value * 0.9)
  }

  let newTicketsFarmed = props.ticketsFarmed
  updateNewValue(newTicketsFarmed)
  updateCurrentValue()

  // for some reason nextTick doesn't help here
  setTimeout(nextStep)
  return setInterval(() => {
    nextStep(() => clearInterval(interval.value))
  }, incrementTimeStep.value)
}

const interval = ref<any>(null)
onMounted(() => {
  interval.value = animate()
})

onUnmounted(() => {
  clearInterval(interval.value)
})

</script>

<template>
  <div class="counter-animation tabular-nums"
    :style="{ '--transition-duration': incrementTimeStep * 0.9 + 'ms' }"
  >
    <div
      class="counter-animation__cell"
      v-for="newNum, i in newTicketsFarmedArray"
      :key="i"
    >
      <div
        class="counter-animation__cell-unit"
        :class="{ 'counter-animation__active counter-animation_leave-to': changedNumbersIndexes.at(i) }"
      >
        {{currentTicketsFarmedArray.at(i) || ''}}
      </div>
      <div
        class="counter-animation__cell-unit counter-animation__cell-unit_new"
        :class="{ 'counter-animation__active counter-animation_enter-from counter-animation_enter-to': changedNumbersIndexes.at(i) }"
      >
        {{newNum ?? '0'}}
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.counter-animation {
  --transition-duration: 0.5s;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row-reverse;
  column-gap: 2px;
  overflow: hidden;
  padding: 0 2px;

  &__active {
    will-change: transform, opacity;
    transition: all var(--transition-duration) linear;
    transition-property: transform, opacity;
  }

  &_enter-from {
    transform: translateY(0);
  }

  &_enter-to {
    transform: translateY(-100%);
  }

  &_leave-to {
    transform: translateY(-100%);
  }

  &__cell {
    position: relative;
    width: 10.3px;

    &-unit {
      height: 25px;
    }

    &-unit_new {
      position: absolute;
    }
  }
}
</style>
