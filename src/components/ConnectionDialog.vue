<script setup lang="ts">
import { watch, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import VButton from './UI/VButton.vue';
import LoaderText from './LoaderText.vue';
import { useOnline } from '@vueuse/core';
import VOverlay from './VOverlay.vue';

const { t } = useI18n()

const emit = defineEmits(['connection-lost', 'connection-restored'])

const isOpen = ref(false)
const online = useOnline()

const onConnectionRestored = () => {
  isOpen.value = false
  emit('connection-restored')
}

const onConnectionLost = () => {
  emit('connection-lost')
  isOpen.value = true
}

watch(online, (isOnline) => {
  if (!isOnline) {
    onConnectionLost()
  }
}, { immediate: true })
</script>

<template>
  <VOverlay
    :isOpen="isOpen"
    class="overlay_gradient flex items-center justify-center px-11"
  >
    <div class="connection-dialog">
      <p class="text-[32px] text-shadow text-shadow_fat mb-3">
        {{ t('connection.title') }}
      </p>
      <p class="text-[14px] text-[#1D3161] text-center mb-5">
        {{ t('connection.description') }}
      </p>
      <div class="h-[47px] w-full">
        <LoaderText class="text-center text-[#1A338E] font-bold text-md leading-[47px]" :is-loading="!online" />
        <VButton
          v-if="online"
          class="!w-full"
          type="success"
          :text="t('actions.continue')"
          @click="onConnectionRestored"
        />
      </div>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.connection-dialog {
  position: relative;
  width: 100%;
  padding: 24px;

  background-color: white;
  border-radius: 12px;
  box-shadow: #1D316180 0 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
