<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useHasGyroscopeAccess } from '@/composables/useGyroscope';
import { useHeaderColor } from "@/composables/useHeaderColor";
import LoaderText from './LoaderText.vue';
import LongLoadDialog from './LongLoadDialog.vue'
import { cloudStorageService } from '@/shared/storage/cloudStorageService';
import { NOT_FIRST_VISIT_MAIN_LOADING } from '@/shared/constants/storedPlayerData'
import { sendAnalyticsEvent } from '@/utils/analytics';

const MIN_LOADING_TIME = 3500;

const { setHeaderForLoadingScreen } = useHeaderColor();
setHeaderForLoadingScreen();

const props = defineProps<{
  isLoading: boolean
}>();

const emit = defineEmits(['close']);
const { checkGyroscopeAccess } = useHasGyroscopeAccess();

const isOpen = ref(true);
const minLoadingEnded = ref(false);

// The loading screen remains open if:
// - props.isLoading is true, or
// - the minimum loading time hasn't ended, or
const isLoading = computed(() => {
  return props.isLoading || !minLoadingEnded.value
});

watch(isLoading, (newValue) => {
  if (!newValue) {
    close();
  }
});

const close = () => {
  isOpen.value = false;
  emit('close');
};

onMounted(async () => {
  checkGyroscopeAccess();

  const isNotFirstTimeVisit = await cloudStorageService.load(NOT_FIRST_VISIT_MAIN_LOADING)
  if (!isNotFirstTimeVisit) {
    sendAnalyticsEvent('new_user', { step: 'main_loading_screen' })
    await cloudStorageService.save(NOT_FIRST_VISIT_MAIN_LOADING, true)
  }

  // Start the minimum loading timer.
  setTimeout(() => {
    minLoadingEnded.value = true;
  }, MIN_LOADING_TIME);
});

const appVersion = 'Beta v' + import.meta.env.VITE_CLIENT_VERSION;
</script>

<template>
  <div v-if="isOpen" class="main-loading-screen">
    <div v-if="isLoading" class="w-full h-full loading-screen">
      <LoaderText class="loading-text text-2xl" :isLoading="isLoading" />
      <p class="absolute right-2 bottom-1 font-extrabold text-[16px] text-[#332B85]">
        {{ appVersion }}
      </p>
    </div>
    <!-- Optionally, you can re-enable onboarding if needed -->
    <!-- <OnboardingScreen v-else-if="isFirstTimeVisit" @close="close" /> -->
  </div>
  <LongLoadDialog v-if="isLoading"/>
</template>

<style lang="scss" scoped>
.main-loading-screen {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1010;
  opacity: 1;
  background: #140b49;
  pointer-events: all;
}

.loading-screen {
  user-select: none;
  background: url('@/assets/images/temp/loading-screens/loading-screen-skin-event.png') no-repeat center/cover;
}

.loading-text {
  color: white;
  position: absolute;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
}
</style>
