<script setup lang="ts">
import platform from '@/assets/sprites/environment/platforms/static.png'
import LongLoadDialog from '@/components/LongLoadDialog.vue'
import { DEFAULT_SKIN_ID, SKIN_ID_TO_IMAGE } from '@/constants/skins.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { useFadeAnimationStore } from '@/stores/animationStore'
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const { playerState } = usePlayerState()
const userSkin = computed(() => playerState.value?.skin ?? DEFAULT_SKIN_ID)

const animationStore = useFadeAnimationStore()
const dots = ref('.')
let interval: ReturnType<typeof setInterval> | undefined = undefined
watch(() => animationStore.animate, (isLoading) => {
  if (isLoading) {
    interval = setInterval(() => {
      dots.value = dots.value.length < 3 ? dots.value + '.' : '.'
    }, 500)
  } else {
    clearInterval(interval)
    dots.value = '.'
  }
})
</script>

<template>
  <Transition>
    <div v-if="animationStore.animate" class="screen-fade-animation loading-screen">
      {{ t('loading') }}{{ dots }}
      <div class="jump-animation">
        <img class="jump-animation__uni w-[70px]" :src="SKIN_ID_TO_IMAGE[userSkin]" alt="uni" />
        <img class="jump-animation__platform" :src="platform" alt="platform" />
      </div>
      <LongLoadDialog />
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.screen-fade-animation {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 1;
  background: bottom / cover no-repeat url('@/assets/images/temp/background.png');
  box-shadow: inset 0 0 0 1000px #11386ee5;
  pointer-events: all;
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: end;
  padding-bottom: 10%;
  color: white;
  font-size: 2rem;
  user-select: none;
}

.jump-animation {
  position: absolute;
  top: 50%;
  left: auto;

  @keyframes smoothbounce {
    from {
      transform: translate3d(0, 0, 0);
    }
    to {
      transform: translate3d(0, -130px, 0);
    }
  }

  &__uni {
    position: relative;
    left: 0;
    top: -6px;

    animation: smoothbounce 0.5s;
    animation-direction: alternate;
    animation-iteration-count: infinite;
  }

  &__platform {
    width: 70px;
    position: relative;
    top: -10px;
  }
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s linear;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
