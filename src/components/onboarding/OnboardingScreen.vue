<script setup lang="ts">
import Glide, { Swipe, Controls, Autoplay } from '@glidejs/glide/dist/glide.modular.esm.js';
import VButton from '@/components/UI/VButton.vue';
import { onMounted, ref } from "vue";
import { setMiniAppHeaderColor } from '@telegram-apps/sdk'
import { sendAnalyticsEvent } from '@/utils/analytics'

import one from '@/assets/images/temp/onboarding/1.png'
import two from '@/assets/images/temp/onboarding/2.png'
import three from '@/assets/images/temp/onboarding/3.png'
import four from '@/assets/images/temp/onboarding/4.png'
import five from '@/assets/images/temp/onboarding/5.png'
import six from '@/assets/images/temp/onboarding/6.png'

const emit = defineEmits(['close'])
if (setMiniAppHeaderColor.isSupported()) {
  setMiniAppHeaderColor('#140b49')
}

const steps = [
  { image: one },
  { image: two },
  { image: three },
  { image: four },
  { image: five },
  { image: six },
]

const glide = ref<Glide | null>(null)
const buttonText = ref('Next')

onMounted(() => {
  glide.value = new Glide('#onboarding', {
    type: 'slider',
    focusAt: 'center',
    perView: 1,
    rewind: false,
    gap: 0,
  })

  glide.value.mount({ Swipe, Controls, Autoplay })
  glide.value.on(['mount.after', 'run'], () => {
    if (glide.value?.index === 5) {
      buttonText.value = 'Finish'
    } else {
      buttonText.value = 'Next'
    }
  })
  // glide.play(5000)
})


const onClick = () => {
  if (!glide.value) return
  sendAnalyticsEvent('onboarding', { step: glide.value.index + 1 })
  if (glide.value.index === 5) {
    emit('close')
  } else {
    glide.value.go('>')
  }
}
</script>

<template>
  <div class="w-full h-full">
    <div id="onboarding" class="h-full relative glide">
      <div class="h-full glide__track" data-glide-el="track">
        <ul class="h-full glide__slides">
          <li
            class="onboarding__step"
            v-for="step, index in steps"
            :key="index"
            :style="{
              backgroundImage: `url(${step.image})`,
            }"
          ></li>
        </ul>
      </div>
      <div class="absolute w-full px-8 bottom-[43px] space-y-[10px]">
        <div class="glide__bullets" data-glide-el="controls[nav]">
          <button v-for="_, index in steps" :key="index" class="glide__bullet" :data-glide-dir="`=${index}`"></button>
        </div>
        <VButton
          class="!w-full"
          :text="buttonText"
          @click="onClick"
        />
      </div>
    </div>
    <div class="px-5">
    </div>
  </div>
</template>

<style lang="scss">
.onboarding {
  &__step {
    background-position: bottom center;
    background-size: 100% auto;
    background-repeat: no-repeat;
  }
}

.glide__bullets {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.glide__bullet {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: linear-gradient(180deg, #0AA1FF 0%, #01DCFF 100%);
  box-shadow: 0px 2px #00000040, 0px 1px #FFFFFF inset;

  &--active {
    opacity: 1;

    & ~ * {
      opacity: 0.2;
    }
  }
}
</style>
