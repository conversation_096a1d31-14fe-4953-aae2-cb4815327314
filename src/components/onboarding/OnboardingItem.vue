<script setup lang="ts">

const props = defineProps<{
  step: {
    title: string
    description: string
    image: string
  }
}>()

</script>

<template>
  <li class="flex flex-col items-center justify-between">
    <div class="text-center">
      <h1 class="text-4xl">{{ props.step.title }}</h1>
      <p class="text-lg">{{ props.step.description }}</p>
    </div>
    <div>
      <img :src="props.step.image" class="w-[80%] mx-auto" />
    </div>
    <div></div>
  </li>
</template>
