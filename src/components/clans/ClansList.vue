<script setup lang="ts">
import TextField from '@/components/UI/TextField.vue';
import VButton from '@/components/UI/VButton.vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n'
import { useClanInfo, useClansList, useStartClanEvent } from '@/services/client/useClans'
import LeaderboardItem from '@/components/events/LeaderboardItem.vue';
import clanImage from '@/assets/images/temp/clan-badge.png'
import { openTelegramLink } from '@telegram-apps/sdk';
import LoaderText from '@/components/LoaderText.vue';
import InstructionButton from '@/components/UI/InstructionButton.vue';
import { useClanEventStore } from '@/stores/clanEventStore';
import { useToast } from '@/stores/toastStore';
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue';

const props = withDefaults(defineProps<{
  userClan?: number | null
}>(), {
  userClan: undefined
})
const emit = defineEmits(['open-clan'])

const { t } = useI18n()
const searchText = ref('')
const { clans, isLoading } = useClansList()
const addBotToGroupLink = 'https://t.me/UniJumpieBot?startgroup=c'

const createClan = () => {
  openTelegramLink(addBotToGroupLink)
}

const { showToast } = useToast()
const { startClanEvent } = useStartClanEvent()
const { isClanEventActive, days, hours, minutes, seconds } = useClanEventStore()

const { clanInfo } = useClanInfo(props.userClan ?? undefined)
const startEvent = () => {
  startClanEvent().then(() => {
    showToast('Clan Event has been started', 'info')
  })
}
</script>

<template>
  <div class="view-container menu-item clans-view flex flex-col">
    <h1 v-if="!userClan" class="text-[30px] leading-[46px] text-shadow text-center">
      {{ t('clans.clans') }}
    </h1>
    <div v-if="userClan" class="relative flex items-center justify-between gap-x-5 bg-[#C0C0C0] rounded-[10px] p-5 mb-3">
      <div class="flex-1">
        <p class="text-[20px] leading-[26px] text-shadow text-shadow_black text-shadow_thin">
          {{ t('clans.event.name') }}
        </p>
        <i18n-t
          class="text-[12px] text-shadow text-shadow_black text-shadow_thin text-white whitespace-pre"
          tag="p"
          keypath="clans.event.total_prize"
        >
          <template v-slot:ton>
            <span class="text-[26px] leading-[30px] text-[#FFD000]">100 TON</span>
          </template>
        </i18n-t>
      </div>
      <div class="flex-1">
        <CountdownTimerManual
          v-if="isClanEventActive"
          class="bg-[#07070773] rounded-[5px] px-[10px] py-[5px] text-[14px]"
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
        />
        <VButton
          v-else-if="clanInfo?.isEventPossible"
          type="success"
          size="small"
          :text="t('actions.startEvent')"
          @click="startEvent"
        />
        <p class="text-[14px] leading-[18px] text-[#1D3161] whitespace-pre">
          {{ t('clans.event.requirenment') }}
        </p>
      </div>
      <InstructionButton
        class="absolute top-[5px] right-[5px]"
        :instruction-type="'clan-event-instruction'"
      />
    </div>
    <div v-else class="relative bg-[#D9D9D9] rounded-[10px] p-5 mb-2">
      <i18n-t
        class="text-[20px] leading-[24px] text-shadow text-shadow_black text-shadow_thin text-white whitespace-pre"
        tag="p"
        keypath="clans.event.description_1"
      >
        <template v-slot:ton>
          <span class="text-[#FFD000]">100 TON</span>
        </template>
      </i18n-t>
      <InstructionButton
        class="absolute top-[5px] right-[5px]"
        :instruction-type="'clan-create-instruction'"
        :button="{
          text: t('actions.open_telegram'),
          onClick: createClan
        }"
      />
    </div>
    <div v-if="!userClan" class="flex items-center gap-2 mb-2 px-4">
      <TextField
        class="flex-1"
        v-model="searchText"
        :placeholder="t('search')"
        size="small"
      />
      <VButton
        class="basis-[95px]"
        type="success"
        :text="t('actions.create')"
        size="small"
        @click="createClan"
      />
    </div>
    <div class="flex flex-col flex-1 overflow-hidden px-3 bg-[#003579] rounded-[11px]">
      <p class="text-[#6DB0ED] mt-2 text-center">
        {{ userClan ? 'Leave your team before joining a new one' : 'Top Clans recomendations'}}
      </p>
      <LoaderText
        v-if="isLoading"
        class="w-full h-full flex items-center justify-center text-[24px] text-[#6DB0ED]"
        is-loading
      />
      <div v-else class="flex-1 overflow-y-auto">
        <div class="clans-view__shadow-gradient"></div>
        <LeaderboardItem
          v-for="(clan, index) in clans"
          class="mb-[6px]"
          :class="userClan === clan.id ? '!top-[10px] !bottom-[5px]' : ''"
          :key="clan.id"
          :username="clan.name"
          :rank-index="index"
          :active="userClan === clan.id"
          :avatar="clanImage"
          :balance="userClan ? clan.rating : clan.membersCount"
          :balance-type="userClan ? 'tickets' : 'refsFake'"
          name-class="text-shadow text-shadow_black text-shadow_thin text-white"
          score-class="text-[#BD764A] font-extrabold"
          @click="() => emit('open-clan', clan.id)"
        />
        <div class="clans-view__shadow-gradient"></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
