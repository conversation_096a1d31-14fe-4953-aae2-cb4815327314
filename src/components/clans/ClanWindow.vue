<script setup lang="ts">
import { computed, ref } from 'vue'
import ModalWindow from '@/components/UI/ModalWindow.vue'
import AvatarItem from '@/components/UI/AvatarItem.vue'
import { useClanInfo } from '@/services/client/useClans'
import LeaderboardList, { type LeaderboardCurrency } from '@/components/events/LeaderboardList.vue'
import { formatNumberToShortString } from '@/utils/number'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import VButton from '@/components/UI/VButton.vue'
import { openTelegramLink } from '@telegram-apps/sdk'
import clanImage from '@/assets/images/temp/clan-badge.png'
import { useFloating, offset } from '@floating-ui/vue';
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const emit = defineEmits(['close'])
const props = defineProps<{
  isOpen: boolean,
  clanId: number,
  userClan?: number | null
}>()

const { clanInfo, isLoading } = useClanInfo(() => props.clanId, () => props.isOpen)

const mappedUsersList = computed(() => {
  if (!clanInfo.value) return []
  return clanInfo.value.list.map(user => {
    return {
      id: user.id,
      name: (user.firstName + ' ' + user.lastName).trim(),
      league: user.leagueLevel,
      balance: user.rating,
      currency: 'tickets' as LeaderboardCurrency,
      isLeader: !!user.isClanLeader
    }
  })
})

const reference = ref<null | HTMLDivElement>(null);
const floating = ref(null);
const { floatingStyles } = useFloating(reference, floating, {
  placement: 'bottom',
  middleware: [offset(10)],
});
const isOpenTooltip = ref(false)
const showTootip = () => {
  isOpenTooltip.value = true;
}
const hideTootip = () => {
  isOpenTooltip.value = false;
}
</script>

<template>
  <ModalWindow
    class="clan-info-dialog"
    :title="t('clans.title')"
    :is-open="isOpen"
    :is-loading="isLoading"
    short
    @click="hideTootip"
    @close="() => emit('close')"
  >
    <div v-if="clanInfo" class="flex-0 flex items-center gap-x-3 bg-[#00EEFF66] rounded-[11px] px-2 py-[10px] mb-[10px]">
      <div class="flex-0">
        <AvatarItem :src="clanImage" class="flex-0" size="63" />
      </div>
      <div class="space-y-3 min-w-0">
        <div class="text-[20px] text-shadow text-shadow_black truncate px-[2px]">
          {{ clanInfo.clanName }}
        </div>
        <div class="flex gap-x-5">
          <BalanceItem
            icon-name="ticket-bg"
          >
            {{ formatNumberToShortString(clanInfo.rating) }}
          </BalanceItem>
          <BalanceItem
            icon-name="ref-bg"
          >
            {{ formatNumberToShortString(clanInfo.membersCount) }}
          </BalanceItem>
        </div>
      </div>
    </div>
    <div class="flex-1 overflow-auto">
      <LeaderboardList
        :leaderboard="mappedUsersList"
        name-class="text-white text-shadow text-shadow_black"
      />
    </div>
    <VButton
      ref="reference"
      v-if="clanInfo?.clanLink"
      class="!w-[90%] mx-auto mt-[10px]"
      type="success"
      :text="t('actions.join')"
      stop-propagation
      @click="() => !userClan ? openTelegramLink(clanInfo!.clanLink!) : showTootip()"
    />
    <div v-if="isOpenTooltip" ref="floating" class="bg-white rounded-[15px] py-[14px] px-2" :style="floatingStyles">
      <p class="text-[16px] text-[#1E4073] text-center whitespace-pre">
        Leave your current Clan to join a new one
      </p>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.clan-info-dialog {
  padding: 11px;
  height: 80%;
  display: flex;
  flex-direction: column;

  .modal-window__title {
    font-size: 32px;
  }
}
</style>
