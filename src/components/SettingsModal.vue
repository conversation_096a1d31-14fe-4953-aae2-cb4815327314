<script lang="ts" setup>
import GameSettings from './GameSettings.vue';
import ModalWindowTabs from './UI/ModalWindowTabs.vue';
import { useUser } from "@/composables/useUser";
import PlayerProfile from '@/components/profile/PlayerProfile.vue';

defineProps<{
  isOpen: boolean
}>();

const emit = defineEmits(['close']);
const { user } = useUser()
</script>

<template>
  <ModalWindowTabs
    class="h-[460px]"
    :is-open="isOpen"
    :tabs="[
      { name: 'playerProfile.title', id: 'profile' },
      { name: 'settings.title', id: 'settings' },
    ]"
    @close="() => emit('close')"
  >
    <template #settings>
      <GameSettings />
    </template>
    <template #profile>
      <PlayerProfile :player-id="user.getId()" :is-open="isOpen" />
    </template>
  </ModalWindowTabs>
</template>

<style lang="scss">
</style>