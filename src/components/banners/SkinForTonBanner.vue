<script setup lang="ts">
import eventBanner from '@/assets/images/temp/banners/skin-for-ton/banner.png'
import eventImage from '@/assets/images/temp/banners/skin-for-ton/image.png'
import tonImage from '@/assets/images/temp/currency/ton.png'
import EventBanner from '@/components/events/EventBanner.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { TON_SKIN_ID } from '@/constants/skins.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { useSkinsList } from '@/services/client/useSkins.ts'
import leaguesService from '@/services/local/leagues.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService.ts'
import { computed, onMounted, ref } from 'vue'
import TonPurchaseButton from '@/components/TonPurchaseButton.vue'
import { usePurchase } from '@/composables/usePurchase'
import { getCurrencyRealAmount } from '@/constants/currency'

const USER_HAS_SEEN_SKIN_FOR_TON = 'hasSeenSkinForTonBanner'
const IS_TON_SKIN_PURCHASE_IN_PROGRESS = 'isTonSkinPurchaseInProgressNew'

const { isOpen } = defineProps<{ isOpen: boolean }>()

const emit = defineEmits(['close'])

const { purchaseInvoice } = usePurchase()
const { playerState } = usePlayerState()
const { skinsList } = useSkinsList()

const {
  isOpen: isOpenBanner,
  openWindowInQueue,
  closeWindowInQueue
} = useWindowQueue('skin-for-ton-banner')

const closeBanner = () => {
  emit('close')
  closeWindowInQueue()
}

const openBanner = async () => {
  const hasAccess = leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'offers')

  await cloudStorageService.load<boolean>(USER_HAS_SEEN_SKIN_FOR_TON).then(skinForTonRes => {
    if (!!skinForTonRes || playerState.value?.tutorial || !hasAccess) {
      emit('close')
    } else {
      cloudStorageService.save(USER_HAS_SEEN_SKIN_FOR_TON, true)
      openWindowInQueue()
    }
  })
}

const isTonSkinPurchaseInProgress = ref(false)
cloudStorageService.load<string>(IS_TON_SKIN_PURCHASE_IN_PROGRESS).then(inProgress => {
  isTonSkinPurchaseInProgress.value = !!inProgress
})

const skin = computed(() => {
  return skinsList.value.find(skin => skin.id === TON_SKIN_ID)
})

//TODO need to put the purchaseSkinWithTon logic separately
const purchaseSkinWithTon = () => {
  if (!skin.value || !skin.value.price) return

  purchaseInvoice(
    'skin',
    +skin.value.shopItem!,
    skin.value.id,
    { currency: skin.value.price.currency, amount: skin.value.price.amount }
  ).then(() => {
    cloudStorageService.save(IS_TON_SKIN_PURCHASE_IN_PROGRESS, true)
    isTonSkinPurchaseInProgress.value = true
  })
}

onMounted(openBanner)
</script>

<template>
  <EventBanner
    class="skin-for-ton-banner w-full h-full"
    :isOpen="isOpenBanner || isOpen"
    :banner="eventBanner"
    :image="eventImage"
    imageFullSize
    showCloseButton
    @close-button="closeBanner"
  >
    <div
      class="bg-[#16FFFF99] flex flex-col justify-center items-center w-full rounded-[9px] pt-[6px] pb-[9px] px-[2px] mb-1"
    >
      <i18n-t
        class="text-[14px] leading-[18px] text-center whitespace-normal text-shadow text-shadow_black text-white"
        tag="p"
        keypath="skinForTon.description"
      >
        <template v-slot:ton>
          <span class="text-[#FFE134]">TON!</span>
        </template>
      </i18n-t>
    </div>
    <TonPurchaseButton
      v-if="skin?.price"
      class="!w-full"
      type="success"
      :text="getCurrencyRealAmount(skin.price.amount, skin.price.currency)"
      :image="tonImage"
      :isTransactionInProgress="isTonSkinPurchaseInProgress"
      @purchase="purchaseSkinWithTon"
    />
  </EventBanner>
</template>

<style lang="scss">
.skin-for-ton-banner {
  top: 0;

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border-radius: 20px;

    &__banner {
      width: 80%;
      top: -25px;
    }
  }
}
</style>
