<script setup lang="ts">
import { useShopItems } from '@/services/client/useShopItems'
import type { ProgressiveOfferItem } from '@/services/openapi/types.gen'
import { computed, ref, useTemplateRef, watch } from 'vue'
import LoaderText from '../../LoaderText.vue'

import deepBannerImage from '@/assets/images/temp/snake-offer/banner-deep.png'
import moonBannerImage from '@/assets/images/temp/snake-offer/banner-moon.png'

import { REWARD_TO_IMAGE } from '@/composables/useIconImage'

import RewardItem from '@/components/RewardItem.vue'
import VButton from '@/components/UI/VButton.vue'
import VOverlay from '@/components/VOverlay.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage.ts'
import { usePurchase } from '@/composables/usePurchase'
import { useToast } from '@/stores/toastStore'
import { formatNumberWithSeparator } from '@/utils/number.ts'
import CountdownTimerManual from '../../UI/CountdownTimerManual.vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const EVENT_ID_TO_BANNER_IMAGE: Record<number, string> = {
  10004: moonBannerImage,
  10005: deepBannerImage
}

const EVENT_ID_TO_CARD_CLASS: Record<number, string> = {
  10004: 'snake-offer__step_moon',
  10005: 'snake-offer__step_deep'
}

defineProps<{
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()

const emit = defineEmits(['close'])

const closeBanner = () => {
  emit('close')
}

const { showToast } = useToast()

const { shopItems, isLoading } = useShopItems()
const progressiveOffer = computed(() =>
  shopItems.value?.progressiveOffers.find(
    offer => (offer.id === 10004 || offer.id === 10005) && !offer.isCompleted
  )
)

const filteredSteps = computed(
  () => progressiveOffer.value?.items.filter(s => !s.isPurchased) ?? []
)

let stepsCount = ref(0)

watch(
  filteredSteps,
  newValue => {
    stepsCount.value = newValue.length ?? 0
  },
  { immediate: true }
)

const { purchaseProgressive, isPendingPurchaseProgressive } = usePurchase()

const purchase = (item: ProgressiveOfferItem) => {
  purchaseProgressive(progressiveOffer.value!.id, item.price!, item.rewards)
    .then(removePurchasedElement)
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
      }
    })
}

interface FlexItemInfo {
  element: Element
  x: number
  y: number
  width: number
  height: number
  index: number
}

const containerRef = useTemplateRef('containerRef')

const removePurchasedElement = () => {
  const oldFlexItemsInfo = getElementInfo(containerRef.value!)
  stepsCount.value = stepsCount.value - 1
  containerRef.value?.removeChild(containerRef.value?.children[0])
  const newFlexItemsInfo = getElementInfo(containerRef.value!)
  runAnimation(oldFlexItemsInfo, newFlexItemsInfo)
}

const getElementInfo = (container: Element) => {
  return Array.from(container.children).map((item, index) => {
    const rect = item.getBoundingClientRect()
    return {
      element: item,
      x: rect.left,
      y: rect.top,
      width: rect.right - rect.left,
      height: rect.bottom - rect.top,
      index
    }
  })
}

const runAnimation = (oldFlexItemsInfo: FlexItemInfo[], newFlexItemsInfo: FlexItemInfo[]) => {
  for (const newFlexItemInfo of newFlexItemsInfo) {
    const oldFlexItemInfo = oldFlexItemsInfo.find(
      itemInfo => itemInfo.element === newFlexItemInfo.element
    )

    const translateX = oldFlexItemInfo!.x - newFlexItemInfo.x
    const translateY = oldFlexItemInfo!.y - newFlexItemInfo.y
    const scaleX = oldFlexItemInfo!.width / newFlexItemInfo.width
    const scaleY = oldFlexItemInfo!.height / newFlexItemInfo.height

    newFlexItemInfo.element.animate(
      [
        {
          transform: `scale(${scaleX}, ${scaleY}) translate(${translateX}px, ${translateY}px)`
        },
        { transform: 'none' }
      ],
      {
        duration: 150 * newFlexItemInfo.index,
        easing: 'ease-in'
      }
    )
  }
}
</script>

<template>
  <div class="close-button-wrapper">
    <div class="close-button !relative" @click="closeBanner"></div>
  </div>
  <div class="snake-offer" v-if="progressiveOffer">
    <div class="snake-offer__top">
      <img
        class="snake-offer__banner"
        :src="EVENT_ID_TO_BANNER_IMAGE[progressiveOffer.id]"
        alt="event banner"
      />
      <div class="snake-offer__timer">
        <span v-text="t('endsIn')"></span>
        <CountdownTimerManual
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
          digital
        />
      </div>
    </div>
    <div class="snake-offer__bottom-wrapper">
      <div
        class="snake-offer__bottom"
        ref="containerRef"
        :class="[{ '!justify-end': stepsCount === 3 || stepsCount === 7 }]"
      >
        <div
          v-for="(step, i) in filteredSteps"
          :key="i"
          class="snake-offer__step-wrapper"
          :style="{
            '--i': `${i}`
          }"
        >
          <div
            class="snake-offer__step"
            :class="{
              [EVENT_ID_TO_CARD_CLASS[progressiveOffer.id]]: true
            }"
          >
            <div
              class="flex items-center justify-center rounded-[10px] h-[60%] gap-x-3 px-2 bg-[#FFFFFF7A] w-[90%]"
            >
              <RewardItem
                v-for="reward in step.rewards"
                :key="reward.type"
                class="w-[45%] h-full"
                value-class="translate-y-[-15px]"
                :type="reward.type"
                :amount="reward.value"
                :image="REWARD_TO_IMAGE[reward.type] ?? ''"
              />
            </div>
            <div class="flex items-center justify-center relative !w-[60%]">
              <VButton
                class="pointer-events-auto mx-auto min-w-[110px] !text-[17px]"
                :image-class="
                  step.price?.amount ? CURRENCY_TO_IMAGE_CLASS[step.price!.currency] : ''
                "
                :text="
                  step.price?.amount
                    ? formatNumberWithSeparator(step.price.amount)
                    : t('free')
                "
                :type="step.price?.amount > 0 ? 'success' : 'accent'"
                size="small"
                :disabled="isPendingPurchaseProgressive"
                @click="() => step.isAvailable && purchase(step)"
              >
              </VButton>
              <img
                v-if="!step.isAvailable"
                class="absolute right-[-12px] bottom-0 w-[30px] rotate-[-25deg]"
                src="@/assets/images/temp/locks/lock.png"
                alt="lock"
              />
            </div>
          </div>
        </div>
        <div class="absolute w-full h-full !z-20 pointer-events-none">
          <div class="relative w-full h-full flex justify-center">
            <img
              v-if="stepsCount > 1"
              class="absolute w-[35px] top-[12%]"
              src="@/assets/images/temp/snake-offer/arrow-right.png"
              alt="arrow right"
            />
            <img
              v-if="stepsCount > 2"
              class="absolute w-[35px] top-[30%] right-[20%]"
              src="@/assets/images/temp/snake-offer/arrow-down.png"
              alt="arrow down"
            />
            <img
              v-if="stepsCount > 3"
              class="absolute w-[35px] top-[46%]"
              src="@/assets/images/temp/snake-offer/arrow-left.png"
              alt="arrow down"
            />
            <img
              v-if="stepsCount > 4"
              class="absolute w-[35px] top-[64%] left-[20%]"
              src="@/assets/images/temp/snake-offer/arrow-down.png"
              alt="arrow down"
            />
            <img
              v-if="stepsCount > 5"
              class="absolute w-[35px] top-[80%]"
              src="@/assets/images/temp/snake-offer/arrow-right.png"
              alt="arrow right"
            />
          </div>
        </div>
      </div>
    </div>
    <VOverlay
      :is-open="isLoading || isPendingPurchaseProgressive"
      class="flex items-center justify-center"
    >
      <LoaderText is-loading class="text-[24px] text-[#6db0ed]" />
    </VOverlay>
  </div>
</template>

<style lang="scss" scoped>
.close-button-wrapper {
  position: absolute;
  top: calc(var(--inset-top) + 11px);
  right: 11px;
  z-index: 10000;
  background: linear-gradient(180deg, #fc7100 0%, #ff7a21 100%);
  border-radius: 50%;
  padding: 4px;
  border: 3px solid #a62603;
  box-shadow: 0px -4px 0px 0px #ec4e32 inset;

  .close-button {
    --close-btn-background-color: #a62603;
  }
}

.snake-offer {
  position: relative;

  display: flex;
  flex-direction: column;

  width: 95%;
  height: 90%;

  &__top {
    position: relative;
    flex: 0 0 130px;
  }

  &__bottom-wrapper {
    flex: 1;
    overflow: hidden;
  }

  &__bottom {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: space-between;
    overflow: hidden;
    width: 100%;
    height: 100%;
    gap: 5px;
  }

  &__step-wrapper {
    position: relative;
    width: calc((100% - 15px) / 2);
    height: calc((100% - 35px) / 3);
    margin: 5px 0;
    color: #fff;
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    order: var(--i);

    &:nth-child(6n + 1) {
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-1.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-1.png');
        }
      }
    }

    &:nth-child(6n + 2) {
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-2.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-2.png');
        }
      }
    }

    &:nth-child(6n + 3) {
      order: calc(var(--i) + 1);
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-3.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-3.png');
        }
      }
    }

    &:nth-child(6n + 4) {
      order: calc(var(--i) - 1);
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-4.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-4.png');
        }
      }
    }

    &:nth-child(6n + 5) {
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-5.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-5.png');
        }
      }
    }

    &:nth-child(6n) {
      .snake-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/snake-offer/moon-card-6.png');
        }
        &_deep {
          background-image: url('@/assets/images/temp/snake-offer/dive-card-6.png');
        }
      }
    }

    &:nth-child(7n) {
      order: calc(var(--i) + 1);
    }

    &:nth-child(8n) {
      order: calc(var(--i) - 1);
    }
  }

  &__step {
    width: 100%;
    height: 100%;
    border: 5px solid #ffd634;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 18px;

    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
  }

  &__timer {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 5px;
    background-color: #07070773;
    padding: 5px 10px;
    font-size: 14px;
    z-index: 2;
  }

  &__banner {
    position: absolute;
    top: -35%;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    width: 90%;
    z-index: 1;
  }
}
</style>
