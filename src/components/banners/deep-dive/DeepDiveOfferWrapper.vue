<script setup lang="ts">
import { useWindowQueue } from '@/composables/useWindowQueue'
import { usePlayerState } from '@/services/client/usePlayerState'
import leaguesService from '@/services/local/leagues'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { onMounted } from 'vue'

import VOverlay from '../../VOverlay.vue'
import DeepDiveOffer from './DeepDiveOffer.vue'

const HAS_SEEN_EVENT_BANNER_KEY_OLD = 'hasSeenDeepDiveOfferBanner'

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenDeepDive'

const HAS_SEEN_EVENT_BANNER_MIN_BALANCE_KEY = 'hasSeenDeepDiveMinBalance'
const HAS_SEEN_EVENT_BANNER_SECOND_MIN_BALANCE_KEY = 'hasSeenDeepDiveSecondMinBalance'
const EVENT_BANNER_BALANCE_KEY = 'deepDiveBalance'

const HAS_SEEN_TIME_SPENT_IN_GAME_KEY = 'hasSeenDeepDiveTimeSpentInGame'
const TIME_SPENT_IN_GAME_KEY = 'deepDiveTimeSpentInGame'

const MIN_BALANCE = 10
const SECOND_MIN_BALANCE = MIN_BALANCE * 3
const SHOW_BANNER_AFTER_TIME_SPENT_IN_GAME = 1000 * 60 * 30 // 30 minutes

const { isOpen } = defineProps<{
  isOpen: boolean
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()
const emit = defineEmits(['close'])

const {
  isOpen: isOpenBanner,
  openWindowInQueue,
  closeWindowInQueue
} = useWindowQueue('deep-dive-banner')
const { playerState } = usePlayerState()

const checkBannerOnStart = async () => {
  const hasAccess = leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'dynamicCoins')
  if (!hasAccess || playerState.value!.tutorial) {
    return false
  }
  const hasSeen = sessionStorage.getItem(HAS_SEEN_EVENT_BANNER_KEY)
  if (hasSeen) return false
  sessionStorage.setItem(HAS_SEEN_EVENT_BANNER_KEY, '1')
  // for everyone who already seen the banner, we need to delete the key
  await cloudStorageService.delete(HAS_SEEN_EVENT_BANNER_KEY_OLD)
  return true
}

const checkBannerMinBalance = () => {
  const hasSeen = sessionStorage.getItem(HAS_SEEN_EVENT_BANNER_MIN_BALANCE_KEY)
  if (hasSeen) return false
  const balance = playerState.value!.dynamicCoins ?? 0
  const storedBalance = sessionStorage.getItem(EVENT_BANNER_BALANCE_KEY)
  const storedBalanceNumber = parseInt(storedBalance ?? '0')
  if (storedBalance === null) {
    sessionStorage.setItem(EVENT_BANNER_BALANCE_KEY, balance.toString())
  } else if (balance - storedBalanceNumber >= MIN_BALANCE) {
    sessionStorage.setItem(EVENT_BANNER_BALANCE_KEY, balance.toString())
    sessionStorage.setItem(HAS_SEEN_EVENT_BANNER_MIN_BALANCE_KEY, '1')
    return true
  }
  return false
}

const checkBannerSecondMinBalance = () => {
  // show banner only once after 30 minutes of playing if not seen after second min balance before
  const hasSeenTimer = sessionStorage.getItem(HAS_SEEN_TIME_SPENT_IN_GAME_KEY)
  const hasSeen = sessionStorage.getItem(HAS_SEEN_EVENT_BANNER_SECOND_MIN_BALANCE_KEY)
  if (hasSeen || hasSeenTimer) return false
  const balance = playerState.value!.dynamicCoins ?? 0
  const storedBalance = sessionStorage.getItem(EVENT_BANNER_BALANCE_KEY)
  const storedBalanceNumber = parseInt(storedBalance ?? '0')
  if (storedBalance === null) {
    sessionStorage.setItem(EVENT_BANNER_BALANCE_KEY, balance.toString())
  } else if (balance - storedBalanceNumber >= SECOND_MIN_BALANCE) {
    sessionStorage.setItem(EVENT_BANNER_BALANCE_KEY, balance.toString())
    sessionStorage.setItem(HAS_SEEN_EVENT_BANNER_SECOND_MIN_BALANCE_KEY, '1')
    return true
  }
  return false
}

const checkBannerTimeSpentInGame = () => {
  // show banner only once after 30 minutes of playing if not seen after second min balance before
  const hasSeen = sessionStorage.getItem(HAS_SEEN_EVENT_BANNER_SECOND_MIN_BALANCE_KEY)
  if (hasSeen) return false
  const now = new Date().getTime()
  const timeSpentInGame = sessionStorage.getItem(TIME_SPENT_IN_GAME_KEY)
  if (!timeSpentInGame) {
    sessionStorage.setItem(TIME_SPENT_IN_GAME_KEY, now.toString())
    return false
  }
  const timeSpentInGameNumber = parseInt(timeSpentInGame)
  if (now - timeSpentInGameNumber >= SHOW_BANNER_AFTER_TIME_SPENT_IN_GAME) {
    sessionStorage.setItem(TIME_SPENT_IN_GAME_KEY, now.toString())
    sessionStorage.setItem(HAS_SEEN_TIME_SPENT_IN_GAME_KEY, '1')
    return true
  }
  return false
}

const closeBanner = () => {
  emit('close')
  closeWindowInQueue()
}

onMounted(async () => {
  const isOpen = await checkBannerOnStart()
  const isOpenMinBalance = checkBannerMinBalance()
  const isOpenSecondMinBalance = checkBannerSecondMinBalance()
  const isOpenTimeSpentInGame = checkBannerTimeSpentInGame()
  if (isOpen || isOpenMinBalance || isOpenSecondMinBalance || isOpenTimeSpentInGame) {
    openWindowInQueue()
  }
})
</script>

<template>
  <VOverlay
    :isOpen="isOpen || isOpenBanner"
    class="flex items-center justify-center px-[11px]"
    @click-self="closeBanner"
  >
    <DeepDiveOffer
      :days="days"
      :hours="hours"
      :minutes="minutes"
      :seconds="seconds"
      @close="closeBanner"
    />
  </VOverlay>
</template>
