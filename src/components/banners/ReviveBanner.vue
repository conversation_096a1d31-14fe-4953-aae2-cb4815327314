<script setup lang="ts">
import eventBanner from '@/assets/images/temp/banners/revive/banner.png'
import eventImage from '@/assets/images/temp/banners/revive/image.png'
import EventBanner from '@/components/events/EventBanner.vue'
import { useTime } from '@/composables/useTime.ts'
import dayjs from '@/plugins/dayjs'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const { t } = useI18n()

const HAS_SEEN_BANNER_KEY = 'hasSeenReviveBannerDate'

const emit = defineEmits(['close'])

const route = useRoute()
const isFirstTutorial = !!route.query.tutorial

const { getCurrentDayEnd } = useTime()

const isOpenBanner = ref(false)

const closeBanner = () => {
  emit('close')
  isOpenBanner.value = false
}

const openBanner = async () => {
  const reviveBannerOpenDate = await cloudStorageService.load<number>(HAS_SEEN_BANNER_KEY)
  const now = Math.floor(dayjs().valueOf() / 1000)
  const isSameDay =
    reviveBannerOpenDate && getCurrentDayEnd(now) === getCurrentDayEnd(reviveBannerOpenDate)
  if (isSameDay || isFirstTutorial) {
    emit('close')
  } else {
    cloudStorageService.save(HAS_SEEN_BANNER_KEY, now)
    isOpenBanner.value = true
  }
}

defineExpose({
  openBanner
})
</script>

<template>
  <EventBanner
    class="revive-banner w-full h-full"
    :isOpen="isOpenBanner"
    :buttons="[{ text: t('actions.got'), type: 'success', onClick: closeBanner }]"
    :banner="eventBanner"
    :image="eventImage"
    imageFullSize
  >
    <div
      class="bg-[#32004DA6] flex flex-col justify-center items-center w-full rounded-[9px] p-3 mb-3"
    >
      <p
        class="text-[15px] leading-[20px] text-center whitespace-normal text-shadow text-shadow_black text-white"
      >
        {{ t('reviveBanner.description') }}
      </p>
    </div>
  </EventBanner>
</template>

<style lang="scss">
.revive-banner {
  top: 0;

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border-radius: 20px;

    &__banner {
      width: 183px;
      top: -25px;
    }
  }
}
</style>
