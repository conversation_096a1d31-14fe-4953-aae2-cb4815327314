<script setup lang="ts">
import { onMounted, watch } from 'vue'

import ExploitersBanners from '@/components/banners/exploiters/ExploitersBanners.vue'
import VOverlay from '@/components/VOverlay.vue'
import { useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { usePlayerState } from '@/services/client/usePlayerState'

const HAS_SEEN_BANNER_KEY = 'hasSeenExploitersBanner'

const {
  isOpen: isOpenWelcomeBanner,
  openWindowInQueue: openWelcomeWindowInQueue,
  closeWindowInQueue: closeWelcomeWindowInQueue
} = useWindowQueue('exploiters-welcome-window')

const {
  isOpen: isOpenCollectBanner,
  openWindowInQueue: openCollectWindowInQueue,
  closeWindowInQueue: closeCollectWindowInQueue
} = useWindowQueue('exploiters-collect-window')

const {
  isOpen: isOpenLastChanceBanner,
  openWindowInQueue: openLastChanceWindowInQueue,
  closeWindowInQueue: closeLastChanceWindowInQueue
} = useWindowQueue('exploiters-last-chance-window')

const { isOpen } = defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits(['close', 'start-game'])

const { playerState } = usePlayerState()
const { checkWelcomeBanner } = useEventWelcomeCheck()

const startGame = () => {
  closeBanner()
  emit('start-game')
}

const closeBanner = () => {
  emit('close')
  closeWelcomeWindowInQueue()
  closeCollectWindowInQueue()
  closeLastChanceWindowInQueue()
}

const checkHeistEndResult = () => {
  const heistRepelled = playerState.value!.dailyWriteOffState?.heistEndResult
  const isHeistEnd = heistRepelled !== undefined
  if (!isHeistEnd) return
  if (heistRepelled) {
    openCollectWindowInQueue()
  } else if (!heistRepelled) {
    openLastChanceWindowInQueue()
  }
}

watch(() => playerState.value!.dailyWriteOffState?.heistEndResult, checkHeistEndResult)

onMounted(async () => {
  if (!playerState.value!.dailyWriteOffState) return
  const isOpenWelcomeBanner = await checkWelcomeBanner(
    HAS_SEEN_BANNER_KEY,
    !!playerState.value!.dailyWriteOffState?.timestampToFail,
    true
  )
  if (isOpenWelcomeBanner) {
    openWelcomeWindowInQueue()
  }

  checkHeistEndResult()
})
</script>

<template>
  <VOverlay
    :isOpen="isOpen || isOpenWelcomeBanner || isOpenCollectBanner || isOpenLastChanceBanner"
    class="overlay_transparent"
  >
    <ExploitersBanners
      :isOpen="isOpen"
      :isOpenWelcomeBanner="isOpenWelcomeBanner"
      :isOpenCollectBanner="isOpenCollectBanner"
      :isOpenLastChanceBanner="isOpenLastChanceBanner"
      @close="closeBanner"
      @start-game="startGame"
    />
  </VOverlay>
</template>
