<script setup lang="ts">
import { useTime } from '@/composables/useTime.ts'
import { useWindowQueue } from '@/composables/useWindowQueue'
import dayjs from '@/plugins/dayjs'
import { usePlayerState } from '@/services/client/usePlayerState'
import leaguesService from '@/services/local/leagues'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { onMounted } from 'vue'

import VOverlay from '../../VOverlay.vue'
import ScrollingOffer from './ScrollingOffer.vue'

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenScrollingOfferBanner'

const { isOpen } = defineProps<{
  isOpen: boolean
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()
const emit = defineEmits(['close'])

const { getCurrentDayEnd } = useTime()

const {
  isOpen: isOpenBanner,
  openWindowInQueue,
  closeWindowInQueue
} = useWindowQueue('scrolling-offer-banner')
const { playerState } = usePlayerState()

const checkBanner = async () => {
  const hasAccess = leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'offers')
  if (!hasAccess || playerState.value!.tutorial) {
    return false
  }
  const offerBannerOpenDate = await cloudStorageService.load<number>(HAS_SEEN_EVENT_BANNER_KEY)
  const now = Math.floor(dayjs().valueOf() / 1000)
  const isSameDay =
    offerBannerOpenDate && getCurrentDayEnd(now) === getCurrentDayEnd(offerBannerOpenDate)
  if (isSameDay) return false
  cloudStorageService.save(HAS_SEEN_EVENT_BANNER_KEY, now)
  return true
}

const closeBanner = () => {
  emit('close')
  closeWindowInQueue()
}

onMounted(async () => {
  const isOpen = await checkBanner()
  if (isOpen) {
    openWindowInQueue()
  }
})
</script>

<template>
  <VOverlay
    :isOpen="isOpen || isOpenBanner"
    class="flex items-center justify-center px-[11px]"
    @click-self="closeBanner"
  >
    <ScrollingOffer
      :days="days"
      :hours="hours"
      :minutes="minutes"
      :seconds="seconds"
      @close="closeBanner"
    />
  </VOverlay>
</template>
