<script setup lang="ts">
import type { PuzzleOfferItem } from '@/services/openapi/types.gen'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'

defineProps<{
  items: PuzzleOfferItem[],
  triangles: string[],
}>()

const emit = defineEmits(['click'])
</script>

<template>
  <div class="fragment-offer__puzzle-box">
    <div
      v-for="reward, index in items"
      :key="reward.idx"
      class="fragment-offer__puzzle-triangle"
      :class="{ 'fragment-offer__puzzle-triangle_hidden': reward.isPurchased }"
      @click="() => emit('click', reward.idx, reward.price)"
    >
      <div class="fragment-offer__puzzle-price text-shadow text-shadow_black">
        <div
          v-if="reward.price.currency !== 'usd'"
          class="icon-bg fragment-offer__puzzle-price-currency"
          :class="CURRENCY_TO_IMAGE_CLASS[reward.price.currency]"
        ></div>
        {{ (reward.price.currency === 'usd' ? '$ ' : '') + reward.price.amount }}
      </div>
      <img :src="triangles[index]" alt="triangle" />
    </div>
  </div>
</template>

<style lang="scss">
.fragment-offer {
  &__puzzle-box {
    position: relative;
  }

  &__puzzle-price {
    position: absolute;
    transform: translate(-50%, -50%);

    display: flex;
    flex-direction: column;
    align-items: center;

    font-size: 16px;
    line-height: 20px;
    color: white;
    z-index: 1;
    white-space: nowrap;
  }

  &__puzzle-price-currency {
    margin-bottom: -12px;
  }

  &__puzzle-triangle {
    position: absolute;
    opacity: 1;
    transition: opacity 0.3s;

    &_hidden {
      opacity: 0;
      pointer-events: none;
    }

    img {
      position: absolute;
      object-fit: contain;
      width: 100%;
      height: 100%;
    }

    &:nth-child(1) {
      top: 0;
      left: 0;
      width: 100%;
      height: 48%;
      clip-path: polygon(0 0, 100% 0, 100% 8%, 54% 100%, 47% 100%, 0 6%);

      .fragment-offer__puzzle-price {
        top: 40%;
        left: 50%;
      }
    }
    &:nth-child(2) {
      bottom: 0;
      left: 0;
      width: 100%;
      height: 48%;
      clip-path: polygon(46% 0, 53% 0, 100% 95%, 100% 100%, 0 100%, 0 92%);

      .fragment-offer__puzzle-price {
        top: 60%;
        left: 50%;
      }
    }
    &:nth-child(3) {
      top: 0;
      left: 0;
      width: 48%;
      height: 100%;
      clip-path: polygon(7% 0, 100% 46%, 100% 53%, 6% 100%, 0 100%, 0 0);

      .fragment-offer__puzzle-price {
        top: 50%;
        left: 40%;
      }
    }
    &:nth-child(4) {
      top: 0;
      right: 0;
      width: 48%;
      height: 100%;
      clip-path: polygon(0 46%, 91% 0, 100% 0, 100% 100%, 92% 100%, 0 53%);

      .fragment-offer__puzzle-price {
        top: 50%;
        left: 60%;
      }
    }
  }
}
</style>
