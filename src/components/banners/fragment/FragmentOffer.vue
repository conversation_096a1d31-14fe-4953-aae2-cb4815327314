<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Price } from '@/services/openapi/types.gen'

import { formatNumberWithSeparator } from '@/utils/number'
import { useShopItems } from '@/services/client/useShopItems'
import { usePurchase } from '@/composables/usePurchase'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useToast } from '@/stores/toastStore'
import { useI18n } from 'vue-i18n'
import { useReward } from '@/composables/useReward'

import LoaderText from '../../LoaderText.vue'
import CountdownTimerManual from '../../UI/CountdownTimerManual.vue'
import FragmentBox from '@/components/banners/fragment/FragmentBox.vue'
import RewardItem from '@/components/RewardItem.vue'

import banner from '@/assets/images/temp/fragment/banner.png'
import frozenBorder from '@/assets/images/temp/fragment/border-frozen.png'
import { CURRENCY_TO_IMAGE_CLASS, REWARD_TO_IMAGE } from '@/composables/useIconImage'

import image1 from '@/assets/images/temp/fragment/image-1.png'
import image2 from '@/assets/images/temp/fragment/image-2.png'
import image3 from '@/assets/images/temp/fragment/image-3.png'
import image4 from '@/assets/images/temp/fragment/image-4.png'
import image5 from '@/assets/images/temp/fragment/image-5.png'
import image6 from '@/assets/images/temp/fragment/image-6.png'
import image7 from '@/assets/images/temp/fragment/image-7.png'
import image8 from '@/assets/images/temp/fragment/image-8.png'
import image9 from '@/assets/images/temp/fragment/image-9.png'
import image10 from '@/assets/images/temp/fragment/image-10.png'

import tr1a from '@/assets/images/temp/fragment/1-a.png'
import tr1b from '@/assets/images/temp/fragment/1-b.png'
import tr1c from '@/assets/images/temp/fragment/1-c.png'
import tr1d from '@/assets/images/temp/fragment/1-d.png'
import tr2a from '@/assets/images/temp/fragment/2-a.png'
import tr2b from '@/assets/images/temp/fragment/2-b.png'
import tr2c from '@/assets/images/temp/fragment/2-c.png'
import tr2d from '@/assets/images/temp/fragment/2-d.png'
import tr3a from '@/assets/images/temp/fragment/3-a.png'
import tr3b from '@/assets/images/temp/fragment/3-b.png'
import tr3c from '@/assets/images/temp/fragment/3-c.png'
import tr3d from '@/assets/images/temp/fragment/3-d.png'
import tr4a from '@/assets/images/temp/fragment/4-a.png'
import tr4b from '@/assets/images/temp/fragment/4-b.png'
import tr4c from '@/assets/images/temp/fragment/4-c.png'
import tr4d from '@/assets/images/temp/fragment/4-d.png'
import { hapticsService } from '@/shared/haptics/hapticsService'
import InstructionButton from '@/components/UI/InstructionButton.vue'

const { t } = useI18n()

const IMAGES = [
  image1, image2, image3, image4, image5,
  image6, image7, image8, image9, image10
]

const TRIANGLES_1 = [tr1a, tr1b, tr1c, tr1d]
const TRIANGLES_2 = [tr2a, tr2b, tr2c, tr2d]
const TRIANGLES_3 = [tr3a, tr3b, tr3c, tr3d]
const TRIANGLES_4 = [tr4a, tr4b, tr4c, tr4d]

defineProps<{
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()

const emit = defineEmits(['close'])

const closeBanner = () => {
  emit('close')
}

const { showToast } = useToast()
const { showRewards } = useReward()
const { purchasePuzzle, isPendingPurchasePuzzle, onPuzzlePurchased, getNextPuzzle } = usePurchase()

const { shopItems, isLoading } = useShopItems()
const { playerState } = usePlayerState()

const puzzleOfferIndex = computed(() =>
  shopItems.value?.puzzleOffers[0].currentStage ?? 0
)
const puzzleOffer = computed(() =>
  shopItems.value?.puzzleOffers[0]
)
const isPuzzleRevealed = ref(false)
const grandPrize = computed(() => puzzleOffer.value?.stageGrantPrize)

const purhcase = (offerId: number, itemIndex: number, price: Price) => {
  hapticsService.triggerImpactHapticEvent('light')
  if (isPendingPurchasePuzzle.value) return
  purchasePuzzle(offerId, itemIndex, price)
    .then(() => {
      onPuzzlePurchased(offerId, itemIndex)
      isPuzzleRevealed.value = !!puzzleOffer.value?.items.every(offer => offer.isPurchased)
      if (isPuzzleRevealed.value) {
        setTimeout(() => {
          showRewards(grandPrize.value!)
          getNextPuzzle(offerId)
          isPuzzleRevealed.value = false
        }, 2000)
      }
    })
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
      }
    })
}
</script>

<template>
  <div class="sliced-window fragment-offer">
    <InstructionButton
      class="absolute top-[5px] left-[5px]"
      instruction-type="ice-fragment-instruction"
    />
    <img
      class="sliced-window__banner fragment-offer__banner"
      :src="banner"
      alt="event banner"
    />
    <div class="close-button" @click="closeBanner"></div>
    <div class="sliced-window__top">
      <p class="text-[14px] text-shadow text-shadow_black text-shadow_thin text-center mb-[7px]">
        {{ t('fragmentOffer.description') }}
      </p>
      <div
        v-if="grandPrize"
        class="bg-[#00348F33] flex justify-evenly items-center w-full h-[92px] rounded-[5px]"
      >
        <RewardItem
          v-for="reward in grandPrize"
          :key="reward.type"
          class="w-[25%] h-[65%]"
          value-class="translate-y-[9px]"
          :image="REWARD_TO_IMAGE[reward.type] ?? ''"
          :type="reward.type"
          :amount="reward.value"
        />
      </div>
      <div class="sliced-window__timer">
        <span v-text="t('endsIn')"></span>
        <CountdownTimerManual
          class="text-[10px] leading-[14px] text-white"
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
          digital
        />
      </div>
    </div>
    <div class="sliced-window__top-placeholder"></div>
    <div class="sliced-window__bottom fragment-offer__bottom">
      <div class="sliced-window__balance fragment-offer__balance">
        <div class="icon-bg !w-6 !h-6" :class="CURRENCY_TO_IMAGE_CLASS['puzzleCoins']"></div>
        <span class="text-20px text-shadow text-shadow_black">
          {{ formatNumberWithSeparator(playerState?.puzzleCoins ?? 0) }}
        </span>
      </div>
      <div v-if="puzzleOffer" class="fragment-offer__puzzle-container">
        <img class="fragment-offer__puzzle-image" :src="IMAGES[puzzleOfferIndex]" alt="bg image" />
        <div
          class="fragment-offer__puzzle-overlay"
          :class="{ 'fragment-offer__puzzle-overlay_hidden': isPuzzleRevealed }"
        ></div>
        <img class="fragment-offer__puzzle-border-frozen" :src="frozenBorder" alt="bg image" />
        <FragmentBox
          :items="puzzleOffer.items.slice(0, 4)"
          :triangles="TRIANGLES_1"
          @click="(itemIndex: number, price: Price) => purhcase(puzzleOffer!.id, itemIndex, price)"
        />
        <FragmentBox
          :items="puzzleOffer.items.slice(4, 8)"
          :triangles="TRIANGLES_2"
          @click="(itemIndex: number, price: Price) => purhcase(puzzleOffer!.id, itemIndex, price)"
        />
        <FragmentBox
          :items="puzzleOffer.items.slice(8, 12)"
          :triangles="TRIANGLES_3"
          @click="(itemIndex: number, price: Price) => purhcase(puzzleOffer!.id, itemIndex, price)"
        />
        <FragmentBox
          :items="puzzleOffer.items.slice(12, 16)"
          :triangles="TRIANGLES_4"
          @click="(itemIndex: number, price: Price) => purhcase(puzzleOffer!.id, itemIndex, price)"
        />
      </div>
      <!-- <VButton class="!w-[90%]" type="accent" :text="t('actions.claim')" /> -->
    </div>
    <LoaderText
      class="sliced-window__loading"
      :class="{
        'sliced-window__loading_active': isLoading || isPendingPurchasePuzzle
      }"
      :is-loading="isLoading || isPendingPurchasePuzzle"
    />
  </div>
</template>

<style lang="scss">
.fragment-offer {
  --sliced-outline-color: #FFD634;
  --sliced-top-bg: linear-gradient(180deg, #D88AFF 0%, #AF64FF 100%);
  --sliced-top-shadow:
    0 2px 1px 0 #00000040,
    0 2px 0 0 #E0A6FF inset,
    0 -4px 0 0 #9360E8 inset;
  --sliced-bg: linear-gradient(180deg, #D88AFF 0%, #AF64FF 100%);

  width: auto;
  max-width: 90vw;
  height: auto;
  max-height: 80vh;

  &__bottom {
    width: 100%;
    padding: 40px 20px 20px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    row-gap: 8px;
  }

  &__puzzle-container {
    position: relative;
    border: 5px solid #FFD634;
    border-radius: 10px;
    /** calc(90vw - 9px - 40px) */
    // 90vw - banner max-width; 40px - padding; 9px - margin
    /** calc(80vh - 135px - 60px - 9px) */
    // 80vh - banner max-height; 135px - sliced-window__top-placeholder height;
    // 60px - Y padding; 9px - margin
    width: min(calc(90vw - 9px - 40px), calc(80vh - 135px - 60px - 9px));
    aspect-ratio: 1 / 1;

    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;

    overflow: hidden;

    box-shadow: 0px 0px 22px -1px rgba(0,0,0,0.75);
    -webkit-box-shadow: 0px 0px 22px -1px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 0px 22px -1px rgba(0,0,0,0.75);
  }

  &__puzzle-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &__puzzle-border-frozen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
  }

  &__puzzle-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #2f75d8;
    opacity: 0.64;
    transition: opacity 0.7s linear;

    &_hidden {
      opacity: 0;
    }
  }

  &__balance {
    top: 130px;
  }

  &__banner {
    max-width: 290px;
    transform: translate(-50%, calc(-75%));
  }
}
</style>
