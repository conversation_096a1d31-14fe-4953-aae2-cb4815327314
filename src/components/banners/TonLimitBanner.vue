<script setup lang="ts">
import eventBanner from '@/assets/images/temp/banners/ton-limit/banner.png'
import eventImage from '@/assets/images/temp/banners/ton-limit/image.png'
import EventBanner from '@/components/events/EventBanner.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { isOpen, openWindowInQueue, closeWindowInQueue } = useWindowQueue('ton-limit-reached-banner')

onMounted(openWindowInQueue)
</script>

<template>
  <EventBanner
    class="ton-limit-banner w-full h-full"
    :isOpen="isOpen"
    :buttons="[{ text: t('actions.got'), type: 'success', onClick: closeWindowInQueue }]"
    :banner="eventBanner"
    :image="eventImage"
    imageFullSize
  >
    <div
      class="bg-[#32004DA6] flex flex-col justify-center items-center w-full rounded-[9px] p-1 mb-3"
    >
      <i18n-t
        class="text-[16px] leading-[20px] text-center whitespace-normal text-shadow text-shadow_black text-white"
        tag="p"
        :keypath="'tonLimit.description'"
      >
        <template #ton>
          <span class="text-shadow_yellow"> 0.25 TON! </span>
        </template>
        <template #league>
          <span class="text-shadow_yellow"> 7th League! </span>
        </template>
      </i18n-t>
    </div>
  </EventBanner>
</template>

<style lang="scss">
.ton-limit-banner {
  top: 0;

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border-radius: 20px;
    border: 6px solid #ff383e;

    &__banner {
      width: 80%;
      top: -20px;
    }
  }
}
</style>
