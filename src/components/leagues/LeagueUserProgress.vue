<script setup lang="ts">
import { computed } from 'vue';
import NewBadge from '../UI/NewBadge.vue'
import ProgressBar from '../UI/ProgressBar.vue';
import RedDotBadge from '../UI/RedDotBadge.vue'
import { usePlayerState } from "@/services/client/usePlayerState";
import leaguesList from '@/preload/leagues-list.json'
import LeagueBadge from './LeagueBadge.vue';
import { getLeaugesMaxLevel } from '@/utils/leagues';
import { useHasUserSeenLeagues } from '@/stores/hasUserSeenStore';

const { playerState } = usePlayerState()

const props = defineProps<{
  userLeagueNumber: number;
}>()

const hasUserSeenLeaguesStore = useHasUserSeenLeagues()

const maxLeagueLevel = getLeaugesMaxLevel(leaguesList.list)
const userLeague = computed(() => leaguesList.list.find((league) => league.leagueLevel === props.userLeagueNumber))
const userTickets = computed(() => playerState.value!.tickets ?? 0)

const ticketsStart = computed(() => userLeague.value?.ticketsRange[0] ?? 0)
const ticketsEnd = computed(() => userLeague.value?.ticketsRange[1] ?? 1)
const ticketsDelta = computed(() => ticketsEnd.value - ticketsStart.value)

const userTicketsProgress = computed(() =>
  maxLeagueLevel === props.userLeagueNumber
    ? ticketsDelta.value
    : Math.max(userTickets.value - ticketsStart.value, 0)
)

</script>

<template>
  <div class="league-user-progress relative" @click="hasUserSeenLeaguesStore.onClick">
    <LeagueBadge
      class="absolute z-20 -left-[20px] top-1/2 -translate-y-1/2"
      :league="userLeagueNumber"
    />
    <ProgressBar
      class="relative z-10 h-[14px] w-[109px] !rounded-[3px]"
      inner-wrapper-class="p-[2px] rounded-[1px]"
      :progress="userTicketsProgress"
      :goal="ticketsDelta"
      :transition-speed="0"
    />
    <div class="league-user-progress__skew-lines space-x-[18px]">
      <div class="league-user-progress__skew-lines-block"></div>
      <div class="league-user-progress__skew-lines-block"></div>
    </div>
    <NewBadge v-if="!hasUserSeenLeaguesStore.hasSeenLeagues" class="-top-[5px] -right-[10px] z-10" />
    <RedDotBadge v-else-if="hasUserSeenLeaguesStore.hasNewLeagueInfo" class="-top-[9px] -right-[7px]"/>
  </div>
</template>

<style lang="scss" scoped>
.league-user-progress {
  padding: 6px 5px 7px 25px;
  background: linear-gradient(360deg, #4CBCFA 0%, #B6E4FF 92.65%);
  border: 2px solid #002C6E;
  border-radius: 9px;
  box-shadow: 0px -2px #0082CA inset, 0px 1px #FFFFFF inset;

  &:active {
    background: linear-gradient(360deg, #3d9dd1 0%, #90b8cf 92.65%);
  }

  &__skew-lines {
    position: absolute;
    top: 0;
    right: 10px;
    height: 100%;
    display: flex;

    &-block {
      height: 100%;
      transform: skew(-15deg);

      &:first-child {
        background-color: #FFFFFF4A;
        width: 30px;
      }
      &:last-child {
        position: relative;
        left: -10px;
        background-color: #FFFFFF4A;
        width: 13px;
      }
    }
  }
}
</style>
