<script setup lang="ts">
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { useI18n } from 'vue-i18n';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'
import VOverlay from '../VOverlay.vue';

export type TabProp = {
  id: string
  name: string
}

const { t } = useI18n()

const props = defineProps<{
  isOpen: boolean;
  title?: string;
  class?: string;
  overlayClass?: string;
  hideClose?: boolean;
  tabs: TabProp[];
  short?: boolean;
}>()
const emit = defineEmits(['close'])

const close = () => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('close')
}

</script>

<template>
  <VOverlay
    :isOpen="isOpen"
    class="modal-window-overlay flex items-center justify-center"
    :class="props.overlayClass || ''"
    @click-self="() => emit('close')"
    :short="short"
  >
    <div class="modal-window-tab" :class="props.class">
      <slot></slot>
      <div v-if="!hideClose" class="close-button absolute top-[12px] right-[13px]" @click="close"></div>
      <TabGroup>
        <TabPanels as="div" class="modal-window-tab__panels">
          <TabPanel v-for="tab in props.tabs" :key="tab.id" as="template">
            <slot :name="tab.id"></slot>
          </TabPanel>
        </TabPanels>
        <TabList class="modal-window-tab__tab-list">
          <Tab
            v-for="tab in props.tabs"
            :key="tab.id"
            as="template"
            v-slot="{ selected }"
          >
            <button
              :class="{
                'modal-window-tab__tab': true,
                'modal-window-tab__tab_active': selected
              }"
            >
              <div class="text-shadow">
                {{ t(tab.name) }}
              </div>
            </button>
          </Tab>
        </TabList>
      </TabGroup>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.modal-window-overlay {
  padding: 0 16px;
}

.modal-window-tab {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;

  .close-button {
    z-index: 1;
    --close-btn-background-color: #275a9d;
  }

  &__panels {
    position: relative;
    flex: 1 1 auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 44px 13px 20px;
    background: linear-gradient(180deg, #009BE0 0%, #0074C6 100%);
    border-radius: 12px 12px 0 0;
    box-shadow: inset 0 2px #6AC1E9;
  }

  &__tab-list {
    display: flex;
    flex: 0 0 38px;
  }

  &__tab {
    flex: 1;
    background: linear-gradient(180.96deg, #437BC7 -119.77%, #83FBFF 91.41%);
    box-shadow: 0px -4px 0px 0px #D8FDFF inset;
    border-radius: 0 0 12px 12px;

    font-size: 20px;
    color: white;

    &:focus-within {
      outline: none;
    }

    &_active {
      background: linear-gradient(180deg, #0074C6 0%, #278CDF 106.86%);
      box-shadow: 0px -4px 0px 0px #7DC3FF52 inset;
      color: white;
    }
  }
}
</style>
