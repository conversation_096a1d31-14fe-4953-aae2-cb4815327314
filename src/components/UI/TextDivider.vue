<script setup lang="ts">
withDefaults(
  defineProps<{ color?: string }>(),
  { color: '#b3dcf7' }
)
</script>

<template>
  <div class="text-divider" :style="{ '--text-divider-color': color }">
    <div class="text-divider__line"></div>
    <slot></slot>
    <div class="text-divider__line"></div>
  </div>
</template>

<style lang="scss">
.text-divider {
  --text-divider-color: #b3dcf7;
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 10px;

  color: var(--text-divider-color);
  font-size: 14px;
  line-height: 19px;

  &__line {
    flex: 1;
    height: 3px;
    background-color: var(--text-divider-color);
    border-radius: 2px;
  }
}
</style>