<script setup lang="ts">
import AchievementNotification from '@/components/achievements/AchievementNotification.vue'
import {
  type AchievementName,
  ACHIEVEMENTS_IMAGES,
  ACHIEVEMENTS_LEVELS_FRAMES
} from '@/constants/achievements.ts'
import { useNotification } from '@/stores/notificationStore.ts'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'

const notificationsStore = useNotification()
const { notification } = storeToRefs(notificationsStore)

const achievementLevel = computed(() => {
  const level = notification.value.achievement?.levels[0].level
  return level || 1
})
</script>

<template>
  <div
    class="notification"
    :class="{
      notification_show: notification.achievement?.id
    }"
    :style="{
      '--time': `${notification.timeout}ms`
    }"
  >
    <AchievementNotification
      v-if="notification.achievement?.id"
      :image="ACHIEVEMENTS_IMAGES[notification.achievement?.id as AchievementName] ?? ''"
      :frame-image="ACHIEVEMENTS_LEVELS_FRAMES[achievementLevel - 1] ?? ''"
      :name="notification.achievement?.id"
      :level="achievementLevel"
    />
  </div>
</template>

<style scoped lang="scss">
.notification {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 100%;
  z-index: 9999;
  transform: translateY(0);
  opacity: 0;
  --time: 5s;

  &_show {
    animation: slide-in-anim var(--time) forwards;
  }

  @keyframes slide-in-anim {
    0% {
      opacity: 0;
      transform: translateY(0);
    }
    10% {
      opacity: 1;
      transform: translateY(-200%);
    }
    90% {
      opacity: 1;
      transform: translateY(-200%);
    }
    100% {
      opacity: 0;
      transform: translateY(0);
    }
  }
}
</style>
