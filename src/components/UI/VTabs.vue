<script setup lang="ts">
import { TabGroup, Tab<PERSON>ist, Tab, TabPane<PERSON>, TabPanel } from '@headlessui/vue'
import { useI18n } from 'vue-i18n'
import RedDotBadge from '@/components/UI/RedDotBadge.vue'

const { t } = useI18n()

export type TabProp = {
  id: string
  name: string
  notification?: boolean
  disabled?: boolean
}

const props = defineProps<{
  tabs: TabProp[]
  isShadow?: boolean
}>()

</script>

<template>
  <div class="tabs-group">
    <TabGroup>
      <TabPanels as="div" class="tabs-group__panels">
        <TabPanel v-for="tab in props.tabs" :key="tab.id" as="template">
          <slot :name="tab.id"></slot>
        </TabPanel>
      </TabPanels>
      <TabList class="tabs-group__tab-list">
        <div v-if="props.isShadow" class="tabs-group__tab-list-shadow-gradient" />
        <Tab
          v-for="tab in props.tabs"
          :key="tab.id"
          as="template"
          v-slot="{ selected }"
          :disabled="tab.disabled"
        >
          <button
            :class="{
              'tabs-group__tab': true,
              'tabs-group__tab_active': selected,
              'tabs-group__tab_disabled': tab.disabled,
            }"
          >
            <RedDotBadge v-if="tab.notification" class="top-[0px] right-[1px]" />
            <div class="text-shadow" :class="{ 'text-shadow_black': tab.disabled }">
              {{ t(tab.name) }}
            </div>
          </button>
        </Tab>
      </TabList>
    </TabGroup>
  </div>
</template>
