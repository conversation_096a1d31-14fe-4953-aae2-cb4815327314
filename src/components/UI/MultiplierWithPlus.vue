<script setup lang="ts">
import BalanceItem from './BalanceItem.vue'

const { multiplier, plus } = defineProps<{
  multiplier: number
  plus: number | string
}>()
</script>

<template>
  <div class="multiplier-with-plus">
    <BalanceItem
      class="multiplier-with-plus__multiplier"
      image-class="multiplier-with-plus__multiplier-image"
      bar-class="multiplier-with-plus__multiplier-bar"
      balance-class="multiplier-with-plus__multiplier-balance"
      iconName="multiplier-tickets-bg"
      gold
    >
      {{ multiplier }}
    </BalanceItem>
    <BalanceItem
      v-if="plus"
      class="multiplier-with-plus__plus"
      image-class="multiplier-with-plus__plus-image"
      balance-class="multiplier-with-plus__plus-balance text-shadow text-shadow_black text-shadow_fat text-gradient"
      bar-class="multiplier-with-plus__plus-bar"
    >
      +{{ plus }}
    </BalanceItem>
  </div>
</template>

<style lang="scss">
.multiplier-with-plus {
  display: flex;

  &__multiplier {
    z-index: 10;

    &-image {
      top: -21px;
      left: 5px;
      width: 62px;
      height: 62px;
    }

    &-bar {
      height: 32px;
      padding: 0 10px 0 35px;
    }

    &-balance {
      font-size: 24px;
      line-height: 32px;
    }
  }

  &__plus {
    position: relative;
    left: -10px;

    &-image {
      left: 5px;
      width: 53px;
      height: 53px;
    }

    &-bar {
      height: 32px;
      padding: 0 10px 0 22px;
    }

    &-balance {
      font-size: 28px;
      line-height: 28px;
      color: #c44f00;
      white-space: nowrap;
    }
  }
}
</style>
