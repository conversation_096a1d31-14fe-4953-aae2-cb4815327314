<script setup lang="ts">
export type Props = {
  animated?: boolean
}

withDefaults(defineProps<Props>(), {
  animated: true
})
</script>

<template>
  <div class="marker" :class="{ marker_animated: animated }">
    <slot></slot>
  </div>
</template>

<style lang="scss">
.marker {
  position: absolute;
  background: linear-gradient(180deg, #ff844b 0%, #e05515 100%);
  width: 15px;
  height: 15px;
  border-radius: 50%;
  box-shadow:
    #00000040 0 2,
    inset #ffa652 0 1px;
  z-index: 1;

  &_with-text {
    text-align: center;
    width: auto;
    height: auto;
    min-width: 20px;
    min-height: 16px;
    padding: 4px 5px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 800;
  }

  &_animated {
    animation: bounce 3s infinite;
  }

  @keyframes bounce {
    0%, 35%, 65%, 100% {
      transform: translateY(0) scale(1);
    }
    45% {
      transform: translateY(-5px) scale(1.09);
    }
    50% {
      transform: translateY(0) scale(1);
    }
    55% {
      transform: translateY(-2px) scale(1.03);
    }
  }
}
</style>
