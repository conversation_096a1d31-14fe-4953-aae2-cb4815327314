<script lang="ts" setup>
const props = defineProps<{
  min: number;
  max: number;
  step: number;
}>();

const value = defineModel()
const emit = defineEmits(['onChange'])
</script>

<template>
  <div class="range-input">
    <input
      type="range"
      v-model="value"
      :min="props.min"
      :max="props.max"
      :onchange="() => emit('onChange', value)"
      :step='props.step'
    />
  </div>
</template>

<style lang="scss" scoped>
.range-input {
  display: flex;
  align-items: center;

  input[type="range"] {
    margin-right: 10px;
  }
}
</style>
