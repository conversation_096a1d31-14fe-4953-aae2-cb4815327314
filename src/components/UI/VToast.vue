<script setup lang="ts">
import { useToast } from '@/stores/toastStore';

const { toast } = useToast();
</script>

<template>
  <div class="toast text-[#1A338E] font-bold" :class="{
    'toast_show': toast.show,
    'toast_info': toast.type === 'info',
    'toast_danger': toast.type === 'danger',
    'toast_warning': toast.type === 'warning',
  }">
    {{ toast.message }}
  </div>
</template>

<style scoped lang="scss">
.toast {
  position: fixed;
  top: calc(var(--inset-top) + 6px);
  right: 10px;
  max-width: 250px;
  transform: translateX(100%);
  z-index: 9999;
  padding: 10px 20px;
  border-radius: 5px;
  opacity: 0;
  transition: opacity 0.2s, transform 0.2s;
  pointer-events: none;
  white-space: pre-wrap;
  text-align: right;
  
  &_show {
    transform: translateX(0%);
    opacity: 1;
    pointer-events: all;
  }

  &_info {
    background-color: #DBEAFF;
  }

  &_danger {
    color: white;
    background-color: #bb3235;
  }

  &_warning {
    color: black;
    background-color: #FFD700;
  }
}
</style>
