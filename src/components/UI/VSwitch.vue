<script setup lang="ts">
import { Switch } from '@headlessui/vue'

const { enabled } = defineProps<{
  enabled: boolean
}>()

const emit = defineEmits(['onChange'])
</script>

<template>
  <Switch
    :class="{ switch_active: enabled }"
    class="switch focus:outline-none"
    @click="() => emit('onChange', !enabled)"
  >
    <span
      aria-hidden="true"
      :class="{ switch__circle_active: enabled }"
      class="switch__circle"
    ></span>
  </Switch>
</template>

<style lang="scss">
.switch {
  position: relative;
  display: inline-flex;
  height: 30px;
  width: 56px;
  padding: 3px;
  border-radius: 999999px;
  transition: background-color 200ms ease-in-out;
  background-color: #1e4073;

  &_active {
    background-color: #60e232;

    .switch__circle {
      transform: translateX(26px);
    }
  }

  &__circle {
    pointer-events: none;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background-color: white;
    transition: transform 200ms ease-in-out;
    border: 1px solid black;
    box-shadow:
      0 2px #00000040,
      inset 0 -4px #b1cee7;
  }
}
</style>
