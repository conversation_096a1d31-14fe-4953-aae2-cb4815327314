<script setup lang="ts">
const { selected } = defineProps<{
  selected: boolean
}>()

const emit = defineEmits(['click'])
</script>

<template>
  <div class="radio-button" @click="() => emit('click')">
    <div class="radio-button__circle">
      <div v-if="selected" class="check-icon"></div>
    </div>
    <slot></slot>
  </div>
</template>

<style lang="scss">
.radio-button {
  display: flex;
  align-items: center;
  column-gap: 10px;
  cursor: pointer;

  &__circle {
    width: 30px;
    height: 30px;
    padding: 1px;
    border-radius: 50%;
    background-color: #1E4073;
  }
}
</style>
