<script setup lang="ts">
import { LEAGUE_TO_AVATAR } from '@/constants/leagues'
import { computed, type StyleValue } from 'vue';

const props = defineProps<{
  size?: string,
  src?: string | null,
  rounded?: boolean,
  league?: number,
}>()

const image = computed(() => props.src || LEAGUE_TO_AVATAR[props.league || 1])

const style: StyleValue = {
  borderRadius: props.rounded ? '50%' : '0',
}
if (props.size) {
  style.height = props.size + 'px'
  style.width = props.size + 'px'
}
</script>

<template>
  <div
    class="avatar"
    :style="{
      height: props.size + 'px',
      width: props.size + 'px',
      borderRadius: props.rounded ? '50%' : '0',
    }"
  >
    <div
      class="avatar__image"
      :style="{ backgroundImage: `url(${image})` }"
      alt="avatar"
    ></div>
  </div>
</template>

<style lang="scss">
.avatar {
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  &__image {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 100%;
    width: 100%;
  }
}
</style>
