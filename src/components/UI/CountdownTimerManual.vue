<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps<{
  days?: number
  hours?: number
  minutes: number
  seconds: number
  digital?: boolean
}>()

const formatTime = (time: number) => time.toString().padStart(2, '0')
</script>

<template>
  <span class="countdown-timer tabular-nums">
    <template v-if="digital">
      <span v-if="props.days">{{ props.days }}{{ t('days') }}&nbsp;</span>
      <span v-if="props.hours">{{ formatTime(props.hours) }}:</span>
      <span>{{ formatTime(minutes) }}:</span>
      <span>{{ formatTime(seconds) }}</span>
    </template>
    <template v-else>
      <span v-if="props.days">{{ props.days }}{{ t('days') }}&nbsp;</span>
      <span v-if="props.hours">{{ formatTime(props.hours) }}{{ t('hours') }}&nbsp;</span>
      <span v-if="props.minutes && !props.days"
        >{{ formatTime(props.minutes) }}{{ t('minutes') }}&nbsp;</span
      >
      <span v-if="!props.hours"
        >{{ formatTime(props.seconds) }}{{ t('seconds') }}</span
      >
    </template>
  </span>
</template>

<style scoped>
.countdown-timer {
  display: inline-block;
  text-align: center;
}
</style>
