<script setup lang="ts">
import { computed } from 'vue'

export type BadgeSize = 'small' | 'large'

export type Props = {
  size?: BadgeSize
  animated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'small',
  animated: true
})

const badgeSize = computed(() => `badge_size_${props.size}`)
const badgeClass = computed(() => {
  const result = `badge ${badgeSize.value}`
  return props.animated ? result + ' ' + 'badge_animated' : result
})
</script>

<template>
  <div class="absolute">
    <div :class="badgeClass">
      <span class="badge_text">NEW</span>
    </div>
  </div>
</template>

<style lang="scss">
.badge {
  padding: 0 10px;

  background: linear-gradient(180deg, #fc8046 0%, #e05616 100%);
  border: 1px solid #662000;
  border-radius: 18px;
  box-shadow: inset 0 -2px #ae3700;

  font-size: 14px;
  line-height: 20px;
  color: white;
  white-space: nowrap;
  z-index: 10;

  &_text {
    filter: drop-shadow(0 -1px #B10000);
  }

  &_size {
    &_small {
      font-size: 10px;
      line-height: 16px;
    }
  }

  &_animated {
    animation: bounce 3s infinite;
  }

  @keyframes bounce {
    0%,
    35%,
    65%,
    100% {
      transform: translateY(0) scale(1);
    }
    45% {
      transform: translateY(-5px) scale(1.09);
    }
    50% {
      transform: translateY(0) scale(1);
    }
    55% {
      transform: translateY(-2px) scale(1.03);
    }
  }
}
</style>
