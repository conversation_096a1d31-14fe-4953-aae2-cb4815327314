<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  progress: number
  goal: number
  innerClass?: string
  innerWrapperClass?: string
  isCompleted?: boolean
  pointer?: boolean
  transitionSpeed?: number
  stackable?: boolean
}>()

const percentage = computed(() => Math.min((props.progress / props.goal) * 100, 100))

</script>

<template>
  <div
    class="progress-bar"
  >
    <div
      class="progress-bar__inner-wrapper"
      :class="[innerWrapperClass || '', { 'progress-bar__inner-wrapper_stackable': stackable }]"
    >
      <template v-if="stackable">
        <div
          v-for="i in goal"
          :key="i"
          class="progress-bar__stackable-inner"
          :class="{ 'progress-bar__stackable-inner_completed': i <= progress }"
        >
        </div>
      </template>
      <div
        v-else
        class="progress-bar__inner"
        :style="{
          '--progress-width': `${percentage}%`,
          '--transition-speed': `${transitionSpeed ?? 1}s`
        }"
        :class="[innerClass || '', isCompleted ? 'progress-bar__inner_completed' : '']"
      >
      </div>
      <div class="progress-bar__content">
        <slot></slot>
      </div>
    </div>
    <div class="progress-bar__append" v-if="$slots.append">
      <slot name="append"></slot>
    </div>
    <div
      v-if="pointer"
      class="progress-bar__pointer"
      :style="{ left: `${percentage}%` }"
    >
      {{ progress }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
</style>
