<script lang="ts" setup>
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import VOverlayShow from '../VOverlayShow.vue';

const props = defineProps<{
  isOpen: boolean;
  class: string;
}>()
const emit = defineEmits(['close'])

const onCloseClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('close')
}

</script>

<template>
  <VOverlayShow
    :isOpen="props.isOpen"
    class="overlay_short"
    @click-self="() => emit('close')"
  >
    <Transition name="slide">
      <div
        v-if="props.isOpen"
        class="slide-window"
        :class="props.class"
      >
        <div class="close-button absolute top-[17px] right-[16px]" @click="onCloseClick"></div>
        <slot></slot>
      </div>
    </Transition>
  </VOverlayShow>
</template>

<style lang="scss" scoped>
.slide-window {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;

  padding: 17px 25px 35px;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(360deg, #8DD7F6 0%, #CBEFFF 92.65%);
  box-shadow: 0 2px 1px #00000040, 0 4px #FFFFFF inset;

  transform: translateY(0);

  .close-button {
    --close-btn-background-color: #275a9d;
  }
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateY(100%);
}
</style>
