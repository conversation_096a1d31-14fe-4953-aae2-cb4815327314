<script setup lang="ts">
import { useCountdownTimer, type TimerId } from '@/composables/useCountdownTimer'
import { onBeforeUnmount, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const emit = defineEmits(['countdown-finished', 'tick'])

const props = withDefaults(
  defineProps<{
    timerId: TimerId
    totalSeconds?: number
    time?: {
      hours: number
      minutes: number
      seconds: number
    }
    digital?: boolean
  }>(),
  {
    totalSeconds: undefined,
    time: () => ({
      hours: 0,
      minutes: 0,
      seconds: 0
    }),
    digital: false
  }
)

const {
  days,
  hours,
  minutes,
  seconds,
  displayDays,
  displayHours,
  displayMinutes,
  displaySeconds,
  formatTime,
  initTimerWithTotal,
  initTimerWithTime,
  stopTimer
} = useCountdownTimer(props.timerId, {
  onTimerEnd: () => emit('countdown-finished'),
  onTimerTick: (countdown: number) => emit('tick', countdown)
})

// Initialize countdown based on props
onMounted(() => {
  if (props.totalSeconds !== undefined) {
    initTimerWithTotal(props.totalSeconds)
  } else if (props.time) {
    initTimerWithTime(props.time)
  }
})

// Clean up the interval when the component is destroyed
onBeforeUnmount(() => {
  stopTimer()
})

defineExpose({
  initTimerWithTotal
})
</script>

<template>
  <span class="countdown-timer tabular-nums">
    <template v-if="digital">
      <span v-if="displayDays">{{ days }}{{ t('days') }}&nbsp;</span>
      <span v-if="displayHours">{{ formatTime(hours) }}:</span>
      <span>{{ formatTime(minutes) }}:</span>
      <span>{{ formatTime(seconds) }}</span>
    </template>
    <template v-else>
      <span v-if="displayDays">{{ days }}{{ t('days') }}&nbsp;</span>
      <span v-if="displayHours">{{ formatTime(hours) }}{{ t('hours') }}&nbsp;</span>
      <span v-if="displayMinutes">{{ formatTime(minutes) }}{{ t('minutes') }}&nbsp;</span>
      <span v-if="displaySeconds">{{ formatTime(seconds) }}{{ t('seconds') }}</span>
    </template>
  </span>
</template>

<style scoped>
.countdown-timer {
  display: inline-block;
  text-align: center;
}
</style>
