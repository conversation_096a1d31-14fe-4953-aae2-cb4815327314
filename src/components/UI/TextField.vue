<script setup lang="ts">
import { ref } from 'vue';

type InputType = 'text' | 'number';
type InputSize = 'small' | 'medium';

const {
  label = '',
  type = 'text',
  size = 'medium',
  placeholder,
  min,
  max
} = defineProps<{
  label?: string;
  placeholder: string;
  type?: InputType;
  size?: InputSize;
  min?: number;
  max?: number;
  disabled?: boolean;
}>();

const inputValue = defineModel<number | string | null>();

const inputRef = ref<HTMLInputElement | null>(null);
const focusInput = () => {
  inputRef.value?.focus();
};
</script>

<template>
  <div
    class="text-field-container"
    @click="focusInput"
  >
    <label
      :for="label"
      class="text-field-label"
    >
      <p>{{ label }}</p>
      <slot name="label-append"></slot>
    </label>
    <div
      class="text-field"
      :class="`text-field_${size}`"
    >
      <input
        :id="label"
        v-model="inputValue"
        :placeholder="placeholder"
        :type="type"
        :min="min"
        :max="max"
        :disabled="disabled"
        ref="inputRef"
      />
      <slot name="button-append"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text-field-container {
  width: 100%;
}

.text-field-label {
  width: inherit;
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  
  color: #1E4073;
  font-size: 12px;
  line-height: 16px;
  font-weight: 600;
}

.text-field {
  width: inherit;
  display: flex;
  justify-content: space-between;
  column-gap: 10px;
  padding: 9px 11px 9px 14px;
  background-color: #0F4589;
  border-radius: 5px;
  
  color: #6DB0ED;
  font-size: 20px;
  line-height: 28px;
  font-weight: 800;

  &:focus-within {
    outline: 2px solid white;
  }

  input {
    flex: 1;
    height: 28px;
    color: white;
    background-color: #0F4589;

    &:focus {
      outline: none;
    }
  }

  &_small {
    padding: 0 11px;
    font-size: 16px;
    line-height: 24px;

    input {
      height: 32px;
    }
  }

  ::placeholder {
    color: #6DB0ED;
  }

  /* hide arrows on type="number" */
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }
}
</style>
