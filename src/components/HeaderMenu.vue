<template>
  <div id="header-bar">
    <div>
      <slot name="left"></slot>
    </div>
    <div class="ml-auto">
      <slot name="right"></slot>
    </div>
    <slot></slot>
  </div>
</template>

<style lang="scss">
#header-bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;

  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: var(--inset-top) 16px 0;
  height: var(--header-height);
  width: inherit;
}
</style>
