<script lang="ts" setup>
const props = defineProps<{
  src: string
  animated?: boolean
}>()
</script>

<template>
  <div
    class="skin"
    :class="{
      'skin_animated': props.animated,
    }">
    <img class="skin__image" :src="props.src" alt="skin" />
  </div>
</template>

<style lang="scss" scoped>
.skin {
  --shadow-color-1: #2C548B;
  --shadow-color-2: rgba(11, 101, 164, 0);
  --jump-height: -60px;
  position: relative;
  overflow: visible;
  height: fit-content;
  z-index: 1;
  aspect-ratio: 1 / 1;
  
  @keyframes smoothbounce {
    from { transform: translate3d(0, 0, 0); }
    to { transform: translate3d(0, var(--jump-height), 0); }
  }

  @keyframes shadow-transform {
    from {
      transform: scaleX(100%) translateY(40%);
    }

    to {
      transform: scaleX(80%) translateY(40%);
    }
  }

  &_animated {
    .skin__image {
      animation: smoothbounce 0.3s infinite alternate;
    }

    &::after {
      animation: shadow-transform 0.3s infinite alternate;
    }
  }

  &__image {
    width: 100%;
    transition: transform 0.3s;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 5%;
    transform: translateY(40%);
    width: 100%;
    height: 15%;
    border-radius: 50%;
    background: radial-gradient(50% 50% at 50% 50%, var(--shadow-color-1) 17.5%, var(--shadow-color-2) 100%);
    transition: transform 0.3s;
    z-index: -1;
  }
}
</style>
