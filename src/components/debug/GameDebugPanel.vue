<script setup lang="ts">
import RangeInput from '@/components/UI/RangeInput.vue'
import { useGameDebugAdapter } from '@/composables/debug/useGameDebugAdapter'
import { useGameAdapter } from '@/composables/useGameAdapter.ts'
import { ref } from 'vue'
import VButton from '../UI/VButton.vue'

const emit = defineEmits(['pause', 'unpause', 'restart'])

const {
  changePlatforms,
  changeCharacter,
  changeTickets,
  changeBackground,
  toggleDebug,
  changeAngularVelocity,
  changeMaxVelocity,
  changeSmoothingFactor,
  toggleBoots,
  toggleAim,
  toggleControls
} = useGameDebugAdapter()

const { pauseGame, unpauseGame } = useGameAdapter()

const isOpen = ref(false)

const toggleOpenPanel = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    pauseGame()
  } else {
    unpauseGame()
  }
}

const currentVelocity = ref(1.2)
const setAngularVelocityValue = (value: number) => {
  currentVelocity.value = value
  changeAngularVelocity(value)
}

const maxVelocity = ref(350)
const setMaxVelocityValue = (value: number) => {
  maxVelocity.value = value
  changeMaxVelocity(value)
}

const smoothing = ref(0.2)
const setSmoothingFactor = (value: number) => {
  smoothing.value = value
  changeSmoothingFactor(value)
}

const actions = [
  {
    label: 'Change Platforms',
    action: changePlatforms
  },
  {
    label: 'Change Skin',
    action: changeCharacter
  },
  {
    label: 'Toggle Boots',
    action: toggleBoots
  },
  {
    label: 'Toggle Aim',
    action: toggleAim
  },
  {
    label: 'Change Tickets',
    action: changeTickets
  },
  {
    label: 'Toggle Debug Mode',
    action: toggleDebug
  },
  {
    label: 'Change Background',
    action: changeBackground
  },
  {
    label: 'Toggle Controls',
    action: toggleControls
  },
  {
    label: 'Restart',
    action: () => {
      emit('restart')
      toggleOpenPanel()
    }
  }
]
</script>

<template>
  <VButton
    class="!absolute bottom-4 right-4 z-[1000]"
    text="X"
    type="accent"
    @click="toggleOpenPanel"
  />
  <div
    v-if="isOpen"
    class="absolute max-w-[calc(100%-100px)] rounded-md bg-gray-600 bottom-3 left-3 p-3 z-[1000]"
  >
    <div class="flex flex-wrap justify-start items-start gap-1">
      <div v-for="action in actions" :key="action.label">
        <VButton :text="action.label" type="success" size="small" @click="action.action" />
      </div>
    </div>
    <div class="text-[15px]">Angular velocity factor: {{ currentVelocity }}</div>
    <RangeInput :min="0" :max="2" :step="0.1" @on-change="setAngularVelocityValue" />
    <div class="text-[15px]">Max velocity: {{ maxVelocity }}</div>
    <RangeInput :min="0" :max="1000" :step="5" @on-change="setMaxVelocityValue" />
    <div class="text-[15px]">Smoothing factor: {{ smoothing }}</div>
    <RangeInput :min="0" :max="1" :step="0.05" @on-change="setSmoothingFactor" />
  </div>
</template>
