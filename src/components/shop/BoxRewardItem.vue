<script lang="ts" setup>
import multiplierIcon from '@/assets/images/temp/multiplier-icon.png'
import { formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number.ts'

const { src, description, chance, multiplier } = defineProps<{
  src: string
  description: string
  multiplier?: number | null
  chance: number
}>()
</script>

<template>
  <div class="box-reward">
    <img class="box-reward__image" :src="src" alt="box reward" />
    <div class="box-reward__percentage">
      <div class="text-[10px]">{{ formatNumberWithSeparator(chance) }} %</div>
    </div>
    <div v-if="multiplier" class="box-reward__multiplier">
      <img class="w-[10px]" :src="multiplierIcon" alt="multiplier" />
      <p class="text-[10px] leading-[10px] text-[#FFD903]">
        {{ formatNumberToShortString(multiplier) }}
      </p>
    </div>
    <div
      class="box-reward__description"
      :class="{
        'box-reward__description-low': chance < 5,
        'box-reward__description-medium': chance < 40 && chance >= 5,
        'box-reward__description-high': chance >= 40
      }"
    >
      <div class="text-[14px] leading-[20px]">
        {{ description }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box-reward {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 98px;

  &__description {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 8px;
    width: 100%;
    border-radius: 0 0 4px 4px;

    &-high {
      background: #5ad23d;
    }

    &-medium {
      background: #01a4fa;
    }

    &-low {
      background: #f52ddf;
    }
  }

  &__image {
    top: 0;
    position: absolute;
    max-height: 77px;
    max-width: 70px;
  }

  &__percentage {
    position: absolute;
    right: 5px;
    bottom: 33px;
  }

  &__multiplier {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 5px;
    bottom: 33px;
  }
}
</style>
