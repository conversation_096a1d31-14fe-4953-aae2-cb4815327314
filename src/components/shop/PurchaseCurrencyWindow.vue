<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import { usePurchase } from '@/composables/usePurchase'
import { ITEM_ID_TO_IMAGE, ITEM_ID_TO_STYLE } from '@/constants/shop'
import type { PlayerStateResponse, Price, ShopItemPrice, ShopItem as ShopItemType } from '@/services/openapi'
import { computed, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import ShopItem, { type ShopItemStyle } from './ShopItem.vue'
import PurchaseItemWindow from '@/components/shop/PurchaseItemWindow.vue'

type CurrencyToPurchaseType = keyof Pick<PlayerStateResponse, 'hard' | 'magicHorns'>

const { t } = useI18n()

const emit = defineEmits(['close', 'purchased', 'notEnoughFunds'])

const purchaseItemWindowRef = useTemplateRef('purchaseItemWindowRef')
const isOpen = ref(false)
const currencyNeededRef = ref(0)
const currencyType = ref<CurrencyToPurchaseType>('hard')
const offers = ref<ShopItemType[]>([])
const sortedOffers = computed(() => {
  return offers.value
    .slice()
    .sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount)
    .filter(o => o.value >= currencyNeededRef.value)
})

const openWindow = (offersParam: ShopItemType[], type: CurrencyToPurchaseType, currencyNeeded?: number) => {
  offers.value = offersParam
  currencyType.value = type
  currencyNeededRef.value = currencyNeeded ?? 0
  isOpen.value = true
}

const closeWindow = () => {
  isOpen.value = false
  purchaseItemWindowRef.value?.closeWindow()
  emit('close')
}

const { purchaseItem, isPurchasingItem } = usePurchase()

const tryPurchaseItem = (currencyType: CurrencyToPurchaseType, id: number, value: number, price: ShopItemPrice) => {
  if (price.prices.length > 1) {
    purchaseItemWindowRef.value?.openWindow(currencyType, { id, value, price })
  } else {
    purchaseCurrency(currencyType, id, value, price.prices[0] ?? price.displayPrice)
  }
}

const purchaseCurrency = (currencyType: CurrencyToPurchaseType, id: number, value: number, price: Price) => {
  purchaseItem(currencyType, id, value, price)
    .then((currency) => {
      if (currency === 'ton') return
      emit('purchased', currencyType)
      closeWindow()
    })
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        emit('notEnoughFunds', reason.payload)
      }
    })
}

defineExpose({
  openWindow,
  closeWindow
})
</script>

<template>
  <ModalWindow
    class="purchase-currency-dialog"
    :title="
      currencyNeededRef > 0
        ? `Not Enough ${t(`reward.${currencyType}`)}`
        : `Purchase ${t(`reward.${currencyType}`)}`
    "
    :is-open="isOpen"
    :is-loading="isPurchasingItem"
    @close="closeWindow"
  >
    <div
      v-if="currencyNeededRef > 0"
      class="bg-[#2397D529] rounded-[9px] flex items-center justify-center gap-x-1 py-3 mb-4"
    >
      <div
        class="icon-bg !w-[24px] !h-[24px]"
        :class="[CURRENCY_TO_IMAGE_CLASS[currencyType]]"
      ></div>
      <p class="text-[20px] text-shadow text-shadow_black text-shadow_thin">
        <span class="text-[#FFE657]">{{ currencyNeededRef }}</span>
        {{ t(`reward.${currencyType}`) }} needed
      </p>
    </div>
    <div
      class="purchase-currency-dialog__list"
      :style="{
        '--columns': `${Array(sortedOffers.slice(0, 3).length).fill('31%').join(' ')}`
      }"
    >
      <ShopItem
        v-for="item in sortedOffers.slice(0, 3)"
        :key="item.id"
        :image="ITEM_ID_TO_IMAGE[item.id]"
        :type="(ITEM_ID_TO_STYLE[item.id] as ShopItemStyle)"
        :amount="item.value"
        image-size="large"
        :price="item.price.displayPrice.amount"
        :currency="item.price.displayPrice.currency"
        @click="() => tryPurchaseItem(currencyType, item.id, item.value!, item.price)"
      />
    </div>
  </ModalWindow>
  <PurchaseItemWindow
    :class="{ 'opacity-0': isPurchasingItem }"
    ref="purchaseItemWindowRef"
    @purchase="(
      type: CurrencyToPurchaseType,
      itemId: number,
      value: number,
      price: Price
    ) => purchaseCurrency(type, itemId, value, price)"
  />
</template>

<style lang="scss">
.purchase-currency-dialog {
  padding: 11px;
  background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
  --columns: 31% 31% 31%;

  .modal-window__title {
    color: #ffe657;
    font-size: 26px;
  }

  &__list {
    display: grid;
    grid-template-columns: var(--columns);
    justify-content: center;
    grid-auto-rows: 151px;
    gap: 24px 11px;
  }
}
</style>
