<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import { ITEM_ID_TO_IMAGE, ITEM_ID_TO_STYLE } from '@/constants/shop'
import type { Price, RewardType, ShopItem as ShopItemType } from '@/services/openapi'
import { ref } from 'vue'
import ShopItem, { type ShopItemStyle } from './ShopItem.vue'
import VButton from '@/components/UI/VButton.vue'
import TonPurchaseButton from '@/components/TonPurchaseButton.vue'
import { useI18n } from 'vue-i18n'
import { getCurrencyRealAmount } from '@/constants/currency'

const { t } = useI18n()

const emit = defineEmits(['close', 'purchase'])

const isOpen = ref(false)
const itemType = ref<RewardType | undefined>(undefined)
const itemToPurchase = ref<ShopItemType | null>(null)

const openWindow = (type: RewardType, item: ShopItemType) => {
  itemType.value = type
  itemToPurchase.value = item
  isOpen.value = true
}

const closeWindow = () => {
  isOpen.value = false
  emit('close')
}

const purchaseItem = (type: RewardType, itemId: number, value: number, price: Price) => {
  emit('purchase', type, itemId, value, price)
}

defineExpose({
  openWindow,
  closeWindow
})
</script>

<template>
  <ModalWindow
    class="purchase-item-dialog"
    :title="t(`reward.${itemType}`)"
    :is-open="isOpen"
    @close="closeWindow"
  >
    <div
      v-if="itemToPurchase && itemType"
      class="flex flex-col items-center gap-y-4"
    >
      <ShopItem
        v-if="itemToPurchase"
        class="w-[130px] h-[151px]"
        image-size="medium"
        :image="ITEM_ID_TO_IMAGE[itemToPurchase.id]"
        :type="(ITEM_ID_TO_STYLE[itemToPurchase.id] as ShopItemStyle)"
        :amount="itemToPurchase.value"
        :price="itemToPurchase.price.displayPrice.amount"
        :currency="itemToPurchase.price.displayPrice.currency"
      />
      <template
        v-for="price in itemToPurchase.price.prices"
        :key="price.currency"
      >
        <TonPurchaseButton
          v-if="price.currency === 'ton'"
          class="!w-full max-w-[245px]"
          size="medium"
          type="accent"
          :text="getCurrencyRealAmount(price.amount, 'ton')"
          :image-class="CURRENCY_TO_IMAGE_CLASS[price.currency]"
          @purchase="() => purchaseItem(itemType!, itemToPurchase!.id, itemToPurchase!.value, price)"
        />
        <VButton
          v-else
          class="!w-full max-w-[245px]"
          size="medium"
          type="success"
          :text="price.amount"
          :image-class="CURRENCY_TO_IMAGE_CLASS[price.currency]"
          @click="() => purchaseItem(itemType!, itemToPurchase!.id, itemToPurchase!.value, price)"
        />
      </template>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.purchase-item-dialog {
  padding: 11px 11px 38px;
  max-width: 344px;
  background: linear-gradient(180deg, #009BE0 0%, #0074C6 100%);
  --columns: 31% 31% 31%;

  .modal-window__title {
    font-size: 26px;
  }
}
</style>
