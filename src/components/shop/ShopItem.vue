<script setup lang="ts">
import { formatNumberWithSeparator } from '@/utils/number';
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { useI18n } from 'vue-i18n';
import type { Currency } from '@/services/openapi';
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage';

const { t } = useI18n()

export type ShopItemStyle = 'blue' | 'gold' | 'pink'

const {
  type = 'blue',
  price,
  currency,
  image = '',
  shineImage = '',
  imageSize = 'medium',
  isBestDeal = false,
  name = '',
  amount = 0
} = defineProps<{
  type?: ShopItemStyle
  name?: string
  price: number
  currency: Currency
  image?: string
  shineImage?: string
  imageSize?: 'small' | 'medium' | 'large' | 'box'
  isBestDeal?: boolean
  amount?: number
}>()

const onClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}
</script>

<template>
  <article
    class="shop-item"
    :class="'shop-item_' + type"
    @click="onClick"
  >
    <div class="shop-item__info">
      <div v-if="isBestDeal" class="shop-item__badge top-[9px] left-1/2 -translate-x-1/2 z-10 uppercase">
        {{ t('shop.bestDeal') }}
      </div>
      <h1 v-if="name" class="shop-item__name text-shadow text-shadow_black">
        {{ name }}
      </h1>
      <div v-if="amount" class="shop-item__amount text-shadow text-shadow_black">
        {{ formatNumberWithSeparator(amount) }}
      </div>
      <img
        class="shop-item__image"
        :class="[`shop-item__image_${imageSize}`]"
        :src="image"
        alt="shop item"
      />
      <div v-if="shineImage" class="shine-rotate-animation w-[140px] h-[140px]">
        <img
          class="shine-pulse-animation"
          :class="[`shop-item__shine-image_${imageSize}`]"
          :src="shineImage"
          alt="shine"
        />
      </div>
    </div>
    <div class="shop-item__price text-[16px] text-shadow text-shadow_black">
      <div v-if="price > 0" class="icon-bg !w-5 !h-5" :class="CURRENCY_TO_IMAGE_CLASS[currency]"></div>
      {{ price > 0 ? formatNumberWithSeparator(price) : t('shop.free') }}
    </div>
  </article>
</template>

<style lang="scss" scoped>
.shop-item {
  --shop-info-background-color: #0568E0;
  --shop-info-background: linear-gradient(360deg, #025BB4 4.64%, var(--shop-info-background-color) 100%);
  --shop-shadow-color: #00000040;
  --shop-inner-shadow-color-top: #289BF9;
  --shop-inner-shadow-color-bottom: #15467A;
  --shop-price-background-color: #0350A4;

  border-radius: 9px;
  box-shadow: 0 2px var(--shop-shadow-color);
  display: flex;
  flex-direction: column;

  &__info {
    flex: 1;
    position: relative;
    z-index: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 9px 9px 0 0;
    background: var(--shop-info-background);
    box-shadow: inset 0 2px var(--shop-inner-shadow-color-top);
    overflow: hidden;
  }

  &__badge {
    padding: 0 10px;
    position: absolute;
    border-radius: 18px;
    font-size: 10px;
    line-height: 16px;
    white-space: nowrap;
    z-index: 10;
    border-color: #ff923a;
    color: #e64900;
    background: linear-gradient(180deg, #ffe134 0%, #ffa11d 126.79%);
    box-shadow: 0 2px #8a3310;
  }

  &__price {
    flex: 0 0 31px;
    border-radius: 0 0 9px 9px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 3px;
    background-color: var(--shop-price-background-color);
    box-shadow: inset 0 -4px var(--shop-inner-shadow-color-bottom);
  }

  &__name {
    position: absolute;
    width: 100%;
    left: 0;
    top: 3px;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    z-index: 2;
  }

  &__amount {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 7px;
    font-size: 18px;
    line-height: 24px;
    text-align: center;
    z-index: 2;
  }

  &__image {
    position: absolute;
    z-index: 1;

    &_large {
      width: 130px;
    }

    &_medium {
      width: 100px;
    }

    &_box {
      width: 90px;
      transform: translateY(10px);
    }

    &_small {
      width: 50px;
    }
  }

  &__shine-image {
    width: 140px;

    &_large {
      width: 140px;
    }

    &_medium {
      width: 110px;
    }

    &_small {
      width: 110px;
    }
  }

  &_blue {
    &:active {
      --shop-info-background-color: #0958ba;
    }
  }

  &_gold {
    --shop-info-background-color: #fddb41;
    --shop-info-background: linear-gradient(
      360deg,
      #d67332 4.64%,
      var(--shop-info-background-color) 100%
    );
    --shop-inner-shadow-color-top: #ffd900;
    --shop-inner-shadow-color-bottom: #aa3f14;
    --shop-price-background-color: #d06828;

    &:active {
      --shop-info-background-color: #c6ab32;
    }
  }

  &_pink {
  --shop-info-background-color: #D125E9;
  --shop-info-background: linear-gradient(360deg, #A804D2 8.28%, var(--shop-info-background-color) 107.28%);
  --shop-inner-shadow-color-top: #ED55FF;
  --shop-inner-shadow-color-bottom: #7E0F97;
  --shop-price-background-color: #950CB4;

    &:active {
      --shop-info-background-color: #a71db9;
    }
  }
}
</style>
