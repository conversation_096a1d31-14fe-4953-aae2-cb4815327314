<script setup lang="ts">
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import type { Currency } from '@/services/openapi'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { formatNumberWithSeparator } from '@/utils/number.ts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const {
  id,
  image,
  flagImage,
  price,
  currency,
  duration,
  type = 'small'
} = defineProps<{
  id: string
  image: string
  flagImage: string
  price: number
  currency: Currency
  duration: number
  type: 'small' | 'large'
}>()

const hours = computed(() => Math.floor(duration  / 3600))
const minutes = computed(() => Math.floor((duration % 3600) / 60))

const onClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}
</script>

<template>
  <article
    class="shop-magnet-filed-item"
    :class="{
      'shop-magnet-filed-item_large': type === 'large'
    }"
    @click="onClick"
  >
    <div class="shop-magnet-filed-item__flag">
      <img class="w-full" :src="flagImage" alt="flag image" />
      <p class="absolute ml-3 top-1.5 text-[13px] text-shadow text-shadow_black">
        {{ hours || minutes }} {{ t(`${hours ? 'hoursFull' : 'minutesFull'}`) }}
      </p>
    </div>
    <img class="shop-magnet-filed-item__image" :src="image" alt="magnet image" />
    <div class="w-[25%] flex-col align-center">
      <div class="shop-magnet-filed-item__type">
        <p
          class="w-full text-center leading-[18px] text-shadow text-shadow_black"
          :class="{
            'text-shadow_yellow': type === 'large'
          }"
          :style="{ fontSize: type === 'large' ? '15px' : '13px' }"
        >
          {{ t(`magnetFields.${id}`) }}
        </p>
        <p class="w-full text-center text-[13px] leading-[18px] text-shadow text-shadow_black">
          {{ t('magnetFields.magnet') }}
        </p>
      </div>
      <div class="shop-magnet-filed-item__price">
        <span v-if="currency === 'usd'" class="text-shadow text-shadow_black">$</span>
        <img v-else class="w-[20px]" :src="starImage" alt="star" />
        <p class="text-[16px] text-shadow text-shadow_black">
          {{ formatNumberWithSeparator(price) }}
        </p>
      </div>
    </div>
  </article>
</template>

<style lang="scss" scoped>
.shop-magnet-filed-item {
  --magnet-filed-type-inner-shadow-color-top: #ffe78f;
  --magnet-filed-type-background-color: #ffe35b;
  --magnet-filed-type-background: linear-gradient(
    360deg,
    #ffaa00 4.64%,
    var(--magnet-filed-type-background-color) 100%
  );
  --magnet-filed-price-background: #ff972f;
  --magnet-filed-price-inner-shadow-color-bottom: #e07021;
  --magnet-filed-image-shadow-color: #2441c6;
  --magnet-filed-shadow-color: #00000040;

  border-radius: 9px;
  box-shadow: 0 2px var(--magnet-filed-shadow-color);
  display: flex;
  position: relative;

  &:active {
    opacity: 0.7;
  }

  &_large {
    --magnet-filed-type-inner-shadow-color-top: #ff9bf1;
    --magnet-filed-type-background-color: #fe78f1;
    --magnet-filed-type-background: linear-gradient(
      360deg,
      #ea47da 4.64%,
      var(--magnet-filed-type-background-color) 100%
    );
    --magnet-filed-price-background: #db26d5;
    --magnet-filed-price-inner-shadow-color-bottom: #ac2eb4;
    --magnet-filed-image-shadow-color: #6710c7;
  }

  &__flag {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: absolute;
    left: -7px;
    top: 10px;
    width: 90px;
  }

  &__image {
    width: 75%;
    height: 80px;
    border-radius: 0 0 0 9px;
    box-shadow: 0 4px 0 0 var(--magnet-filed-image-shadow-color);
  }

  &__type {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60%;
    border-radius: 0 9px 0 0;
    background: var(--magnet-filed-type-background);
    box-shadow: inset 0 2px var(--magnet-filed-type-inner-shadow-color-top);
  }

  &__price {
    height: 40%;
    column-gap: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 0 9px 0;
    background: var(--magnet-filed-price-background);
    box-shadow: inset 0 -4px var(--magnet-filed-price-inner-shadow-color-bottom);
  }
}
</style>
