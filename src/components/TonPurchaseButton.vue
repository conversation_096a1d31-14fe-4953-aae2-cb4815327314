<script setup lang="ts">
import VButton, { type ButtonProps } from '@/components/UI/VButton.vue';
import { useIsWalletConnected, useWalletConnection } from '@/composables/useWallet';
import { useI18n } from 'vue-i18n'

const {
  text = '',
  type = 'default',
  size = 'large',
  image = '',
  imageClass = '',
  disabled = false,
  haptic = true,
  stopPropagation = true,
  isTransactionInProgress = false
} = defineProps<ButtonProps & { isTransactionInProgress?: boolean }>()
const emit = defineEmits(['purchase'])

const { t } = useI18n()

const { isWalletConnectedEverywhere } = useIsWalletConnected()
const { connectWallet } = useWalletConnection()

const purchase = () => {
  emit('purchase')
}

</script>

<template>
  <VButton
    :text="
    isTransactionInProgress
      ? t('skins.requirenments.inProgress')
      : !isWalletConnectedEverywhere
          ? t('actions.connectWallet')
          : text"
    :type="type"
    :size="size"
    :image="isWalletConnectedEverywhere && !isTransactionInProgress ? image : undefined"
    :imageClass="isWalletConnectedEverywhere && !isTransactionInProgress ? imageClass : undefined"
    :disabled="disabled || isTransactionInProgress"
    :haptic="haptic"
    :stopPropagation="stopPropagation"
    @click="() => !isWalletConnectedEverywhere ? connectWallet() : purchase()"
  />
</template>
