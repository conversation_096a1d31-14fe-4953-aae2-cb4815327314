<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import { useDeviceOrientation } from '@/composables/useDeviceOrientation';

const { t } = useI18n()
const isPortrait = useDeviceOrientation()
</script>

<template>
  <div v-if="!isPortrait" class="portrait-orientation-alert">
    {{ t('portraitOrientationRequired') }}
  </div>
</template>

<style lang="scss" scoped>
.portrait-orientation-alert {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: black;

  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
}
</style>
