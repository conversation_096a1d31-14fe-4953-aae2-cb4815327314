<script setup lang="ts">
import AchievementAvatarItem from '@/components/UI/AchievementAvatarItem.vue'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import ProgressBar from '@/components/UI/ProgressBar.vue'
import RedDotBadge from '@/components/UI/RedDotBadge.vue'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { formatNumberToShortString } from '@/utils/number.ts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps<{
  id: number
  image: string
  frameImage: string
  level: number
  multiplier: number
  isDone: boolean
  isCollected: boolean
  progress: {
    current: number
    goal: number
  }
}>()

const emit = defineEmits(['claimReward'])

const claimReward = () => {
  emit('claimReward')
}

const canBeCollected = computed(() => {
  return props.isDone && !props.isCollected
})

const progressText = computed(() => {
  if (props.isCollected) {
    return t('achievements.completed')
  } else if (canBeCollected.value) {
    return t('achievements.tapToClaim')
  } else {
    return `${formatNumberToShortString(props.progress.current)}/${formatNumberToShortString(props.progress.goal)}`
  }
})

const onClick = () => {
  if (canBeCollected.value) {
    hapticsService.triggerImpactHapticEvent('light')
    claimReward()
  }
}
</script>

<template>
  <div class="relative achievement-container">
    <div class="achievement" @click="onClick">
      <AchievementAvatarItem size="75" :frame-src="frameImage" :src="image" />
      <div class="min-w-0 flex-1 space-y-2">
        <div>
          <p class="text-[15px] text-shadow text-shadow_black leading-[20px]">
            {{ t(`achievements.list.${id}.${level}.name`) }}
          </p>

          <p class="text-[#0065A6] text-[11px] leading-[15px]">
            {{ t(`achievements.list.${id}.${level}.description`) }}
          </p>
        </div>
        <div class="flex w-full">
          <ProgressBar
            class="flex-1"
            inner-wrapper-class="p-[2px]"
            :progress="isDone || isCollected ? progress.goal : progress.current"
            :goal="progress.goal"
            :is-completed="props.isCollected"
          >
            <p class="text-shadow text-shadow_black text-shadow_thin">
              {{ progressText }}
            </p>
          </ProgressBar>
          <div class="achievement__reward-container" :class="{ '!left-auto': progress }">
            <BalanceItem
              class="achievement__reward"
              imageClass="achievement__reward-image"
              iconName="multiplier-tickets-bg"
              barClass="achievement__reward-bar"
              balanceClass="achievement__reward-balance text-gradient text-shadow text-shadow_black"
            >
              +{{ formatNumberToShortString(multiplier) }}
            </BalanceItem>
          </div>
        </div>
      </div>
    </div>

    <div v-if="props.isCollected" class="check-icon achievement-container__collected-mark" />
    <RedDotBadge v-else-if="canBeCollected" class="-top-[4px] -right-[4px]"/>
  </div>
</template>

<style lang="scss">
.achievement-container {
  .achievement {
    width: 100%;
    position: relative;
    height: 88px;
    padding: 7px 10px 10px 8px;

    background: linear-gradient(360deg, #72c3e6 0%, #b7e9ff 92.65%);
    box-shadow:
      #00000033 0 2px,
      inset #ffffff 0 3px,
      inset #4e9dd7 0 -5px;
    border: 1px #032f70 solid;
    border-radius: 9px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    &__reward-container {
      position: relative;
      left: 11px;
    }

    &__reward {
      &-image {
        bottom: -6px;
        width: 40px;
        height: 40px;
      }

      &-bar {
        height: 22px;

        &::after {
          background-color: #255fa3cc;
          box-shadow: inset #3561a2b2 0 -12px;
        }
      }

      &-balance {
        font-size: 18px;
        font-weight: 900;
      }
    }
  }

  &__collected-mark {
    position: absolute;
    top: 10px;
    right: -5px;
    transform: translateY(-50%);
  }
}
</style>
