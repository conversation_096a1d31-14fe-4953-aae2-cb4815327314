<script setup lang="ts">
import LoaderText from '@/components/LoaderText.vue'
import AchievementItem from '@/components/achievements/AchievementItem.vue'
import {
  ACHIEVEMENTS_IMAGES,
  ACHIEVEMENTS_LEVELS_FRAMES,
  ACHIEVEMENTS_ORDER,
  type AchievementName
} from '@/constants/achievements.ts'
import { useAchievementsState } from '@/services/client/useAchievements.ts'
import { useClaimAchievement } from '@/services/client/useClaimAchievement.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { useAchievementReward } from '@/stores/rewardStore.ts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const achievementStore = useAchievementReward()
const { playerState } = usePlayerState()
const { claimAchievement } = useClaimAchievement()
const { isLoading, achievementsList } = useAchievementsState()

const playerMultiplier = computed(() => playerState.value!.multiplier ?? 1)

const sortedAchievements = computed(() => {
  return achievementsList.value.slice().sort((a, b) => {
    return (
      ACHIEVEMENTS_ORDER.indexOf(a.id as AchievementName) -
      ACHIEVEMENTS_ORDER.indexOf(b.id as AchievementName)
    )
  })
})

const claimAchievementReward = async (id: number, level: number, multiplier: number) => {
  const currentMultiplier = playerMultiplier.value
  await claimAchievement(id).then(() => {
    achievementStore.showReward(id, level, currentMultiplier, multiplier)
  })
}
</script>

<template>
  <div class="achievements-view flex flex-col">
    <header class="shrink-0 text-center space-y-[2px] pb-[42px] tracking-normal">
      <h1 class="text-[30px] leading-[40px] text-shadow">
        {{ t('achievements.title') }}
      </h1>
      <p
        class="font-default font-extrabold text-[15px] leading-[22px] text-[#6DB0ED] max-[398px]:max-w-[340px]"
      >
        {{ t('achievements.description') }}
      </p>
    </header>
    <div v-if="!isLoading" class="mb-4">
      <div class="space-y-2 mb-5">
        <template v-for="item in sortedAchievements" :key="item.id">
          <AchievementItem
            v-if="ACHIEVEMENTS_IMAGES[item.id as AchievementName]"
            :id="item.id"
            :image="ACHIEVEMENTS_IMAGES[item.id as AchievementName] ?? ''"
            :frame-image="
              ACHIEVEMENTS_LEVELS_FRAMES[(item.readyToClaim[0]?.level || item.currentLevel) - 1] ??
              ''
            "
            :level="item.readyToClaim[0]?.level || item.currentLevel"
            :multiplier="item.readyToClaim[0]?.multiplier || item.currentMultiplier"
            :is-done="!!item.readyToClaim.length"
            :is-collected="item.isClaimed"
            :progress="{
              current: item.currentProgress,
              goal: item.currentRequirement
            }"
            @claim-reward="
              claimAchievementReward(
                item.id,
                item.readyToClaim[0]?.level || item.currentLevel,
                item.readyToClaim[0]?.multiplier || item.currentMultiplier
              )
            "
          />
        </template>
      </div>
    </div>
    <LoaderText class="text-[#AFD0FF] text-[20px] text-center mt-11" :isLoading="isLoading" />
  </div>
</template>

<style lang="scss">
.achievements-view {
  height: 100%;
  width: inherit;
  overflow: visible;
  position: relative;
  padding: 22px 20px 0;
}
</style>
