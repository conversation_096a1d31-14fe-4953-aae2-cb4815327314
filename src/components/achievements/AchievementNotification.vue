<script setup lang="ts">
import AchievementAvatarItem from '@/components/UI/AchievementAvatarItem.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { image, frameImage, name, level } = defineProps<{
  image: string
  frameImage: string
  name: number
  level: number
}>()
</script>

<template>
  <div class="achievement-notification-container relative">
    <div class="flex items-center justify-start w-full">
      <AchievementAvatarItem size="70" :frame-src="frameImage" :src="image" />
      <span class="w-[70%] h-full text-[14px] leading-[16px] whitespace-pre-wrap p-1">
        {{ t(`achievements.list.${name}.${level}.description`) }}
      </span>
    </div>
    <div class="check-icon absolute right-3"></div>
  </div>
</template>

<style lang="scss" scoped>
.achievement-notification-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  background-color: #00478aad;
  border-radius: 9px;
}
</style>
