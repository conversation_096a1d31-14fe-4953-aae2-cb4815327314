<script setup lang="ts">
import { computed } from 'vue';
import { formatNumberToShortString } from '@/utils/number'
import AvatarItem from '@/components/UI/AvatarItem.vue';
import RedDotBadge from '@/components/UI/RedDotBadge.vue'
import { getUsername } from '@/utils/user';
import ticketImage from '@/assets/images/temp/currency/ticket.png'

const props = defineProps<{
  username: string;
  ticketsClaimed: number;
  ticketsUnclaimed: number;
  league?: number;
}>()

const isUnclaimed = computed(() => props.ticketsUnclaimed > 0)

</script>

<template>
  <div class="referral" :class="{
    'referral_unclaimed': isUnclaimed
  }">
    <div class="flex-1 min-w-[0] flex items-center gap-x-[5px]">
      <AvatarItem :league="league" size="35" class="flex-none" />
      <div class="font-extrabold text-[20px] text-[#6DB0ED] truncate">
        {{ getUsername(username) }}
      </div>
    </div>
    <div class="flex-1 flex items-center justify-end">
      <div class="flex justify-end items-center gap-x-[2px]">
        <img :src="ticketImage" class="flex-none w-[32px] -rotate-[15deg]" alt="ticket" />
        <p class="text-[14px] text-shadow">
          {{ formatNumberToShortString(ticketsUnclaimed || ticketsClaimed).toString() }}
        </p>
      </div>
    </div>
    <RedDotBadge v-if="isUnclaimed" class="-top-[4px] -right-[4px]"/>
  </div>
</template>

<style lang="scss">

.referral {
  position: relative;
  display: flex;
  align-items: center;
  column-gap: 8px;

  height: 47px;
  padding: 0 16px 0 6px;

  background-color: #0F4589;
  border-radius: 5px;
  opacity: 0.7;

  &_unclaimed {
    opacity: 1;
  }
}
</style>
