<script setup lang="ts">
import { computed, ref } from 'vue';
import VButton from '../UI/VButton.vue';
import { useI18n } from 'vue-i18n';
import AvatarItem from '../UI/AvatarItem.vue';
import BalanceItem from '../UI/BalanceItem.vue';
import SlideWindow from '../UI/SlideWindow.vue';
import ProgressBar from '../UI/ProgressBar.vue';
import { formatNumberToShortString } from '@/utils/number.ts'
import type { RewardInfo } from '@/services/openapi';
import { isTimeRewardType } from '@/types'
import { useIconImage } from '@/composables/useIconImage';

const { t } = useI18n()

const { getImageClass } = useIconImage()

const props = defineProps<{
  isOpen: boolean;
  id: number;
  image: string;
  description: string;
  reward: RewardInfo;
  isDone: boolean;
  isCollected: boolean;
  shouldCheck: boolean;
  progress: {
    current: number;
    goal: number;
  }
}>()

const emit = defineEmits(['close', 'doAction', 'check', 'claimReward'])

const isChecking = ref(false)

const doAction = (id: number) => {
  if (props.shouldCheck) {
    isChecking.value = true
  }
  emit('doAction', id)
}

const claimReward = (id: number) => {
  emit('claimReward', id, props.progress.goal > 0)
}

const check = (id: number) => {
  emit('check', id)
  setTimeout(() => {
    isChecking.value = false
  }, 500)
}

const close = () => {
  isChecking.value = false
  emit('close')
}

const isDone = computed(() => {
  return props.isDone
})

const isRewardLimitedByTime = computed(() => {
  return isTimeRewardType(props.reward.type)
})

const hours = computed(() => isRewardLimitedByTime.value ? (Math.floor((props.reward.value)  / 3600)) : 0)
const minutes = computed(() => isRewardLimitedByTime.value ? (Math.floor(((props.reward.value) % 3600) / 60)) : 0)
</script>

<template>
  <SlideWindow
    :isOpen="isOpen"
    class="mission-details"
    @close="close"
  >
    <AvatarItem class="mb-[9px]" size="90" :src="image" />
    <div class="mission-details__info">
      <h1 class="text-[#1E4073] text-[20px] leading-[27px]">
        {{ t(`earn.missions.${description}.name`, { goal: progress?.goal ?? 0 }) }}
      </h1>
      <div class="flex-1 w-full">
        <div class="flex justify-center max-w-[75%] mx-auto">
          <ProgressBar
            v-if="progress?.goal"
            class="flex-1 p-[3px] w-[121px]"
            :progress="progress.current"
            :goal="progress.goal"
            :transition-speed="0"
          >
            <p class="text-[22px] text-shadow text-shadow_black text-shadow_thin">
              {{ progress.current }}/{{ formatNumberToShortString(progress.goal) }}
            </p>
          </ProgressBar>
          <BalanceItem
            :image-class="isRewardLimitedByTime ? 'mission-details__reward-image-heart' : 'mission-details__reward-image'"
            bar-class="mission-details__reward-bar"
            balance-class="mission-details__reward-balance"
            :iconName="getImageClass(reward.type)"
          >
            <span v-if="isRewardLimitedByTime">
              +{{ hours || minutes }}{{ t(`${hours ? 'hours' : 'minutes'}`) }}
            </span>
            <span v-else> +{{ formatNumberToShortString(reward.value) }}</span>
          </BalanceItem>
        </div>
      </div>
      <p v-if="!shouldCheck" class="font-extrabold text-[#274E8880] text-[16px]">
        {{ t('earn.timeToVerify') }}
      </p>
    </div>
    <VButton
      v-if="isChecking"
      type="accent"
      :text="t(`actions.check`)"
      @click="() => check(id)"
      class="!w-full"
    />
    <VButton
      v-else-if="!isDone"
      type="success"
      :text="t(`earn.missions.${description}.action`)"
      @click="() => doAction(id)"
      class="!w-full"
    />
    <VButton
      v-else-if="!isCollected"
      :text="t('actions.getReward')"
      :type="shouldCheck ? 'success' : 'accent'"
      @click="() => claimReward(id)"
      class="!w-full"
    />
  </SlideWindow>
</template>

<style lang="scss">
.mission-details {
  min-height: 380px;

  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  row-gap: 17px;

  &__info {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 13px;
    background-color: #2397D529;
    padding: 13px 0 5px 0;
    width: 100%;
    border-radius: 7px;
  }

  &__reward {
    &-image {
      width: 38px;
      height: 38px;

      &-heart {
        width: 43px;
        height: 43px;
        left: -3px;
      }
    }

    &-bar {
      height: 32px;
      padding-left: 27px;
    }

    &-balance {
      font-size: 24px;
    }
  }
}
</style>
