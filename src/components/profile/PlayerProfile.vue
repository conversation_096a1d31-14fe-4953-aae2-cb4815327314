<script lang="ts" setup>
import { SKIN_ID_TO_IMAGE } from '@/constants/skins'
import SkinItem from '@/components/skins/SkinItem.vue';
import AvatarItem from '@/components/UI/AvatarItem.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import { formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number'
import { usePlayerProfile } from '@/services/client/usePlayerProfile';
import LoaderText from '@/components/LoaderText.vue';
import { useI18n } from 'vue-i18n'
import { computed } from 'vue';

const { t } = useI18n()

const props = defineProps<{
  playerId: number
  isOpen: boolean
}>()

const getFullName = (user: { firstName?: string | null, lastName?: string | null }) => {
  return `${user.firstName} ${user.lastName}`.trim()
}

const getAvgScore = (total: number, amount: number) => {
  if (amount === 0) return 0
  return (total / amount).toFixed(0)
}

const id = computed(() => props.playerId)
const enabled = computed(() => props.isOpen)

const {
  isLoading,
  playerProfile
} = usePlayerProfile(id, enabled)

</script>

<template>
  <div class="flex-1">
    <div v-if="isLoading" class="w-full h-full flex items-center justify-center">
      <LoaderText is-loading />
    </div>
    <div v-else-if="playerProfile.id < 0" class="w-full h-full flex items-center justify-center">
      Player not found
    </div>
    <div class="w-full h-full flex flex-col" v-else>
      <div class="light-box flex items-start gap-x-[12px] w-full p-[10px] mb-[15px] truncate">
        <AvatarItem
          class="shrink-0"
          size="54"
          :league="playerProfile.leagueLevel"
        />
        <div class="player-profile__name min-w-0">
          <div class="truncate">
            {{ getFullName(playerProfile) }}
          </div>
        </div>
      </div>
      <div class="flex items-center w-fit mx-auto mb-[31px]">
        <BalanceItem
          class="player-profile__ticket z-10"
          image-class="player-profile__ticket-image"
          bar-class="player-profile__ticket-bar"
          balance-class="player-profile__ticket-balance text-shadow text-shadow_black text-shadow_thin"
          iconName="ticket-bg"
        >
          {{ formatNumberToShortString(playerProfile.tickets) }}
        </BalanceItem>
        <BalanceItem
          class="player-profile__multiplier z-10"
          image-class="player-profile__multiplier-image"
          balance-class="player-profile__multiplier-balance"
          bar-class="player-profile__multiplier-bar"
          iconName="multiplier-bg"
          gold
        >
          {{ formatNumberToShortString(playerProfile.multiplier) }}
        </BalanceItem>
      </div>
      <div class="flex-1 flex items-end justify-between gap-x-10 pl-[23px]">
        <SkinItem
          class="player-profile__skin"
          animated
          :src="SKIN_ID_TO_IMAGE[playerProfile.skin]"
        />
        <div class="self-start space-y-[13px] flex-1">
          <div class="light-box player-profile__stats">
            <img class="player-profile__stats-image" src="@/assets/images/temp/high-score-icon.png" alt="high score" />
            <div class="player-profile__stats-title">
              {{t('playerProfile.allTimeScore')}}
            </div>
            <div class="player-profile__stats-value text-shadow text-shadow_black">
              {{ formatNumberToShortString(playerProfile.highestScore) }}
            </div>
          </div>
          <div class="light-box player-profile__stats">
            <img class="player-profile__stats-image" src="@/assets/images/temp/avg-score-icon.png" alt="avg score" />
            <div class="player-profile__stats-title">
              {{t('playerProfile.averageScore')}}
            </div>
            <div class="player-profile__stats-value text-shadow text-shadow_black">
              {{ formatNumberToShortString(+getAvgScore(playerProfile.totalScore, playerProfile.gamesPlayed)) }}
            </div>
          </div>
          <div class="light-box player-profile__stats">
            <img class="player-profile__stats-image" src="@/assets/images/temp/games-played-icon.png" alt="games played" />
            <div class="player-profile__stats-title">
              {{t('playerProfile.gamesPlayed')}}
            </div>
            <div class="player-profile__stats-value text-shadow text-shadow_black">
              {{ formatNumberWithSeparator(playerProfile.gamesPlayed) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.player-profile {
  &__name {
    display: flex;
    align-items: center;
    padding: 0 16px 0 16px;
    height: 32px;
    border-radius: 6px;
    transform: none;
    background-color: #0826739C;
    box-shadow: inset #00000012 0 -12px;
  }

  &__skin {
    width: 121px;
    --jump-height: -100px !important;
  }

  &__stats {
    position: relative;
    overflow: visible;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    min-height: 41px;
    padding: 0 10px 0 28px;

    &-title {
      font-size: 10px;
      line-height: 14px;
      color: #1E4073;
      font-weight: 800;
    }

    &-value {
      font-size: 16px;
      line-height: 20px;
    }

    &-image {
      position: absolute;
      left: -24px;
      width: 45px;
    }
  }

  &__ticket {
    &-image {
      width: 43px;
      height: 43px;
    }

    &-bar {
      height: 29px;
      padding: 0 14px 0 23px;

      &:after {
        border-radius: 0;
      }
    }

    &-balance {
      font-size: 17px;
    }
  }

  &__multiplier {
    &-image {
      left: 5px;
      width: 35px;
      height: 35px;
    }

    &-bar {
      height: 29px;
      padding: 0 10px 0 26px;
    }

    &-balance {
      font-size: 17px;
    }
  }
}

.light-box {
  border-radius: 9px;
  background: #00EEFF66;
}
</style>
