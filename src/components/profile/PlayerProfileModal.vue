<script lang="ts" setup>
import ModalWindow from '@/components/UI/ModalWindow.vue';
import { usePlayerProfileStore } from '@/stores/playerProfileStore';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n'
import PlayerProfile from './PlayerProfile.vue';

const { t } = useI18n()

const store = usePlayerProfileStore()
const { isOpen, playerId } = storeToRefs(store)
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    :title="t('playerProfile.title')"
    class="profile-modal"
    @close="store.closeProfile"
  >
    <PlayerProfile :player-id="playerId" :is-open="isOpen" />
  </ModalWindow>
</template>

<style lang="scss">
.profile-modal {
  height: 420px;
  padding-bottom: 22px;

  display: flex;
  flex-direction: column;
}
</style>
