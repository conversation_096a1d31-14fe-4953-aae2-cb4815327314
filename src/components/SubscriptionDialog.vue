<script setup lang="ts">
import subscriptionBanner from '@/assets/images/temp/banners/subscription-banner.png'
import unlimitedHeart from '@/assets/images/temp/unlimited-heart-with-shine.png'
import ModalWindow from '@/components/UI/ModalWindow.vue'
import VButton from '@/components/UI/VButton.vue'
import { MAIN_CHANNEL_LINK } from '@/constants'
import { openTelegramLink } from '@telegram-apps/sdk'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const SUBSCRIPTION_REWARD = 20

defineProps<{
  isOpen: boolean
}>()
const emit = defineEmits(['close'])

const closeDialog = () => {
  emit('close')
}

const subscribeMainChannel = () => openTelegramLink(MAIN_CHANNEL_LINK)
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    class="subscription-dialog"
    :title="t('joinCommunity')"
    @close="closeDialog"
  >
    <img :src="subscriptionBanner" class="w-full mt-5" alt="subscription banner" />
    <div class="w-full flex items-center justify-between my-3">
      <div class="flex-1 flex-col items-center justify-center">
        <p class="text-[16px] leading-[22px] text-[#BBEAFF] text-center mb-1">
          {{ t('subscription.description') }}
        </p>
        <p class="text-[16px] leading-[22px] text-[#BBEAFF] text-center whitespace-pre-wrap">
          {{ t('subscription.details') }}
        </p>
      </div>
      <div class="subscription-dialog__reward">
        <p class="absolute top-1 text-[13px] leading-[17px] text-[#FFFFFF] text-center">
          {{ t('reward.title') }}
        </p>
        <img :src="unlimitedHeart" class="w-[83px] absolute top-0" alt="unlimited live" />
        <p class="absolute bottom-1 text-[24px] leading-[24px] text-[#FFFFFF] text-center">
          {{ SUBSCRIPTION_REWARD }}{{ t('minutes') }}
        </p>
      </div>
    </div>
    <VButton
      type="success"
      :text="t('actions.subscribe')"
      @click="subscribeMainChannel"
      class="!w-full"
    />
  </ModalWindow>
</template>

<style lang="scss">
.subscription-dialog {
  padding: 10px 18px 12px;

  &__reward {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    width: 93px;
    height: 93px;
    background: #004c9f59;
    border-radius: 9px;
  }
}
</style>
