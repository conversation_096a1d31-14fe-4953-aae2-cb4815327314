<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import SlideWindow from '../UI/SlideWindow.vue';
import VButton from '../UI/VButton.vue';

const { t } = useI18n()

defineProps<{
  isOpen: boolean;
}>()
const emit = defineEmits(['close', 'confirm'])

</script>

<template>
  <SlideWindow
    :isOpen="isOpen"
    class="h-[400px]"
    @close="() => emit('close')"
  >
    <h1 class="text-[32px] leading-[44px] text-center text-[#1E4073] mb-1">
      {{ t('warning') }}
    </h1>
   
    <div class="bg-[#2397D529] rounded-[7px] p-2 text-center text-[#5C91BD] text-[20px] leading-[28px] mb-4 whitespace-pre-line">
      {{ t('wallet.disconnectWalletAlert') }}
    </div>

    <VButton
      class="!w-full"
      type="accent"
      :text="t('actions.disconnect')"
      @click="() => (emit('close'), emit('confirm'))"
    />
  </SlideWindow>
</template>
