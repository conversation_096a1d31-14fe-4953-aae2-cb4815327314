<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import SlideWindow from '../UI/SlideWindow.vue';
import { CURRENCY_IMAGES, CURRENCY_NAMES, getCurrencyFormattedAmount } from '@/constants/currency';
import TextField from '../UI/TextField.vue';
import { computed, ref } from 'vue';
import { truncateString } from '@/utils/string';
import VButton from '../UI/VButton.vue';
import { useMakeTransaction } from '@/services/client/useAssets';
import { useToast } from '@/stores/toastStore';
import { sendAnalyticsEvent } from '@/utils/analytics'

const { t } = useI18n()

const props = defineProps<{
  isOpen: boolean;
  walletAddress: string;
  currency: string;
  balance: number;
  minAmount: number;
}>()
const emit = defineEmits(['close'])

const { showToast } = useToast()
const { makeTransaction, isPending } = useMakeTransaction(() => {
  showToast(t('wallet.withdraw.successMessage'), 'info', 5000)
  emit('close')
})

const amount = ref<number>(0)
const setMaxAmount = () => {
  amount.value = props.balance
}

const handleMakeTransaction = () => {
  sendAnalyticsEvent('made_transaction')
  makeTransaction(
    props.currency,
    getCurrencyFormattedAmount(amount.value, props.currency)
  )
}

const canMakeTransaction = computed(() => {
  return (
    amount.value &&
    amount.value > 0 &&
    amount.value >= props.minAmount &&
    amount.value <= props.balance
  )
})
</script>

<template>
  <SlideWindow
    :isOpen="isOpen"
    class="withdraw-form"
    @close="() => emit('close')"
  >
    <h1 class="text-[20px] leading-[27px] text-center text-[#1E4073]">
      {{ t('wallet.withdraw.title') }}
    </h1>
    
    <div class="asset !bg-[#2397D529]">
      <img class="asset__image" :src="CURRENCY_IMAGES[currency]" alt="currency" />
      <div class="asset__info">
        <div class="asset__info-line">
          <p class="text-[16px] text-[#1E4073] font-extrabold uppercase">
            {{ CURRENCY_NAMES[currency] }}
          </p>
        </div>
        <div class="asset__info-line text-[13px] text-[#1E4073] font-semibold capitalize">
          <p>
            {{ currency }}
          </p>
        </div>
      </div>
    </div>

    <TextField
      :label="t('wallet.withdraw.yourWallet')"
      :placeholder="truncateString(walletAddress, 12)"
      disabled
    />

    <TextField
      v-model="amount"
      :label="t('wallet.withdraw.amount')"
      :placeholder="t('wallet.withdraw.minimum', { amount: minAmount, currency })"
      type="number"
    >
      <template #label-append>
        {{ t('wallet.withdraw.available', { amount: balance }) }}
      </template>
      <template #button-append>
        <div @click.stop="setMaxAmount">
          MAX
        </div>
      </template>
    </TextField>

    <div class="flex-1"></div>
    <VButton
      class="!w-full "
      type="success"
      :disabled="!canMakeTransaction || isPending"
      :text="t('actions.makeTransaction')"
      @click="handleMakeTransaction"
    />
  </SlideWindow>
</template>

<style lang="scss">
.withdraw-form {
  height: 400px;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
}

</style>