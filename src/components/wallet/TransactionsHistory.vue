<script lang="ts" setup>
import { TabGroup, Tab<PERSON>ist, Tab, TabPanels, TabPanel } from '@headlessui/vue'
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import SlideWindow from '../UI/SlideWindow.vue';
import {
  CURRENCY_IMAGES,
  CURRENCY_NAMES,
  getCurrencyRealAmount
} from '@/constants/currency';
import dayjs from '@/plugins/dayjs';
import { truncateString } from '@/utils/string';
import VButton from '../UI/VButton.vue';
import { useWithdrawHistory, useTransactionHistory, useCancelTransaction } from '@/services/client/useAssets';
import LoaderText from '../LoaderText.vue';
import type { ExternalCurrencyType, RewardInfo } from '@/services/openapi';
import TransactionsList from '@/components/wallet/TransactionsList.vue';

type Transaction = {
  amount: number
  createdAt: string
  currency: ExternalCurrencyType
  id: string
  status: string
  rewards?: Array<RewardInfo>
  fee?: number
  updatedAt?: string
  wallet?: string
}

const { t } = useI18n()

const props = defineProps<{
  isOpen: boolean;
}>()

const emit = defineEmits(['close'])

const needFetchTransactions = computed<boolean>(() => props.isOpen);

const {
  transactions: withdrawTransactions,
  isLoading: isLoadingWithdraw
} = useWithdrawHistory(needFetchTransactions)

const {
  transactions: purchaseTransactions,
  isLoading: isLoadingPurchase
} = useTransactionHistory(needFetchTransactions)
const { cancelTransaction, isPending } = useCancelTransaction()

const transactions = computed(() => {
  const allTransactions = [...withdrawTransactions.value, ...purchaseTransactions.value]
  return allTransactions as Transaction[]
})

const isOpenDetails = ref<boolean>(false);
const transactionDetailsId = ref<string>('');
const transactionDetails = computed(() => {
  return transactions.value.find((transaction) => transaction.id === transactionDetailsId.value) ?? null;
});
const openTransactionDetails = (transactionId: string) => {
  transactionDetailsId.value = transactionId;
  console.log('transactionDetailsId.value')
  // nextTick doesn't work as expected (maybe it shouldnt work that way)
  setTimeout(() => {
    isOpenDetails.value = true;
  });
};
const closeTransactionDetails = () => {
  isOpenDetails.value = false;
};
</script>

<template>
  <SlideWindow
    :isOpen="isOpen"
    :class="`transactions ${isOpenDetails ? 'transactions_active' : ''}`"
    @close="() => emit('close')"
  >
    <h1 class="text-[20px] leading-[27px] text-center text-[#1E4073]">
      {{ t('wallet.history') }}
    </h1>
    
    <TabGroup as="div">
      <TabList class="tabs-group-simple__tab-list">
        <Tab
          as="template"
          v-slot="{ selected }"
        >
          <button
            class="tabs-group-simple__tab"
            :class="{
              'tabs-group-simple__tab_active': selected,
            }"
          >
            <div class="text-base text-[#1E4073]">
              Deposit
            </div>
          </button>
        </Tab>
        <Tab
          as="template"
          v-slot="{ selected }"
        >
          <button
            class="tabs-group-simple__tab"
            :class="{
              'tabs-group-simple__tab_active': selected,
            }"
          >
            <div class="text-base text-[#1E4073]">
              Withdraw
            </div>
          </button>
        </Tab>
      </TabList>
      <TabPanels as="div" class="tabs-group-simple__panels h-[264px] px-2">
        <TabPanel as="template">
          <TransactionsList
            :is-loading="isLoadingPurchase"
            :transactions="(purchaseTransactions as Transaction[])"
            @showDetails="openTransactionDetails"
          />
        </TabPanel>
        <TabPanel as="template">
          <TransactionsList
            :is-loading="isLoadingWithdraw"
            :transactions="(withdrawTransactions as Transaction[])"
            @showDetails="openTransactionDetails"
          />
        </TabPanel>
      </TabPanels>
    </TabGroup>
  </SlideWindow>
  <SlideWindow
    v-if="transactionDetails !== null"
    :isOpen="isOpenDetails"
    class="transactions"
    @close="closeTransactionDetails"
  >
    <h1 class="text-[20px] leading-[27px] text-center text-[#1E4073]">
      {{ t('wallet.details.title') }}
    </h1>
    <div class="transaction-details">
      <div class="asset !bg-[#2397D529]">
        <img class="asset__image" :src="CURRENCY_IMAGES[transactionDetails.currency]" alt="currency" />
        <div class="asset__info">
          <div class="asset__info-line">
            <p class="text-[16px] text-[#1E4073] font-extrabold uppercase">
              {{ CURRENCY_NAMES[transactionDetails.currency] }}
            </p>
          </div>
          <div class="asset__info-line text-[13px] text-[#1E4073] font-semibold capitalize">
            <p>
              {{ transactionDetails.currency }}
            </p>
          </div>
        </div>
      </div>
      <div class="transaction-details__info">
        <div>
          <p>{{ t('wallet.details.amount') }}</p>
          <p>
            {{ getCurrencyRealAmount(transactionDetails.amount, transactionDetails.currency) }}
            {{ transactionDetails.currency.toUpperCase() }}
          </p>
        </div>
        <div v-if="transactionDetails.fee">
          <p>{{ t('wallet.details.fee') }}</p>
          <p>
            {{ getCurrencyRealAmount(transactionDetails.fee, transactionDetails.currency) }}
            {{ transactionDetails.currency.toUpperCase() }}
          </p>
        </div>
        <div v-if="transactionDetails.wallet">
          <p>{{ t('wallet.details.address') }}</p>
          <p>{{ truncateString(transactionDetails.wallet, 12) }}</p>
        </div>
        <div>
          <p>{{ t('wallet.details.status') }}</p>
          <p :class="['transaction-status', `transaction-status_${transactionDetails.status}`]">
            {{ t(transactionDetails.status) }}
          </p>
        </div>
        <div>
          <p>{{ t('wallet.details.lastUpdate') }}</p>
          <p>{{ dayjs(transactionDetails.updatedAt || transactionDetails.createdAt).format('DD.MM.YYYY HH:mm:ss') }}</p>
        </div>
      </div>
    </div>
    <VButton
      v-if="transactionDetails.status === 'pending'"
      class="!w-full"
      type="danger"
      :text="t('actions.cancelTransaction')"
      :disabled="isPending"
      @click="cancelTransaction(transactionDetails.id)"
    />
  </SlideWindow>
</template>

<style lang="scss">
.transactions {
  height: 400px;

  display: flex;
  flex-direction: column;
  row-gap: 16px;

  &_active {
    transform: translateY(-50px) !important;
  }
}

.transaction-details {
  display: flex;
  flex-direction: column;
  row-gap: 14px;

  &__info {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    padding: 13px 15px;
    background-color: #0F4589;
    border-radius: 7px;

    & > div {
      display: flex;
      justify-content: space-between;

      & > p {
        font-size: 16px;
        line-height: 22px;

        &:nth-child(1) {
          font-weight: 700;
          color: #6DB0ED;
        }
        &:nth-child(2) {
          font-weight: 800;
        }
      }
    }
  }
}

.transaction-status {
  text-transform: capitalize;

  &_success {
    color: #00DD16;
  }

  &_pending {
    color: #FFC800;
  }

  &_processing {
    color: #FFC800;
  }

  &_canceled {
    color: #FF4423;
  }

  &_error {
    color: #FF4423;
  }
}
</style>
