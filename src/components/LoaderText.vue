<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const props = defineProps<{
  isLoading: boolean
  text?: string
}>()

const dots = ref('.')
let interval: ReturnType<typeof setInterval> | undefined = undefined
watch(() => props.isLoading, (isLoading) => {
  if (isLoading) {
    interval = setInterval(() => {
      dots.value = dots.value.length < 3 ? dots.value + '.' : '.'
    }, 500)
  } else {
    clearInterval(interval)
    dots.value = '.'
  }
}, { immediate: true })
</script>

<template>
  <div v-if="isLoading">
    {{ text || t('loading') }}{{ dots }}
  </div>
</template>
