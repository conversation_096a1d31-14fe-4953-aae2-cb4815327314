<script setup lang="ts">
import AvatarItem from '../UI/AvatarItem.vue';
import trophy1 from '@/assets/images/temp/onepercent/trophy_1.png';
import trophy2 from '@/assets/images/temp/onepercent/trophy_2.png';
import trophy3 from '@/assets/images/temp/onepercent/trophy_3.png';
import { formatNumberToShortString } from '@/utils/number';
import BalanceItem from '../UI/BalanceItem.vue';
import type { EventRewardCurrency } from '@/services/openapi'
import { useIconImage } from '@/composables/useIconImage';
import crownIcon from '@/assets/images/temp/crown-icon.svg'

const { getImageClass } = useIconImage()

const trophies: Record<number, string> = {
  1: trophy1,
  2: trophy2,
  3: trophy3
};

const {
  username,
  rankIndex,
  hideRank = false,
  score,
  scoreTypeImage,
  balance,
  league,
  balanceType = 'hard',
  active = false,
  isLeader = false,
  avatar = '',
  nameClass = '',
  scoreClass = ''
} = defineProps<{
  username: string;
  rankIndex: number;
  hideRank?: boolean;
  league?: number;
  score?: string;
  scoreTypeImage?: string;
  balance?: number;
  balanceType?: EventRewardCurrency | 'refsFake';
  active?: boolean;
  isLeader?: boolean;
  avatar?: string;
  nameClass?: string;
  scoreClass?: string;
}>();

const rank = rankIndex + 1;
</script>

<template>
  <div class="scoreboard-item" :class="{ 'scoreboard-item_active': active }">
    <div v-if="!hideRank" class="scoreboard-item__place text-shadow text-shadow_black">
      <template v-if="rank <= -1">
        <p class="text-[20px] leading-[20px] relative -top-[4px]">...</p>
      </template>
      <template v-else-if="rank === 0 || rank > 1000">
        1K+
      </template>
      <template v-else-if="rank > 3">
        {{ rank }}
      </template>
      <template v-else>
        <img :src="trophies[rank]" class="scoreboard-item__place-trophy" alt="trophy" />{{ rank }}
      </template>
    </div>
    <AvatarItem size="40" :src="avatar" class="shrink-0" :league="league" />
    <div class="scoreboard-item__info">
      <div class="min-w-0 p-px">
        <div class="flex gap-x-2">
          <div
            class="scoreboard-item__info-name font-extrabold text-[16px] leading-[16px] text-[#BD764A] truncate px-[2px]"
            :class="nameClass"
          >
            {{ username }}
          </div>
          <img v-if="isLeader" class="w-4" :src="crownIcon" alt="crown icon" />
        </div>
        <div v-if="score !== undefined" class="flex items-center">
          <img v-if="scoreTypeImage" :src="scoreTypeImage" class="w-[18px] h-[18px] mr-1" alt="score type" />
          <p class="text-[18px] leading-[20px] whitespace-nowrap truncate px-[2px]" :class="scoreClass">
            {{ score }}
          </p>
        </div>
      </div>
    </div>
    <BalanceItem
      v-if="balanceType"
      image-class="scoreboard-item__reward-image"
      balance-class="scoreboard-item__reward-amount"
      bar-class="scoreboard-item__reward-bar"
      :iconName="getImageClass(balanceType)"
    >
      {{ formatNumberToShortString(balance || 0) }}
    </BalanceItem>
    <div class="scoreboard-item__skew-lines space-x-[10px]">
      <div class="scoreboard-item__skew-lines-block"></div>
      <div class="scoreboard-item__skew-lines-block"></div>
    </div>
  </div>
</template>

<style lang="scss">
.league-leaderboard {
  .scoreboard-item {
    &__info {
      -webkit-text-stroke: 3px black;
      filter: drop-shadow(0 2px black);

      &-name {
        color: white;
        padding-left: 2px;
      }
    }

    &__reward {
      &-bar {
        min-width: auto;
        width: auto;
        padding-left: 20px;
        padding-right: 9px;
        justify-content: center;

        &::after {
          width: 100%;
        }
      }
    }
  }
  .scoreboard-item:not(.scoreboard-item_active) {
    background: linear-gradient(360deg, #4CBCFA 0%, #B6E4FF 92.65%);
    border-color: #002C6E;
    box-shadow: #00000033 0 2px, inset #FFFFFF 0 4px;

    .scoreboard-item {
      &__info {
        padding-left: 2px;
        -webkit-text-stroke: 3px black;
        filter: drop-shadow(0 2px black);

        &-name {
          color: white;
        }
      }

      &__place::after {
        background-color: #44A4DA;
      }

      &__skew-lines-block {
        &:first-child {
          background-color: #FFFFFFCC;
        }
        &:last-child {
          background-color: #FFFFFF73;
        }
      }

      &__reward {
        &-bar {
          &::after {
            background-color: #0826739C;
          }
        }
      }
    }
  }
}

.scoreboard-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 12px 0 8px;
  height: 54px;
  border: 1.5px solid #BD591B;
  border-radius: 9px;
  background: linear-gradient(360deg, #F1D5AE 0%, #FEF7CA 92.65%);
  box-shadow: #00000033 0 2px, inset #FFFFFF 0 3px;
  transform: translate(0, 0);

  &_active {
    position: sticky;
    top: 0;
    bottom: 0;
    background: linear-gradient(360deg, #FF8D2A 0%, #FFC439 102.52%);
    border-color: #000000;
    box-shadow: #00000033 0 2px, inset #FCFFA8 0 3px;
    z-index: 10;

    .scoreboard-item {
      &__place::after {
        background-color: #EF7613;
      }

      &__skew-lines-block {
        &:first-child {
          background-color: #FAFF5DA6;
        }
        &:last-child {
          background-color: #FFFA9C66;
        }
      }
    }
  }

  &__place {
    position: relative;
    flex: 0 0 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    transform: translate(0, 0);

    &-trophy {
      position: absolute;
      top: -4px;
      left: 0;
      width: 100%;
      z-index: -1;
    }

    &::after {
      content: '';
      position: absolute;
      width: 34px;
      height: 34px;
      background-color: #EFBF90;
      border-radius: 50%;
      z-index: -2;
    }
  }

  &__info {
    flex: 1 1 auto;
    min-width: 0;
    margin-right: 8px;
  }

  &__reward {
    &-bar {
      min-width: 75px;
      padding-left: 18px;
      padding-right: 6px;
      justify-content: center;

      &::after {
        background-color: #972D009C;
        width: 100%;
      }
    }

    &-amount {
      white-space: nowrap;
      font-size: 16px;
      line-height: 16px;
      color: white;
    }
  }

  &__skew-lines {
    position: absolute;
    top: 0;
    left: 53px;
    height: 100%;
    display: flex;
    z-index: -1;

    &-block {
      height: 100%;
      transform: skew(-15deg);

      &:first-child {
        background-color: #FFFFFFCC;
        width: 44px;
      }
      &:last-child {
        position: relative;
        left: -10px;
        background-color: #FFFFFF73;
        width: 122px;
      }
    }
  }
}
</style>
