<script setup lang="ts">
import { computed } from 'vue';

import LeaderboardItem from '@/components/events/LeaderboardItem.vue';

import { useUser } from "@/composables/useUser";
import { usePlayerProfileStore } from '@/stores/playerProfileStore';
import type { EventRewardCurrency } from '@/services/openapi'

export type LeaderboardCurrency = EventRewardCurrency

export type Leader = {
  id: number
  name: string
  league?: number
  value?: string
  balance?: number
  currency?: LeaderboardCurrency
  isLeader?: boolean
}

export type MeInfo = {
  rank: number,
  score?: string,
  balance?: number,
  currency?: LeaderboardCurrency,
  league?: number
  isLeader?: boolean
}

const profileStore = usePlayerProfileStore()

const props = withDefaults(defineProps<{
  leaderboard: Leader[];
  user?: MeInfo;
  scoreTypeImage?: string;
  nameClass?: string;
  scoreClass?: string;
}>(), {
  user: undefined,
  scoreTypeImage: '',
  nameClass: '',
  scoreClass: 'text-shadow text-shadow_black',
})

const MAX_LEADERS_RANK_TO_SHOW = 100
const LAST_RANK_TO_SHOW = 1000

const { getUser } = useUser()
const userName = getUser().getName()
const userId = getUser().getId()

const isUserBetweenTopAndThousand = computed(() => {
  return props.user?.rank !== undefined
    && props.user.rank !== null
    && props.user.rank > MAX_LEADERS_RANK_TO_SHOW
    && props.user.rank < LAST_RANK_TO_SHOW
})

const isUserOutOfThousand = computed(() => {
  return props.user !== undefined && (props.user.rank === undefined || LAST_RANK_TO_SHOW < props.user.rank)
})

const first100Leaders = computed(() => {
  return props.leaderboard.slice(0, MAX_LEADERS_RANK_TO_SHOW)
})

const thousandthPlace = computed(() => {
  // last item in array (if leaderboard.length === MAX_LEADERS_RANK_TO_SHOW + 1)
  // is LAST_RANK_TO_SHOW rank in backend
  return props.leaderboard.at(MAX_LEADERS_RANK_TO_SHOW)
})
</script>

<template>
  <div class="space-y-2">
    <LeaderboardItem
      v-for="(participant, index) in first100Leaders"
      :active="participant.id === userId"
      :key="index"
      :username="participant.name"
      :score="participant.value"
      :scoreTypeImage="scoreTypeImage"
      :rank-index="index"
      :balanceType="participant.currency"
      :balance="participant.balance"
      :is-leader="participant.isLeader"
      :league="participant.league"
      :nameClass="nameClass"
      :scoreClass="scoreClass"
      @click="() => profileStore.openProfile(participant.id)"
    />

    <template v-if="isUserBetweenTopAndThousand && user">
      <div class="three-dots">
        ...
      </div>
      <LeaderboardItem
        :username="userName"
        :score="user.score"
        :scoreTypeImage="scoreTypeImage"
        :rank-index="(user.rank ?? -2)"
        :balance="user.balance"
        :balanceType="user.currency"
        :league="user.league"
        :is-leader="user.isLeader"
        active
        :nameClass="nameClass"
        :scoreClass="scoreClass"
        @click="() => profileStore.openProfile(userId)"
      />
    </template>

    <template v-if="thousandthPlace && !isUserOutOfThousand">
      <div class="three-dots">
        ...
      </div>
      <LeaderboardItem
        :username="thousandthPlace.name"
        :score="thousandthPlace.value"
        :scoreTypeImage="scoreTypeImage"
        :active="thousandthPlace.id === userId"
        :rank-index="-1"
        :balance="thousandthPlace.balance"
        :league="thousandthPlace.league"
        :is-leader="thousandthPlace.isLeader"
        :balanceType="thousandthPlace.currency"
        :nameClass="nameClass"
        :scoreClass="scoreClass"
        @click="() => profileStore.openProfile(thousandthPlace!.id)"
      />
    </template>

    <template v-if="isUserOutOfThousand && user">
      <div class="three-dots">
        ...
      </div>
      <LeaderboardItem
        :username="userName"
        :score="user.score"
        :scoreTypeImage="scoreTypeImage"
        :rank-index="-1"
        :balance="user.balance"
        :balanceType="user.currency"
        :league="user.league"
        :is-leader="user.isLeader"
        active
        :nameClass="nameClass"
        :scoreClass="scoreClass"
        @click="() => profileStore.openProfile(userId)"
      />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.three-dots {
  font-size: 40px;
  line-height: 40px;
  text-align: center;
  margin-top: -10px !important;
}
</style>
