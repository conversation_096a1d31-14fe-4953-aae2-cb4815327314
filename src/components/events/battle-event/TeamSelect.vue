<script setup lang="ts">
import { computed } from 'vue'

import SkinItem from '@/components/skins/SkinItem.vue'

import { SKIN_ID_TO_IMAGE } from '@/constants/skins.ts'
import { useSkinsList } from '@/services/client/useSkins.ts'
import { useI18n } from 'vue-i18n'
import { type Skin } from '@/services/openapi'
import RewardItem from '@/components/RewardItem.vue'

import tonImageL from '@/assets/images/temp/big-icons/ton-l.png'
import hardImage from '@/assets/images/temp/big-icons/hard-l.png'
import spinImage from '@/assets/images/temp/big-icons/wheel-spin-l.png'
import { BATTLE_EVENT_TEAMS } from '@/constants/events'

const { t } = useI18n()

defineProps<{
  selectedSkin: Skin | null
}>()

const emit = defineEmits(['selectSkin'])

const { skinsList } = useSkinsList()
const skins = Object.keys(BATTLE_EVENT_TEAMS)
const requiredSkin1 = computed(() => skinsList.value.find(s => s.id === +skins[0]))
const requiredSkin2 = computed(() => skinsList.value.find(s => s.id === +skins[1]))
</script>

<template>
  <!-- Requirement Banner -->
    <!-- Event Description -->
    <div class="community-battle-banner__box h-[35%] flex flex-col justify-center items-center text-wrap p-1">
      <p class="text-[16px] text-[#FFE35C] text-shadow text-shadow_black text-center">
        {{ t('total_prize_pool') }}
      </p>
      <div class="flex-1 flex justify-around items-center -mt-2 w-full">
        <RewardItem
          class="w-[25%] h-[90%]"
          type="hard"
          :amount="33000"
          :image="hardImage"
          value-class="text-[16px]"
        />
        <RewardItem
          class="w-[25%] h-[90%]"
          type="ton"
          :amount="30"
          :image="tonImageL"
          value-class="text-[16px]"
          dont-get-currency-real-amount
        />
        <RewardItem
          class="w-[25%] h-[90%]"
          type="ton"
          :amount="110"
          :image="spinImage"
          value-class="text-[16px]"
          dont-get-currency-real-amount
        />
      </div>
    </div>
    <h2 class="text-[13px] leading-[13px] text-shadow text-shadow_black text-shadow_thin text-center mb-1">
      {{ t('battle_event.team_select') }}
    </h2>
    <!-- Event Purchases -->
    <div class="flex-1 flex w-full justify-between gap-x-4">
      <div
        v-if="requiredSkin1"
        class="community-battle-banner__purchase community-battle-banner__purchase_blue"
        :class="{ 'community-battle-banner__purchase_selected': selectedSkin?.id === requiredSkin1.id }"
        @click="() => requiredSkin1 && emit('selectSkin', requiredSkin1)"
      >
        <div class="community-battle-banner__purchase_info">
          <p class="absolute left-1/2 -translate-x-1/2 text-[15px] text-shadow text-shadow_thin text-shadow_black">
            {{ BATTLE_EVENT_TEAMS[requiredSkin1.id].name }}
          </p>
          <SkinItem
            :src="SKIN_ID_TO_IMAGE[requiredSkin1.id]"
            class="community-battle-banner__skin"
            :animated="selectedSkin?.id === requiredSkin1.id"
          />
        </div>
        <div class="community-battle-banner__purchase_price">
          <div class="flex items-center justify-center gap-1 pb-1">
            <p class="text-[20px] text-shadow text-shadow_thin text-shadow_black">
              {{ selectedSkin?.id === requiredSkin1.id ? t('selected') : t('actions.select') }}
            </p>
          </div>
        </div>
      </div>

      <div
        v-if="requiredSkin2"
        class="community-battle-banner__purchase community-battle-banner__purchase_blue"
        :class="{ 'community-battle-banner__purchase_selected': selectedSkin?.id === requiredSkin2.id }"
        @click="() => requiredSkin2 && emit('selectSkin', requiredSkin2)"
      >
        <div class="community-battle-banner__purchase_info">
          <p class="absolute left-1/2 -translate-x-1/2 text-[15px] text-shadow text-shadow_thin text-shadow_black">
            {{ BATTLE_EVENT_TEAMS[requiredSkin2.id].name }}
          </p>
          <SkinItem
            :src="SKIN_ID_TO_IMAGE[requiredSkin2.id]"
            class="community-battle-banner__skin -scale-x-100"
            :animated="selectedSkin?.id === requiredSkin2.id"
          />
        </div>
        <div class="community-battle-banner__purchase_price">
          <div class="flex items-center justify-center gap-1 pb-1">
            <p class="text-[20px] text-shadow text-shadow_thin text-shadow_black">
              {{ selectedSkin?.id === requiredSkin2.id ? t('selected') : t('actions.select') }}
            </p>
          </div>
        </div>
      </div>
    </div>
</template>

<style lang="scss">
.community-battle-banner {
  &__skin {
    --jump-height: -25% !important;
    position: absolute !important;
    bottom: 5%;
    height: 70% !important;
  }

  &__purchase {
    --skin-item-background: linear-gradient(360deg, #67ffd9 0%, #b2ffe5 92.65%);
    --skin-item-price-background-color: #1CCFABC7;
    --skin-item-inner-shadow-color-top: #e1f6ff;
    --skin-item-inner-shadow-color-bottom: #2ea28f;
    --skin-item-shadow-color: #00000040;

    height: 100%;
    flex: 1 1 auto;
    position: relative;
    border-radius: 9px;
    background: var(--skin-item-background);
    box-shadow: 0 2px var(--skin-item-shadow-color);

    &:active {
      --skin-item-background: #043870;
    }

    &_info {
      position: relative;
      padding: 2px 0 5px;
      height: calc(100% - 30px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      border-radius: 9px 9px 0 0;
      box-shadow: inset 0 2px var(--skin-item-inner-shadow-color-top);
    }

    &_price {
      position: absolute;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      border-radius: 0 0 9px 9px;
      width: 100%;
      background: var(--skin-item-price-background-color);
      box-shadow: inset 0 -4px var(--skin-item-inner-shadow-color-bottom);

      &__claimed {
        color: #a1754a;
      }
    }

    &_orange {
      --skin-item-background: linear-gradient(360deg, #ffaa00 0%, #fffb00 92.65%);
      --skin-item-price-background-color: #f19204;
      --skin-item-inner-shadow-color-top: #fffacb;
      --skin-item-inner-shadow-color-bottom: #d86800;

      &:active {
        --skin-item-background: #ffaa00;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_blue {
      --skin-item-background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
      --skin-item-price-background-color: #0084e8c7;
      --skin-item-inner-shadow-color-top: #e1f6ff;
      --skin-item-inner-shadow-color-bottom: #006bb5;

      &.community-battle-banner__purchase_selected {
        --skin-item-background: #1eadea;
      }

      &:active {
        --skin-item-background: #1eadea;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_pink {
      --skin-item-background: linear-gradient(360deg, #e027fe 0%, #ff9838 92.65%);
      --skin-item-price-background-color: #ad20d4;
      --skin-item-inner-shadow-color-top: #ffcc7b;
      --skin-item-inner-shadow-color-bottom: #8602ad;

      &:active {
        --skin-item-background: #e027fe;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_cream {
      --skin-item-background: linear-gradient(360deg, #fbf0c4 0%, #fef8e0 92.65%);
      --skin-item-price-background-color: #efbf90;
      --skin-item-inner-shadow-color-top: #ffffff;
      --skin-item-inner-shadow-color-bottom: #c6905b;

      &:active {
        --skin-item-background: #fbf0c4;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }
  }
}
</style>
