<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

import LoaderText from '@/components/LoaderText.vue'
import CountdownTimer from '@/components/UI/CountdownTimer.vue'
import InstructionButton from '@/components/UI/InstructionButton.vue'
import LeaderboardList, { type MeInfo } from '@/components/events/LeaderboardList.vue'
import { getCurrencyRealAmount } from '@/constants/currency'
import type { EventLeader } from '@/services/openapi'
import type { InstructionType } from '@/constants/instructions'
import VOverlay from '../VOverlay.vue'
import { formatNumberToShortString } from '@/utils/number'
import type { TimerId } from '@/composables/useCountdownTimer'

const props = defineProps<{
  id: TimerId
  leaderboard: EventLeader[]
  isLoading: boolean
  userInfo: MeInfo
  countdown?: number
  eventBanner: string
  scoreTypeImage?: string
  instructionType?: InstructionType
}>()

const emit = defineEmits(['close'])

const route = useRoute()
const timeLeft = parseInt((route.query.time as string) ?? '0')

const close = () => {
  emit('close')
}

const isEventOver = ref(false)

const mappedLeaderboard = computed(() => {
  return props.leaderboard.map(leader => {
    return {
      id: leader.id,
      name: leader.name,
      value: formatNumberToShortString(leader.value),
      league: leader.leagueLevel,
      balance: leader.reward?.amount
        ? getCurrencyRealAmount(leader.reward.amount, leader.reward.currency)
        : undefined,
      currency: leader.reward?.currency
    }
  })
})
</script>

<template>
  <VOverlay
    isOpen
    class="overlay_short flex items-center justify-center px-[11px]"
    @click-self="close"
  >
    <div class="event-view">
      <InstructionButton
        v-if="instructionType"
        class="absolute top-[5px] left-[5px]"
        :instruction-type="instructionType"
      />
      <img class="event-view__banner" :src="eventBanner" alt="event banner" />
      <div class="close-button absolute top-[7px] right-[7px]" @click="close" />

      <div class="h-[21px] mb-[3px]">
        <div v-if="!isEventOver" class="event-view__timer">
          <CountdownTimer
            v-if="timeLeft > 0 || (countdown && countdown > 0)"
            :timer-id="id"
            class="text-white text-shadow text-shadow_thin"
            :total-seconds="timeLeft || countdown"
            v-on:countdown-finished="isEventOver = true"
          />
        </div>
        <div v-else class="text-[16px] leading-[21px] text-center text-[#7300AA]">
          Event is over
        </div>
      </div>

      <slot name="description"></slot>

      <div v-if="isLoading" class="flex-1 flex items-center justify-center">
        <LoaderText class="text-center font-extrabold text-md leading-[47px]" is-loading />
      </div>
      <div
        v-else
        class="flex-1 event-view__scoreboard"
        :class="{ 'event-view__scoreboard_scrollable': leaderboard.length > 5 }"
      >
        <LeaderboardList
          :leaderboard="mappedLeaderboard"
          :user="userInfo"
          :scoreTypeImage="scoreTypeImage"
        ></LeaderboardList>
      </div>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.event-view {
  position: relative;
  top: 20px; // compensate for the banner overflow
  width: 100%;
  height: 70%;
  border: 5px solid #ffd634;
  border-radius: 18px;
  padding: 50px 10px 10px;
  background: var(--event-background);

  display: flex;
  flex-direction: column;

  &__banner {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, calc(-100% + 45px));
    width: 85%;
  }

  &__timer {
    width: 93px;
    height: 100%;
    padding: 5px 0;
    margin: auto;

    border-radius: 5px;
    background-color: #07070773;

    text-align: center;
    color: white;
    font-size: 12px;
  }

  &__scoreboard {
    position: relative;
    overflow: auto;
    padding: 0 5px;
    padding-top: 14px;

    &_scrollable {
      padding-top: 0;

      &::before {
        content: '';
        position: sticky;
        display: block;
        width: 100%;
        height: 14px;
        z-index: 1;
        background: var(--event-list-top-shadow);
        top: 0;
      }

      &::after {
        content: '';
        position: sticky;
        display: block;
        width: 100%;
        height: 14px;
        z-index: 1;
        background: var(--event-list-bottom-shadow);
        bottom: 0;
      }
    }
  }
}
</style>
