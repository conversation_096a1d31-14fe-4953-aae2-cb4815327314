<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

import { useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck'
import { useTime } from '@/composables/useTime.ts'
import { usePlayerState } from '@/services/client/usePlayerState'

import eventBanner from '@/assets/images/temp/ton-event/banner.png'
import eventImage from '@/assets/images/temp/ton-event/image.png'
import CountdownTimer from '@/components/UI/CountdownTimer.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { TON_MINING_INSTRUCTION } from '@/constants/instructions.ts'
import leaguesService from '@/services/local/leagues'
import { useI18n } from 'vue-i18n'
import EventBanner from './EventBanner.vue'

const { t } = useI18n()

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenTonEventBanner'
const TON_EVENT_DAILY_LIMIT = 1000
const TON_EVENT_TOTAL_LIMIT = 4000

const {
  isOpen: isOpenWelcomeBanner,
  openWindowInQueue: openWelcomeWindowInQueue,
  closeWindowInQueue: closeWelcomeWindowInQueue
} = useWindowQueue('tonmining-welcome-window')

const { isOpen, tonTimeLeftInSeconds } = defineProps<{
  isOpen: boolean
  tonTimeLeftInSeconds: number
}>()
const emit = defineEmits(['close', 'start-game'])

const { playerState } = usePlayerState()
const { checkWelcomeBanner } = useEventWelcomeCheck()
const { getSecondsToNextDayStart } = useTime()

const isTonSpawnEnabled = computed(() => {
  return playerState.value?.tonOnPlatformEvent?.isTonSpawnEnabled
})

const timerRef = ref<typeof CountdownTimer | null>(null)
getSecondsToNextDayStart().then(result => {
  timerRef.value?.initTimerWithTotal(result)
})

const closeWelcomeBanner = () => {
  closeWelcomeWindowInQueue()
}

const onCloseClick = () => {
  emit('close')
}

const onStartGame = () => {
  emit('start-game')
}

onMounted(async () => {
  const isOpenWelcomeBanner = await checkWelcomeBanner(
    HAS_SEEN_EVENT_BANNER_KEY,
    playerState.value!.tonOnPlatformEvent !== undefined,
    leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'tonMiningEvent')
  )
  if (isOpenWelcomeBanner) {
    openWelcomeWindowInQueue()
  }
})
</script>

<template>
  <div>
    <!-- Welcome banner -->
    <EventBanner
      class="ton-banner w-full h-full"
      :isOpen="isOpenWelcomeBanner"
      :buttons="[{ text: t('tonEvent.letsPlay'), type: 'success', onClick: onStartGame }]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="TON_MINING_INSTRUCTION"
      @close="closeWelcomeBanner"
    >
      <template #details>
        <slot>
          <div class="ton-banner__limitations">
            <p class="text-[16px] leading-[22px] text-shadow">
              {{ t('tonEvent.eventLimitDescription') }}
            </p>
            <div class="icon-bg relative ton-bg !w-[27px] !h-[27px] mx-2" />
            <p class="text-[24px] leading-[33px] text-shadow_yellow">{{ TON_EVENT_DAILY_LIMIT }}</p>
          </div>
        </slot>
      </template>
      <p class="text-[12px] leading-[16px] text-white text-center my-4">
        {{ t('tonEvent.eventStartDescription') }}
      </p>
    </EventBanner>

    <!-- Event Info banner -->
    <EventBanner
      class="ton-banner w-full h-full"
      :isOpen="isOpen"
      :buttons="
        isTonSpawnEnabled
          ? [{ text: t('tonEvent.letsPlay'), type: 'success', onClick: onStartGame }]
          : [{ text: t('tonEvent.nextPoolIn'), fontSize: 20, disabled: true }]
      "
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="TON_MINING_INSTRUCTION"
      @close="onCloseClick"
    >
      <template #details>
        <slot>
          <div class="ton-banner__timer">
            <CountdownTimer
              timer-id="tonEventBanner"
              class="text-[12px] leading-[16px] text-[#FFFFFF]"
              :total-seconds="tonTimeLeftInSeconds"
            />
          </div>
        </slot>
      </template>
      <i18n-t
        v-if="isTonSpawnEnabled"
        class="text-[12px] leading-[16px] text-white text-center whitespace-pre my-4"
        tag="p"
        :keypath="'tonEvent.eventDescription'"
      >
        <template #limit>
          <span class="text-[#FFE134]"> {{ TON_EVENT_DAILY_LIMIT }} TON </span>
        </template>
        <template #totalLimit>
          <span class="text-[#FFE134]"> {{ TON_EVENT_TOTAL_LIMIT }} TON </span>
        </template>
        <template #friends>
          <span class="text-[#FFE134]"> 1000 new </span>
        </template>
        <template #bonus>
          <span class="text-[#FFE134]"> 100 TON </span>
        </template>
      </i18n-t>
      <p v-else class="text-[12px] leading-[16px] text-white text-center whitespace-pre mt-6 mb-4">
        {{ t('tonEvent.eventNoTonAvailableDescription') }}
      </p>
      <template #button-content>
        <div v-if="!isTonSpawnEnabled" class="button-timer-wrapper">
          <CountdownTimer
            timer-id="tonEventPool"
            class="text-[16px] leading-[22px] text-[#FF9500]"
            ref="timerRef"
          />
        </div>
      </template>
    </EventBanner>
  </div>
</template>

<style lang="scss">
.ton-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #0054b3 0%, #0075e2 92.65%);

  .event-banner {
    &__banner {
      width: 332px;
      top: -25px;
    }
  }

  &__limitations {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 250px;
    height: 36px;
    border-radius: 5px;
    background: #043870;
  }

  &__timer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 93px;
    height: 21px;
    background: #043870;
    border-radius: 5px;
  }
}

.button-timer-wrapper {
  margin-left: 10px;
  min-width: 92px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  position: relative;
  border-radius: 5px;
  transform: skew(-10deg);
  background: #04040494 58%;

  .countdown-timer {
    transform: skew(10deg);
  }
}
</style>
