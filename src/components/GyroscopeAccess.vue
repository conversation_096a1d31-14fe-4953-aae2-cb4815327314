<script setup lang="ts">
import gyroscopeImage from '@/assets/images/temp/gyroscope.png'
import { checkDeviceMotionPermission } from '@/utils/permissions'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import VButton from './UI/VButton.vue'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { useGyroscopeStore } from '@/stores/gyroscopeStore'
import VOverlay from './VOverlay.vue'

const props = defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits(['granted', 'denied', 'close'])

const { t } = useI18n()
const gyroscopeRequestStore = useGyroscopeStore()

watch(
  () => props.isOpen,
  () => sendAnalyticsEvent('gyroscope_access_shown'),
  { once: true }
)

const allowAccess = () => {
  checkDeviceMotionPermission()
    .finally(() => {
      emit('close')
      gyroscopeRequestStore.setRequest(false)
    })
    .then(() => {
      sendAnalyticsEvent('gyroscope_access_granted')
      emit('granted')
    })
    .catch(() => {
      sendAnalyticsEvent('gyroscope_access_denied')
      emit('denied')
    })
}
</script>

<template>
  <VOverlay
    :isOpen="props.isOpen"
    class="gyroscope-access-container"
  >
    <p class="text-center text-[40px] whitespace-pre text-shadow">
      {{ t('allowGyroscope') }}
    </p>
    <img class="w-[207px]" :src="gyroscopeImage" />
    <VButton
      class="!w-full max-w-[312px]"
      type="success"
      :text="t('actions.allowAccess')"
      @click="allowAccess"
    />
  </VOverlay>
</template>

<style lang="scss" scoped>
.gyroscope-access-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 47px;
}
</style>
