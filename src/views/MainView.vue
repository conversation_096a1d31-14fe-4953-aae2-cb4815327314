<script setup lang="ts">
import ConnectionDialog from '@/components/ConnectionDialog.vue'
import HeaderMenu from '@/components/HeaderMenu.vue'
import LivesBar from '@/components/lives/LivesBar.vue'
import LivesModal from '@/components/lives/LivesModal.vue'
import PlayerProfileModal from '@/components/profile/PlayerProfileModal.vue'
import LeagueRewardAutomatic from '@/components/rewards/LeagueRewardAutomatic.vue'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { formatNumberToShortString } from '@/utils/number'
import { customTruncate, formatNumberWithSeparator } from '@/utils/number.ts'
import { computed, ref } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'

const { isLoading, playerState } = usePlayerState()

const route = useRoute()
const router = useRouter()

const isWheelSpinPage = computed(() => route.path.includes('/wheel-spin'))
const isMenuPage = computed(() => route.path.includes('/menu'))

const isOpenLivesModal = ref(false)

const goToShopForHard = () => {
  sendAnalyticsEvent('go_to_shop', { from: 'header-hard' })
  router.push({ name: 'shop', query: { scrollTo: 'hard' } })
}
</script>

<template>
  <div class="main-view">
    <HeaderMenu>
      <template #left>
        <div v-if="!isLoading && playerState" class="relative flex gap-x-6 pl-4">
          <BalanceItem
            v-if="isWheelSpinPage"
            iconName="ton-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{
              formatNumberWithSeparator(
                customTruncate(getCurrencyRealAmount(playerState.ton ?? 0, 'ton'))
              )
            }}
          </BalanceItem>
          <BalanceItem
            v-if="!isWheelSpinPage"
            iconName="soft-coin-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{ formatNumberToShortString(playerState.soft ?? 0) }}
          </BalanceItem>
          <BalanceItem
            iconName="hard-coin-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
            add-button
            @add="goToShopForHard"
          >
            {{ formatNumberToShortString(playerState.hard ?? 0) }}
          </BalanceItem>
          <BalanceItem
            v-if="isMenuPage"
            iconName="ticket-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{ formatNumberToShortString(playerState.tickets ?? 0) }}
          </BalanceItem>
          <LivesBar v-if="!isMenuPage" size="small" show-timer @add="isOpenLivesModal = true" />
        </div>
      </template>
    </HeaderMenu>
    <RouterView />
  </div>
  <ConnectionDialog />
  <PlayerProfileModal />
  <LivesModal :isOpen="isOpenLivesModal" @close="() => (isOpenLivesModal = false)" />
  <LeagueRewardAutomatic />
</template>

<style lang="scss"></style>
