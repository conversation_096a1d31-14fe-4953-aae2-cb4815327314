<script setup lang="ts">
import friendsBanner from '@/assets/images/temp/banners/mates-banner.png'
import LoadingScreen from '@/components/UI/LoadingScreen.vue'
import TextDivider from '@/components/UI/TextDivider.vue'
import PurchaseCurrencyWindow from '@/components/shop/PurchaseCurrencyWindow.vue'
import PurchaseItemWindow from '@/components/shop/PurchaseItemWindow.vue'
import ShopBoxDetails from '@/components/shop/ShopBoxDetails.vue'
import ShopItem, { type ShopItemStyle } from '@/components/shop/ShopItem.vue'
import { useLootboxTimer } from '@/composables/useLootboxTimer'
import { usePurchase } from '@/composables/usePurchase'
import {
  LOOTBOX_TYPE_TO_IMAGE,
  LOOTBOX_TYPE_TO_SHINE_IMAGE,
  LOOTBOX_TYPE_TO_STYLE,
  LOOTBOX_REWARDS_ORDER,
  type LootboxRewardId
} from '@/constants/lootboxes.ts'
import { ITEM_ID_TO_IMAGE, ITEM_ID_TO_SHINE_IMAGE, ITEM_ID_TO_STYLE } from '@/constants/shop'
import { useGetFreeLootbox, useOpenLootbox } from '@/services/client/useLootboxes.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useShopItems } from '@/services/client/useShopItems'
import leaguesService from '@/services/local/leagues.ts'
import type {
  LootBoxOffer,
  LootBoxType,
  Price,
  RewardType,
  ShopItemPrice,
} from '@/services/openapi'
import { useLootboxReward } from '@/stores/rewardStore'
import { useToast } from '@/stores/toastStore'
import { type ShopScrollTarget } from '@/types'
import { computed, ref, useTemplateRef, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const { t } = useI18n()

const { playerState } = usePlayerState()

const userLeagueNumber = computed(() => playerState.value!.leagueLevel ?? 1)

const { shopItems, isLoading: isLoadingShopItems, refetch: refetchShopItems } = useShopItems()

const lootboxesList = computed(
  () =>
    shopItems.value?.lootboxes
      .slice()
      .sort((a, b) => a.offers[0].price.displayPrice.amount - b.offers[0].price.displayPrice.amount) ?? []
)
const wheelSpinsList = computed(
  () => shopItems.value?.wheelSpins.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount) ?? []
)
const friendsList = computed(
  () => shopItems.value?.friends.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount) ?? []
)
const hardList = computed(
  () => shopItems.value?.hard.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount) ?? []
)
const softList = computed(
  () => shopItems.value?.soft.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount) ?? []
)

const lootBoxStore = useLootboxReward()
const {
  isFreeLootboxAvailable,
  hours,
  minutes,
  seconds
} = useLootboxTimer()
const { showToast } = useToast()

const { purchaseItem: purchaseItemMutation, isPurchasingItem } = usePurchase()
const { openLootbox, isPending: isPendingOpenLootbox } = useOpenLootbox()
const { getFreeLootbox, isPending: isPendingBoxGetFree } = useGetFreeLootbox()

const isLoading = computed(() => (
  isLoadingShopItems.value ||
  isPurchasingItem.value ||
  isPendingBoxGetFree.value ||
  isPendingOpenLootbox.value
))

const boxToDetailsId = ref<string | undefined>()

const boxDetails = computed(() => {
  return (
    lootboxesList.value.find(b => b.lootboxType === boxToDetailsId.value) || lootboxesList.value[0]
  )
})

const sortedRewards = computed(() => {
  return boxDetails.value?.rewards.slice().sort((a, b) => {
    return (
      LOOTBOX_REWARDS_ORDER.indexOf(a.id as LootboxRewardId) - LOOTBOX_REWARDS_ORDER.indexOf(b.id as LootboxRewardId)
    )
  })
})

const openBoxDetails = (id: string) => {
  const box = lootboxesList.value.find(b => b.lootboxType === id) || null
  if (box) {
    boxToDetailsId.value = id
  }
}

const closeBoxDetails = () => {
  boxToDetailsId.value = undefined
}

const getFreeBox = (lootboxType: LootBoxType) => {
  try {
    getFreeLootbox().then(async () => {
      openLootbox(lootboxType).then(data => {
        lootBoxStore.showReward([{ type: lootboxType, rewards: data.rewards }])
      })
    })
  } catch (err) {
    showToast('Something went wrong', 'warning')
  }
}

const purchaseStarsWindowRef = useTemplateRef('purchaseStarsWindowRef')
const purchaseItemWindowRef = useTemplateRef('purchaseItemWindowRef')

const tryPurchaseItem = (type: RewardType, id: number, value: number, price: ShopItemPrice) => {
  if (price.prices.length > 1) {
    purchaseItemWindowRef.value?.openWindow(type, { id, value, price })
  } else {
    purchaseItem(type, id, value, price.prices[0] ?? price.displayPrice)
  }
}

const purchaseItem = (type: RewardType, itemId: number, value: number, price: Price) => {
  purchaseItemMutation(type, itemId, value, price).then(() => {
    purchaseItemWindowRef.value?.closeWindow()
  }).catch(reason => {
    if (reason?.message === 'NOT_ENOUGH_FUNDS') {
      if (price.currency === 'hard') {
        purchaseStarsWindowRef.value?.openWindow(hardList.value, 'hard', reason.payload)
      } else if (price.currency === 'soft') {
        closeBoxDetails()
        scrollTo('soft')
      }
    }
  })
}

const lootboxElemRef = useTemplateRef('lootboxElemRef')
const wheelSpinsElemRef = useTemplateRef('wheelSpinsElemRef')
const friendsElemRef = useTemplateRef('friendsElemRef')
const hardElemRef = useTemplateRef('hardElemRef')
const softElemRef = useTemplateRef('softElemRef')

const SCROLL_TARGET_TO_REF = {
  lootbox: lootboxElemRef,
  wheelSpins: wheelSpinsElemRef,
  friends: friendsElemRef,
  hard: hardElemRef,
  soft: softElemRef
}

const scrollTo = (target: ShopScrollTarget) => {
  const element = SCROLL_TARGET_TO_REF[target]
  element.value?.scrollIntoView({ behavior: 'smooth', block: 'center' })
}

const route = useRoute()
const scrollTarget = route.query.scrollTo as undefined | ShopScrollTarget
watch(
  [isLoading, lootboxElemRef, wheelSpinsElemRef, friendsElemRef, hardElemRef, softElemRef],
  ([loading, el1, el2, el3, el4, el5]) => {
    if (!loading && el1 && el2 && el3 && el4 && el5 && scrollTarget) {
      // TODO: all elements should be rendered for propper scrolling
      // TODO: find way without setTimeout.
      // Currently, without timeout it scrolls to not desired position
      setTimeout(() => {
        scrollTo(scrollTarget)
      }, 200)
    }
  },
  { immediate: true }
)
</script>

<template>
  <div ref="scrollContainerRef" class="view-container menu-item menu-item_three space-y-[24px]">
    <!-- Loot boxes list -->
    <div ref="lootboxElemRef" v-if="lootboxesList.length">
      <TextDivider class="mb-[10px]"> {{ t('reward.boxes') }}: </TextDivider>
      <div
        class="shop-view__list"
        :style="{
          gridTemplateColumns: `repeat(${lootboxesList.length}, 1fr)`
        }"
      >
        <ShopItem
          v-for="item in lootboxesList
            .slice()
            .sort(
              (a, b) =>
                a.offers.find((o: LootBoxOffer) => o.value === 1)!.price.displayPrice.amount -
                b.offers.find((o: LootBoxOffer) => o.value === 1)!.price.displayPrice.amount
            )"
          :key="item.lootboxType"
          :type="LOOTBOX_TYPE_TO_STYLE[item.lootboxType]"
          :image="LOOTBOX_TYPE_TO_IMAGE[item.lootboxType]"
          imageSize="box"
          :name="t(`lootboxes.${item.lootboxType}`)"
          :price="
            item.lootboxType === 'rainbowLootBox' && isFreeLootboxAvailable
              ? 0
              : item.offers.find((b: LootBoxOffer) => b.value === 1)?.price.displayPrice.amount ?? 0
          "
          :currency="item.offers[0].price.displayPrice.currency"
          @click="() => openBoxDetails(item.lootboxType)"
        />
      </div>
    </div>

    <!-- Wheel spins list -->
    <div
      ref="wheelSpinsElemRef"
      v-if="wheelSpinsList.length && leaguesService.hasAccess(userLeagueNumber, 'offers')"
    >
      <TextDivider class="mb-[10px]"> {{ t('reward.wheelSpins') }}: </TextDivider>
      <div class="shop-view__list">
        <ShopItem
          v-for="item in wheelSpinsList"
          :key="item.id"
          :type="(ITEM_ID_TO_STYLE[item.id] as ShopItemStyle)"
          :image="ITEM_ID_TO_IMAGE[item.id]"
          :shine-image="ITEM_ID_TO_SHINE_IMAGE[item.id]"
          :amount="item.value"
          :price="item.price.displayPrice.amount"
          :currency="item.price.displayPrice.currency"
          :is-best-deal="item.id === 610"
          @click="() => tryPurchaseItem('wheelSpins', item.id, item.value, item.price)"
        />
      </div>
    </div>

    <!-- Friends list -->
    <div ref="friendsElemRef" v-if="friendsList.length">
      <TextDivider class="mb-[10px]"> {{ t('reward.refs') }}: </TextDivider>
      <div class="relative">
        <img class="w-full mb-[20px]" :src="friendsBanner" alt="friends banner" />
        <div
          class="absolute right-[11px] top-1/2 -translate-y-1/2 text-[13px] leading-[16px] text-end whitespace-pre"
        >
          {{ t('shop.friendsDescription') }}
        </div>
      </div>
      <div class="shop-view__list">
        <ShopItem
          v-for="item in friendsList"
          :key="item.id"
          :type="(ITEM_ID_TO_STYLE[item.id] as ShopItemStyle)"
          :image="ITEM_ID_TO_IMAGE[item.id]"
          :shine-image="ITEM_ID_TO_SHINE_IMAGE[item.id]"
          :amount="item.value"
          :price="item.price.displayPrice.amount"
          :currency="item.price.displayPrice.currency"
          :is-best-deal="item.id === 6"
          @click="() => tryPurchaseItem('refsFake', item.id, item.value, item.price)"
        />
      </div>
    </div>

    <!-- Stars/hard list -->
    <div ref="hardElemRef" v-if="hardList.length">
      <TextDivider class="mb-[10px]"> {{ t('reward.hard') }}: </TextDivider>
      <div class="shop-view__list">
        <ShopItem
          v-for="item in hardList"
          :key="item.id"
          :image="ITEM_ID_TO_IMAGE[item.id]"
          :type="(ITEM_ID_TO_STYLE[item.id] as ShopItemStyle)"
          image-size="large"
          :amount="item.value"
          :price="item.price.displayPrice.amount"
          :currency="item.price.displayPrice.currency"
          :is-best-deal="item.id === 112"
          @click="() => tryPurchaseItem('hard', item.id, item.value, item.price)"
        />
      </div>
    </div>

    <!-- Coins/soft list -->
    <div ref="softElemRef" v-if="softList.length">
      <TextDivider class="mb-[10px]"> {{ t('reward.soft') }}: </TextDivider>
      <div class="shop-view__list">
        <ShopItem
          v-for="item in softList"
          :key="item.id"
          :image="ITEM_ID_TO_IMAGE[item.id]"
          :type="(ITEM_ID_TO_STYLE[item.id] as ShopItemStyle)"
          :shine-image="ITEM_ID_TO_SHINE_IMAGE[item.id]"
          :amount="item.value"
          :price="item.price.displayPrice.amount"
          :currency="item.price.displayPrice.currency"
          :is-best-deal="item.id === 202"
          @click="() => tryPurchaseItem('soft', item.id, item.value, item.price)"
        />
      </div>
    </div>
  </div>
  <LoadingScreen
    v-if="isLoading"
    is-open
  />
  <ShopBoxDetails
    v-if="boxDetails?.lootboxType"
    :isOpen="boxToDetailsId !== undefined"
    :lootboxType="boxDetails?.lootboxType"
    :image="LOOTBOX_TYPE_TO_IMAGE[boxDetails.lootboxType]"
    :shine-image="LOOTBOX_TYPE_TO_SHINE_IMAGE[boxDetails.lootboxType]"
    :offers="
      boxDetails.offers.filter((o: LootBoxOffer) => o.id !== 504)
    "
    :rewards="sortedRewards"
    :is-free="boxDetails.lootboxType === 'rainbowLootBox'"
    :is-free-available="isFreeLootboxAvailable"
    :hours="hours"
    :minutes="minutes"
    :seconds="seconds"
    :isLoading="isLoading"
    @get-free="(lootboxType: LootBoxType) => getFreeBox(lootboxType)"
    @purchase="
      (id: number, value: number, price: ShopItemPrice) => {
        tryPurchaseItem(boxDetails?.lootboxType, id, value, price)
      }
    "
    @close="closeBoxDetails"
  />
  <PurchaseItemWindow
    :class="{ 'opacity-0': isPurchasingItem }"
    ref="purchaseItemWindowRef"
    @purchase="(
      type: RewardType,
      itemId: number,
      value: number,
      price: Price
    ) => purchaseItem(type, itemId, value, price)"
  />
  <PurchaseCurrencyWindow
    ref="purchaseStarsWindowRef"
    @purchased="
      () => {
        // we have to refetch offers because cheapest hard currency offer is limited
        // and should dissapear after meeting the condition on server
        refetchShopItems()
      }
    "
  />
</template>

<style lang="scss" scoped>
.shop-banner {
  height: 55px;
  box-shadow:
    0 2px #00000040,
    inset 0 1px #a7e0ff;
}

.shop-view {
  &__list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: 151px;
    gap: 24px 11px;
  }
  &__column {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-auto-rows: 84px;
    gap: 24px 11px;
  }
}
</style>
