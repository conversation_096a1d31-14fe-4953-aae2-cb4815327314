<script setup lang="ts">
import VButton from '@/components/UI/VButton.vue';

import { useI18n } from 'vue-i18n';
import VStep from '@/components/VStep.vue';
import { useIsWalletConnected, useWalletConnection } from '@/composables/useWallet';
import { toNano, useTonTransaction } from '@/composables/useTonTransaction';
import LoaderText from '@/components/LoaderText.vue';

const { t } = useI18n()

const { isLoading, isWalletConnectedEverywhere, isTransactionMade } = useIsWalletConnected()

const { connectWallet } = useWalletConnection()
const { sendTransaction } = useTonTransaction()
</script>

<template>
  <div class="airdrop h-full flex flex-col justify-between">
    <header class="text-center mb-6 tracking-normal">
      <p class="font-extrabold text-[16px] leading-[22px] text-[#6DB0ED]">
        {{ t('airdrop.tasks.info') }}
      </p>
    </header>

    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <LoaderText class="text-[#6DB0ED] text-[20px]" isLoading />
    </div>
    <template v-else>
      <div class="space-y-[22px] mx-auto mb-[46px] pl-[28px] pr-[16px]">
        <VStep
          :step="1"
          :text="t('airdrop.tasks.connectWallet')"
          :isDone="isWalletConnectedEverywhere"
        />
        <VStep
          :step="2"
          :text="t('airdrop.tasks.transaction')"
          :isDone="!!isTransactionMade"
        />
      </div>
      <div
        class="tickets-banner"
        :class="isTransactionMade ? 'tickets-banner_lg' : 'tickets-banner_sm'"
      >
        <p class="tickets-banner__description">
          {{ t('airdrop.ticketsBanner') }}
        </p>
        <p
          v-if="isTransactionMade"
          class="tickets-banner__title text-shadow text-shadow_black"
        >
          {{ t('airdrop.comingSoon') }}
        </p>
      </div>
      <div v-if="!isTransactionMade" class="airdrop-instruction flex flex-col mt-[19px]">
        <div class="flex-1">
          <div class="ml-4 mb-2 mr-2">
            <p>
              {{ t('airdrop.instructionSteps.1') }}
            </p>
            <p>
              {{ t('airdrop.instructionSteps.2') }}
            </p>
          </div>
          <p class="airdrop-instruction__empty-balance">
            {{ t('airdrop.instructionText') }}
          </p>
        </div>
        <VButton
          v-if="!isWalletConnectedEverywhere"
          class="!w-full"
          type="success"
          :text="t('actions.connectWallet')"
          @click="connectWallet"
        />
        <VButton
          v-else-if="!isTransactionMade"
          class="!w-full"
          type="success"
          :text="t('actions.makeTransaction')"
          @click="() => sendTransaction(toNano(0.5))"
        />
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.airdrop {
  padding: 22px 20px;
  margin-bottom: 24px;
}

.tickets-banner {
  position: relative;
  background: linear-gradient(104.87deg, #67EDFF -9.26%, #1689DB 122.4%);
  border-radius: 9px;
  padding: 14px 11px;
  font-size: 14px;
  color: white;
  box-shadow: 0px 4px 0px 0px #F0A8FF40 inset;


  &__description {
    max-width: 50%;
    text-align: start;
    float: right;
  }

  &__title {
    position: absolute;
    bottom: 9px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    font-size: 22px;
    line-height: 30px;
  }

  &_sm {
    background: url('@/assets/images/temp/airdrop/banner-sm.png') no-repeat;
    background-size: cover;
  }

  &_lg {
    flex: 1;
    background: url('@/assets/images/temp/airdrop/banner-lg.png') no-repeat;
    background-size: cover;
    background-position: center;
  }
}

.airdrop-instruction {
  flex: 1;

  width: 100%;
  padding: 16px 10px 14px 8px;

  background: linear-gradient(360deg, #72C3E6 0%, #B7E9FF 92.65%);
  border-radius: 9px;
  box-shadow: #00000033 0 2px, inset white 0 4px;

  p {
    font-size: 16px;
    line-height: 20px;
    font-weight: 700;
    color: #1E4073;
  }

  &__empty-balance {
    padding: 3px 10px 8px 10px;
    margin: 0 7px 16px 8px;
    background-color: #2397D529;

    p {
      font-weight: 600;
    }
  }
}
</style>
