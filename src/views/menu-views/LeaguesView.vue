<script setup lang="ts">
import arrowLeft from '@/assets/images/temp/arrow-left.svg'
import LeagueComponent from '@/components/leagues/LeagueComponent.vue'
import { useEventInstructionCheck } from '@/composables/useInstructionCheck.ts'
import { LEAGUES_INSTRUCTION } from '@/constants/instructions.ts'
import leaguesList from '@/preload/leagues-list.json'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useInstructionStore } from '@/stores/instructionStore.ts'
import Glide, { Controls, Swipe } from '@glidejs/glide/dist/glide.modular.esm.js'
import { onMounted, ref } from 'vue'
import type { RewardInfo } from '@/services/openapi'

const LEAGUES = leaguesList.list.sort((a, b) => a.leagueLevel - b.leagueLevel)

const instructionStore = useInstructionStore()

const { checkInstruction } = useEventInstructionCheck()
const { playerState } = usePlayerState()

const glide = ref<Glide | null>(null)
const startAtLeagueIndex = (playerState.value!.leagueLevel ?? 1) - 2 // first league (at 0 index) is 2
const currentLeague = ref(LEAGUES[startAtLeagueIndex].leagueLevel)

onMounted(async () => {
  const isEventInstruction = await checkInstruction(LEAGUES_INSTRUCTION)
  if (isEventInstruction) {
    instructionStore.showInstruction(LEAGUES_INSTRUCTION)
  }

  glide.value = new Glide('#leagues', {
    type: 'slider',
    focusAt: 'center',
    perView: 1,
    rewind: false,
    startAt: startAtLeagueIndex,
    animationDuration: 200,
    dragThreshold: 10,
    gap: 0
  })

  glide.value.mount({ Swipe, Controls })
  glide.value.on(['mount.after', 'run'], () => {
    currentLeague.value = LEAGUES[glide.value!.index].leagueLevel
  })
})
</script>

<template>
  <div class="view-container menu-item !p-0">
    <div id="leagues" class="h-full relative glide">
      <div class="h-full glide__track" data-glide-el="track">
        <ul class="h-full glide__slides">
          <li class="leagues__item" v-for="(leagueItem, key) in LEAGUES" :key="key">
            <LeagueComponent
              :class="{
                league_right: leagueItem.leagueLevel > currentLeague,
                league_left: leagueItem.leagueLevel < currentLeague
              }"
              :league="leagueItem.leagueLevel"
              :league-name="leagueItem.name"
              :rewards="(leagueItem.rewards as RewardInfo[])"
              :active="leagueItem.leagueLevel === currentLeague"
              :rendered="Math.abs(leagueItem.leagueLevel - currentLeague) < 2"
              :tickets-range="(leagueItem.ticketsRange as [number, number])"
              :user-league="playerState!.leagueLevel ?? 1"
              :user-tickets="playerState!.tickets ?? 0"
              :is-last="key === LEAGUES.length - 1"
            />
          </li>
        </ul>
      </div>
      <div class="glide__arrows leagues__arrows pointer-events-none" data-glide-el="controls">
        <button
          class="glide__arrow glide__arrow--left leagues__arrow pointer-events-auto"
          data-glide-dir="<"
        >
          <img
            v-show="currentLeague !== LEAGUES[0].leagueLevel"
            class="w-[14px] mx-auto"
            :src="arrowLeft"
            alt="arrow"
          />
        </button>
        <button
          class="glide__arrow glide__arrow--right leagues__arrow pointer-events-auto -scale-x-100"
          data-glide-dir=">"
        >
          <img
            v-show="currentLeague !== LEAGUES.at(-1)?.leagueLevel"
            class="w-[14px] mx-auto"
            :src="arrowLeft"
            alt="arrow"
          />
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.leagues {
  &__item {
    height: 100%;
  }

  &__arrows {
    position: absolute;
    left: 0;
    top: 29px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: calc(50% - 16px);
  }

  &__arrow {
    width: 66px;
    height: 100%;
  }
}
</style>
