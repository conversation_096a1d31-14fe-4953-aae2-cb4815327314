<script lang="ts" setup>
import VButton from '@/components/UI/VButton.vue';
// import { Button } from '@tonconnect/ui';
import { useNetwork } from '@vueuse/core';
import { useWallectDisconnection, useIsWalletConnected } from '@/composables/useWallet';
import { cloudStorage } from '@telegram-apps/sdk';
import { useControlMethodStore } from '@/stores/controlMethodStore';

const { rtt, downlink, downlinkMax } = useNetwork()
const { disconnectWallet } = useWallectDisconnection()
const {
  isWalletConnectedLocaly,
  isWalletConnectedOnServer
} = useIsWalletConnected()
const controlStore = useControlMethodStore()

const dropCloudStorage = async () => {
  const keys = await cloudStorage.getKeys()
  cloudStorage.deleteItem(keys).then(() => console.log('Deleted items: ', keys))
}

// const dropCloudItem = (key: string) => {
//   cloudStorage.deleteItem(key).then(() => console.log('Deleted item: ', key))
// }
</script>

<template>
  <div>
    <div class="absolute left-10 top-2">
      <div>RTT: {{ rtt }}</div>
      <div>Downlink: {{ downlink }}</div>
      <div>DownlinkMax: {{ downlinkMax }}</div>
    </div>
  </div>
  <div class="flex flex-col items-end absolute right-10 top-1/3 space-y-3">
    isWalletConnectedLocaly: {{ isWalletConnectedLocaly }}<br />
    isWalletConnectedOnServer: {{ isWalletConnectedOnServer }}
    <VButton type="accent" text="Disconnect wallet on server" @click="disconnectWallet" />
  </div>
  <div class="flex flex-col items-end absolute right-10 top-2/3 space-y-3">
    <VButton type="accent" text="Clean Cloud Storage" @click="dropCloudStorage" />
    <VButton type="accent" text="Clean Control Method" @click="controlStore.cleanMethod" />
  </div>
</template>

<style lang="scss">
</style>
