<script setup lang="ts">
import FooterHome from '@/components/FooterHome.vue';
import { computed } from 'vue';
import { RouterView, useRoute } from 'vue-router'

const ROUTE_NAMES_WITHOUT_SHADOW = ['leagues', 'wheel-spin']

const route = useRoute()

const hasShadow = computed(() => {
  return !ROUTE_NAMES_WITHOUT_SHADOW.includes(route.name as string)
})

</script>

<template>
  <div class="menu-container footer-home-wrapper" @click.stop>
    <div class="menu-view" :class="{ 'menu-view_shadow': hasShadow }">
      <RouterView />
    </div>
    <div class="absolute bottom-0 left-0 w-full z-0">
      <FooterHome />
    </div>
  </div>
</template>

<style lang="scss">
.menu-container {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: inherit;
  height: inherit;
}

.footer-home-wrapper {
  --footer-button-size: 44px;
  --footer-padding-y: 17px;
  --footer-top-offset: 20px;
  --footer-top-padding: calc(var(--footer-padding-y) + var(--footer-top-offset));
  --footer-bottom-padding: max(var(--footer-padding-y), var(--inset-bottom));
  --footer-height: calc(var(--footer-button-size) + var(--footer-bottom-padding) + var(--footer-padding-y));

  // small viewport phones like iPhone 8
  @media screen and (max-height: 667px) {
    --footer-height: max(var(--inset-bottom), 0px);
  }
}

.menu-view {
  width: inherit;
  height: calc(100% - var(--footer-height));
  position: relative;
  z-index: 1;

  // Add a shadow to the header
  &_shadow {
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: -10px;
      height: var(--header-height);
      width: calc(100% + 20px);
      box-shadow: var(--menu-color) 0 10px 10px;
      z-index: 2;
    }
  }
}

// dont show the shadow when the route is animating
// showing the shadow during the animation causes a bug with shadow positioning
.route-animation-enter-active,
.route-animation-leave-active {
  .menu-view::before {
    display: none;
  }
}
</style>
