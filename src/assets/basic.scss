@import './font/style.css';

/* color palette from <https://github.com/vuejs/theme> */
:root {
  --safe-area-inset-top: var(--tg-viewport-safe-area-inset-top, 0px);
  --content-safe-area-inset-top: var(--tg-viewport-content-safe-area-inset-top, 0px);
  --safe-area-inset-bottom: var(--tg-viewport-safe-area-inset-bottom, 0px);
  --content-safe-area-inset-bottom: var(--tg-viewport-content-safe-area-inset-bottom, 0px);
  --inset-top: calc(var(--safe-area-inset-top) + var(--content-safe-area-inset-top));
  --inset-bottom: calc(var(--safe-area-inset-bottom) + var(--content-safe-area-inset-bottom));
  --header-height-fixed: 48px;
  --header-height: calc(var(--header-height-fixed) + var(--inset-top));

  --menu-color: #28599C;
  --menu-background: var(--menu-color);
  --menu-background-1: var(--menu-color) radial-gradient(50% 50% at 50% 50%, rgba(16, 212, 255, 0.54) 17.5%, rgba(16, 212, 255, 0) 100%);
  --menu-background-2: var(--menu-color) radial-gradient(60% 50% at 50% 50%, rgba(16, 212, 255, 0.54) 40%, rgba(16, 212, 255, 0) 100%);
  --menu-background-3: var(--menu-color) radial-gradient(60% 50% at 50% 50%, rgba(16, 212, 255, 0) 40%, rgba(16, 212, 255, 0) 100%);
  --menu-background-4: var(--menu-color) radial-gradient(45% 35% at 50% 35%, rgba(16, 212, 255, 0.54) 40%, rgba(16, 212, 255, 0) 100%);

  --primary: #5E48A4;
  --primary-100: #BABCF5;
  --primary-200: #C6B1FF;
  --primary-300: #AD98FF;
  --primary-400: #907CCC;
  --primary-500: #886DCF;
  --primary-550: #8758EB;
  --primary-600: #7971CC;
  --primary-700: #6247AA;
  --primary-750: #3C0058;
  --primary-800: #370051;
  --primary-900: #2C186B;
  --primary-1000: #1D3161;
  --primary-1100: #1A2C59;

  --default: #4296F8;
  --secondary: #C4CFDD;
  --success: #48C254;
  --accent: #F5802A;
  --accent-100: #FFEADA;
  --accent-400: #F2994A;

  --white: #ffffff;
  --black: #000000;
  --gray: rgba(3, 16, 47, 0.48);
  --light-gray: #bfc3c4;
  --green: #48C254;
  --dark-green: #2f845e;
  --orange: #FF8A00;
  --dark-blue: #1D3161;
  --blue: #4296F8;
  --light-blue: #B4D1FF;
  --brown: #854442;
  --red: #ff4c4c;
  --yellow: #f1c40f;
  --stroke-color: #3F1C9A;
  --shadow-color: #222222;
  --overlay-color: #141044D9;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

html {
  overflow: hidden;
}

body {
  width: 100dvw;
  height: 100dvh;
  overflow: inherit;
  user-select: none;
}

a {
  color: white;
  text-decoration: none;
}

img {
  max-width: none;
}

//eruda console cheats input height increase to match some devices
.eruda-js-input {
  height: 50px!important;
}
