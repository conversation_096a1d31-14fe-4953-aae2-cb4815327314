<svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6615_20783)">
<rect x="14" y="10.8647" width="14.0004" height="4.09038" rx="2.04519" transform="rotate(-180 14 10.8647)" fill="white"/>
<rect x="9.04492" y="1.81934" width="14.0004" height="4.09038" rx="2.04519" transform="rotate(90 9.04492 1.81934)" fill="white"/>
</g>
<defs>
<filter id="filter0_d_6615_20783" x="-0.000488281" y="0.819336" width="14.0005" height="15.0005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.278431 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6615_20783"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6615_20783" result="shape"/>
</filter>
</defs>
</svg>
