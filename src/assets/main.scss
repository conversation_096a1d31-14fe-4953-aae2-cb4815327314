@use "tailwindcss/base";
@use "tailwindcss/components";
@use "tailwindcss/utilities";

@use 'basic.scss';
@use 'typography.scss';
@use 'components/_style.scss';
@use 'animations.scss';

#app {
  width: inherit;
  height: inherit;
  max-width: 100dvw;
  max-height: 100dvh;
}

#app-view {
  width: inherit;
  height: inherit;
}

.main-view {
  width: inherit;
  height: inherit;
  padding-top: var(--header-height);
}

.view-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.menu-item {
  z-index: 1;
  background: var(--menu-background);
  padding: 22px 20px 16px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  
  &_one {
    background: var(--menu-background-1);
  }
  
  &_two {
    background: var(--menu-background-2);
  }

  &_three {
    background: var(--menu-background-3);
  }

  &_four {
    background: var(--menu-background-4);
  }

  &_bottom-gradient {
    &::after {
      content: '';
      position: absolute;
      z-index: 10;
      bottom: 0;
      width: 100%;
      height: 30px;
      background: linear-gradient(360deg, #29579a 14.82%, rgba(41, 87, 154, 0) 68.56%);
    }
  }
}

.menu-item-tabs {
  z-index: 1;
  overflow: visible;
}

.white-spot {
  position: absolute;
  background-color: white;
  border-radius: 50%;
}
