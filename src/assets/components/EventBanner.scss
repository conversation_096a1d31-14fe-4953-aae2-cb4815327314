
.event-banner {
  position: relative;

  display: flex;
  flex-direction: column;
  align-items: center;

  width: 100%;
  max-width: 346px;
  border: 6px solid #ffd634;
  border-radius: 13px;
  background: var(--event-background);

  &__banner {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, calc(-100% + 45px));
    pointer-events: none;
    width: 90%;
  }

  &__image-container {
    border-radius: 7px 7px 0 0;
    aspect-ratio: 2.11 / 1;
    width: 100%;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;

    &_full {
      border-radius: 7px;
    }
  }

  &__details {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 20;
  }

  &__description {
    color: #c2e2ff;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    white-space: pre;
    letter-spacing: normal;
  }

  &__content {
    width: 100%;
    padding: 8px 12px 20px;
  }

  &__loading {
    position: absolute;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    inset: 0;
    background-color: #064c8dbd;
    border-radius: inherit;

    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 24px;
    color: #6db0ed;
    transition: opacity 0.3s;
    transition-delay: 1s;

    &_active {
      opacity: 1;
      pointer-events: auto;
    }
  }
}