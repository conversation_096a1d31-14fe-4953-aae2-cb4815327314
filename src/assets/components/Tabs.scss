
.tabs-group {
  width: inherit;
  height: inherit;

  display: flex;
  flex-direction: column;

  &__panels {
    flex: 1 1 0;
    overflow-y: auto;
    background: var(--menu-background);
  }

  &__tab-list {
    position: relative;
    z-index: 100;
    display: flex;
    flex-grow: 0;

    &-shadow-gradient {
      position: absolute;
      z-index: 1;
      top: -28px;
      width: 100%;
      height: 28px;
      background: linear-gradient(360deg, #29579a 14.82%, rgba(41, 87, 154, 0) 68.56%);
    }
  }

  &__tab {
    position: relative;
    flex: 1;
    border-bottom: 2px solid #002C6E;
    background: #83FBFF;
    border-radius: 0 0 12px 12px;
    padding: 7.5px 0;
    box-shadow: inset #D8FDFF 0 -5px ;

    font-size: 20px;
    color: white;

    &:focus-within {
      outline: none;
    }

    &_active {
      background-color: #29579A;
      box-shadow: inset #7DC3FF52 0 -5px ;
      color: white;
    }

    &_disabled {
      background-color: #c5c5c5;
      box-shadow: inset #97979752 0 -5px ;
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.tabs-group-simple {
  width: inherit;
  height: inherit;

  display: flex;
  flex-direction: column;

  &__panels {
    flex: 1 1 0;
    background: #2397D54D;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
  }

  &__tab-list {
    position: relative;
    z-index: 100;
    display: flex;
    column-gap: 2px;
  }

  &__tab {
    flex: 1;
    position: relative;
    background: #2397D529;
    border-radius: 7px 7px 0 0;
    padding: 1px 0;

    font-size: 20px;
    color: white;

    &:focus-within {
      outline: none;
    }

    &_active {
      background-color: #2397D54D;
    }
  }
}
