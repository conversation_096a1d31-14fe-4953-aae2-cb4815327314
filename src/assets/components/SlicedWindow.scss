.sliced-window {
  --sliced-outline-color: #dcfae7;
  --sliced-top-height: 157px;
  --sliced-top-bg: linear-gradient(180deg, #00abfa 0%, #276eed 100%);
  --sliced-top-shadow: 
    0 2px 1px 0 #00000040,
    0 2px 0 0 #8ef6ff inset,
    0 -4px 0 0 #006090 inset;
  --sliced-bg: transparent;

  position: relative;

  display: flex;
  flex-direction: column;
  align-items: center;

  width: 90%;
  height: 80%;

  outline: var(--sliced-outline-color) solid 5px;
  outline-offset: -5px;
  border-radius: 18px;
  transform: translateY(20px);
  background: var(--sliced-bg);
  
  &__top {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    height: var(--sliced-top-height);
    width: 100%;
    padding: 34px 12px 0px 12px;
    background: var(--sliced-top-bg);
    border-radius: 18px;
    box-shadow: var(--sliced-top-shadow);
  }

  &__top-placeholder {
    flex: 0 0 calc(var(--sliced-top-height) - 22px);
  }

  &__bottom {
    flex: 1;
    // width: 100%;
    overflow-y: auto;
    border-radius: 13px;
    overscroll-behavior-y: none;
    pointer-events: auto;
    margin: 4.5px;
  }

  &__banner {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, calc(-100% + 20px));
    pointer-events: none;
    width: 90%;
    z-index: 10;
  }

  &__timer {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%);
    border-radius: 5px;
    background-color: #004375;
    padding: 3px 10px;
    font-size: 12px;
  }

  &__balance {
    position: absolute;
    z-index: 2;
    top: 170px;
    left: -5px;
    padding: 0 16px 0 12px;
    height: 32px;
    border: 1px solid #8d4e18;
    border-radius: 6px;
    background: linear-gradient(180deg, #f8e1b5 0%, #f0e3a4 100%);
    box-shadow:
      0 3px 0.2px 0 #00000040,
      0 -2px 0 0 #b4964b inset,
      0 1px 0 0 #fee775 inset;

    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  &__loading {
    position: absolute;
    pointer-events: none;
    opacity: 0;

    font-size: 24px;
    color: #6db0ed;
    transition: opacity 0.3s;
    transition-delay: 1s;

    &_active {
      opacity: 1;
    }
  }

  .close-button {
    position: absolute;
    top: 7px;
    right: 7px;
    z-index: 10;
    --close-btn-background-color: #004375;
  }
}