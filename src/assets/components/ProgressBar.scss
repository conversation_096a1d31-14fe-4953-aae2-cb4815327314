.progress-bar {
  overflow: hidden;
  transform: skew(-15deg);
  background: linear-gradient(360deg, #00539D 0%, #002564 66.67%);
  border-radius: 5px;
  display: flex;

  &__inner-wrapper {
    position: relative;
    flex: 1;
    height: 100%;
    border-radius: 3px;

    &_stackable {
      display: flex;
      gap: 1px;
    }
  }

  &__inner {
    --progress-width: 0;
    --transition-speed: 0s;
    border-radius: inherit;
    background: linear-gradient(90deg, #FFA200 -1.1%, #FFE02F 86.33%);
    height: inherit;
    width: var(--progress-width);
    transition: width var(--transition-speed) linear;

    &_completed {
      background: linear-gradient(90deg, #3BBB00 -1.1%, #99F100 86.33%);
    }
  }

  &__stackable-inner {
    flex: 1;
    background-color: #DEF5FF;
    border-radius: 2px;
    transform: skew(15deg);

    &_completed {
      background-color: #FFA100;
    }

    &:first-child {
      border-radius: 27px 2px 2px 8px/64px 2px 2px 6px;
    }
  }

  &__append {
    border-radius: inherit;
    padding: 0 11px;
    height: 100%;
    background: linear-gradient(180deg, rgba(0, 37, 100, 0.9) 0%, rgba(0, 83, 157, 0.9) 43.75%);
    display: flex;
    justify-content: center;
    align-items: center;

    * {
      transform: skew(15deg);
    }
  }

  &__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) skew(15deg);
    color: white;
    font-size: 14px;
  }

  &__pointer {
    position: absolute;
    top: 100%;
    transform: translateX(-50%);
    padding: 4px 12px;
    
    border-radius: 6px;
    background-color: white;
    box-shadow: #000000 0 2px, inset #0000001A 0 -3px;
    color: white;
    font-size: 14px;

    &::before {
      content: '';
      position: absolute;
      top: -13px;
      left: 50%;
      transform: translateX(-50%);
      border: 8px solid transparent;
      border-radius: 2px;
      border-bottom-color: white;
    }
  }
}
