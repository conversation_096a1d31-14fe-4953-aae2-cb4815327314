.overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  background: #002255D9;
  transition: opacity 0.3s;
  padding-top: var(--inset-top);

  &_short {
    top: var(--header-height);
    padding-top: 0;
  }

  &_gradient {
    background: linear-gradient(180deg, #1D3161E5, #2371CCE5, #1D3161E5);
  }

  &_black {
    background: #000000;
  }

  &_shine {
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      overflow: visible;
      width: 130dvw;
      height: 130dvw;
      background: radial-gradient(50% 50% at 50% 50%, #10D4FF 15.5%, rgba(16, 212, 255, 0) 91%);
      opacity: 0.8;
      z-index: -2;
    }
  }

  &_not-transparent {
    background: linear-gradient(0deg, #28599C 0%, #0473F4 43%, #73B7FF 100%);
  }

  &_transparent {
    background: transparent;
  }
}

.overlay-enter-active,
.overlay-leave-active {
  transition: opacity 0.3s linear;
}

.overlay-enter-from,
.overlay-leave-to {
  opacity: 0;
}