
.balance-item {
  position: relative;
  width: fit-content;
  display: flex;
  align-items: center;
  
  &__image {
    position: absolute;
    z-index: 1;
    left: 0;
    transform: translateX(-46%);
    width: 30px;
    height: 30px;
  }

  &__bar {
    flex: 1;
    position: relative;
    height: 21px;
    padding: 0 16px 0 20px;
    display: flex;
    align-items: center;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 0 5px 5px 0;
      background-color: #0826739C;
      box-shadow: inset #00000012 0 -12px;
      transform: skew(-15deg);
    }

    &_gold::after {
      background-color: #FFE02F;
      box-shadow: #00000040 0 1px, inset #FFC636 0 -8px;
    }
  }

  &__balance {
    position: relative;
    z-index: 1;
    font-size: 13px;
    line-height: 1;
    font-weight: 800;
    white-space: nowrap;

    &_gold {
      color: #C44F00;
    }
  }

  &__add {
    position: absolute !important;
    right: 0;
    bottom: 0;
    transform: translateX(50%);
  }
}
