@use 'Dialog.scss';
@use 'BalanceItem.scss';
@use 'Button.scss';
@use 'ProgressBar.scss';
@use 'Icons.scss';
@use 'League.scss';
@use 'RewardScreen.scss';
@use 'EventBanner.scss';
@use 'Overlay.scss';
@use 'Tabs.scss';
@use 'SlicedWindow.scss';

.check-circle {
  position: relative;
  width: 28px;
  height: 28px;
  background: linear-gradient(90deg, #67FF76 0%, #3E9947 100%);
  border-radius: 50%;
  border: 2px solid #3E9947;
  box-shadow: #3E9947 0px 3px, inset #FFFFFF40 0 2px 1px 2px;
  transform: rotate(10deg);

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: url('@/assets/images/temp/check-icon.svg') no-repeat center;
    background-size: 20px;
    transform: rotate(-10deg);
  }
}

.check-icon {
  width: 28px;
  height: 28px;
  background: url('@/assets/images/temp/check-icon.png') no-repeat;
  background-size: cover;
}
