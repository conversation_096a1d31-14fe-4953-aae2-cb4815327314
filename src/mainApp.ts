import './assets/main.scss'

import { VueQueryPlugin } from '@tanstack/vue-query'
import { createApp } from 'vue'
import App from './App.vue'
import { i18n } from './plugins/i18n'
import { pinia } from './plugins/pinia'
import router from './router'

import { datadogLogs } from '@datadog/browser-logs'
import { datadogRum } from '@datadog/browser-rum'

export function initApp() {
  datadogRum.init({
    applicationId: '82b0c764-81f2-4b80-bd1a-a0133fbef51e',
    clientToken: 'pub51fbdc9396f3eac513ebcce7eac4bd78',
    site: 'datadoghq.eu',
    service: 'uni',
    env: __ENV__,
    version: __VERSION__,
    sessionSampleRate: 100,
    sessionReplaySampleRate: 0,
    trackUserInteractions: true,
    trackResources: true,
    trackLongTasks: true,
    defaultPrivacyLevel: 'mask-user-input'
  })

  datadogLogs.init({
    clientToken: 'pub51fbdc9396f3eac513ebcce7eac4bd78',
    site: 'datadoghq.eu',
    forwardErrorsToLogs: true,
    sessionSampleRate: 100
  })

  if (__DEV__) {
    try {
      import('eruda').then(eruda => {
        eruda.default.init({
          useShadowDom: false,
          tool: ['console', 'elements', 'network', 'resources', 'info']
        })
        eruda.default.position({ x: 20, y: -200 })
      })
    } catch (e) {
      console.error(e)
    }
  }

  const app = createApp(App)
  app.use(i18n)
  app.use(pinia)
  app.use(router)
  app.use(VueQueryPlugin)

  return app
}
