/* eslint-disable */
// @ts-nocheck

// code generated by pbf v4.0.1

export function readCreateSession(pbf, end = undefined) {
  return pbf.readFields(
    readCreateSessionField,
    { mapgen_ver: 0, config_ver: 0, used_boosters: [], control_mode: 0 },
    (end = undefined)
  )
}
function readCreateSessionField(tag, obj, pbf) {
  if (tag === 1) obj.mapgen_ver = pbf.readVarint()
  else if (tag === 2) obj.config_ver = pbf.readVarint()
  else if (tag === 3) pbf.readPackedVarint(obj.used_boosters)
  else if (tag === 4) obj.control_mode = pbf.readVarint()
}
export function writeCreateSession(obj, pbf) {
  if (obj.mapgen_ver) pbf.writeVarintField(1, obj.mapgen_ver)
  if (obj.config_ver) pbf.writeVarintField(2, obj.config_ver)
  if (obj.used_boosters) pbf.writePackedVarint(3, obj.used_boosters)
  if (obj.control_mode) pbf.writeVarintField(4, obj.control_mode)
}

export function readSession(pbf, end = undefined) {
  return pbf.readFields(
    readSessionField,
    {
      id: 0,
      outer_seed: [],
      inner_seed: [],
      multiplier: 0,
      highest_score: 0,
      excluded_presets: [],
      total_score_event: 0,
      highest_score_event: 0,
      ton_event_allowed_amount_of_coins: 0,
      ton_event_coin_value: 0,
      trump_event_allowed_amount_of_coins: 0,
      trump_event_coin_value: 0,
      ton_coin_values: [],
      ton_spawn_positions: [],
      used_boosters: [],
      custom_coin_type: 0,
      custom_coin_values: [],
      custom_coin_spawn_positions: [],
      dynamic_coin_type: 0,
      dynamic_coin_values: [],
      dynamic_coin_spawn_positions: [],
      puzzle_coins_type: 0,
      puzzle_coins_values: [],
      puzzle_coins_spawn_positions: []
    },
    (end = undefined)
  )
}
function readSessionField(tag, obj, pbf) {
  if (tag === 1) obj.id = pbf.readVarint()
  else if (tag === 2) obj.outer_seed.push(pbf.readString())
  else if (tag === 3) obj.inner_seed.push(pbf.readString())
  else if (tag === 4) obj.multiplier = pbf.readVarint()
  else if (tag === 5) obj.highest_score = pbf.readVarint()
  else if (tag === 6) pbf.readPackedVarint(obj.excluded_presets)
  else if (tag === 100) obj.total_score_event = pbf.readVarint(true)
  else if (tag === 101) obj.highest_score_event = pbf.readVarint(true)
  else if (tag === 102) obj.ton_event_allowed_amount_of_coins = pbf.readVarint(true)
  else if (tag === 103) obj.ton_event_coin_value = pbf.readVarint(true)
  else if (tag === 104) obj.trump_event_allowed_amount_of_coins = pbf.readVarint(true)
  else if (tag === 105) obj.trump_event_coin_value = pbf.readVarint(true)
  else if (tag === 300) pbf.readPackedVarint(obj.ton_coin_values)
  else if (tag === 301) pbf.readPackedVarint(obj.ton_spawn_positions)
  else if (tag === 400) pbf.readPackedVarint(obj.used_boosters)
  else if (tag === 500) obj.custom_coin_type = pbf.readVarint()
  else if (tag === 501) pbf.readPackedVarint(obj.custom_coin_values)
  else if (tag === 502) pbf.readPackedVarint(obj.custom_coin_spawn_positions)
  else if (tag === 503) obj.dynamic_coin_type = pbf.readVarint()
  else if (tag === 504) pbf.readPackedVarint(obj.dynamic_coin_values)
  else if (tag === 505) pbf.readPackedVarint(obj.dynamic_coin_spawn_positions)
  else if (tag === 506) obj.puzzle_coins_type = pbf.readVarint()
  else if (tag === 507) pbf.readPackedVarint(obj.puzzle_coins_values)
  else if (tag === 508) pbf.readPackedVarint(obj.puzzle_coins_spawn_positions)
}
export function writeSession(obj, pbf) {
  if (obj.id) pbf.writeVarintField(1, obj.id)
  if (obj.outer_seed) for (const item of obj.outer_seed) pbf.writeStringField(2, item)
  if (obj.inner_seed) for (const item of obj.inner_seed) pbf.writeStringField(3, item)
  if (obj.multiplier) pbf.writeVarintField(4, obj.multiplier)
  if (obj.highest_score) pbf.writeVarintField(5, obj.highest_score)
  if (obj.excluded_presets) pbf.writePackedVarint(6, obj.excluded_presets)
  if (obj.total_score_event) pbf.writeVarintField(100, obj.total_score_event)
  if (obj.highest_score_event) pbf.writeVarintField(101, obj.highest_score_event)
  if (obj.ton_event_allowed_amount_of_coins)
    pbf.writeVarintField(102, obj.ton_event_allowed_amount_of_coins)
  if (obj.ton_event_coin_value) pbf.writeVarintField(103, obj.ton_event_coin_value)
  if (obj.trump_event_allowed_amount_of_coins)
    pbf.writeVarintField(104, obj.trump_event_allowed_amount_of_coins)
  if (obj.trump_event_coin_value) pbf.writeVarintField(105, obj.trump_event_coin_value)
  if (obj.ton_coin_values) pbf.writePackedVarint(300, obj.ton_coin_values)
  if (obj.ton_spawn_positions) pbf.writePackedVarint(301, obj.ton_spawn_positions)
  if (obj.used_boosters) pbf.writePackedVarint(400, obj.used_boosters)
  if (obj.custom_coin_type) pbf.writeVarintField(500, obj.custom_coin_type)
  if (obj.custom_coin_values) pbf.writePackedVarint(501, obj.custom_coin_values)
  if (obj.custom_coin_spawn_positions) pbf.writePackedVarint(502, obj.custom_coin_spawn_positions)
  if (obj.dynamic_coin_type) pbf.writeVarintField(503, obj.dynamic_coin_type)
  if (obj.dynamic_coin_values) pbf.writePackedVarint(504, obj.dynamic_coin_values)
  if (obj.dynamic_coin_spawn_positions) pbf.writePackedVarint(505, obj.dynamic_coin_spawn_positions)
  if (obj.puzzle_coins_type) pbf.writeVarintField(506, obj.puzzle_coins_type)
  if (obj.puzzle_coins_values) pbf.writePackedVarint(507, obj.puzzle_coins_values)
  if (obj.puzzle_coins_spawn_positions) pbf.writePackedVarint(508, obj.puzzle_coins_spawn_positions)
}

export function readSessionUpdate(pbf, end = undefined) {
  return pbf.readFields(readSessionUpdateField, { id: 0, chunks: [], score: 0 }, (end = undefined))
}
function readSessionUpdateField(tag, obj, pbf) {
  if (tag === 1) obj.id = pbf.readVarint()
  else if (tag === 2) obj.chunks.push(readChunkUpdate(pbf, pbf.readVarint() + pbf.pos))
  else if (tag === 10) obj.score = pbf.readVarint()
}
export function writeSessionUpdate(obj, pbf) {
  if (obj.id) pbf.writeVarintField(1, obj.id)
  if (obj.chunks) for (const item of obj.chunks) pbf.writeMessage(2, writeChunkUpdate, item)
  if (obj.score) pbf.writeVarintField(10, obj.score)
}

export function readChunkUpdate(pbf, end = undefined) {
  return pbf.readFields(readChunkUpdateField, { index: 0, entities: [] }, (end = undefined))
}
function readChunkUpdateField(tag, obj, pbf) {
  if (tag === 1) obj.index = pbf.readVarint()
  else if (tag === 2) obj.entities.push(readEntityUpdate(pbf, pbf.readVarint() + pbf.pos))
}
export function writeChunkUpdate(obj, pbf) {
  if (obj.index) pbf.writeVarintField(1, obj.index)
  if (obj.entities) for (const item of obj.entities) pbf.writeMessage(2, writeEntityUpdate, item)
}

export function readEntityUpdate(pbf, end = undefined) {
  return pbf.readFields(
    readEntityUpdateField,
    { update_type: 0, entity_index: 0, position_x: 0, position_y: 0, time: 0 },
    (end = undefined)
  )
}
function readEntityUpdateField(tag, obj, pbf) {
  if (tag === 1) obj.update_type = pbf.readVarint()
  else if (tag === 2) obj.entity_index = pbf.readVarint()
  else if (tag === 3) obj.position_x = pbf.readFloat()
  else if (tag === 4) obj.position_y = pbf.readFloat()
  else if (tag === 5) obj.time = pbf.readVarint()
}
export function writeEntityUpdate(obj, pbf) {
  if (obj.update_type) pbf.writeVarintField(1, obj.update_type)
  if (obj.entity_index) pbf.writeVarintField(2, obj.entity_index)
  if (obj.position_x) pbf.writeFloatField(3, obj.position_x)
  if (obj.position_y) pbf.writeFloatField(4, obj.position_y)
  if (obj.time) pbf.writeVarintField(5, obj.time)
}
