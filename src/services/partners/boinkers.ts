function getBoinkersUrl(userTelegramId: string, type: string) {
  return `https://boom.astronomica.io/api/partner/check?partnerName=UnijumpPartner&userTelegramId=${userTelegramId}&apiKey=${import.meta.env.VITE_BOINKERS_API_KEY}&${type}=1`
}

export async function checkBoinkers10Spins(userTelegramId: string): Promise<boolean> {
  const url = getBoinkersUrl(userTelegramId, 'check10Spins')
  const response = await fetch(url)
  if (response.ok) {
    const data = await response.json()
    return data.isSpinnedSlutMachine10Times
  }
  return false
}

export async function checkBoinkers3MoonedBoinkers(userTelegramId: string): Promise<boolean> {
  const url = getBoinkersUrl(userTelegramId, 'check3MoonedBoinkers')
  const response = await fetch(url)
  if (response.ok) {
    const data = await response.json()
    return data.completed3Boinkers
  }
  return false
}
