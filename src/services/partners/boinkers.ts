function getBoinkersUrl(userTelegramId: string, type: string) {
  return `https://boom.astronomica.io/api/partner/check?partnerName=UnijumpPartner&userTelegramId=${userTelegramId}&apiKey=46d4174d1514235c903b6ea82c5fcc3a92dbe2d1567db124608dd65b5d209718&${type}=1`
}

export async function checkBoinkers10Spins(userTelegramId: string): Promise<boolean> {
  const url = getBoinkersUrl(userTelegramId, 'check10Spins')
  const response = await fetch(url)
  if (response.ok) {
    const data = await response.json()
    return data.isSpinnedSlutMachine10Times
  }
  return false
}

export async function checkBoinkers3MoonedBoinkers(userTelegramId: string): Promise<boolean> {
  const url = getBoinkersUrl(userTelegramId, 'check3MoonedBoinkers')
  const response = await fetch(url)
  if (response.ok) {
    const data = await response.json()
    return data.completed3Boinkers
  }
  return false
}
