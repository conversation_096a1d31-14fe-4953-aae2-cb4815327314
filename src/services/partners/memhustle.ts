function getUrl(userTelegramId: string, type: string) {
  return `https://memhustle-backend-bff-http.memhustle.io/partner/quests/progress?type=${type}&userId=${userTelegramId}`
}

function getAuthHeader() {
  return {
    Authorization: `Bearer ${import.meta.env.VITE_MEMHUSTLE_TOKEN}`
  }
}

export async function checkMemhustlePurchaseFort(userTelegramId: string): Promise<boolean> {
  const url = getUrl(userTelegramId, 'PURCHASE_FORT')
  const response = await fetch(url, { headers: getAuthHeader() })
  if (response.ok) {
    const data = await response.json()
    return data.isCompleted
  }
  return false
}

export async function checkMemhustleTutorialComplete(userTelegramId: string): Promise<boolean> {
  const url = getUrl(userTelegramId, 'COMPLETE_TUTORIAL_REACH_LEVEL')
  const response = await fetch(url, { headers: getAuth<PERSON>eader() })
  if (response.ok) {
    const data = await response.json()
    return data.isCompleted
  }
  return false
}
