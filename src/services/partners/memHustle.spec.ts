import { describe, expect, it, vi, beforeEach } from 'vitest'
import { checkMemHustlePurchaseFort, checkMemHustleCompleteTutorial } from './memHustle'

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('memHustle service', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  describe('checkMemHustlePurchaseFort', () => {
    it('should return true when PURCHASE_FORT quest is completed', async () => {
      const mockResponse = {
        progress: [
          {
            type: 'PURCHASE_FORT',
            currentAmount: 1,
            condition: 1,
            isCompleted: true
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustlePurchaseFort('123456789')

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'https://memhustle-backend-stage-bff-http.memhustle.net/partner/quests/progress?type=PURCHASE_FORT&userId=123456789',
        {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXJ0bmVyTmFtZSI6IlVuaSBKdW1wIiwiaWQiOiJlNzQ2NDcxMS0yMWUxLTQxNjUtOTVmYy0yYzc3YmI0YzVhMTAiLCJpYXQiOjE3NTE1NDk0MTZ9.M0tzZ5PdBsYkqqI9NrX8DQuH-VIOdxj6D3-hq87g7FY'
          }
        }
      )
    })

    it('should return false when PURCHASE_FORT quest is not completed', async () => {
      const mockResponse = {
        progress: [
          {
            type: 'PURCHASE_FORT',
            currentAmount: 0,
            condition: 1,
            isCompleted: false
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustlePurchaseFort('123456789')

      expect(result).toBe(false)
    })

    it('should return false when quest is not found in response', async () => {
      const mockResponse = {
        progress: [
          {
            type: 'OTHER_QUEST',
            currentAmount: 1,
            isCompleted: true
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustlePurchaseFort('123456789')

      expect(result).toBe(false)
    })

    it('should return false when API request fails', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500
      })

      const result = await checkMemHustlePurchaseFort('123456789')

      expect(result).toBe(false)
    })

    it('should return false when network error occurs', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await checkMemHustlePurchaseFort('123456789')

      expect(result).toBe(false)
    })
  })

  describe('checkMemHustleCompleteTutorial', () => {
    it('should return true when COMPLETE_TUTORIAL_REACH_LEVEL quest is completed', async () => {
      const mockResponse = {
        progress: [
          {
            type: 'COMPLETE_TUTORIAL_REACH_LEVEL',
            currentAmount: 1,
            isCompleted: true
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustleCompleteTutorial('123456789')

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'https://memhustle-backend-stage-bff-http.memhustle.net/partner/quests/progress?type=COMPLETE_TUTORIAL_REACH_LEVEL&userId=123456789',
        {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXJ0bmVyTmFtZSI6IlVuaSBKdW1wIiwiaWQiOiJlNzQ2NDcxMS0yMWUxLTQxNjUtOTVmYy0yYzc3YmI0YzVhMTAiLCJpYXQiOjE3NTE1NDk0MTZ9.M0tzZ5PdBsYkqqI9NrX8DQuH-VIOdxj6D3-hq87g7FY'
          }
        }
      )
    })

    it('should return false when COMPLETE_TUTORIAL_REACH_LEVEL quest is not completed', async () => {
      const mockResponse = {
        progress: [
          {
            type: 'COMPLETE_TUTORIAL_REACH_LEVEL',
            currentAmount: 0,
            isCompleted: false
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustleCompleteTutorial('123456789')

      expect(result).toBe(false)
    })

    it('should handle empty response array', async () => {
      const mockResponse = { progress: [] }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await checkMemHustleCompleteTutorial('123456789')

      expect(result).toBe(false)
    })
  })
})
