const MEMHUSTLE_BASE_URL = 'https://memhustle-backend-stage-bff-http.memhustle.net'
const MEMHUSTLE_AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXJ0bmVyTmFtZSI6IlVuaSBKdW1wIiwiaWQiOiJlNzQ2NDcxMS0yMWUxLTQxNjUtOTVmYy0yYzc3YmI0YzVhMTAiLCJpYXQiOjE3NTE1NDk0MTZ9.M0tzZ5PdBsYkqqI9NrX8DQuH-VIOdxj6D3-hq87g7FY'

interface MemHustleQuestResponse {
  type: string
  currentAmount: number
  condition?: number
  isCompleted: boolean
}

interface MemHustleApiResponse {
  progress: MemHustleQuestResponse[]
}

function getMemHustleUrl(userTelegramId: string, type: string) {
  return `${MEMHUSTLE_BASE_URL}/partner/quests/progress?type=${type}&userId=${userTelegramId}`
}

async function fetchMemHustleQuest(userTelegramId: string, questType: string): Promise<boolean> {
  const url = getMemHustleUrl(userTelegramId, questType)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${MEMHUSTLE_AUTH_TOKEN}`
      }
    })
    
    if (response.ok) {
      const data: MemHustleQuestResponse[] = await response.json()
      
      // Find the quest with matching type
      const quest = data.find(q => q.type === questType)
      
      return quest?.isCompleted || false
    }
    
    return false
  } catch (error) {
    console.error(`Error fetching memHustle quest ${questType}:`, error)
    return false
  }
}

export async function checkMemHustlePurchaseFort(userTelegramId: string): Promise<boolean> {
  return fetchMemHustleQuest(userTelegramId, 'PURCHASE_FORT')
}

export async function checkMemHustleCompleteTutorial(userTelegramId: string): Promise<boolean> {
  return fetchMemHustleQuest(userTelegramId, 'COMPLETE_TUTORIAL_REACH_LEVEL')
}
