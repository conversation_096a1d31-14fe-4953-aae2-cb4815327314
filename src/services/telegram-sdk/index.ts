import { isMobile } from '@/utils/device'
import {
  bindViewportCssVars,
  enableClosingConfirmation,
  expandViewport,
  init,
  isBackButtonSupported,
  isFullscreen,
  mountBackButton,
  mountClosingBehavior,
  mountMiniApp,
  mountViewport,
  postEvent,
  requestFullscreen
} from '@telegram-apps/sdk'

export async function initSDK() {
  init()
  isBackButtonSupported() && mountBackButton()
  mountClosingBehavior()
  mountMiniApp()
  await mountViewport()
  expandViewport()
  bindViewportCssVars()
  if (!isFullscreen() && requestFullscreen.isAvailable() && isMobile()) {
    await requestFullscreen()
  }

  enableClosingConfirmation()

  /** "component" not working, used post event instead */
  // swipeBehavior.isSupported()
  //   && (swipeBehavior.mount(), true)
  //   && swipeBehavior.disableVertical()
  // @ts-ignore
  postEvent('web_app_toggle_orientation_lock', { locked: true })
  postEvent('web_app_setup_swipe_behavior', { allow_vertical_swipe: false })
}
