import { useReferralRewardErrorHandling } from '@/composables/useErrorHandling'
import { getReferralsListKey } from '@/services/client/useReferralsList'
import {
  claimReferralRewardMutation,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { leaveOnlyOnePageOfInfiniteQuery } from './utils'

export function useClaimReferralReward(onSuccess?: (ticketsClaimed: number) => void) {
  const queryClient = useQueryClient()

  const { data, mutateAsync } = useMutation({
    ...claimReferralRewardMutation(),
    onError: (error: { error: string }) => {
      useReferralRewardErrorHandling(error.error)
    },
    onSuccess: data => {
      onSuccess && onSuccess(data.ticketsClaimed)
      // should invalidate for tickets farming state update
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      // refetch only first page of referrals list
      queryClient.setQueryData(getReferralsListKey(), leaveOnlyOnePageOfInfiniteQuery)
      queryClient.invalidateQueries({ queryKey: getReferralsListKey() })
    }
  })

  const claimReferralReward = () => {
    // hey-api generation for POST requires body
    return mutateAsync({
      body: {}
    })
  }

  return {
    refLink: data,
    claimReferralReward
  }
}
