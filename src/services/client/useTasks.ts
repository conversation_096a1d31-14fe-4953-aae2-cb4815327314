import {
  getDailyTasksOptions,
  getGlobalTasksOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useGlobalTasks() {
  const { data, isLoading, refetch } = useQuery({
    ...getGlobalTasksOptions()
  })

  const globalTasks = computed(() => {
    return data.value?.tasks ?? []
  })

  return {
    isLoading,
    globalTasks,
    refetchTasks: refetch
  }
}

export function useDailyTasks() {
  const { data, isLoading, refetch } = useQuery({
    ...getDailyTasksOptions()
  })

  const currentDailyTasks = computed(() => {
    return data.value?.tasks ?? []
  })

  const currentOnboardingTasks = computed(() => {
    return data.value?.beginningTasks ?? []
  })

  const isLootboxAvailable = computed(() => {
    return data.value?.lootboxAvailable
  })

  const lootboxProgress = computed(() => {
    return data.value?.lootboxProgress ?? 0
  })

  return {
    isLoading,
    currentDailyTasks,
    currentOnboardingTasks,
    isLootboxAvailable,
    lootboxProgress,
    refetchTasks: refetch
  }
}
