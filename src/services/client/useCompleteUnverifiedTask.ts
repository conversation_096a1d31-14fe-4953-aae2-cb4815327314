import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  completeGlobalTaskMutation,
  getGlobalTasksQueryKey,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerStateResponse, PlayerTasksResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useCompleteUnverifiedTask() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...completeGlobalTaskMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (_, variables) => {
      const taskId = variables.body.taskId
      queryClient.setQueryData(getGlobalTasksQueryKey(), (oldData: PlayerTasksResponse) => {
        if (!oldData) return oldData
        const newTasks: PlayerTasksResponse['tasks'] = oldData.tasks.slice().map(task => {
          if (task.taskId === taskId) {
            return {
              ...task,
              completed: true
            }
          }
          return task
        })
        return {
          tasks: newTasks
        }
      })
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          hasUnclaimedTasks: true
        }
        return newData
      })
    }
  })

  const completeUnverifiedTask = (id: number) => {
    return mutateAsync({
      body: {
        taskId: id
      }
    })
  }

  return { completeUnverifiedTask }
}
