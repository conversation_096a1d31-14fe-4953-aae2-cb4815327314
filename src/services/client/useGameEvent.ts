import {
  getBattleEventLeadersOptions,
  getBattleEventUserInfoOptions,
  getClanEventLeadersOptions,
  getClanEventUserInfoOptions,
  getCustomCoinEventUserInfoOptions,
  getCustomCoinsLeadersOptions,
  getHotrecordLeadersOptions,
  getHotrecordUserInfoOptions,
  getOnepercentLeadersOptions,
  getOnepercentUserInfoOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed, watch } from 'vue'

export function useOnepercentLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getOnepercentLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useOnepercentUserInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getOnepercentUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}

export function useHotrecordLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getHotrecordLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useHotrecordUserInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getHotrecordUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}

export function useCustomCoinLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getCustomCoinsLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useCustomCoinUserInfo(enabled = true) {
  const { data, isLoading, isFetching, refetch } = useQuery({
    ...getCustomCoinEventUserInfoOptions(),
    enabled
  })

  return {
    isLoading,
    isFetching,
    userInfo: data,
    refetchUserInfo: refetch
  }
}

export function useClanEventLeaders(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getClanEventLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useUserClanEventInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getClanEventUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}

export function useBattleEventUserInfo(enabled = true) {
  const { data, isLoading, isFetching, refetch } = useQuery({
    ...getBattleEventUserInfoOptions(),
    enabled
  })

  return {
    isLoading,
    isFetching,
    userInfo: data,
    refetchUserInfo: refetch
  }
}

export function useBattleEventLeaders(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getBattleEventLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  const score = computed(() => {
    const teamScore = data.value?.teamScore ?? 0
    const enemyTeamScore = data.value?.enemyTeamScore ?? 0
    const totalScore = teamScore + enemyTeamScore
    const teamPercent = Math.round((teamScore / totalScore) * 100)
    const enemyTeamPercent = 100 - teamPercent

    return {
      teamScore,
      enemyTeamScore,
      totalScore,
      teamPercent: isNaN(teamPercent) ? 50 : teamPercent,
      enemyTeamPercent: isNaN(enemyTeamPercent) ? 50 : enemyTeamPercent
    }
  })

  const teams = computed(() => {
    return {
      enemyTeamSkin: data.value?.enemyTeamSkin ?? 0,
      teamSkin: data.value?.teamSkin ?? 0
    }
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard,
    score,
    teams
  }
}
