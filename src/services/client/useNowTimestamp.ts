import { getUtcOptions } from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'

// Shared state across all instances of useNowTimestamp
let sharedRefetchPromise: Promise<any> | null = null

export function useNowTimestamp() {
  const { data, isPending, isStale, refetch } = useQuery({
    ...getUtcOptions(),
    staleTime: 3000, // 3 seconds
    enabled: true
  })

  const getNow = async () => {
    if (!isStale.value && data.value) {
      return data.value.utc
    }

    // If there's already a refetch in progress, return that promise
    if (sharedRefetchPromise) {
      return sharedRefetchPromise.then(response => response.data!.utc)
    }

    // Otherwise, start a new refetch and store the promise
    sharedRefetchPromise = refetch()
    return sharedRefetchPromise
      .then(response => response.data!.utc)
      .finally(() => {
        // Clear the promise when done
        sharedRefetchPromise = null
      })
  }

  return {
    now: data,
    getNow,
    isPending
  }
}
