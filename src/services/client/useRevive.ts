import {
  useDefault<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  usePurchaseErrorHandling,
  useReviveErrorHandling
} from '@/composables/useErrorHandling'
import {
  getPlayerStateQueryKey,
  getReviveInfoMutation,
  purchaseReviveMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { isExternalCurrencyType } from '@/types'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import type { PlayerStateResponse, RevivalPrice } from '../openapi'
import { updatePlayerStateDataProp } from './utils'

export function useGetRevive() {
  const { mutateAsync, isPending } = useMutation({
    ...getReviveInfoMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      useReviveErrorHandling(error.error)
    },
    throwOnError: true,
    retry: (failureCount, error: { error: string }) => {
      if (error?.error === 'SESSION_NOT_FOUND' || failureCount >= 3) {
        return false
      }
      return true
    },
    retryDelay: 1000
  })

  const getReviveInfo = (sessionId: number) => {
    return mutateAsync({
      body: { sessionId }
    })
  }

  return {
    isPending,
    getReviveInfo
  }
}

export function usePurchaseRevive() {
  const queryClient = useQueryClient()
  let revivePrice: RevivalPrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseReviveMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
      useReviveErrorHandling(error.error)
    },
    onSuccess: () => {
      revivePrice
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData || revivePrice === null || isExternalCurrencyType(revivePrice.type))
          return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          [revivePrice.type]: updatePlayerStateDataProp(oldData.magicHorns, -revivePrice.price)
        }
        return newData
      })
    }
  })

  const purchaseRevive = (sessionId: number, score: number, price: RevivalPrice) => {
    revivePrice = price
    return mutateAsync({
      body: {
        currentScore: score,
        sessionId: sessionId,
        currency: price.type
      }
    })
  }

  return { purchaseRevive, isPending }
}
