import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { useQueryClient } from '@tanstack/vue-query'
import { computed } from 'vue'
import type { PlayerStateResponse } from '../openapi'
import { getPlayerStateQueryKey } from '../openapi/@tanstack/vue-query.gen'
import { usePlayerState } from './usePlayerState'

export function useLives() {
  const queryClient = useQueryClient()
  const { playerState } = usePlayerState()
  const { getNow } = useNowTimestamp()

  const lastLifeRestoreAt = computed(() => {
    return playerState.value?.livesRevive ?? undefined
  })

  const lives = computed(() => playerState.value?.lives ?? 0)
  const maxLives = computed(() => playerState.value?.livesMax ?? 0)
  const lifeRestoreTime = computed(() => playerState.value?.liveReviveDuration ?? 0)
  const livesUnlimitedUntil = computed(() => playerState.value?.livesUnlimitedUntil ?? 0)

  const consumeLife = async () => {
    const now = await getNow()
    queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
      if (!oldData) return oldData
      const newLives = oldData.lives! - 1
      return {
        ...oldData,
        lives: newLives,
        livesRevive: oldData.livesRevive ?? (newLives < maxLives.value ? now : undefined)
      }
    })
  }

  const restoreLife = async () => {
    const now = await getNow()
    queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
      if (!oldData) return oldData
      const newLives = Math.min(maxLives.value, (oldData.lives ?? 0) + 1)
      return {
        ...oldData,
        lives: newLives,
        livesRevive: newLives < maxLives.value ? now : undefined
      }
    })
  }

  const consumeUnlimitedLife = async () => {
    queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
      if (!oldData) return oldData
      return {
        ...oldData,
        livesUnlimitedUntil: 0
      }
    })
  }

  return {
    lastLifeRestoreAt,
    lives,
    maxLives,
    lifeRestoreTime,
    livesUnlimitedUntil,
    consumeUnlimitedLife,
    consumeLife,
    restoreLife
  }
}
