import { useUser } from '@/composables/useUser'
import { DEFAULT_SKIN_ID } from '@/constants/skins'
import { useQuery } from '@tanstack/vue-query'
import { computed, type MaybeRef } from 'vue'
import { getUserProfile, type UserProfileResponse } from '../openapi'
import type { RequiredNotNull } from '../types'

export function usePlayerProfile(id: MaybeRef<number>, enabled: MaybeRef<boolean> = true) {
  const { getUser } = useUser()
  const currentUserId = getUser().getId()
  const { data, isLoading } = useQuery({
    queryFn: async ({ queryKey }) => {
      const { data } = await getUserProfile({
        query: {
          id: queryKey[1]
        },
        throwOnError: true
      })
      return data
    },
    queryKey: ['getUserProfile', id] as const,
    staleTime: ({ queryKey }) => {
      return queryKey[1] === currentUserId ? 0 : 1000 * 60 * 1 // 1 minutes
    },
    enabled
  })

  const playerProfile = computed<RequiredNotNull<UserProfileResponse>>(() => ({
    id: data.value?.id ?? -1,
    firstName: data.value?.firstName ?? '',
    lastName: data.value?.lastName ?? '',
    tickets: data.value?.tickets ?? 0,
    multiplier: data.value?.multiplier ?? 1,
    gamesPlayed: data.value?.gamesPlayed ?? 0,
    highestScore: data.value?.highestScore ?? 0,
    totalScore: data.value?.totalScore ?? 0,
    leagueLevel: data.value?.leagueLevel ?? 1,
    skin: data.value?.skin ?? DEFAULT_SKIN_ID,
    clanId: data.value?.clanId ?? 0,
    clanName: data.value?.clanName ?? ''
  }))

  return {
    isLoading: isLoading,
    playerProfile
  }
}
