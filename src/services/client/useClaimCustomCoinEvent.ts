import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  convertCustomCoinsMutation,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useClaimCustomCoinEvent() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...convertCustomCoinsMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const claimEventReward = () => {
    return mutateAsync({
      body: {}
    })
  }

  return { claimEventReward }
}
