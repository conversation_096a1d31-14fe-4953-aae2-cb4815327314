import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import { checkTonInvoiceMutation } from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation } from '@tanstack/vue-query'

export function useCheckTonInvoice() {
  const { mutateAsync } = useMutation({
    ...checkTonInvoiceMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const checkTonInvoice = (invoice: string) => {
    return mutateAsync({
      body: { invoice }
    }).then(data => {
      return data.isPaid
    })
  }

  return { checkTonInvoice }
}
