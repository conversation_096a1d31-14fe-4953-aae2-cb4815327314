import {
  getLeaguesLeadersOptions,
  getLeaguesUserInfoOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed, type Ref } from 'vue'

export function useLeagueLeaders(id: number, enabled: Ref<boolean>) {
  const { data, isLoading } = useQuery({
    ...getLeaguesLeadersOptions({
      path: { league_id: id }
    }),
    retry: false,
    staleTime: Infinity,
    gcTime: 1000, // 1 second
    enabled
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useLeagueUserInfo(enabled: Ref<boolean>) {
  const { data, isLoading, isFetching } = useQuery({
    ...getLeaguesUserInfoOptions(),
    staleTime: Infinity,
    gcTime: 1000, // 1 second
    enabled
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}
