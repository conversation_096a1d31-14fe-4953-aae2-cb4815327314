import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import { claimDailyRewardMutation } from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation } from '@tanstack/vue-query'
// import type { PlayerStateResponse } from '../openapi'
// import { updatePlayerStateDataProp } from './utils'

export function useClaimDailyReward() {
  // const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...claimDailyRewardMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
    /*   //TODO Need to be refactored
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }*/
    /*  onSuccess: data => {
      const reward = data.reward
      if (!reward || reward.type === 'timeBoundMagneticField' || reward.type === 'unlimitedLives') {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      } else {
        queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
          if (!oldData) return oldData
          const type = reward.type as 'hard' | 'soft' | 'lives' | 'ton'
          const newData: PlayerStateResponse = {
            ...oldData,
            [reward.type]: updatePlayerStateDataProp(oldData[type], reward.amount)
          }
          return newData
        })
      }
    }*/
  })

  const claimDailyReward = () => {
    return mutateAsync({
      body: {}
    })
  }

  return { claimDailyReward, isPending }
}
