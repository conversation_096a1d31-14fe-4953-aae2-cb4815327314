import { useDefaultErrorHand<PERSON> } from '@/composables/useErrorHandling'
import { addToPlayerState } from '@/services/client/usePlayerState'
import {
  freeSpinWheelMutation,
  getFortuneWheelConfigOptions,
  getPlayerStateQueryKey,
  spinWheelMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { NumeralRewardType } from '@/types'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useWheelSpinConfig() {
  const { data, isLoading } = useQuery({
    ...getFortuneWheelConfigOptions()
  })

  const sectors = computed(() => data.value?.sectors ?? [])

  return {
    sectors,
    isLoading
  }
}

export function useGetFreeWheelSpin() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...freeSpinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const getFreeWheelSpin = async () => {
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getFreeWheelSpin, isPending }
}

export function useGetWheelSpin(rewardDelay: number = 0) {
  const { updatePlayerState } = addToPlayerState()

  const { mutateAsync, isPending } = useMutation({
    ...spinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: data => {
      updatePlayerState('wheelSpins', -1)
      setTimeout(() => {
        const reward = data.reward
        updatePlayerState(reward.type as NumeralRewardType, reward.value)
      }, rewardDelay)
    }
  })

  const getWheelSpin = async () => {
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getWheelSpin, isPending }
}
