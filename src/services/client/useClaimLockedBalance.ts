import { useDefaultErrorHandler } from '@/composables/useErrorHandling.ts'
import { claimLockedBalanceMutation } from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation } from '@tanstack/vue-query'

export function useClaimLockedBalance() {
  const { mutateAsync, isPending } = useMutation({
    ...claimLockedBalanceMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {}
  })

  const claimLockedBalance = () => {
    return mutateAsync({}).then(data => {
      return data.ok
    })
  }

  return { claimLockedBalance, isPending }
}
