import { useDefaultErrorHand<PERSON> } from '@/composables/useErrorHandling'
import {
  getPlayerStateQueryKey,
  removePlayerFlagMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerFlag, PlayerStateResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useRemovePlayerFlag() {
  const { mutateAsync } = useMutation({
    ...removePlayerFlagMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const removeFlag = (flag: PlayerFlag) => {
    return mutateAsync({
      body: {
        flag
      }
    })
  }

  return { removeFlag }
}

export function usePlayerReceivedEventReward() {
  const queryClient = useQueryClient()
  const { removeFlag } = useRemovePlayerFlag()
  const flags: Record<PlayerFlag, PlayerFlag> = {
    onepercent_reward_received: 'onepercent_reward_received',
    hotrecord_reward_received: 'hotrecord_reward_received',
    custom_coin_reward_received: 'custom_coin_reward_received',
    clan_event_reward_received: 'clan_event_reward_received',
    battle_event_reward_received: 'battle_event_reward_received'
  }

  const onPlayerRecievedOnepercentReward = () => {
    removeFlag(flags.onepercent_reward_received).then(() => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          onepercentEventReward: null
        }
        return newData
      })
    })
  }

  const onPlayerRecievedHotrecordReward = () => {
    removeFlag(flags.hotrecord_reward_received).then(() => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          hotrecordEventReward: null
        }
        return newData
      })
    })
  }

  const onPlayerRecievedCustomCoinReward = () => {
    removeFlag(flags.custom_coin_reward_received).then(() => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          customCoinEventReward: null
        }
        return newData
      })
    })
  }

  const onPlayerRecievedClanEventReward = () => {
    removeFlag(flags.clan_event_reward_received).then(() => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          clanEventReward: null
        }
        return newData
      })
    })
  }

  const onPlayerRecievedCommunityBattleReward = () => {
    removeFlag(flags.battle_event_reward_received).then(() => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          battleEventReward: null
        }
        return newData
      })
    })
  }

  return {
    onPlayerRecievedOnepercentReward,
    onPlayerRecievedHotrecordReward,
    onPlayerRecievedCustomCoinReward,
    onPlayerRecievedClanEventReward,
    onPlayerRecievedCommunityBattleReward
  }
}
