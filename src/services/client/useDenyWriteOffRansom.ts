import { useDefaultErrorHandler } from '@/composables/useErrorHandling.ts'
import { denyWriteOffRansomMutation } from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation } from '@tanstack/vue-query'

export function useDenyWriteOffRansom() {
  const { mutateAsync, isPending } = useMutation({
    ...denyWriteOffRansomMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {}
  })

  const denyWriteOffRansom = () => {
    return mutateAsync({}).then(data => {
      return data.ok
    })
  }

  return { denyWriteOffRansom, isPending }
}
