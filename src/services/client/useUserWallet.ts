import { getPlayerProfileOptions } from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useUserWallet() {
  const { data, isLoading } = useQuery({
    ...getPlayerProfileOptions(),
    staleTime: 1000 * 60 * 5
  })

  const isWalletConnectedOnServer = computed(() => !!data?.value?.wallet)
  const isTransactionMade = computed(() => !!data?.value?.transactionMade)
  const serverWalletAddress = computed(() => data?.value?.wallet ?? '')

  return {
    isLoading,
    playerProfile: data,
    serverWalletAddress,
    isWalletConnectedOnServer,
    isTransactionMade
  }
}
