import type { InfiniteData } from '@tanstack/vue-query'

export function leaveOnlyOnePageOfInfiniteQuery<T = any>(
  data?: InfiniteData<T>
): InfiniteData<T> | undefined {
  if (!data) return data
  return {
    pages: data.pages.slice(0, 1),
    pageParams: data.pageParams.slice(0, 1)
  }
}

export function updatePlayerStateDataProp(prev?: number | null, add?: number | null) {
  return (prev ?? 0) + (add ?? 0)
}
