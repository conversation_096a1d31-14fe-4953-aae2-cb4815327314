import { useDefaultErrorHandler } from '@/composables/useErrorHandling.ts'
import type { LootBoxType } from '@/services/openapi'
import {
  getFreeLootboxMutation,
  getPlayerStateQueryKey,
  openLootboxMutation
} from '@/services/openapi/@tanstack/vue-query.gen.ts'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useGetFreeLootbox() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...getFreeLootboxMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const getFreeLootbox = async () => {
    return mutateAsync({})
  }

  return { getFreeLootbox, isPending }
}

export function useOpenLootbox() {
  const { data, mutateAsync, isPending } = useMutation({
    ...openLootboxMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    retry: 3,
    retryDelay: 1000
  })

  const openLootbox = (lootboxType: LootBoxType) => {
    return mutateAsync({
      body: {
        lootboxType: lootboxType
      }
    })
  }

  return {
    data,
    openLootbox,
    isPending
  }
}
