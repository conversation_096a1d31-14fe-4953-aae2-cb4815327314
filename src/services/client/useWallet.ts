import { useDefaultErrorHand<PERSON>, useWalletErrorHandling } from '@/composables/useErrorHandling'
import {
  connectWalletMutation,
  disconnectWalletMutation,
  getPlayerProfileQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerProfileResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useServerWalletConnect() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...connectWalletMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      useWalletErrorHandling(error.error)
    },
    onSuccess: data => {
      queryClient.setQueryData(getPlayerProfileQueryKey(), (oldData: PlayerProfileResponse) => {
        if (!oldData) return
        return {
          ...oldData,
          wallet: data.wallet
        }
      })
    }
  })

  const connectServerWallet = (address: string) => {
    return mutateAsync({ body: { address } })
  }

  return {
    connectServerWallet
  }
}

export function useServerWalletDisonnect() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...disconnectWalletMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.setQueryData(getPlayerProfileQueryKey(), (oldData: PlayerProfileResponse) => {
        if (!oldData) return
        return {
          ...oldData,
          wallet: null
        }
      })
    }
  })

  const disconnectServerWallet = () => {
    return mutateAsync({ body: {} })
  }

  return {
    disconnectServerWallet
  }
}
