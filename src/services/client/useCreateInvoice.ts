import { useDefaultErrorHand<PERSON> } from '@/composables/useErrorHandling'
import { useTonTransaction } from '@/composables/useTonTransaction'
import {
  createInvoiceMutation,
  createReviveInvoiceByStarsMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { type ExternalPrice } from '@/types'
import { useMutation } from '@tanstack/vue-query'
import { invoice } from '@telegram-apps/sdk'

export function useInvoice() {
  const { sendTransaction } = useTonTransaction()
  const { mutateAsync, isPending } = useMutation({
    ...createInvoiceMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const openInvoice = (itemId: number, price: ExternalPrice): Promise<string> => {
    return mutateAsync({
      body: { itemId, currency: price.currency }
    }).then(data => {
      if (!invoice.isSupported()) {
        throw new Error('Invoice is not supported')
      }
      if (price.currency === 'ton') {
        return sendTransaction(price.amount, data.invoice).then(result =>
          result ? 'paid_ton' : 'canceled'
        )
      }
      return invoice.open(data.invoice, 'url')
    })
  }

  return { openInvoice, isPending }
}

export function useReviveInvoice() {
  const { mutateAsync, isPending } = useMutation({
    ...createReviveInvoiceByStarsMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const openReviveInvoice = (sessionId: number) => {
    return mutateAsync({
      body: { sessionId }
    }).then(data => {
      if (!invoice.isSupported()) {
        throw new Error('Invoice is not supported')
      }
      return invoice.open(data.invoice, 'url')
    })
  }

  return { openReviveInvoice, isPending }
}
