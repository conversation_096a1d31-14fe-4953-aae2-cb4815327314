import { useDefaultError<PERSON>and<PERSON>, useFarmingErrorHandling } from '@/composables/useErrorHandling'
import {
  claimFarmingMutation,
  getPlayerStateQueryKey,
  startFarmingMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerStateResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { updatePlayerStateDataProp } from './utils'

export function useStartFarming() {
  const queryClient = useQueryClient()

  const { data, mutateAsync } = useMutation({
    ...startFarmingMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: data => {
      const startedAt = data.startedAt
      const endsAt = data.endsAt
      const changeRate = data.changeRate
      queryClient.setQueryData<PlayerStateResponse>(getPlayerStateQuery<PERSON>ey(), oldData => {
        if (!oldData) return oldData
        return {
          ...oldData,
          farming: {
            startedAt,
            endsAt,
            changeRate,
            farmedTickets: 0
          }
        }
      })
    }
  })

  const startFarming = () => {
    return mutateAsync({
      body: {}
    })
  }

  return {
    farmingData: data,
    startFarming
  }
}

export function useClaimFarming() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...claimFarmingMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
      useFarmingErrorHandling(error.error)
    },
    onSuccess: data => {
      queryClient.setQueryData<PlayerStateResponse>(getPlayerStateQueryKey(), oldData => {
        if (!oldData) return oldData
        return {
          ...oldData,
          farming: null,
          tickets: updatePlayerStateDataProp(oldData.tickets, data.farmedTickets),
          leaguesToClaim: data.leaguesToClaim
        }
      })
    }
  })

  const claimFarming = () => {
    return mutateAsync({
      body: {}
    })
  }

  return {
    claimFarming,
    isPending
  }
}
