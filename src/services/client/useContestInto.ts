import { useQuery } from '@tanstack/vue-query'
import { getContestInfoOptions } from '../openapi/@tanstack/vue-query.gen'

const ERRORS = ['CONTEST_NOT_FOUND', 'CONTEST_NOT_STARTED', 'CONTEST_ENDED']

export function useContestInfo(contestId: number) {
  const { data, isLoading, isFetching, isError } = useQuery({
    ...getContestInfoOptions({ path: { contest_id: contestId } }),
    retry: (failureCount, error) => {
      if (failureCount >= 3) {
        return false
      }

      // @ts-expect-error Message from server stores in .error
      const errorMessage = error?.error
      if (ERRORS.includes(errorMessage)) {
        return false
      }

      return true
    },
    throwOnError: false,
    retryDelay: 1000
  })

  return {
    isLoading,
    isFetching,
    isError,
    contestInfo: data
  }
}
