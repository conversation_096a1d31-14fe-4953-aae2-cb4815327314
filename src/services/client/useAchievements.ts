import {
  getAchievementsListOptions,
  getAchievementsStateOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useAchievementsState() {
  const { data, isLoading } = useQuery({
    ...getAchievementsStateOptions()
  })
  const achievementsList = computed(() => data.value?.achievements ?? [])

  return {
    isLoading,
    achievementsList
  }
}

export function useAchievementsList(enabled = true) {
  const { data, refetch } = useQuery({
    ...getAchievementsListOptions(),
    enabled
  })
  const achievementsList = computed(() => data.value?.achievements ?? [])

  return {
    achievementsList,
    refetch
  }
}
