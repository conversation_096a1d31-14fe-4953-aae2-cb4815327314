import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  claimDailyTasksLootboxMutation,
  claimDailyTasksMutation,
  claimGlobalTaskMutation,
  getDailyTasksQueryKey,
  getGlobalTasksQueryKey,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type {
  BoostersView,
  DailyTask,
  DailyTasksResponse,
  PlayerStateResponse,
  PlayerTasksResponse
} from '@/services/openapi/types.gen'
import { isNumeralRewardType, isObjectRewardType, isStackableRewardType } from '@/types'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { updatePlayerStateDataProp } from './utils'

export function useClaimGlobalTask() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...claimGlobalTaskMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (data, variables) => {
      const taskId = variables.body.taskId
      const { type, value } = data.reward
      queryClient.setQueryData(getGlobalTasksQueryKey(), (oldData: PlayerTasksResponse) => {
        if (!oldData) return oldData
        const newTasks: PlayerTasksResponse['tasks'] = oldData.tasks.slice().map(task => {
          if (task.taskId === taskId) {
            return {
              ...task,
              claimed: true
            }
          }
          return task
        })
        return {
          ...oldData,
          tasks: newTasks
        }
      })

      if (!isNumeralRewardType(type)) {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      } else {
        const hasMoreUnclaimedTasks = queryClient
          .getQueryData<PlayerTasksResponse>(getGlobalTasksQueryKey())
          ?.tasks.some(task => task.completed && !task.claimed)

        queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
          if (!oldData) return oldData
          const newData: PlayerStateResponse = {
            ...oldData,
            hasUnclaimedTasks: hasMoreUnclaimedTasks,
            leaguesToClaim: data.leaguesToClaim
          }

          if (isStackableRewardType(type)) {
            newData.boostersView = {
              ...(newData.boostersView ?? ({} as BoostersView)),
              [type]: updatePlayerStateDataProp(newData.boostersView?.[type], value)
            }
          } else if (isObjectRewardType(type)) {
            newData[type] = {
              ...(newData[type] ?? ({} as any)),
              amount: updatePlayerStateDataProp(newData[type]?.amount, value)
            }
          } else {
            newData[type] = updatePlayerStateDataProp(newData[type], value)
          }
          return newData
        })
      }
    }
  })

  const claimGlobalTask = (id: number) => {
    return mutateAsync({
      body: {
        taskId: id
      }
    })
  }

  return { claimGlobalTask }
}

export function useClaimDailyTask() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...claimDailyTasksMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (data, variables) => {
      const taskId = variables.body.taskId
      const { type, value } = data.reward
      queryClient.setQueryData(getDailyTasksQueryKey(), (oldData: DailyTasksResponse) => {
        if (!oldData) return oldData
        const taskType = oldData.beginningTasks?.length ? 'beginningTasks' : 'tasks'
        const tasksArray = oldData[taskType]
        const lootboxProgress = oldData.lootboxProgress + 1
        const newCurrentTasks: DailyTask[] = tasksArray.slice().map(task => {
          if (task.taskId === taskId) {
            return {
              ...task,
              claimed: true
            }
          }
          return task
        })
        return {
          ...oldData,
          [taskType]: newCurrentTasks,
          lootboxProgress,
          lootboxAvailable: lootboxProgress >= tasksArray.length
        }
      })

      if (!isNumeralRewardType(type)) {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      } else {
        const dailyTasksData = queryClient.getQueryData<DailyTasksResponse>(getDailyTasksQueryKey())
        const hasMoreUnclaimedTasks =
          dailyTasksData &&
          (dailyTasksData.tasks || dailyTasksData.beginningTasks).some(
            task => task.completed && !task.claimed
          )

        queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
          if (!oldData) return oldData
          const newData: PlayerStateResponse = {
            ...oldData,
            hasUnclaimedDailyTasks: hasMoreUnclaimedTasks
          }

          if (isStackableRewardType(type)) {
            newData.boostersView = {
              ...(newData.boostersView ?? ({} as BoostersView)),
              [type]: updatePlayerStateDataProp(newData.boostersView?.[type], value)
            }
          } else if (isObjectRewardType(type)) {
            newData[type] = {
              ...(newData[type] ?? ({} as any)),
              amount: updatePlayerStateDataProp(newData[type]?.amount, value)
            }
          } else {
            newData[type] = updatePlayerStateDataProp(newData[type], value)
          }
          return newData
        })
      }
    }
  })

  const claimDailyTask = (id: number) => {
    return mutateAsync({
      body: {
        taskId: id
      }
    })
  }

  return { claimDailyTask }
}

export function useClaimTaskLootbox() {
  const queryClient = useQueryClient()
  let isOnboarding = true

  const { mutateAsync } = useMutation({
    ...claimDailyTasksLootboxMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      if (isOnboarding) {
        queryClient.invalidateQueries({ queryKey: getDailyTasksQueryKey() })
      } else {
        queryClient.setQueryData(getDailyTasksQueryKey(), (oldData: DailyTasksResponse) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            lootboxAvailable: false
          }
        })
      }
    }
  })

  const claimDailyLootbox = (isOnboardingTask: boolean) => {
    isOnboarding = isOnboardingTask
    return mutateAsync({
      body: {}
    })
  }

  return { claimDailyLootbox }
}
