import { useDefaultError<PERSON><PERSON><PERSON>, useSkinsErrorHandling } from '@/composables/useErrorHandling'
import {
  getAchievementsStateQueryKey,
  getPlayerStateQueryKey,
  getSkinsOptions,
  getSkinsQueryKey,
  purchaseSkinMutation,
  selectSkinMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { isInGamePrice } from '@/types'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed } from 'vue'
import type { GetSkinsResponse, PlayerStateResponse, Price } from '../openapi'
import { updatePlayerStateDataProp } from './utils'

export function useSkinsList(enabled = true) {
  const { data, isLoading } = useQuery({
    ...getSkinsOptions(),
    enabled
  })

  const skinsList = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    skinsList
  }
}

export function usePurchaseSkin(onSuccess?: () => void, onError?: () => void) {
  const queryClient = useQueryClient()
  let multiplierCache = 0
  let priceCache: Price | undefined = undefined

  const { mutateAsync, isPending } = useMutation({
    ...purchaseSkinMutation(),
    onError: (error: { error: string }) => {
      if (onError) {
        onError()
      } else {
        useDefaultErrorHandler(error.error)
        useSkinsErrorHandling(error.error)
      }
    },
    onSuccess: (_, variables) => {
      const skinId = variables.body.skinId
      onSkinPurchased(skinId, multiplierCache, priceCache)
      if (onSuccess) {
        onSuccess()
      }
    }
  })

  const onSkinPurchased = (skinId: number, multiplier: number, price?: Price) => {
    queryClient.setQueryData(getSkinsQueryKey(), (oldData: GetSkinsResponse) => {
      if (!oldData) return oldData
      const newSkins: GetSkinsResponse['list'] = oldData.list.slice().map(skin => {
        if (skin.id === skinId) {
          return {
            ...skin,
            purchased: true
          }
        }
        return skin
      })
      return { list: newSkins }
    })
    queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
      if (!oldData) return oldData
      const newData: PlayerStateResponse = {
        ...oldData,
        skin: skinId,
        multiplier: updatePlayerStateDataProp(oldData.multiplier, multiplier)
      }
      if (price && isInGamePrice(price)) {
        newData[price.currency] = updatePlayerStateDataProp(oldData[price.currency], -price.amount)
      }

      return newData
    })
    // should invalidate to get updated achievements current progress
    // for soft achievement notification check
    queryClient.invalidateQueries({ queryKey: getAchievementsStateQueryKey() })
  }

  const purchaseSkin = (skinId: number, multiplier: number, price?: Price) => {
    multiplierCache = multiplier
    priceCache = price
    return mutateAsync({
      body: { skinId }
    })
  }

  return { purchaseSkin, onSkinPurchased, isPending }
}

export function useSelectSkin() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...selectSkinMutation(),
    onMutate: async variables => {
      // Cancel any outgoing refetches
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: getPlayerStateQueryKey() })

      // Snapshot the previous value
      const previousPlayerState =
        queryClient.getQueryData<PlayerStateResponse>(getPlayerStateQueryKey())

      // Optimistically update to the new value
      const skinId = variables.body.skinId
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        return {
          ...oldData,
          skin: skinId
        }
      })

      // Return a context object with the snapshotted value
      return { previousPlayerState }
    },
    onError: (error: { error: string }, _, context) => {
      useDefaultErrorHandler(error.error)
      useSkinsErrorHandling(error.error)
      // use the context returned from onMutate to roll back
      if (context) {
        queryClient.setQueryData(getPlayerStateQueryKey(), context.previousPlayerState)
      }
    },
    // Always refetch after error or success:
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const selectSkin = (skinId: number) => {
    return mutateAsync({
      body: { skinId }
    })
  }

  return { selectSkin }
}
