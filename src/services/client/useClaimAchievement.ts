import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import type { AchievementsStateResponse, PlayerStateResponse } from '@/services/openapi'
import {
  claimAchievementMutation,
  getAchievementsStateQueryKey,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useClaimAchievement() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...claimAchievementMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (data, variables) => {
      const achievementId = variables.body.id
      const achievementsData = queryClient.getQueryData<AchievementsStateResponse>(
        getAchievementsStateQueryKey()
      )

      const hasMoreUnclaimedAchievements =
        achievementsData &&
        achievementsData.achievements
          .map(achievement => {
            return achievement.id === achievementId
              ? {
                  ...achievement,
                  readyToClaim: achievement.readyToClaim.slice(1)
                }
              : achievement
          })
          .some(achievement => !!achievement.readyToClaim.length)

      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          multiplier: data.newMultiplier,
          hasUnclaimedAchievements: hasMoreUnclaimedAchievements
        }
        return newData
      })
      // should invalidate for achievements state update
      queryClient.invalidateQueries({ queryKey: getAchievementsStateQueryKey() })
    }
  })

  const claimAchievement = (id: number) => {
    return mutateAsync({
      body: { id }
    })
  }

  return { claimAchievement }
}
