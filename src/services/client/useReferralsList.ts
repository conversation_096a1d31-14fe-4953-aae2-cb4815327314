import { getReferralsList } from '@/services/openapi'
import { useInfiniteQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function getReferralsListKey() {
  return ['getReferralsList']
}

export function useReferralsList() {
  const { data, hasNextPage, fetchNextPage, isFetching } = useInfiniteQuery({
    queryKey: getReferralsListKey(),
    queryFn: async ({ pageParam = '' }) =>
      await getReferralsList({
        query: {
          cursor: pageParam
        }
      }),
    getNextPageParam: lastPage => lastPage.data?.cursor ?? undefined,
    initialPageParam: '',
    enabled: true
  })

  const referralsList = computed(() => {
    return data.value ? data.value.pages.flatMap(page => page.data?.list ?? []) : []
  })

  return {
    isFetching,
    referralsList,
    hasNextPage,
    fetchNextPage
  }
}
