import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  createReferralLinkMutation,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerStateResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useCreateRef() {
  const queryClient = useQueryClient()

  const { data, mutateAsync } = useMutation({
    ...createReferralLinkMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: data => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          reflink: data.reflink
        }
        return newData
      })
    }
  })

  const createRef = () => {
    return mutateAsync({ body: {} })
  }

  return {
    refLink: data,
    createRef
  }
}
