class IntervalService {
  private interval = 1000
  private callbacks: Map<string, Function> = new Map()
  private intervalId: NodeJS.Timeout | undefined = undefined

  private startTimer() {
    if (this.intervalId) return
    this.intervalId = setInterval(() => {
      this.callbacks.forEach(callback => callback())
    }, this.interval)
  }

  private stopTimer() {
    clearInterval(this.intervalId)
    this.intervalId = undefined
  }

  public setInterval(event: string, callback: Function) {
    this.callbacks.set(event, callback)
    this.startTimer()
  }

  public stopInterval(event: string) {
    this.callbacks.delete(event)
    if (this.callbacks.size <= 0) {
      this.stopTimer()
    }
  }
}

export const intervalService = new IntervalService()
