import type { EventBus } from '@/shared/types'

export class UiEventBus implements EventBus {
  private listeners: { [event: string]: Function[] } = {}

  public on(event: string, callback: Function, context?: unknown) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }

    this.listeners[event].push(callback.bind(context))
  }

  public emit(event: string, ...args: any[]) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(...args))
    }
  }

  public off(event: string, callback: Function) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback)
    }
  }

  public offAll(event?: string) {
    if (event) {
      delete this.listeners[event]
    } else {
      this.listeners = {}
    }
  }
}

export const eventBus = new UiEventBus()
