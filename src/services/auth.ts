import { retrieveLaunchParams } from '@telegram-apps/sdk'
;(async () => {
  const { initDataRaw, initData } = retrieveLaunchParams()

  if (!initDataRaw || !initData) {
    return
  }

  const reflink =
    initData.startParam && initData.startParam.startsWith('ref')
      ? initData.startParam.replace('ref', '')
      : undefined

  const login = async (): Promise<any> => {
    const params = {
      initData: initDataRaw,
      reflink
    }
    const url = new URL(window.location.origin + '/api/v1/auth/login')
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) url.searchParams.append(key, value)
    })

    try {
      const response = await fetch(url, { method: 'GET' })
      if (!response.ok) {
        throw new Error(`Auth failed: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('Auth failed:', error)
      if (__DEV__) return
      await new Promise(resolve => setTimeout(resolve, 1000))
      return await login()
    }
  }

  const response = await login()
  window.__AUTH__ = true

  if (__DEV__) {
    console.log('Auth mock url:')
    console.log(
      `${window.location.protocol}://${window.location.hostname}/api/auth_mock?domain=${window.location.hostname}&token=${response?.token ?? ''}`
    )
  }
})()
