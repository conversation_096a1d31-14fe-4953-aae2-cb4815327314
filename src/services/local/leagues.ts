import leaguesList from '@/preload/leagues-list.json'
import type { LeagueFeature } from '@/services/openapi'

class LeaguesService {
  leaguesList

  constructor(leagues: typeof leaguesList) {
    this.leaguesList = leagues
  }

  getLeagueFeatures(leagueLevel: number) {
    const league = leaguesList.list.find(l => l.leagueLevel === leagueLevel)
    return (league?.unlockFeatures ?? []) as LeagueFeature[]
  }

  getRequiredLeague = (feature: LeagueFeature) => {
    return (
      leaguesList.list.find(league => league.unlockFeatures.includes(feature))?.leagueLevel ?? 1
    )
  }

  hasAccess = (userLeague: number, feature: LeagueFeature) => {
    return (
      leaguesList.list.findIndex(
        l => l.unlockFeatures.includes(feature) && l.leagueLevel <= userLeague
      ) !== -1
    )
  }
}
const leaguesService = new LeaguesService(leaguesList)
export default leaguesService
