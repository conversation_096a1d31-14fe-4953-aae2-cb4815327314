<script setup lang="ts">
import { provide } from 'vue'
import { TonConnectUI } from '@tonconnect/ui'

const tonConnectOptions = {
  manifestUrl: `${window.location.origin}/tonconnect/${
    import.meta.env.VITE_TONCONNECT_MANIFEST
  }.json`
}
const tonConnectClient = new TonConnectUI(tonConnectOptions)

provide<TonConnectUI>('ton-connect-client', tonConnectClient)
</script>

<template>
  <div>
    <slot />
  </div>
</template>