import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usePlayerProfileStore = defineStore('playerProfileStore', () => {
  const playerId = ref(-1)
  const isOpen = ref(false)

  const openProfile = (id: number) => {
    playerId.value = id
    isOpen.value = true
  }

  const closeProfile = () => {
    isOpen.value = false
  }

  return {
    isOpen,
    playerId,
    openProfile,
    closeProfile
  }
})
