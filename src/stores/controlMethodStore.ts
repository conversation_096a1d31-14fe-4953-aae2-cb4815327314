import { CLOUD_STORAGE_KEYS } from '@/constants'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useControlMethodStore = defineStore('controlMethodStore', () => {
  const isLoaded = ref(false)
  const controlMehtod = ref<boolean | null>(null)

  const isMethodSelected = computed(() => {
    return controlMehtod.value !== null
  })

  const isGyroscope = computed(() => {
    return controlMehtod.value === true
  })

  const isSwipe = computed(() => {
    return controlMehtod.value === false
  })

  if (!isLoaded.value) {
    isLoaded.value = true
    cloudStorageService
      .load<boolean>(CLOUD_STORAGE_KEYS.isControlMethodGyroscope)
      .then(result => {
        controlMehtod.value = result
      })
      .catch(() => {
        controlMehtod.value = false
      })
  }

  const selectMethod = (isGyroscope: boolean) => {
    controlMehtod.value = isGyroscope
    cloudStorageService.save(CLOUD_STORAGE_KEYS.isControlMethodGyroscope, isGyroscope)
  }

  const cleanMethod = () => {
    controlMehtod.value = null
    cloudStorageService.delete(CLOUD_STORAGE_KEYS.isControlMethodGyroscope)
  }

  return {
    controlMehtod,
    isMethodSelected,
    isGyroscope,
    isSwipe,
    selectMethod,
    cleanMethod
  }
})
