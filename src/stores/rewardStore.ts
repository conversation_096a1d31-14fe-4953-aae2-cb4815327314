import type { LootBoxReward, LootBoxType } from '@/services/openapi'
import type { Reward, SkinReward } from '@/types'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRewardStore = defineStore('rewardStore', () => {
  const isOpen = ref(false)
  const isCurrentRewardVisible = ref(false)
  const rewards = ref<Reward[] | null>(null)
  const reward = ref<Reward | null>(null)
  let closeCallback: Function | null = null

  function showReward(rewardsToShow: Reward[], onClosed: (() => void) | null = null) {
    if (rewardsToShow.length === 0) {
      onClosed && onClosed()
      return
    }
    closeCallback = onClosed
    if (rewardsToShow.length > 1) {
      rewards.value = rewardsToShow
    } else {
      reward.value = rewardsToShow[0]
    }
    isOpen.value = true
  }

  function closeReward() {
    isOpen.value = false
    rewards.value = null
    reward.value = null
    closeCallback && closeCallback()
  }

  return {
    isOpen,
    isCurrentRewardVisible,
    reward,
    rewards,
    showReward,
    closeReward
  }
})

export const useSkinReward = defineStore('skinRewardStore', () => {
  const isOpen = ref(false)
  const isCurrentRewardVisible = ref(false)
  const skins = ref<SkinReward[]>([])
  const skin = ref<SkinReward>({
    skinId: 0,
    multiplier: 0,
    plusMultiplier: 0
  })
  let closeCallback: Function | null = null

  function showReward(skinsToShow: SkinReward[], onClosed: Function | null = null) {
    if (skinsToShow.length === 0) {
      onClosed && onClosed()
      return
    }
    closeCallback = onClosed
    skins.value = skinsToShow
    isOpen.value = true
    nextSkinReward()
  }

  function closeReward() {
    isOpen.value = false
    closeCallback && closeCallback()
  }

  function nextSkinReward() {
    if (skins.value.length) {
      isCurrentRewardVisible.value = true
      skin.value = skins.value.shift()!
    } else {
      closeReward()
    }
  }

  function getMultiplier(
    index: number,
    playerMultiplier: number,
    skins: { multiplier?: number | null }[]
  ) {
    const currentMultiplier = playerMultiplier
    if (index) {
      return (
        currentMultiplier +
        skins.slice(0, index).reduce((acc, value) => acc + (value.multiplier ?? 0), 0)
      )
    }
    return currentMultiplier
  }

  return {
    isOpen,
    skin,
    isCurrentRewardVisible,
    showReward,
    closeReward,
    nextSkinReward,
    getMultiplier
  }
})

type LootboxReward = {
  type: LootBoxType
  rewards: LootBoxReward[]
}

export const useLootboxReward = defineStore('lootBoxRewardStore', () => {
  const isOpen = ref(false)
  const lootboxes = ref<LootboxReward[]>([])
  const lootboxType = ref<LootBoxType | null>(null)
  const rewards = ref<LootBoxReward[]>([])
  let closeCallback: Function | null = null

  function showReward(lootboxesToShow: LootboxReward[], onClosed: Function | null = null) {
    if (lootboxesToShow.length === 0) {
      onClosed && onClosed()
      return
    }
    closeCallback = onClosed
    lootboxes.value = lootboxesToShow
    isOpen.value = true
    nextLootBox()
  }

  function nextLootBox() {
    if (lootboxes.value.length) {
      const nextLootbox = lootboxes.value.shift()!
      lootboxType.value = nextLootbox.type
      rewards.value = nextLootbox.rewards
    } else {
      closeReward()
    }
  }

  function closeReward() {
    isOpen.value = false
    lootboxType.value = null
    rewards.value = []
    closeCallback && closeCallback()
  }

  return {
    isOpen,
    lootboxType,
    rewards,
    nextLootBox,
    showReward,
    closeReward
  }
})

export const useAchievementReward = defineStore('achievementRewardStore', () => {
  const isOpen = ref(false)
  const achievementId = ref<number>(0)
  const achievementLevel = ref<number>(0)
  const multiplier = ref<number>(0)
  const plusMultiplier = ref<number>(0)

  function showReward(
    id: number,
    level: number,
    currentMultiplier: number,
    bonusMultiplier: number
  ) {
    achievementId.value = id
    achievementLevel.value = level
    multiplier.value = currentMultiplier
    plusMultiplier.value = bonusMultiplier
    isOpen.value = true
  }

  function closeReward() {
    isOpen.value = false
  }

  return {
    isOpen,
    achievementId,
    achievementLevel,
    multiplier,
    plusMultiplier,
    showReward,
    closeReward
  }
})
