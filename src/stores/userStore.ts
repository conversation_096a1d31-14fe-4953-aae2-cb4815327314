import type { User } from '@telegram-apps/sdk'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('userStore', () => {
  const user = ref<User | null>(null)

  function setUser(userData: User | null) {
    user.value = userData
  }

  function getName() {
    return ((user.value?.firstName ?? '') + ' ' + (user.value?.lastName ?? '')).trim()
  }

  function getUsername() {
    return user.value?.username ?? ''
  }

  function getAvatar() {
    return user.value?.photoUrl ?? ''
  }

  function getId() {
    return user.value?.id ?? 0
  }

  return {
    user,
    setUser,
    getName,
    getUsername,
    getAvatar,
    getId
  }
})
