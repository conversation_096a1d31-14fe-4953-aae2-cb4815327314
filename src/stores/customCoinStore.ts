import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export const useCustomCoinStore = defineStore('customCoinStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState, refetchPlayerState } = usePlayerState()

  const isCustomCoinPromoTimeOver = ref(false)

  const isCustomCoinActive = computed(() => {
    return playerState.value?.customCoinEvent ?? false
  })

  const customCoinStartedAt = computed(() => {
    return playerState.value?.customCoinEvent?.startedAt ?? 0
  })

  const customCoinEndsAt = computed(() => {
    return playerState.value?.customCoinEvent?.endsAt ?? 0
  })

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'customCoinEvent',
    {
      onTimerEnd: async () => {
        if (!isCustomCoinActive.value) return
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isCustomCoinActive.value === null) return
    const now = await getNow()
    let timeLeft: number
    if (customCoinStartedAt.value > now) {
      timeLeft = customCoinStartedAt.value - now
      isCustomCoinPromoTimeOver.value = false
    } else {
      timeLeft = customCoinEndsAt.value - now
      isCustomCoinPromoTimeOver.value = true
    }
    initTimerWithTotal(timeLeft)
  }

  watch(
    () => isCustomCoinActive.value,
    async () => {
      if (!isCustomCoinActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  return {
    isCustomCoinActive,
    isCustomCoinPromoTimeOver,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
})
