import { usePlayerState } from '@/services/client/usePlayerState'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import { getPlayerStateQueryKey } from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerStateResponse } from '@/services/openapi/types.gen'
import { useQueryClient } from '@tanstack/vue-query'

type EventKey = 'hotrecordEvent' | 'onepercentEvent' | 'customCoinEvent' | 'clanEventState'

const createRankStore = (eventKey: EventKey) => () => {
  const RANK_KEY = eventKey + 'Rank'
  const queryClient = useQueryClient()
  const { playerState } = usePlayerState()
  const storedRank = ref(0)
  const playerRank = computed(() => playerState.value?.[eventKey]?.playerRank ?? 0)

  function getLastRank() {
    cloudStorageService.load<string>(RANK_KEY).then(rank => {
      storedRank.value = parseInt(rank || '0')
    })
  }

  async function updateLastRank(rank: number) {
    storedRank.value = rank
    // update player state
    queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
      if (!oldData) return oldData
      const newData: PlayerStateResponse = {
        ...oldData,
        // @ts-ignore
        [eventKey]: {
          ...oldData[eventKey],
          playerRank: rank
        }
      }
      return newData
    })
    await cloudStorageService.save(RANK_KEY, rank)
  }

  async function deleteLastRank() {
    storedRank.value = 0
    await cloudStorageService.delete(RANK_KEY)
  }

  const isRankChanged = computed(() => {
    return playerRank.value !== 0 && playerRank.value !== storedRank.value
  })

  return {
    playerRank,
    storedRank,
    isRankChanged,
    getLastRank,
    updateLastRank,
    deleteLastRank
  }
}

export const useOnepercentRankStore = defineStore(
  'onepercentRankStore',
  createRankStore('onepercentEvent')
)
export const useHotrecordRankStore = defineStore(
  'hotrecordRankStore',
  createRankStore('hotrecordEvent')
)
export const useCustomCoinRankStore = defineStore(
  'customCoinRankStore',
  createRankStore('customCoinEvent')
)
export const useClanEventRankStore = defineStore(
  'clanEventRankStore',
  createRankStore('clanEventState')
)
