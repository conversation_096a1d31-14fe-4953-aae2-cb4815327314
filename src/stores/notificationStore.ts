import type { UnattainedAchievement } from '@/services/openapi'
import { defineStore } from 'pinia'
import { reactive } from 'vue'

const NOTIFICATION_DEFAULT_TIMEOUT = 5000

type notification = {
  achievement: UnattainedAchievement | null
  timeout: number
}

export const useNotification = defineStore('notificationStore', () => {
  const notification: notification = reactive({
    achievement: null,
    timeout: 0
  })

  function showNotification(
    achievement: UnattainedAchievement,
    timeout: number = NOTIFICATION_DEFAULT_TIMEOUT
  ) {
    notification.achievement = achievement
    notification.timeout = timeout

    setTimeout(() => {
      notification.achievement = null
      notification.timeout = 0
    }, timeout)
  }

  return {
    notification,
    showNotification
  }
})
