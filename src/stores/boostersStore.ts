import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export const useMagneticFieldStore = defineStore('magneticFieldStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState } = usePlayerState()

  const now = ref(0)

  const isActive = computed(() => {
    return (
      playerState.value?.boostersView?.timeBoundMagneticFieldActiveTill &&
      playerState.value?.boostersView?.timeBoundMagneticFieldActiveTill > now.value
    )
  })

  const magnetFieldTimeEndAt = computed(() => {
    return playerState.value?.boostersView?.timeBoundMagneticFieldActiveTill ?? 0
  })

  const recalculateTime = async () => {
    if (isActive.value) {
      now.value = await getNow()
      const endsAt = magnetFieldTimeEndAt.value
      const timeLeft = now.value < endsAt ? Math.floor(endsAt - now.value) : 0
      initTimerWithTotal(timeLeft)
    }
  }

  const { days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'boosterMagnetTimer',
    {
      onTimerEnd: recalculateTime
    }
  )

  watch(
    magnetFieldTimeEndAt,
    async () => {
      if (isActive.value) {
        await recalculateTime()
      }
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  return {
    isActive,
    days,
    hours,
    minutes,
    seconds
  }
})
export const useJumperStore = defineStore('jumperStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState } = usePlayerState()

  const now = ref(0)

  const isActive = computed(() => {
    return (
      playerState.value?.boostersView?.timeBoundJumpersActiveTill &&
      playerState.value?.boostersView?.timeBoundJumpersActiveTill > now.value
    )
  })

  const jumperTimeEndAt = computed(() => {
    return playerState.value?.boostersView?.timeBoundJumpersActiveTill ?? 0
  })

  const recalculateTime = async () => {
    if (isActive.value) {
      now.value = await getNow()
      const endsAt = jumperTimeEndAt.value
      const timeLeft = now.value < endsAt ? Math.floor(endsAt - now.value) : 0
      initTimerWithTotal(timeLeft)
    }
  }

  const { days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'boosterJumperTimer',
    {
      onTimerEnd: recalculateTime
    }
  )

  watch(
    jumperTimeEndAt,
    async () => {
      if (isActive.value) {
        await recalculateTime()
      }
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  return {
    isActive,
    days,
    hours,
    minutes,
    seconds
  }
})
export const useAimBotStore = defineStore('aimBotStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState } = usePlayerState()

  const now = ref(0)

  const isActive = computed(() => {
    return (
      playerState.value?.boostersView?.timeBoundAimbotsActiveTill &&
      playerState.value?.boostersView?.timeBoundAimbotsActiveTill > now.value
    )
  })

  const aimBotTimeEndAt = computed(() => {
    return playerState.value?.boostersView?.timeBoundAimbotsActiveTill ?? 0
  })

  const recalculateTime = async () => {
    if (isActive.value) {
      now.value = await getNow()
      const endsAt = aimBotTimeEndAt.value
      const timeLeft = now.value < endsAt ? Math.floor(endsAt - now.value) : 0
      initTimerWithTotal(timeLeft)
    }
  }

  const { days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'boosterAimTimer',
    {
      onTimerEnd: recalculateTime
    }
  )

  watch(
    aimBotTimeEndAt,
    async () => {
      if (isActive.value) {
        await recalculateTime()
      }
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateTime()
    })
  })

  return {
    isActive,
    days,
    hours,
    minutes,
    seconds
  }
})
