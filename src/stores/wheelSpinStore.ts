import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export const useWheelSpinStore = defineStore('wheelSpin', () => {
  const isFreeSpinBlocked = ref(false)

  const { playerState } = usePlayerState()

  const { getNow } = useNowTimestamp()
  const { hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('wheelSpin', {
    onTimerEnd: () => {
      isFreeSpinBlocked.value = false
    }
  })

  const freeSpinAvailableAtTime = computed(() => {
    return playerState.value?.wheelSpins?.freeAvailableAt ?? 0
  })

  const recalculateFreeSpinAvailableTime = async (availableAt: number) => {
    const now = await getNow()
    const timeLeft = Math.floor(availableAt - now)
    if (timeLeft > 0) {
      isFreeSpinBlocked.value = true
      initTimerWithTotal(timeLeft)
    } else {
      isFreeSpinBlocked.value = false
    }
  }

  const availableWheelSpinsCount = computed(() => {
    return (playerState.value?.wheelSpins?.amount ?? 0) + (isFreeSpinBlocked.value ? 0 : 1)
  })

  watch(
    freeSpinAvailableAtTime,
    () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  return { isFreeSpinBlocked, availableWheelSpinsCount, hours, minutes, seconds }
})
