import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, watch } from 'vue'

export const useCommunityBattleStore = defineStore('communityBattleStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState, refetchPlayerState } = usePlayerState()

  const isCommunityBattleActive = computed(() => {
    return !!playerState.value?.battleEvent
  })

  const battleEventEndsAt = computed(() => {
    return playerState.value?.battleEvent?.endsAt ?? 0
  })

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'communityBattleEvent',
    {
      onTimerEnd: async () => {
        if (!isCommunityBattleActive.value) return
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isCommunityBattleActive.value === null) return
    const now = await getNow()
    const timeLeft = Math.max(battleEventEndsAt.value - now, 0)
    initTimerWithTotal(timeLeft)
  }

  watch(
    () => isCommunityBattleActive.value,
    async () => {
      if (!isCommunityBattleActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  return {
    isCommunityBattleActive,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
})
