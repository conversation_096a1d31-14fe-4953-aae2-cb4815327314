import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export const useExploitersStore = defineStore('exploitersStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState, refetchPlayerState } = usePlayerState()

  const isExploitersHeistTimeOver = ref(false)

  const exploitersHeistTimeToFail = computed(() => {
    return playerState.value?.dailyWriteOffState?.timestampToFail ?? 0
  })

  const isExploitersHeistActive = computed(
    () => exploitersHeistTimeToFail.value && !isExploitersHeistTimeOver.value
  )

  const { days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('exploiters', {
    onTimerEnd: () => {
      refetchPlayerState()
      isExploitersHeistTimeOver.value = true
    }
  })

  const recalculateHeistTime = async () => {
    if (!isExploitersHeistActive.value) return
    const now = await getNow()
    const timeLeft = exploitersHeistTimeToFail.value - now
    initTimerWithTotal(timeLeft)
  }

  watch(
    () => isExploitersHeistActive.value,
    async () => {
      if (!isExploitersHeistActive.value) return
      await recalculateHeistTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateHeistTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateHeistTime()
    })
  })

  return {
    isExploitersHeistActive,

    days,
    hours,
    minutes,
    seconds
  }
})
