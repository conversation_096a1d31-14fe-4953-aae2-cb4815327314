import { NEW_SKINS } from '@/constants/skins'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useSkinsList } from '@/services/client/useSkins.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const SKINS_USER_HAS_SEEN = 'skinsUserHasSeen'
export const USER_HAS_SEEN_LEAGUES = 'userHasSeenLeagues'
export const USER_HAS_SEEN_SHOP = 'userHasSeenShop'
export const USER_HAS_SEEN_WHEEL_SPIN = 'userHasSeenWheelSpin'
export const USER_HAS_SEEN_MAGIC_RAINBOW = 'userHasSeenMagicRainbow'
export const USER_HAS_SEEN_MAGIC_OFFER = 'userHasSeenMagicOffer'
export const USER_HAS_SEEN_SNAKE_OFFER = 'userHasSeenSnakeOffer'
export const USER_HAS_SEEN_LOOT_BOX_OFFER = 'userHasSeenLootBoxOffer'
export const USER_HAS_SEEN_FRAGMENT = 'userHasSeenFragment'
export const TASKS_USER_HAS_SEEN = 'tasksUserHasSeen'

export const useHasUserSeenSkins = defineStore('hasUserSeenSkinsStore', () => {
  const skinsUserHasSeen = ref<number[]>([])
  const isLoaded = ref(false) // used to send only one request to storage

  if (!isLoaded.value) {
    cloudStorageService.load<number[]>(SKINS_USER_HAS_SEEN).then(skins => {
      if (skins && skins.length) {
        skinsUserHasSeen.value = skins
      }
      isLoaded.value = true
    })
  }

  const newSkins = computed(() => {
    return NEW_SKINS.filter(skinId => !skinsUserHasSeen.value.includes(skinId))
  })
  const { skinsList } = useSkinsList(false)

  const hasSeenNewSkins = computed(() => {
    return NEW_SKINS.filter(skinId => skinsList.value.find(({ id }) => id === skinId)).every(
      skinId => skinsUserHasSeen.value.includes(skinId)
    )
  })

  const markSkinAsSeen = async (skinId: number) => {
    skinsUserHasSeen.value.push(skinId)
    await cloudStorageService.save(SKINS_USER_HAS_SEEN, skinsUserHasSeen.value)
  }

  return {
    hasSeenNewSkins,
    newSkins,
    markSkinAsSeen
  }
})

export const useHasUserSeenLeagues = defineStore('hasUserSeenLeaguesStore', () => {
  const hasSeenLeagues = ref<boolean>(false)
  const hasNewLeagueInfo = ref<boolean>(false)
  const isLoaded = ref(false) // used to send only one request to storage

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_LEAGUES).then(hasSeen => {
      hasSeenLeagues.value = !!hasSeen
      isLoaded.value = true
    })
  }

  const markAsSeen = async () => {
    if (!hasSeenLeagues.value) {
      hasSeenLeagues.value = true
      await cloudStorageService.save(USER_HAS_SEEN_LEAGUES, true)
    }
    hasNewLeagueInfo.value = false
  }

  return {
    hasSeenLeagues,
    hasNewLeagueInfo,
    onClick: markAsSeen
  }
})

export const useHasUserSeenTasks = defineStore('hasUserSeenTasksStore', () => {
  const { playerState } = usePlayerState()
  const tasksUserHasSeen = ref<string[]>([])
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<string[]>(TASKS_USER_HAS_SEEN).then(async tasks => {
      if (tasks && tasks.length) {
        tasksUserHasSeen.value = tasks
        // we should delete outdated tasks from storage
        // so if we activate them again we mark them as unseen
        const taskAtStorage = tasks.slice()
        const taskAtStorageWithoutOutdated = taskAtStorage.filter(task =>
          activeTasks.value.includes(task)
        )
        if (taskAtStorageWithoutOutdated.length !== taskAtStorage.length) {
          await cloudStorageService.save(TASKS_USER_HAS_SEEN, taskAtStorageWithoutOutdated)
        }
        isLoaded.value = true
      }
    })
  }

  const activeTasks = computed(() => playerState.value!.newTasks || [])

  const newTasks = computed(() => {
    return activeTasks.value.filter(taskName => !tasksUserHasSeen.value.includes(taskName))
  })

  const hasSeenNewTasks = computed(() => {
    return activeTasks.value.every(taskName => tasksUserHasSeen.value.includes(taskName))
  })

  const markTaskAsSeen = async (taskName: string) => {
    tasksUserHasSeen.value.push(taskName)
    await cloudStorageService.save(TASKS_USER_HAS_SEEN, tasksUserHasSeen.value)
  }

  return {
    newTasks,
    hasSeenNewTasks,
    markTaskAsSeen
  }
})

export const useHasUserSeenShop = defineStore('hasUserSeenShopStore', () => {
  const hasSeenShop = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_SHOP).then(shopRes => {
      hasSeenShop.value = !!shopRes
      isLoaded.value = true
    })
  }

  const markShopAsSeen = () => {
    if (hasSeenShop.value) return
    hasSeenShop.value = true
    cloudStorageService.save(USER_HAS_SEEN_SHOP, true)
  }

  return {
    hasSeenShop,
    markShopAsSeen
  }
})

export const useHasUserSeenFragment = defineStore('hasUserSeenFragmentStore', () => {
  const hasSeenFragment = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_FRAGMENT).then(fragmentRes => {
      hasSeenFragment.value = !!fragmentRes
      isLoaded.value = true
    })
  }

  const markFragmentAsSeen = () => {
    if (hasSeenFragment.value) return
    hasSeenFragment.value = true
    cloudStorageService.save(USER_HAS_SEEN_FRAGMENT, true)
  }

  return {
    hasSeenFragment,
    markFragmentAsSeen
  }
})

export const useHasUserSeenMagicRainbow = defineStore('hasUserSeenMagicRainbowStore', () => {
  const hasSeenMagicRainbow = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_MAGIC_RAINBOW).then(magicRainbowRes => {
      hasSeenMagicRainbow.value = !!magicRainbowRes
      isLoaded.value = true
    })
  }

  const markMagicRainbowAsSeen = () => {
    if (hasSeenMagicRainbow.value) return
    hasSeenMagicRainbow.value = true
    cloudStorageService.save(USER_HAS_SEEN_MAGIC_RAINBOW, true)
  }

  return {
    hasSeenMagicRainbow,
    markMagicRainbowAsSeen
  }
})

export const useHasUserSeenMagicOffer = defineStore('hasUserSeenMagicOfferStore', () => {
  const hasSeenMagicOffer = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_MAGIC_OFFER).then(magicOfferRes => {
      hasSeenMagicOffer.value = !!magicOfferRes
      isLoaded.value = true
    })
  }

  const markMagicOfferAsSeen = () => {
    if (hasSeenMagicOffer.value) return
    hasSeenMagicOffer.value = true
    cloudStorageService.save(USER_HAS_SEEN_MAGIC_OFFER, true)
  }

  return {
    hasSeenMagicOffer,
    markMagicOfferAsSeen
  }
})

export const useHasUserSeenSnakeOffer = defineStore('hasUserSeenSnakeOfferStore', () => {
  const hasSeenSnakeOffer = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_SNAKE_OFFER).then(snakeOfferRes => {
      hasSeenSnakeOffer.value = !!snakeOfferRes
      isLoaded.value = true
    })
  }

  const markSnakeOfferAsSeen = () => {
    if (hasSeenSnakeOffer.value) return
    hasSeenSnakeOffer.value = true
    cloudStorageService.save(USER_HAS_SEEN_SNAKE_OFFER, true)
  }

  return {
    hasSeenSnakeOffer,
    markSnakeOfferAsSeen
  }
})

export const useHasUserSeenWheelSpin = defineStore('hasUserSeenWheelSpinStore', () => {
  const hasSeenWheelSpin = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_WHEEL_SPIN).then(wheelSpinRes => {
      hasSeenWheelSpin.value = !!wheelSpinRes
      isLoaded.value = true
    })
  }

  const markWheelSpinAsSeen = () => {
    if (hasSeenWheelSpin.value) return
    hasSeenWheelSpin.value = true
    cloudStorageService.save(USER_HAS_SEEN_WHEEL_SPIN, true)
  }

  return {
    hasSeenWheelSpin,
    markWheelSpinAsSeen
  }
})

export const useHasUserSeenLootBoxOffer = defineStore('hasUserSeenLootBoxOfferStore', () => {
  const hasSeenLootBoxOffer = ref<boolean>(true)
  const isLoaded = ref(false)

  if (!isLoaded.value) {
    cloudStorageService.load<boolean>(USER_HAS_SEEN_LOOT_BOX_OFFER).then(lootBoxOfferRes => {
      hasSeenLootBoxOffer.value = !!lootBoxOfferRes
      isLoaded.value = true
    })
  }

  const markLootBoxOfferAsSeen = () => {
    if (hasSeenLootBoxOffer.value) return
    hasSeenLootBoxOffer.value = true
    cloudStorageService.save(USER_HAS_SEEN_LOOT_BOX_OFFER, true)
  }

  return {
    hasSeenLootBoxOffer,
    markLootBoxOfferAsSeen
  }
})
