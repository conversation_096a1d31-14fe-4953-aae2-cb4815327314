import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useLives } from '@/services/client/useLives'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, watch } from 'vue'

export const useLifesStore = defineStore('livesStore', () => {
  const { getNow } = useNowTimestamp()
  const {
    lastLifeRestoreAt,
    lives,
    lifeRestoreTime,
    maxLives,
    livesUnlimitedUntil,
    consumeUnlimitedLife,
    consumeLife,
    restoreLife
  } = useLives()

  const { days, hours, minutes, seconds, stopTimer, initTimerWithTotal } = useCountdownTimer(
    'lives',
    {
      onTimerEnd: () => {
        if (isUnlimitedLives.value) {
          consumeUnlimitedLife()
        } else {
          restoreLife()
        }
      }
    }
  )

  const isRestoringLife = computed(() => {
    return lastLifeRestoreAt.value !== undefined
  })

  const recalculateLifeRestoreTime = async () => {
    if (!lastLifeRestoreAt.value) {
      stopTimer()
      return
    }
    const now = await getNow()
    const diff = now - lastLifeRestoreAt.value
    const timeLeft = lifeRestoreTime.value - diff
    initTimerWithTotal(timeLeft)
  }

  watch(
    () => lastLifeRestoreAt.value,
    () => {
      if (!isRestoringLife.value) return
      recalculateLifeRestoreTime()
    },
    { immediate: true }
  )

  const isUnlimitedLives = computed(() => {
    return !!livesUnlimitedUntil.value
  })

  const recalculateUnlimitedLivesTime = async () => {
    if (!isUnlimitedLives.value) return
    const now = await getNow()
    const endsAt = livesUnlimitedUntil.value
    const timeLeft = Math.floor(endsAt - now)
    initTimerWithTotal(timeLeft)
  }

  watch(
    () => livesUnlimitedUntil.value,
    () => {
      if (!isUnlimitedLives.value) return
      recalculateUnlimitedLivesTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateLifeRestoreTime()
      recalculateUnlimitedLivesTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateLifeRestoreTime()
      recalculateUnlimitedLivesTime()
    })
  })

  return {
    lives,
    maxLives,
    isUnlimitedLives,
    isRestoringLife,
    days,
    hours,
    minutes,
    seconds,
    recalculateUnlimitedLivesTime,
    recalculateLifeRestoreTime,
    consumeLife
  }
})
