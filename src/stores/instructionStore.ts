import type { InstructionType } from '@/constants/instructions'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export type InstructionButton = {
  text: string
  onClick: Function
}

export const useInstructionStore = defineStore('instructionStore', () => {
  const instructionType = ref<InstructionType | undefined>(undefined)
  const instructionButton = ref<InstructionButton | null>(null)

  function showInstruction(type: InstructionType, button: InstructionButton | null = null) {
    instructionType.value = type
    instructionButton.value = button
  }

  function hideInstruction() {
    instructionType.value = undefined
  }

  return {
    instructionType,
    instructionButton,
    showInstruction,
    hideInstruction
  }
})
