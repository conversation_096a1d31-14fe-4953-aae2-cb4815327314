import { defineStore } from 'pinia'
import { ref } from 'vue'

function animationStore() {
  const animate = ref(false)

  function runAnimation() {
    if (animate.value) return
    animate.value = true
  }

  function stopAnimation() {
    animate.value = false
  }

  return {
    animate,
    runAnimation,
    stopAnimation
  }
}

export const useFadeAnimationStore = defineStore('fadeAnimationStore', animationStore)
