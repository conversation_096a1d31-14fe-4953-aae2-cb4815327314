import { initSDK } from '@/services/telegram-sdk'
import { isMobile } from '@/utils/device'
import { isTMA, retrieveLaunchParams } from '@telegram-apps/sdk'
import disableDevtool from 'disable-devtool'
import type { App } from 'vue'
import { initApp as initContestApp } from './contestApp.ts'
import { initApp as initMainApp } from './mainApp.ts'
import { initApp as initQrApp } from './qrApp.ts'
import { APP_INSTANCE_KEY, CONTEST_ID, GET_DEFAULT_APP } from './utils/symbols.ts'

if (!__DEV__) {
  disableDevtool({
    clearIntervalWhenDevOpenTrigger: true,
    interval: 300,
    disableMenu: true,
    disableSelect: false,
    ondevtoolclose: function () {
      window.close()
    },
    ondevtoolopen: () => {
      window.close()
    }
  })
}

const getDefaultApp = () => {
  if (!__DEV__ && !isMobile()) {
    return initQrApp()
  } else {
    return initMainApp()
  }
}

const bootstrap = async () => {
  const isTma = await isTMA()
  if (isTma) {
    initSDK()
  }

  let app: App | undefined

  try {
    const initData = retrieveLaunchParams()
    const contestId =
      initData.startParam && initData.startParam.startsWith('contest')
        ? parseInt(initData.startParam.replace('contest', ''), 10)
        : undefined

    if (contestId !== undefined) {
      app = await initContestApp()
      app.provide(CONTEST_ID, contestId)
    }
  } catch (error) {
    console.error(error)
  }

  if (app === undefined) {
    app = getDefaultApp()
  }

  app.provide(GET_DEFAULT_APP, getDefaultApp)
  app.provide(APP_INSTANCE_KEY, app)
  app.mount('#app')

  return app
}

bootstrap()
