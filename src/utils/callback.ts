export function repeatCallbackWithDuration(
  callback: Function,
  duration: number,
  frequency: number
): number | null {
  if (duration <= 0 || frequency <= 0) {
    console.warn('Invalid duration or frequency for repeatCallbackWithDuration.')
    return null
  }

  const iterations = Math.floor(duration / frequency)
  let count = 0

  const intervalId = setInterval(() => {
    if (count >= iterations) {
      clearInterval(intervalId)
      return
    }
    callback()
    count++
  }, frequency)

  return intervalId as unknown as number
}
