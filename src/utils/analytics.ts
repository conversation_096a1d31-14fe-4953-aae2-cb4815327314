type AnalyticsEvent =
  | 'gyroscope_access_granted'
  | 'gyroscope_access_denied'
  | 'gyroscope_access_shown'
  | 'made_transaction'
  | 'quest_viewed'

type NewUserEvents =
  | 'main_loading_screen'
  | 'first_tutorial'
  | 'second_tutorial'
  | 'game_view'
  | 'game_over'
  | 'revive_banner'

type AnalyticsEventWithParams = {
  event_view: {
    event: string
    current_position: number
    total_points: number
  }
  event_end: {
    event: string
    total_points: number
    reward_type: string
    reward_amount: number
  }
  go_to_shop: {
    from: string
  }
  tutorial_passed: {
    last_platform: string
    last_booster: string
    first_ticket: number
    first_booster: number
    collected_boosters: number
    generated_presets: string
    session_count: number
    last_preset: string
  }
  onboarding: {
    step: number
  }
  new_user: {
    step: NewUserEvents
  }
}

export function sendAnalyticsEvent<T extends AnalyticsEvent>(name: T): void
export function sendAnalyticsEvent<T extends keyof AnalyticsEventWithParams>(
  name: T,
  params: AnalyticsEventWithParams[T]
): void
export function sendAnalyticsEvent<T extends keyof AnalyticsEventWithParams>(
  name: T,
  params?: AnalyticsEventWithParams[T]
) {
  window.gtag('event', name, {
    send_to: __GTAG__,
    ...params
  })
}

export function sendTutorialAnalyticsEvent(
  eventName: string,
  eventData: Record<string, any>
): void {
  if (typeof window.gtag === 'function') {
    window.gtag('event', eventName, eventData)
  } else {
    console.warn('Google Analytics is not initialized')
  }
}
