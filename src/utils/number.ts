export function formatNumberWithSeparator(
  number: number | string,
  separator: string = ' '
): string {
  const numberString = number.toString()
  if (numberString.includes('.')) {
    return numberString
  }
  const formattedNumber = numberString.replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  return formattedNumber
}

function truncateNumber(number: number, decimals: number): number {
  const multiplier = Math.pow(10, decimals)
  return Math.floor(number * multiplier) / multiplier
}

export function formatNumberToShortString(number: number, decimals: number = 2): string {
  if (number >= 1_000_000_000_000) {
    return `${truncateNumber(number / 1_000_000_000_000, decimals)}T`
  }
  if (number >= 1_000_000_000) {
    return `${truncateNumber(number / 1_000_000_000, decimals)}B`
  }
  if (number >= 1_000_000) {
    return `${truncateNumber(number / 1_000_000, decimals)}M`
  }
  if (number >= 1_000) {
    return `${truncateNumber(number / 1_000, decimals)}K`
  }
  return formatNumberWithSeparator(number)
}

export function formatSecondsToTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  return `${hours ? `${hours}h` : ''}${minutes ? `${minutes}m` : ''}${remainingSeconds ? `${remainingSeconds}s` : ''}`
}

export function customTruncate(
  num: number,
  maxDecimals: number = 6,
  digitsAfterLeadingZeros: number = 3
): string {
  if (!Number.isFinite(num)) {
    return String(num)
  }
  if (num === 0) {
    return '0'
  }
  const factor = 10 ** maxDecimals
  const truncatedValue = Math.floor(num * factor) / factor
  const str = truncatedValue.toString()
  if (!str.includes('.')) {
    return str
  }
  const [intPart, fraction] = str.split('.')
  let fracPart = fraction.replace(/0+$/, '')

  if (!fracPart) {
    return intPart
  }
  const match = fracPart.match(/^0+/)
  if (match) {
    const leadingZerosCount = match[0].length
    const remainderLength = fracPart.length - leadingZerosCount
    if (remainderLength > digitsAfterLeadingZeros) {
      fracPart = fracPart.slice(0, leadingZerosCount + digitsAfterLeadingZeros)
    }
  }
  return `${intPart}.${fracPart}`
}
