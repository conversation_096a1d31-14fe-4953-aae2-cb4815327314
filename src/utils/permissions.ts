export function checkDeviceMotionPermission(): Promise<void> {
  return new Promise((resolve, reject) => {
    const deviceMotionEvent = DeviceMotionEvent as unknown as {
      requestPermission?: () => Promise<string>
    }

    if (deviceMotionEvent.requestPermission) {
      deviceMotionEvent
        .requestPermission()
        .then((response: string) => {
          const granted = response === 'granted'
          if (granted) {
            resolve()
          } else {
            reject('Permission denied')
          }
        })
        .catch(error => {
          if (error instanceof Error) {
            reject(`Error: ${error.message}`)
          } else {
            reject('Unknown error occurred during permission request')
          }
        })
    } else {
      resolve()
    }
  })
}

export function isDeviceMotionPermissionRequired(): boolean {
  const deviceMotionEvent = DeviceMotionEvent as unknown as {
    requestPermission?: () => Promise<string>
  }
  return typeof deviceMotionEvent.requestPermission === 'function'
}
