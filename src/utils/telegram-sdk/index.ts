import { $version } from '@telegram-apps/sdk'

function versionCompare(v1: string | string[], v2: string | string[]) {
  if (typeof v1 !== 'string') v1 = ''
  if (typeof v2 !== 'string') v2 = ''
  v1 = v1.replace(/^\s+|\s+$/g, '').split('.')
  v2 = v2.replace(/^\s+|\s+$/g, '').split('.')
  const a = Math.max(v1.length, v2.length)
  let i, p1, p2
  for (i = 0; i < a; i++) {
    p1 = parseInt(v1[i]) || 0
    p2 = parseInt(v2[i]) || 0
    if (p1 == p2) continue
    if (p1 > p2) return 1
    return -1
  }
  return 0
}

export function isSupported(ver: string) {
  return versionCompare($version(), ver) >= 0
}

export function isVersionNewerThan(version: string) {
  return $version() >= version
}
