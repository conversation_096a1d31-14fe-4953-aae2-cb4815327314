import { gameplayAssetsPreloader } from '@/game/core/CoreGameplay/GameplayAssetsPreloader'
import { eventBus } from '@/services/eventBus/uiEventBus'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'

const getAssetsLoadingState = () => {
  return gameplayAssetsPreloader.isGameplayAssetsPreloaded
}

export const waitForGameAssetsLoaded = () => {
  const thenable = {
    then(onFulfilled: Function) {
      if (getAssetsLoadingState()) {
        onFulfilled()
      } else {
        const onAssetsPreloadEnded = () => {
          eventBus.off(GAME_EVENTS.ASSETS_PRELOAD_ENDED, onAssetsPreloadEnded)
          onFulfilled()
        }
        eventBus.on(GAME_EVENTS.ASSETS_PRELOAD_ENDED, onAssetsPreloadEnded)
      }
    }
  }

  return thenable
}
