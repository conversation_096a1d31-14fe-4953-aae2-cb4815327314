const SEPARATOR = '...'

export function truncateString(fullStr: string, strLen: number) {
  if (fullStr.length <= strLen) return fullStr

  const sepLen = SEPARATOR.length,
    charsToShow = strLen - sepLen,
    frontChars = Math.ceil(charsToShow / 2),
    backChars = Math.floor(charsToShow / 2)

  return (
    fullStr.substring(0, frontChars) + SEPARATOR + fullStr.substring(fullStr.length - backChars)
  )
}

export function capitalizeFistChar(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
