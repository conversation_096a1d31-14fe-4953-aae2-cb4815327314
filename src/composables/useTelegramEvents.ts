import { useCompleteUnverifiedTask } from '@/services/client/useCompleteUnverifiedTask'
import {
  addToHomeScreen,
  hideBackButton,
  offAddedToHomeScreen,
  onAddedToHomeScreen,
  onBackButtonClick,
  showBackButton
} from '@telegram-apps/sdk'
import { useRoute, useRouter } from 'vue-router'

export function useTelegramEvents() {
  const router = useRouter()
  const route = useRoute()
  const { completeUnverifiedTask } = useCompleteUnverifiedTask()

  const registerTgEvents = () => {
    onBackButtonClick(() => {
      if (route.path === '/game') {
        // handled on game view
        return
      } else {
        router.push('/')
      }
    })
  }

  const handleTgButtonDisplayState = () => {
    router.beforeEach(to => {
      if (to.path === '/') {
        hideBackButton()
      } else {
        showBackButton()
      }
      return true
    })
  }

  // task completion is hardcoded here because user can add to home screen
  // from TMA three dots menu and we should listen to it
  const registerAddedToHomeScreenEvent = async () => {
    if (addToHomeScreen.isAvailable()) {
      const completeAddToHomeScreenTask = async () => {
        const ADD_TO_HOME_SCREEN_TASK_ID = 8
        await completeUnverifiedTask(ADD_TO_HOME_SCREEN_TASK_ID)
        offAddedToHomeScreen(completeAddToHomeScreenTask)
      }
      onAddedToHomeScreen(completeAddToHomeScreenTask)
    }
  }

  return {
    registerTgEvents,
    handleTgButtonDisplayState,
    registerAddedToHomeScreenEvent
  }
}
