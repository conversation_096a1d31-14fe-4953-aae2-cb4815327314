import { TON_SKIN_ID } from '@/constants/skins'
import { useSkinsList } from '@/services/client/useSkins'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { computed, watch } from 'vue'
import { useReward } from './useReward'

const IS_TON_SKIN_PURCHASE_IN_PROGRESS = 'isTonSkinPurchaseInProgressNew'

export function useSkinForTon() {
  const { skinsList, isLoading } = useSkinsList()
  const { showReward } = useReward()
  const tonSkin = computed(() => {
    return skinsList.value.find(skin => skin.id === TON_SKIN_ID)
  })

  const isTonSkinPurchased = computed(() => {
    return isLoading.value || (tonSkin.value?.purchased ?? false)
  })

  watch(tonSkin, newValue => {
    if (newValue && newValue.purchased) {
      cloudStorageService.load<string>(IS_TON_SKIN_PURCHASE_IN_PROGRESS).then(inProgress => {
        if (inProgress) {
          cloudStorageService.delete(IS_TON_SKIN_PURCHASE_IN_PROGRESS)
          showReward(
            {
              type: 'skin',
              value: newValue.id,
              multiplier: newValue.multiplier
            },
            { isAlreadyOnPlayerState: true }
          )
        }
      })
    }
  })

  return {
    isTonSkinPurchased
  }
}
