import { useFadeAnimationStore } from '@/stores/animationStore'

export function useFadeTransitionAnimation() {
  const fadeAnimationStore = useFadeAnimationStore()

  const runTransitionAnimation = () => {
    fadeAnimationStore.runAnimation()
  }

  const stopTransitionAnimation = () => {
    fadeAnimationStore.stopAnimation()
  }

  return {
    runTransitionAnimation,
    stopTransitionAnimation
  }
}
