import { cloudStorageService } from '@/shared/storage/cloudStorageService'

export function useEventWelcomeCheck() {
  const checkWelcomeBanner = (key: string, isEventActive: boolean, hasAccess = true) => {
    return cloudStorageService.load<boolean>(key).then(hasSeen => {
      if (hasAccess && !hasSeen && isEventActive) {
        cloudStorageService.save(key, true)
        return true
      } else if (hasSeen && !isEventActive) {
        cloudStorageService.delete(key)
      }
      return false
    })
  }

  return { checkWelcomeBanner }
}

export function useEventPromoCheck() {
  const checkPromoBanner = (key: string, isPromoActive?: boolean, hasAccess = true) => {
    return cloudStorageService.load<boolean>(key).then(hasSeen => {
      if (hasAccess && !hasSeen && isPromoActive) {
        cloudStorageService.save(key, true)
        return true
      } else if (hasSeen && !isPromoActive) {
        cloudStorageService.delete(key)
      }
      return false
    })
  }

  return { checkPromoBanner }
}
