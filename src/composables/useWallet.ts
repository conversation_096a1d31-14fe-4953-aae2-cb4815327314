import {
  subscribeToWalletConnected,
  useLocalWalletDisconnection,
  useTonConnectUI,
  useTonWallet
} from '@/composables/useLocalWallet'
import { useUserWallet } from '@/services/client/useUserWallet'
import { useServerWalletConnect, useServerWalletDisonnect } from '@/services/client/useWallet'
import { computed } from 'vue'

export function useIsWalletConnected() {
  const { isLoading, isWalletConnectedOnServer, serverWalletAddress, isTransactionMade } =
    useUserWallet()
  const { localWalletAddress } = useTonWallet()

  const isWalletConnectedLocaly = computed(() => !!localWalletAddress.value)
  const isWalletConnectedEverywhere = computed(
    () => !!serverWalletAddress.value && serverWalletAddress.value === localWalletAddress.value
  )

  return {
    isLoading,
    isWalletConnectedEverywhere,
    isWalletConnectedOnServer,
    isWalletConnectedLocaly,
    walletAddress: serverWalletAddress,
    isTransactionMade
  }
}

export function useWalletConnection(onSuccess?: () => void) {
  const { connectServerWallet } = useServerWalletConnect()
  const tonConnectUi = useTonConnectUI()
  const { disconnectWallet, disconnectServerWallet, disconnectLocalWallet } =
    useWallectDisconnection()
  const { isWalletConnectedOnServer } = useIsWalletConnected()

  const connectWallet = (address: string) => {
    if (isWalletConnectedOnServer.value) {
      return disconnectServerWallet().then(() => {
        return connectServerWallet(address)
      })
    } else {
      return connectServerWallet(address)
    }
  }

  const tryConnectWallet = (address: string) => {
    connectWallet(address)
      .then(() => {
        onSuccess && onSuccess()
      })
      .catch(() => {
        disconnectLocalWallet()
      })
  }

  const { localWalletAddress } = useTonWallet()
  const onWalletConnected = subscribeToWalletConnected()

  const openConnectModal = () => {
    if (localWalletAddress.value) {
      // this is only called when local wallet is connected
      // but is not same as wallet on server
      disconnectWallet().then(() => {
        tonConnectUi.openModal()
      })
    } else {
      tonConnectUi.openModal()
    }
    onWalletConnected(tryConnectWallet)
  }

  return { connectWallet: openConnectModal }
}

export function useWallectDisconnection() {
  const { disconnectServerWallet } = useServerWalletDisonnect()
  const { disconnectLocalWallet } = useLocalWalletDisconnection()

  const disconnectWallet = () => {
    return disconnectServerWallet().then(() => {
      return disconnectLocalWallet()
    })
  }

  return {
    disconnectWallet,
    disconnectServerWallet,
    disconnectLocalWallet
  }
}
