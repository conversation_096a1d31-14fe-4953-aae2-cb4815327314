import { IS_HAPTICS_ENABLED } from '@/shared/constants/keys'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { localStorageService } from '@/shared/storage/localStorageService'
import { ref } from 'vue'

export function useSettings() {
  const initIsEnabledHaptics = hapticsService.isEnabledHaptics()
  const isHapticsEnabled = ref(initIsEnabledHaptics)
  const isMusicEnabled = ref(false)
  const isSoundEnabled = ref(false)

  const toggleHapticsEnabled = (value: boolean) => {
    isHapticsEnabled.value = value
    hapticsService.toggleHapticsEnabled(value)
    localStorageService.save(IS_HAPTICS_ENABLED, value)
  }

  return {
    isHapticsEnabled,
    isMusicEnabled,
    isSoundEnabled,
    toggleHapticsEnabled
  }
}
