import { useCreateRef } from '@/services/client/useCreateRef'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useToast } from '@/stores/toastStore.ts'
import { copyToClipboard } from '@/utils/clipboard'
import { createRefLink } from '@/utils/telegram'
import { shareURL } from '@telegram-apps/sdk'

export function useReferralLink() {
  const { showToast } = useToast()
  const { playerState } = usePlayerState()
  const { createRef } = useCreateRef()

  const getRefId = async () => {
    return new Promise<string>(resolve => {
      if (playerState?.value?.reflink) {
        resolve(playerState?.value?.reflink)
      } else {
        createRef().then(response => {
          resolve(response.reflink)
        })
      }
    })
  }

  const copyRefToClipboard = async () => {
    const refId = await getRefId()
    const link = createRefLink(import.meta.env.VITE_REFLINK_ORIGIN, refId)
    return copyToClipboard(link.toString())
  }

  const forwardRefLink = async () => {
    const refId = await getRefId()
    const link = createRefLink(import.meta.env.VITE_REFLINK_ORIGIN, refId)
    if (shareURL.isAvailable()) {
      shareURL(
        link,
        "\n🦄 Hey! I've collected 0.25 TON just for playing. \n\nJoin Uni Jump and start earning TON"
      )
    } else {
      showToast('Something went wrong', 'warning')
    }
  }

  return {
    copyRefToClipboard,
    forwardRefLink
  }
}
