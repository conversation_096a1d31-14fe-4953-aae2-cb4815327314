import {
  TonConnect<PERSON>,
  toUserFriendlyAddress,
  type ConnectedWallet,
  type Wallet
} from '@tonconnect/ui'
import { computed, inject, onMounted, onUnmounted, ref } from 'vue'

export function useTonConnectUI() {
  const tonConnectClient = inject<TonConnectUI>('ton-connect-client')

  return tonConnectClient as TonConnectUI
}

function isWallet(value: unknown) {
  return (
    value !== null &&
    typeof value === 'object' &&
    'device' in value &&
    'provider' in value &&
    'account' in value
  )
}

const getFriendlyAddress = (wallet: Wallet | null) =>
  wallet ? toUserFriendlyAddress(wallet.account.address || '', false) : ''

export function subscribeToWalletConnected() {
  const tonConnectUI = useTonConnectUI()
  const subscribe = (callback: (address: string) => void) => {
    const unsubscribe = tonConnectUI.onStatusChange(wallet => {
      if (wallet !== null) {
        callback(getFriendlyAddress(wallet)), unsubscribe()
      }
    })
  }
  return subscribe
}

export function useTonWallet() {
  const tonConnectUI = useTonConnectUI()
  const wallet = ref<Wallet | null>(tonConnectUI.wallet ?? null)

  const updateWallet = (value: ConnectedWallet | null): void => {
    if (isWallet(value)) {
      wallet.value = value
    } else {
      wallet.value = null
    }
  }

  const subscribeToWalletChanges = (): void => {
    if (!tonConnectUI) return

    if (isWallet(tonConnectUI.wallet)) {
      wallet.value = tonConnectUI.wallet
    }

    const unsubscribe = tonConnectUI.onStatusChange(updateWallet)
    onUnmounted(unsubscribe)
  }

  const localWalletAddress = computed(() => getFriendlyAddress(wallet.value))

  onMounted(subscribeToWalletChanges)

  return {
    wallet,
    localWalletAddress
  }
}

export function useLocalWalletDisconnection() {
  const tonConnectUI = useTonConnectUI()

  return { disconnectLocalWallet: tonConnectUI.disconnect.bind(tonConnectUI) }
}
