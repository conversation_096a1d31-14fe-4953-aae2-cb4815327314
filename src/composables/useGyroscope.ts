import { useGyroscopeStore } from '@/stores/gyroscopeStore'

export function useHasGyroscopeAccess() {
  const gyroscopeRequestStore = useGyroscopeStore()

  const checkGyroscopeAccess = () => {
    const gyroscopeAccessProvidedEvent = (e: DeviceMotionEvent) => {
      clearTimeout(timeoutId)
      if (
        e.accelerationIncludingGravity?.x === null ||
        e.accelerationIncludingGravity?.x === undefined
      ) {
        // its a desktop - do nothing
      }
    }

    window.addEventListener('devicemotion', gyroscopeAccessProvidedEvent, { once: true })

    const timeoutId = setTimeout(() => {
      window.removeEventListener('devicemotion', gyroscopeAccessProvidedEvent)
      gyroscopeRequestStore.setRequest(true)
    }, 1000) // not less than 600ms and not longer than loading screen min delay
  }

  return {
    checkGyroscopeAccess
  }
}
