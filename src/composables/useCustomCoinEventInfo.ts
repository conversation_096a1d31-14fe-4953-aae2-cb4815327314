import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { useCustomCoinUserInfo } from '@/services/client/useGameEvent.ts'
import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import type { PlayerStateResponse } from '@/services/openapi'
import { computed, ref, type Ref } from 'vue'

export function useCustomCoinEventInfo(
  playerState: Ref<PlayerStateResponse, PlayerStateResponse> | Ref<undefined, undefined>
) {
  const { now } = useNowTimestamp()
  const { userInfo } = useCustomCoinUserInfo()

  const isCustomCoinEventPromoTimeOver = ref(true)

  const customCoinEvent = computed(() => {
    return playerState.value?.customCoinEvent ?? null
  })

  const isCustomCoinEventPromoActive = computed(() => {
    const isActive =
      !!customCoinEvent.value && customCoinEvent.value.startedAt > (now.value?.utc ?? 0)
    if (isActive) {
      isCustomCoinEventPromoTimeOver.value = false
    }
    return isActive
  })

  const isCustomCoinEventActive = computed(() => {
    if (customCoinEvent.value === null) return false
    return customCoinEvent.value.startedAt <= (now.value?.utc ?? 0)
  })

  const customCoinTimeLeftInSeconds = computed(() => {
    if (customCoinEvent.value === null) return 0
    return customCoinEvent.value.endsAt - (now.value?.utc ?? 0)
  })

  const customCoinPromoTimeLeftInSeconds = computed(() => {
    if (customCoinEvent.value === null) return 0
    return customCoinEvent.value.startedAt - (now.value?.utc ?? 0)
  })

  const customCoinEventBannerType = computed(() => {
    return customCoinEvent.value?.banner ?? null
  })

  const hasMetCustomCoinRequirement = computed(() => {
    return playerState.value!.customCoinEvent?.hasMetRequirement
  })

  const hasCustomCoinLeaderboard = computed(() => {
    return playerState.value!.customCoinEvent?.hasLeaderboard ?? false
  })

  const hasCustomCoinReward = computed(() => {
    return playerState.value!.customCoinEventReward?.reward?.amount ?? 0
  })

  const userData = computed(() => {
    return {
      rank: userInfo.value?.rank ?? 0,
      score: userInfo.value?.coinsCollected ?? 0,
      balance: userInfo.value?.reward
        ? getCurrencyRealAmount(userInfo.value.reward.amount, userInfo.value.reward.currency)
        : 0,
      currency: userInfo.value?.reward?.currency ?? 'hard',
      league: userInfo.value?.leagueLevel ?? 1
    }
  })

  return {
    isCustomCoinEventPromoTimeOver,
    isCustomCoinEventActive,
    isCustomCoinEventPromoActive,
    customCoinTimeLeftInSeconds,
    customCoinPromoTimeLeftInSeconds,
    customCoinEventBannerType,
    hasMetCustomCoinRequirement,
    hasCustomCoinLeaderboard,
    hasCustomCoinReward,
    userData
  }
}
