import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'

export function useTime() {
  const { getNow } = useNowTimestamp()

  const getCurrentDayEnd = (now: number) => {
    const startOfDay = Math.floor(now / 86400) * 86400 // Start of the day in seconds
    return startOfDay + 86399 // End of the day in seconds
  }

  const getNextDayStart = (now: number) => {
    const startOfDay = Math.floor(now / 86400) * 86400 // Start of the day in seconds
    return startOfDay + 86400 // End of the day in seconds
  }

  const getSecondsToNextDayStart = async () => {
    const now = await getNow()
    return getNextDayStart(now) - now
  }

  return {
    getCurrentDayEnd,
    getNextDayStart,
    getSecondsToNextDayStart
  }
}
