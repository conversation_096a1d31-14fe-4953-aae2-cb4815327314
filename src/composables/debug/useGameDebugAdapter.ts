import { eventBus } from '@/services/eventBus/uiEventBus'
import { UI_DEBUG_EVENTS } from '@/shared/constants/debug/uiDebugEvents'

export function useGameDebugAdapter() {
  const changePlatforms = () => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_PLATFORMS)
  }

  const changeCharacter = () => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_CHARACTER)
  }

  const toggleBoots = () => {
    eventBus.emit(UI_DEBUG_EVENTS.TOGGLE_BOOTS)
  }

  const toggleAim = () => {
    eventBus.emit(UI_DEBUG_EVENTS.TOGGLE_AIM)
  }

  const toggleControls = () => {
    eventBus.emit(UI_DEBUG_EVENTS.TOGGLE_CONTROLS)
  }

  const changeTickets = () => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_TICKETS)
  }

  const changeBackground = () => {
    console.log('Emitting CHANGE_BACKGROUND event')
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_BACKGROUND)
  }

  const toggleDebug = () => {
    eventBus.emit(UI_DEBUG_EVENTS.TOGGLE_DEBUG)
  }

  const changeAngularVelocity = (value: number) => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_ANGULAR_VELOCITY, value)
  }

  const changeMaxVelocity = (value: number) => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_MAX_VELOCITY, value)
  }

  const changeSmoothingFactor = (value: number) => {
    eventBus.emit(UI_DEBUG_EVENTS.CHANGE_SMOOTHING_FACTOR, value)
  }

  return {
    changePlatforms,
    changeCharacter,
    changeTickets,
    changeBackground,
    toggleDebug,
    changeAngularVelocity,
    changeMaxVelocity,
    changeSmoothingFactor,
    toggleBoots,
    toggleAim,
    toggleControls
  }
}
