import { isTMA, setMiniAppHeaderColor, type RGB } from '@telegram-apps/sdk'
import { nextTick } from 'vue'
import { useRouter } from 'vue-router'

const LOADING_HEADER_COLOR = '#9d57e9'
const GAME_HEADER_COLOR = '#73b7ff'
const MENU_HEADER_COLOR_RGB = '#28599C'
const MENU_HEADER_COLOR_VAR = 'var(--menu-color)'

export function useHeaderColor() {
  const router = useRouter()

  const setHeaderColor = (gameHeaderColor: string, telegramColor: RGB) => async () => {
    const headerEl = document.querySelector('#header-bar')
    headerEl?.setAttribute('style', `background-color: ${gameHeaderColor}`)
    if ((await isTMA()) && setMiniAppHeaderColor.isSupported()) {
      setMiniAppHeaderColor(telegramColor)
    }
  }

  const setHeaderForHome = setHeaderColor('transparent', GAME_HEADER_COLOR)
  const setHeaderForGame = setHeaderForHome
  const setHeaderForMenu = setHeaderColor(MENU_HEADER_COLOR_VAR, MENU_HEADER_COLOR_RGB)
  const setHeaderForLoadingScreen = setHeaderColor(LOADING_HEADER_COLOR, LOADING_HEADER_COLOR)

  const setHeaderAccordingToPath = (path: string) => {
    if (path === '/game' || path.includes('/wheel')) {
      setHeaderForGame()
    } else if (path.includes('/menu')) {
      setHeaderForMenu()
    } else {
      setHeaderForHome()
    }
  }

  const registerHeaderAutoChange = () => {
    router.afterEach(to => setHeaderAccordingToPath(to.path))
    const currentPath = router.currentRoute.value.path
    nextTick(() => setHeaderAccordingToPath(currentPath))
  }

  return {
    registerHeaderAutoChange,
    setHeaderAccordingToPath,
    setHeaderForHome,
    setHeaderForGame,
    setHeaderForMenu,
    setHeaderForLoadingScreen
  }
}
