import { ref } from 'vue'

const TIME_TO_CHARGE = 301
const TIME_STEP = 20
const STEPS_COUNT = TIME_TO_CHARGE / TIME_STEP

export function useRewardCounter(onCollected: Function) {
  const targetValue = ref(0)
  const sourceValue = ref(0)
  const isCollecting = ref(false)

  const charge = () => {
    if (isCollecting.value || !sourceValue.value) return
    isCollecting.value = true
    const step = Math.ceil(sourceValue.value / STEPS_COUNT)
    const interval = setInterval(() => {
      if (sourceValue.value - step <= 0) {
        targetValue.value += sourceValue.value
        sourceValue.value = 0
        isCollecting.value = false
        onCollected()
        clearInterval(interval)
        return
      }
      targetValue.value += step
      sourceValue.value -= step
    }, TIME_STEP)
  }

  const setValues = (currentBalance: number, newReward: number) => {
    targetValue.value = currentBalance
    sourceValue.value = newReward
  }

  return {
    targetValue,
    sourceValue,
    isCollecting,
    setValues,
    charge
  }
}
