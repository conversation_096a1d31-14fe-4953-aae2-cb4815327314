import { MIN_TRANSITION_ANIMATION_DELAY } from '@/constants'
import { getCurrencyFormattedAmount } from '@/constants/currency'
import type { SpawnedTokenData } from '@/game/core/CoreGameplay/Collectables/TokenCollectable.ts'
import { useAchievementsList } from '@/services/client/useAchievements.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { eventBus } from '@/services/eventBus/uiEventBus'
import type { StackableBoosterType, UnattainedAchievement } from '@/services/openapi'
import { logger } from '@/shared/Logger'
import { GAME_EVENTS, UI_EVENTS } from '@/shared/constants/uiEvents'
import { cachedAchievementsData } from '@/shared/storage/cachedAchievementsData.ts'
import { boostersCache } from '@/shared/storage/cachedBoosters'
import { cachedPlayerState } from '@/shared/storage/cachedPlayerState'
import { useLifesStore } from '@/stores/livesStore'
import { useNotification } from '@/stores/notificationStore.ts'
import type { TimeBoundBoosterType } from '@/types'
import { customTruncate } from '@/utils/number.ts'
import { reactive, ref } from 'vue'
import { useFadeTransitionAnimation } from './useFadeTransitionAnimation'

type SessionData = {
  id: number
  highestScore: number
  totalScoreEvent?: number
  highestScoreEvent?: number
}

type GameAdapterConfig = {
  canRevive: boolean
  onGameOver: Function
}

export function useGameAdapter(config?: GameAdapterConfig) {
  const { playerState } = usePlayerState()
  const livesStore = useLifesStore()
  const notificationsStore = useNotification()
  const sessionState = reactive({
    sessionId: -1,
    score: '0',
    highScore: '0',
    highScoreEvent: '0',
    totalScoreEvent: '0'
  })
  const boostersRef = ref<StackableBoosterType[]>([])
  const isSessionUpdated = ref(false)
  const ton = ref(0)
  const customCoin = ref('0')
  const battleCoin = ref('0')
  const dynamicCoin = ref('0')
  const puzzleCoin = ref('0')
  const nextTon: { height: string | undefined; amount: string | undefined } = reactive({
    height: undefined,
    amount: undefined
  })
  const nextEventCoin: { height: string | undefined; amount: string | undefined } = reactive({
    height: undefined,
    amount: undefined
  })
  const tickets = ref('0')
  const isRevive = ref(false)
  const isGameOver = ref(false)
  const isTonLimitEnded = ref(false)
  const mobsKillCount = ref(0)
  const isPortrait = ref(window.innerHeight > window.innerWidth)
  const { runTransitionAnimation, stopTransitionAnimation } = useFadeTransitionAnimation()
  const { achievementsList } = useAchievementsList()

  eventBus.on(GAME_EVENTS.ORIENTATION_STATE_CHANGED, (isPortraitNow: boolean) => {
    isPortrait.value = isPortraitNow
    console.log(`Orientation state updated: ${isPortrait.value ? 'Portrait' : 'Landscape'}`)
  })

  const resetState = () => {
    sessionState.score = '0'
    ton.value = 0
    customCoin.value = '0'
    battleCoin.value = '0'
    dynamicCoin.value = '0'
    puzzleCoin.value = '0'
    tickets.value = '0'
    isRevive.value = false
    isGameOver.value = false
    isTonLimitEnded.value = false
    mobsKillCount.value = 0
    nextTon.height = undefined
    nextTon.amount = undefined
    nextEventCoin.height = undefined
    nextEventCoin.amount = undefined
    isSessionUpdated.value = false
  }

  const startGame = (activeBoosters: StackableBoosterType[] = []) => {
    console.log('start activeBoosters', activeBoosters)
    resetState()
    cachedPlayerState.playerState = playerState.value
    boostersCache.active = activeBoosters
    boostersRef.value = activeBoosters
    eventBus.emit(UI_EVENTS.START)
    livesStore.consumeLife()
    if (__DEV__) logger.log('use Game Adapter', '[UI] click on play')
    subscribeToEvents()
  }

  const restartGame = (activeBoosters: StackableBoosterType[] = []) => {
    runTransitionAnimation()
    eventBus.offAll(GAME_EVENTS.GAME_PRELOAD_ENDED)
    setTimeout(() => {
      resetState()
      console.log('restart activeBoosters', activeBoosters)
      cachedPlayerState.playerState = playerState.value
      boostersCache.active = activeBoosters
      boostersRef.value = activeBoosters
      eventBus.emit(UI_EVENTS.RESTART)
      cachedAchievementsData.playerAchievements = achievementsList?.value

      livesStore.consumeLife()
      if (__DEV__) logger.log('use Game Adapter', '[UI] click on restart')
      stopTransitionAnimation()
    }, MIN_TRANSITION_ANIMATION_DELAY)
  }

  const pauseGame = () => {
    eventBus.emit(UI_EVENTS.PAUSE)
  }

  const unpauseGame = () => {
    if (isPortrait.value) {
      eventBus.emit(UI_EVENTS.UNPAUSE)
      console.log('Game unpaused: Device is in portrait mode')
    } else {
      console.warn('Cannot unpause: Device is not in portrait mode')
    }
  }

  const continueGame = () => {
    isRevive.value = false
    eventBus.emit(UI_EVENTS.GAME_CONTINUED)
  }

  const goToRevive = (
    tonData: SpawnedTokenData | undefined,
    eventCoinData: SpawnedTokenData | undefined,
    killedMobsCount: number
  ) => {
    nextTon.height = tonData?.height
    nextTon.amount = tonData && customTruncate(tonData.amount)
    nextEventCoin.height = eventCoinData?.height
    nextEventCoin.amount = eventCoinData && eventCoinData.amount.toString()
    mobsKillCount.value = killedMobsCount
    isRevive.value = true
  }

  const goToGameOver = () => {
    config?.onGameOver()
    isRevive.value = false
    isGameOver.value = true
    eventBus.emit(UI_EVENTS.GAME_OVER)
  }

  const destroyGame = (callback?: Function) => {
    const onGameDestroyed = () => {
      unsubscribeFromEvents()
      if (callback) {
        callback()
      }
    }
    eventBus.emit(UI_EVENTS.DESTROY)
    onGameDestroyed()
  }

  const updateSession = () => {
    isSessionUpdated.value = true
  }

  const scoreUpdate = (currentScore: number) => {
    sessionState.score = currentScore.toFixed(0)
  }

  const tonUpdate = (tonAmount: number) => {
    ton.value = getCurrencyFormattedAmount(tonAmount, 'ton')
  }

  const customCoinUpdate = (customCoinAmount: number) => {
    customCoin.value = customCoinAmount.toString()
  }

  const battleCoinUpdate = (battleCoinAmount: number) => {
    battleCoin.value = battleCoinAmount.toString()
  }

  const dynamicCoinUpdate = (dynamicCoinAmount: number) => {
    dynamicCoin.value = dynamicCoinAmount.toString()
  }

  const puzzleCoinUpdate = (puzzleCoinAmount: number) => {
    puzzleCoin.value = puzzleCoinAmount.toString()
  }

  const showAchievement = (achievement: UnattainedAchievement) => {
    notificationsStore.showNotification(achievement)
  }

  const ticketsUpdate = (ticketsCount: number) => {
    tickets.value = ticketsCount.toFixed(0)
  }

  const onGameEnded = (
    session: SessionData,
    tonData: SpawnedTokenData | undefined,
    eventCoinData: SpawnedTokenData | undefined,
    shouldShowLimitEnded = false,
    killedMobsCount: number
  ) => {
    if (!session) return
    sessionState.sessionId = session.id
    sessionState.highScore = (session.highestScore ?? 0).toFixed(0)
    sessionState.highScoreEvent = (session.highestScoreEvent ?? 0).toFixed(0)
    sessionState.totalScoreEvent = (session.totalScoreEvent ?? 0).toFixed(0)
    isTonLimitEnded.value = shouldShowLimitEnded
    if (config?.canRevive) {
      goToRevive(tonData, eventCoinData, killedMobsCount)
    } else {
      goToGameOver()
    }
  }

  const onBoosterEnded = ({ boosterType }: { boosterType: TimeBoundBoosterType }) => {
    const TIME_BOOSTER_TO_STACKABLE: Record<TimeBoundBoosterType, StackableBoosterType> = {
      timeBoundMagneticField: 'stackableMagneticField',
      timeBoundAimbot: 'stackableAimbot',
      timeBoundJumper: 'stackableJumper'
    }
    boostersRef.value = boostersRef.value.filter(
      booster => booster !== TIME_BOOSTER_TO_STACKABLE[boosterType]
    )
  }

  const subscribeToEvents = () => {
    eventBus.on(GAME_EVENTS.BOOSTER_ENDED, onBoosterEnded)
    eventBus.on(GAME_EVENTS.UPDATE_SCORE, scoreUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_TON, tonUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_CUSTOM_COIN_SCORE, customCoinUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_BATTLE_COIN_SCORE, battleCoinUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_DYNAMIC_COIN_SCORE, dynamicCoinUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_FRAGMENT_SCORE, puzzleCoinUpdate)
    eventBus.on(GAME_EVENTS.UPDATE_TICKETS_SCORE, ticketsUpdate)
    eventBus.on(GAME_EVENTS.GAME_ENDED, onGameEnded)
    eventBus.on(GAME_EVENTS.GAME_PRELOAD_ENDED, stopTransitionAnimation)
    eventBus.on(GAME_EVENTS.SESSION_UPDATE_LOCK_RELEASED, updateSession)
    eventBus.on(GAME_EVENTS.ACHIEVEMENT_UNLOCKED, showAchievement)
  }

  const unsubscribeFromEvents = () => {
    eventBus.off(GAME_EVENTS.BOOSTER_ENDED, onBoosterEnded)
    eventBus.off(GAME_EVENTS.UPDATE_SCORE, scoreUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_TON, tonUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_CUSTOM_COIN_SCORE, customCoinUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_BATTLE_COIN_SCORE, battleCoinUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_DYNAMIC_COIN_SCORE, dynamicCoinUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_FRAGMENT_SCORE, puzzleCoinUpdate)
    eventBus.off(GAME_EVENTS.UPDATE_TICKETS_SCORE, ticketsUpdate)
    eventBus.offAll(GAME_EVENTS.GAME_ENDED)
    eventBus.offAll(GAME_EVENTS.GAME_PRELOAD_ENDED)
    eventBus.off(GAME_EVENTS.SESSION_UPDATE_LOCK_RELEASED, updateSession)
    eventBus.off(GAME_EVENTS.ACHIEVEMENT_UNLOCKED, showAchievement)
  }

  return {
    sessionState,
    tickets,
    ton,
    customCoin,
    battleCoin,
    dynamicCoin,
    puzzleCoin,
    isRevive,
    isGameOver,
    isTonLimitEnded,
    mobsKillCount,
    nextTon,
    nextEventCoin,
    isSessionUpdated,
    boostersRef,
    startGame,
    pauseGame,
    unpauseGame,
    continueGame,
    goToGameOver,
    restartGame,
    destroyGame
  }
}
