import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useTime } from '@/composables/useTime.ts'
import { useDailyTasks } from '@/services/client/useTasks.ts'
import { onMounted, onUnmounted } from 'vue'

export function useDailyTasksTimer() {
  const { refetchTasks } = useDailyTasks()
  const { getSecondsToNextDayStart } = useTime()
  const { hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('dailyTask', {
    onTimerEnd: async () => {
      await refetchTasks()
      await recalculateDailyTasksTime()
    }
  })

  const recalculateDailyTasksTime = async () => {
    await getSecondsToNextDayStart().then(result => {
      initTimerWithTotal(result)
    })
  }

  onMounted(async () => {
    await recalculateDailyTasksTime()
    window.addEventListener('visibilitychange', async () => {
      await recalculateDailyTasksTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateDailyTasksTime()
    })
  })

  return {
    hours,
    minutes,
    seconds
  }
}
