import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export function useLootboxTimer() {
  const isFreeLootboxAvailable = ref(false)

  const { playerState } = usePlayerState()

  const { getNow } = useNowTimestamp()
  const { hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('lootboxTimer', {
    onTimerEnd: () => {
      isFreeLootboxAvailable.value = true
    }
  })

  const freeLootboxAvailableAtTime = computed(() => {
    return playerState.value?.lootboxesInfo?.freeAvailableAt ?? 0
  })

  const recalculateFreeLootboxAvailableTime = async (availableAt: number) => {
    isFreeLootboxAvailable.value = false
    const now = await getNow()
    const endsAt = availableAt
    const timeLeft = Math.floor(endsAt - now)
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    } else {
      isFreeLootboxAvailable.value = true
    }
  }

  watch(
    freeLootboxAvailableAtTime,
    () => {
      recalculateFreeLootboxAvailableTime(freeLootboxAvailableAtTime.value)
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateFreeLootboxAvailableTime(freeLootboxAvailableAtTime.value)
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateFreeLootboxAvailableTime(freeLootboxAvailableAtTime.value)
    })
  })

  return { isFreeLootboxAvailable, hours, minutes, seconds }
}
