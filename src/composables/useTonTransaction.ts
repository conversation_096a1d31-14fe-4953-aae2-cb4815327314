import { useTonConnectUI } from '@/composables/useLocalWallet'
import { TON_WALLET_ADDRESS } from '@/constants'
import { CHAIN } from '@tonconnect/ui'
import { Cell } from '../utils/ton/Cell'
// @ts-ignore
import { bytesToBase64 } from '../utils/ton/Utils'

export function toNano(ton: number) {
  return ton * 1e9
}

export function useTonTransaction() {
  const tonConnectUI = useTonConnectUI()

  const sendTransaction = async (price: number, payload = '') => {
    const comment = new Cell()
    comment.bits.writeUint(0, 32)
    comment.bits.writeString(payload)
    const payloadInBase64 = bytesToBase64(await comment.toBoc())

    const transaction = {
      validUntil: Date.now() + 1e6, // 16 minutes
      network: CHAIN.MAINNET,
      messages: [
        {
          address: TON_WALLET_ADDRESS,
          amount: price.toString(),
          payload: payloadInBase64
        }
      ]
    }

    return tonConnectUI
      .sendTransaction(transaction)
      .then(result => !!result.boc)
      .catch(error => {
        console.error('Failed to send transaction', error)
        return false
      })
  }

  return { sendTransaction }
}
