import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { ref } from 'vue'

export const useHasUserSeen = (id: string) => {
  const hasSeen = ref<boolean>(true)

  cloudStorageService.load<boolean>(id).then(response => {
    hasSeen.value = !!response
  })

  const markAsSeen = () => {
    if (hasSeen.value) return
    hasSeen.value = true
    cloudStorageService.save(id, true)
  }

  return {
    hasSeen,
    markAsSeen
  }
}
