import { Boot } from '@/game/core/Core/Boot'
import { eventBus } from '@/services/eventBus/uiEventBus'
import { SpinePlugin } from '@esotericsoftware/spine-phaser'

const config: Phaser.Types.Core.GameConfig = {
  type: Phaser.WEBGL,
  parent: 'game-container',
  width: window.parent.outerWidth,
  height: window.parent.outerHeight,
  transparent: true,
  //pipeline: undefined,
  physics: {
    default: 'arcade',
    arcade: {
      debug: false,
      overlapBias: 8,
      fps: 60,
      timeScale: 1,
      fixedStep: false
    }
  },

  render: {
    antialias: true,
    pixelArt: false,
    premultipliedAlpha: true,
    powerPreference: 'high-performance',
    clearBeforeRender: true,
    roundPixels: false
  },
  scale: {
    mode: Phaser.Scale.NONE,
    autoCenter: Phaser.Scale.CENTER_BOTH,
    width: window.parent.outerWidth,
    height: window.parent.outerHeight
  },
  failIfMajorPerformanceCaveat: true,
  fps: {
    target: 60,
    min: 30,
    deltaHistory: 15,
    panicMax: 120,
    smoothStep: true,
    forceSetTimeOut: false
  },
  plugins: {
    scene: [{ key: 'spine.SpinePlugin', plugin: SpinePlugin, mapping: 'spine' }]
  }
}

export function initGame() {
  new Boot(config, eventBus)
}
