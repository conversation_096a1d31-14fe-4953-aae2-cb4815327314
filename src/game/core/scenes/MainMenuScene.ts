import { gameplayAssetsPreloader } from '@/game/core/CoreGameplay/GameplayAssetsPreloader.ts'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import { Scene } from 'phaser'

export class MainMenuScene extends Scene {
  private readonly uiEventBus: EventBus

  constructor(uiEventBus: EventBus) {
    super()
    this.uiEventBus = uiEventBus
  }

  preload() {
    gameplayAssetsPreloader
      .preload(this)
      .then(() => {
        this.uiEventBus.emit(GAME_EVENTS.ASSETS_PRELOAD_ENDED)
        console.log('assets preloaded')
      })
      .catch(error => {
        this.uiEventBus.emit(GAME_EVENTS.ASSETS_PRELOAD_ERROR)
        console.log(error)
      })
  }
}
