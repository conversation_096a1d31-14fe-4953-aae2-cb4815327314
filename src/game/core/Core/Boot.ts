import { MainMenuScene } from '@/game/core/scenes/MainMenuScene'
import { Logger, logger } from '@/shared/Logger'
import { GAME_EVENTS, UI_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import Phaser from 'phaser'
import { GameScene, delay } from '../scenes/GameScene'

export class Boot extends Phaser.Game {
  public uiEventBus: EventBus
  private logger: Logger = logger

  constructor(config: Phaser.Types.Core.GameConfig, uiEventBus: EventBus) {
    super(config)
    this.uiEventBus = uiEventBus

    this.scene.add(MainMenuScene.name, new MainMenuScene(uiEventBus))
    this.scene.add(GameScene.name, new GameScene(uiEventBus))

    this.uiEventBus.on(UI_EVENTS.DESTROY, this.clearGameState, this)
    this.uiEventBus.on(UI_EVENTS.START, this.showGame.bind(this))
    this.uiEventBus.on(UI_EVENTS.RESTART, this.restartGame.bind(this))
    this.uiEventBus.on(UI_EVENTS.PAUSE, this.pauseGame.bind(this))
    this.uiEventBus.on(UI_EVENTS.UNPAUSE, this.resumeGame.bind(this))
    this.scene.start(MainMenuScene.name)

    if (__DEV__) {
      this.logger.toggleLogging()
      this.logger.log('Boot', '[UI] subscribe on events')
    }
  }

  private pauseGame() {
    if (__DEV__) this.logger.log('Boot', 'pause game')
    this.isPaused = true
    this.pause()
  }

  private resumeGame() {
    this.isPaused = false
    this.resume()
  }

  restartGame() {
    console.log('restart')
    delay(100).then(() => {
      this.showGame(true)
    })
  }

  showGame(restart: boolean = false): void {
    if (this.scene.isActive(MainMenuScene.name)) {
      const mainMenu = this.scene.getScene(MainMenuScene.name)
      if (mainMenu) {
        this.scene.stop(MainMenuScene.name)
      }
    }
    try {
      let gameScene = this.scene.getScene(GameScene.name) as GameScene
      if (this.isPaused) {
        this.resumeGame()
      }
      if (gameScene) {
        gameScene.shutdown()
        if (restart) {
          gameScene.setRestart()
        }
        gameScene.scene.stop()
        gameScene.scene.start()
        console.log('game scene restarted')
      } else {
        gameScene = new GameScene(this.uiEventBus)
        gameScene.create().then(() => {
          this.scene.add(GameScene.name, gameScene)
          this.scene.start(GameScene.name, { retain: false })
          gameScene.shutdown()
        })
      }
    } catch (error: any) {
      console.error('Error starting game scene:', error)
    }
  }

  clearGameState(): void {
    if (this.scene.isActive(GameScene.name)) {
      const gameScene = this.scene.getScene(GameScene.name)
      if (gameScene) {
        ;(gameScene as GameScene).shutdown()
      }
      this.scene.stop(GameScene.name)
    }
    this.uiEventBus.off(UI_EVENTS.DESTROY, this.clearGameState)
    this.uiEventBus.emit(GAME_EVENTS.GAME_STATE_CLEARED)
  }
}
