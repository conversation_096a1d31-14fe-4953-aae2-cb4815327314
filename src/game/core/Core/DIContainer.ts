import { logger } from '@/shared/Logger'

type Constructor<T> = new (...args: any[]) => T

class DIContainer {
  private services = new Map<
    Constructor<any>,
    {
      constructor?: Constructor<any>
      dependencies: Array<Constructor<any> | any>
      instance?: any
    }
  >()

  register<T>(
    serviceType: Constructor<T>,
    dependencies: Array<Constructor<any> | any> = [],
    singleton: boolean = false
  ): void {
    this.services.set(serviceType, {
      constructor: serviceType,
      dependencies,
      instance: singleton ? undefined : null
    })
  }

  registerInstance<T>(serviceType: Constructor<T>, instance: T): void {
    this.services.set(serviceType, { constructor: undefined, dependencies: [], instance })
  }

  resolve<T>(serviceType: Constructor<T>): T {
    const service = this.services.get(serviceType)
    if (!service) {
      const error = new Error(`Service ${serviceType.name} not found`)
      if (__DEV__) logger.error(error.message)
      throw error
    }

    if (service.instance !== null) {
      if (service.instance === undefined) {
        const resolvedDependencies = service.dependencies.map(dep =>
          this.isConstructor(dep) ? this.resolve(dep) : dep
        )
        service.instance = new service.constructor!(...resolvedDependencies)
      }
      return service.instance
    }

    // For non-singletons or first-time singleton creation
    const resolvedDependencies = service.dependencies.map(dep =>
      this.isConstructor(dep) ? this.resolve(dep) : dep
    )
    return new service.constructor!(...resolvedDependencies)
  }

  get<T>(serviceType: Constructor<T>): T {
    const service = this.services.get(serviceType)
    if (!service) {
      const error = new Error(`Service ${serviceType.name} not found`)
      if (__DEV__) logger.error(error.message)
      throw error
    }

    if (service.instance !== null && service.instance !== undefined) {
      return service.instance
    }

    const error = new Error(`Service ${serviceType.name} instance not available`)
    if (__DEV__) logger.error(error.message)
    throw error
  }

  private isConstructor(value: any): value is Constructor<any> {
    return typeof value === 'function' && value.prototype && value.prototype.constructor === value
  }
}

export default DIContainer
