import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { ChunkInfo } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { InteractionType } from '@/game/core/MapGen/GameSessionManager'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { HapticsService, hapticsService } from '@/shared/haptics/hapticsService'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import Phaser from 'phaser'
import { getRandomNumberInRange } from '../../HelperFunctions'
import { Rect } from '../../MapGen/Utils'
import { BoostersConsts } from '../Constants/BoostersConsts'
import type { BoosterOptions } from './BoosterOptions'
import Sprite = Phaser.GameObjects.Sprite

export class BaseBooster {
  protected sprite: Sprite | null = null
  protected boosterGroup: Phaser.Physics.Arcade.StaticGroup
  protected boosterType: EntityType
  protected body!: Phaser.Physics.Arcade.Sprite
  protected staticBody!: Phaser.Physics.Arcade.StaticBody
  protected scene: GameScene | null = null
  public isUsed = false
  protected gameInterface: GameInterface
  protected chunkInfo: ChunkInfo
  protected haptics: HapticsService = hapticsService
  private platformOffsetX: number
  protected bounceTween: Phaser.Tweens.Tween | null = null
  public isActive!: boolean
  public nextFree: this | null = null

  constructor(options: BoosterOptions) {
    const { boosterGroup, scene, position, entityType, gameInterface, chunkInfo } = options
    this.boosterGroup = boosterGroup
    this.scene = scene
    this.boosterType = entityType
    this.gameInterface = gameInterface
    this.chunkInfo = chunkInfo

    const { x, y } = position
    const texture = entityType.toString()
    this.createOrUpdateBoosterSprite(texture, x, y)
    this.configureBoosterPhysics()
    this.platformOffsetX = this.adjustOffsetToBoosterType()
  }

  private configureBoosterPhysics(): void {
    this.body = this.sprite as Phaser.Physics.Arcade.Sprite
    this.scene!.physics.add.existing(this.sprite!)
    this.body.setImmovable(true)
    this.staticBody = this.sprite!.body as Phaser.Physics.Arcade.StaticBody
    this.staticBody.checkCollision.up = true
    this.staticBody.checkCollision.down = false
    this.staticBody.checkCollision.left = false
    this.staticBody.checkCollision.right = false
    this.body.refreshBody()
  }

  private createOrUpdateBoosterSprite(textureKey: string, x: number, y: number): void {
    const currentFrame = `${textureKey}.png`

    if (!this.sprite || !this.sprite.texture || !this.sprite.frame) {
      this.sprite = this.boosterGroup.create(x, y, AtlasNames.BOOSTERS, currentFrame)
      this.sprite!.setOrigin(0, 0)
      this.sprite!.setDepth(DepthOrder.Boosters)
      this.sprite!.scale = 0.5
    } else {
      if (
        this.sprite.texture.key !== AtlasNames.BOOSTERS ||
        this.sprite.frame.name !== currentFrame
      ) {
        try {
          this.sprite.setTexture(AtlasNames.BOOSTERS, currentFrame)
        } catch (err) {
          console.warn('Failed to set texture on existing booster sprite, recreating sprite', err)
          this.sprite.destroy()
          this.sprite = this.boosterGroup.create(x, y, AtlasNames.BOOSTERS, currentFrame)
          this.sprite!.setOrigin(0, 0)
          this.sprite!.setDepth(DepthOrder.Boosters)
          this.sprite!.scale = 0.5
        }
      }
      if (this.sprite!.x !== x || this.sprite!.y !== y) {
        this.sprite!.setPosition(x, y)
      }
    }

    this.startBounceTween()
  }

  reset(options: BoosterOptions): void {
    const { boosterGroup, position, entityType, chunkInfo, gameInterface } = options
    this.boosterType = entityType
    this.gameInterface = gameInterface
    this.chunkInfo = chunkInfo

    const { x, y } = position
    const texture = entityType.toString()
    this.sprite!.setPosition(x, y)
    this.setVisualOffset(0)
    this.createOrUpdateBoosterSprite(texture, this.sprite!.x, this.sprite!.y)
    this.configureBoosterPhysics()
    this.setVisible(true)
    this.setActive(true)
    this.isUsed = false
    this.platformOffsetX = this.adjustOffsetToBoosterType()
    this.sprite?.setAlpha(1)

    if (this.body && this.body.body) {
      this.body.body.enable = true
      this.body.body.checkCollision.none = false
      this.body.body.checkCollision.up = true
      this.body.refreshBody()
    }

    boosterGroup.add(this.sprite!)
  }

  setCustomYOffset() {}

  setVisualOffset(height: number) {
    this.sprite!.y += height
    this.staticBody.y += height
  }

  returnToPool(): void {
    this.setActive(false)
    this.setVisible(false)
    if (this.body && this.body.body) {
      this.body.body.enable = false
      this.body.body.checkCollision.none = true
    }
    this.platformOffsetX = 0
    this.boosterGroup.remove(this.sprite!)
    this.isUsed = true
  }

  applyEffect(playerPos: Vector2): void {
    this.isUsed = true

    if (__DEV__) {
      const playerX = playerPos.x
      const playerY = Math.round(-playerPos.y - this.chunkInfo.prevChunkCumulativeLenght) - 31.5
      const objectX = this.sprite!.x
      const objectY = Math.round(-this.sprite!.y - this.chunkInfo.prevChunkCumulativeLenght)
      const playerRect = new Rect(playerX, playerY, 46, 63)
      const rect = new Rect(
        objectX,
        objectY,
        ENTITIES_SIZES[this.boosterType][0],
        ENTITIES_SIZES[this.boosterType][1]
      )
      const intersects = playerRect.intersects(rect, 25)
    }

    this.gameInterface.registerInteraction(
      this.chunkInfo,
      InteractionType.BoosterCollide,
      this.sprite!.x,
      this.sprite!.y
    )
  }

  adjustOffsetToBoosterType() {
    let offset
    switch (this.boosterType) {
      case EntityType.BoosterBigFly:
        offset = getRandomNumberInRange(BoostersConsts.BIG_FLY_OFFSET_X)
        break
      case EntityType.BoosterSmallFly:
        offset = getRandomNumberInRange(BoostersConsts.SMALL_FLY_OFFSET_X)
        break
      case EntityType.BoosterBigJump:
        offset = getRandomNumberInRange(BoostersConsts.BIG_JUMP_OFFSET_X)
        break
      case EntityType.BoosterSmallJump:
        offset = getRandomNumberInRange(BoostersConsts.SMALL_JUMP_OFFSET_X)
        break

      case EntityType.BoosterShield:
        offset = getRandomNumberInRange(BoostersConsts.SHIELD_OFFSET_X)
        break
      default:
        offset = getRandomNumberInRange(10)
        break
    }
    return offset
  }

  syncPosition(x: number) {
    this.sprite!.x = x + this.platformOffsetX
    this.staticBody.x = x + this.platformOffsetX
  }

  protected startBounceTween(): void {
    if (this.bounceTween) {
      this.bounceTween.stop()
      this.bounceTween = null
    }
    if (this.sprite) {
      const initialY = this.sprite.y
      this.bounceTween = this.scene!.tweens.add({
        targets: this.sprite,
        y: { from: initialY - 5, to: initialY + 5 },
        duration: 1000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      })
    }
  }

  destroyBooster(): void {
    if (this.boosterGroup.getChildren().includes(this.sprite!)) {
      this.boosterGroup.remove(this.sprite!, false)
    }
    if (this.sprite!.body) {
      this.scene!.physics.world.disable(this.sprite!)
    }
    this.sprite!.destroy(true)
    this.scene = null
  }

  setActive(active: boolean): this {
    this.sprite!.setActive(active)
    return this
  }

  setVisible(visible: boolean): this {
    this.sprite!.setVisible(visible)
    return this
  }

  getSprite(): Sprite {
    return this.sprite!
  }

  getType(): EntityType {
    return this.boosterType
  }

  enableBoosterBody(enable: boolean) {
    this.body.body!.enable = enable
  }
}
