import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import Phaser from 'phaser'
import { BaseBooster } from './BaseBooster'
import type { BoosterOptions } from './BoosterOptions'

export class BoosterPool<T extends BaseBooster & { isActive: boolean; nextFree: T | null }> {
  private readonly scene: GameScene
  private readonly BoosterClass: new (options: BoosterOptions) => T
  private readonly gameInterface: GameInterface
  private readonly pool: Set<T> = new Set()

  private freeListHead: T | null = null
  private freeListCount: number = 0
  private readonly freePoolLimit: number

  private totalTakenFromPool: number = 0
  private totalCreatedNew: number = 0

  constructor(
    scene: GameScene,
    BoosterClass: new (options: BoosterOptions) => T,
    gameInterface: GameInterface,
    freePoolLimit: number = 10
  ) {
    this.scene = scene
    this.BoosterClass = BoosterClass
    this.gameInterface = gameInterface
    this.freePoolLimit = freePoolLimit
  }

  preload(
    count: number,
    entityType: EntityType,
    boosterGroup: Phaser.Physics.Arcade.StaticGroup
  ): void {
    for (let i = 0; i < count; i++) {
      const options: BoosterOptions = {
        boosterGroup,
        scene: this.scene,
        position: { x: -1000, y: -1000 },
        entityType,
        gameInterface: this.gameInterface,
        chunkInfo: {
          index: 0,
          id: 0,
          platformIndex: 0,
          length: 0,
          prevChunkCumulativeLenght: 0,
          generation: 0
        }
      }
      const booster = new this.BoosterClass(options)
      booster.setActive(false).setVisible(false)
      booster.returnToPool()
      booster.isActive = false
      booster.nextFree = null

      this.pool.add(booster)
      // Add booster to the free list.
      booster.nextFree = this.freeListHead
      this.freeListHead = booster
      this.freeListCount++
    }
  }

  get(options: BoosterOptions): T {
    let booster: T
    if (this.freeListHead) {
      booster = this.freeListHead
      this.freeListHead = booster.nextFree
      booster.nextFree = null
      this.freeListCount--
      booster.reset(options)
      booster.setActive(true).setVisible(true)
      booster.isActive = true
      this.totalTakenFromPool++
    } else {
      booster = new this.BoosterClass(options)
      booster.isActive = true
      booster.nextFree = null
      booster.setActive(true).setVisible(true)
      this.pool.add(booster)
      this.totalCreatedNew++
    }
    return booster
  }

  return(booster: T): void {
    booster.returnToPool()
    booster.setActive(false).setVisible(false)
    booster.isActive = false

    if (this.freeListCount < this.freePoolLimit) {
      booster.nextFree = this.freeListHead
      this.freeListHead = booster
      this.freeListCount++
    } else {
      this.pool.delete(booster)
      booster.destroyBooster()
    }
  }

  clear(): void {
    this.pool.forEach(booster => booster.destroyBooster())
    this.pool.clear()
    this.freeListHead = null
    this.freeListCount = 0
  }
}
