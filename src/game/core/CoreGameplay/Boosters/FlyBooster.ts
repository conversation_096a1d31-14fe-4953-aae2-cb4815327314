import type { BoosterOptions } from '@/game/core/CoreGameplay/Boosters/BoosterOptions'
import { Vector2 } from '@esotericsoftware/spine-phaser'
import { BaseBooster } from './BaseBooster'

export class FlyBooster extends BaseBooster {
  constructor(options: BoosterOptions) {
    super(options)
    this.setVisualOffset(-15)
    this.startBounceTween()
  }

  reset(options: BoosterOptions) {
    super.reset(options)
    this.setVisualOffset(-15)
    this.startBounceTween()
  }

  applyEffect(playerPos: Vector2): void {
    super.applyEffect(playerPos)
    this.setActive(false)
    this.setVisible(false)
  }
}
