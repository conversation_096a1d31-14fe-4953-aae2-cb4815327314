import type { BoosterOptions } from '@/game/core/CoreGameplay/Boosters/BoosterOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { EntityType } from '../../MapGen/MapGenerator'
import { BaseBooster } from './BaseBooster'

export class JumpBooster extends BaseBooster {
  constructor(options: BoosterOptions) {
    super(options)

    this.setVisualOffset(-15)
    this.startBounceTween()
  }

  override reset(options: BoosterOptions): void {
    super.reset(options)
    this.setVisualOffset(-15)
    this.startBounceTween()
    this.isUsed = false
  }

  override applyEffect(playerPos: Vector2): void {
    if (this.bounceTween) {
      this.bounceTween.stop()
      this.bounceTween = null
    }
    super.applyEffect(playerPos)
    if (this.boosterType === EntityType.BoosterSmallJump) {
      this.haptics.triggerImpactHapticEvent('medium')
      if (this.sprite) {
        this.sprite.y -= 100
        this.sprite.anims.play('arrowAnim').once('animationcomplete', () => {
          this.isUsed = false
          this.fadeBooster()
        })
      }
    } else {
      this.haptics.triggerImpactHapticEvent('heavy')
      if (this.sprite) {
        this.sprite.y -= 100
        this.sprite.anims.play('doubleArrowAnim').once('animationcomplete', () => {
          this.isUsed = false
          this.fadeBooster()
        })
      }
    }
  }

  protected fadeBooster(): void {
    if (this.sprite) {
      this.bounceTween = this.scene!.tweens.add({
        targets: this.sprite,
        alpha: 0.2,
        duration: 300,
        yoyo: false,
        repeat: 0,
        ease: 'Linear',
        onComplete: () => {
          this.sprite?.setAlpha(1)
          this.returnToPool()
        }
      })
    }
  }

  override returnToPool(): void {
    if (this.bounceTween) {
      this.bounceTween.stop()
      this.bounceTween = null
    }
    super.returnToPool()
  }

  override destroyBooster(): void {
    if (this.bounceTween) {
      this.bounceTween.stop()
      this.scene?.tweens.remove(this.bounceTween)
      this.bounceTween.destroy()
      this.bounceTween = null
    }
    super.destroyBooster()
  }
}
