import type { BoosterOptions } from '@/game/core/CoreGameplay/Boosters/BoosterOptions'
import { Vector2 } from '@esotericsoftware/spine-phaser'
import { BaseBooster } from './BaseBooster'

export class Shield<PERSON>ooster extends BaseBooster {
  constructor(options: BoosterOptions) {
    super(options)
    this.staticBody.checkCollision.up = false
  }

  override reset(options: BoosterOptions) {
    super.reset(options)
    this.staticBody.checkCollision.up = false
  }

  override setVisualOffset(height: number) {
    const customOffset = height - 15
    super.setVisualOffset(customOffset)
  }

  override applyEffect(playerPos: Vector2): void {
    this.enableBoosterBody(false)
    super.applyEffect(playerPos)
    this.setActive(false)
    this.setVisible(false)
  }
}
