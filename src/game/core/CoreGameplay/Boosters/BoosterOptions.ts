import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { ChunkInfo } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import Phaser from 'phaser'

export interface BoosterOptions {
  boosterGroup: Phaser.Physics.Arcade.StaticGroup
  scene: GameScene
  position: { x: number; y: number }
  entityType: EntityType
  gameInterface: GameInterface
  chunkInfo: ChunkInfo
}
