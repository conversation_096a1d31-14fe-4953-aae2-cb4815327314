import type { BoosterOptions } from '@/game/core/CoreGameplay/Boosters/BoosterOptions.ts'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import Phaser from 'phaser'
import type { GameScene } from '../../scenes/GameScene'
import { BaseBooster } from './BaseBooster'
import { BoosterPool } from './BoosterPool'
import { FlyBooster } from './FlyBooster'
import { JumpBooster } from './JumpBooster'
import { ShieldBooster } from './ShieldBooster'

export class BoostersFactory {
  private smallJumpPool: BoosterPool<JumpBooster>
  private bigJumpPool: BoosterPool<JumpBooster>
  private smallFlyPool: BoosterPool<FlyBooster>
  private bigFlyPool: BoosterPool<FlyBooster>
  private shieldPool: BoosterPool<ShieldBooster>

  constructor(scene: GameScene, gameInterface: GameInterface) {
    this.smallJumpPool = new BoosterPool<JumpBooster>(scene, JumpBooster, gameInterface)
    this.bigJumpPool = new BoosterPool<JumpBooster>(scene, JumpBooster, gameInterface)
    this.smallFlyPool = new BoosterPool<FlyBooster>(scene, FlyBooster, gameInterface)
    this.bigFlyPool = new BoosterPool<FlyBooster>(scene, FlyBooster, gameInterface)
    this.shieldPool = new BoosterPool<ShieldBooster>(scene, ShieldBooster, gameInterface)
  }

  async preloadBoosters(boosterGroup: Phaser.Physics.Arcade.StaticGroup): Promise<void> {
    await Promise.all([
      this.smallJumpPool.preload(10, EntityType.BoosterSmallJump, boosterGroup),
      this.bigJumpPool.preload(10, EntityType.BoosterBigJump, boosterGroup),
      this.smallFlyPool.preload(5, EntityType.BoosterSmallFly, boosterGroup),
      this.bigFlyPool.preload(5, EntityType.BoosterBigFly, boosterGroup),
      this.shieldPool.preload(5, EntityType.BoosterShield, boosterGroup)
    ])
  }

  public createBooster(options: BoosterOptions): BaseBooster {
    switch (options.entityType) {
      case EntityType.BoosterSmallJump:
        return this.smallJumpPool.get(options)
      case EntityType.BoosterBigJump:
        return this.bigJumpPool.get(options)
      case EntityType.BoosterSmallFly:
        return this.smallFlyPool.get(options)
      case EntityType.BoosterBigFly:
        return this.bigFlyPool.get(options)
      case EntityType.BoosterShield:
        return this.shieldPool.get(options)
      default:
        throw new Error('Invalid BoosterType')
    }
  }

  public returnBoosterToPool(booster: BaseBooster): void {
    switch (booster.getType()) {
      case EntityType.BoosterSmallJump:
        this.smallJumpPool.return(booster as JumpBooster)
        break
      case EntityType.BoosterBigJump:
        this.bigJumpPool.return(booster as JumpBooster)
        break
      case EntityType.BoosterSmallFly:
        this.smallFlyPool.return(booster as FlyBooster)
        break
      case EntityType.BoosterBigFly:
        this.bigFlyPool.return(booster as FlyBooster)
        break
      case EntityType.BoosterShield:
        this.shieldPool.return(booster as ShieldBooster)
        break
      default:
        throw new Error('Invalid BoosterType')
    }
  }

  clearBoosterPools(): void {
    this.smallJumpPool.clear()
    this.bigJumpPool.clear()
    this.smallFlyPool.clear()
    this.bigFlyPool.clear()
    this.shieldPool.clear()
  }
}
