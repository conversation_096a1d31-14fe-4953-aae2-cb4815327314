import type { BoostersFactory } from '@/game/core/CoreGameplay/Boosters/BoostersFactory'
import type { CollectablesFactory } from '@/game/core/CoreGameplay/Collectables/CollectablesFactory'
import { PlayerConsts } from '@/game/core/CoreGameplay/Constants/PlayerConsts'
import type { DecorationsBuilder } from '@/game/core/CoreGameplay/Decorations/DecorationsBuilder.ts'
import type { EnemiesFactory } from '@/game/core/CoreGameplay/Enemies/EnemiesFactory'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { PlatformFactory } from '@/game/core/CoreGameplay/Platforms/PlatformFactory'
import { MapGenerator, type Chunk } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'
import { LevelBuilder } from './LevelBuilder'

export class GameBuilder {
  private mapGenerator!: MapGenerator
  private _levelBuilder!: LevelBuilder
  private logger: Logger = logger
  private hasInitialLevel: boolean = false
  private initiatingInProgress: boolean = false
  private nextScoreThreshold: number
  private currentChunksLength: number = 0
  private readonly scene: GameScene
  private readonly platformFactory: PlatformFactory
  private readonly boostersFactory: BoostersFactory
  private readonly mobFactory: EnemiesFactory
  private readonly collectablesFactory: CollectablesFactory
  private readonly decorationsBuilder: DecorationsBuilder

  constructor(
    scene: GameScene,
    platformFactory: PlatformFactory,
    boostersFactory: BoostersFactory,
    mobFactory: EnemiesFactory,
    collectablesFactory: CollectablesFactory,
    decorationsBuilder: DecorationsBuilder
  ) {
    this.scene = scene
    this.platformFactory = platformFactory
    this.boostersFactory = boostersFactory
    this.mobFactory = mobFactory
    this.collectablesFactory = collectablesFactory
    this.decorationsBuilder = decorationsBuilder
    this.nextScoreThreshold = 0
  }

  async buildGame(mapGenerator: MapGenerator, gameInterface: GameInterface) {
    this.mapGenerator = mapGenerator
    this._levelBuilder = new LevelBuilder(
      this.scene,
      gameInterface,
      this.platformFactory,
      this.boostersFactory,
      this.mobFactory,
      this.collectablesFactory,
      this.decorationsBuilder
    )

    await this._levelBuilder.preparePool()
    this.getNextChunks(10000)
    this.nextScoreThreshold += 8000
    this.hasInitialLevel = true
  }

  public getNextChunks(length: number) {
    if (__DEV__) {
      this.logger.info(`Get next chunk: ${length}`)
    }

    try {
      const chunks: Chunk[] = []
      let score: number = 0
      do {
        const chunk = this.mapGenerator.generateChunk(this.currentChunksLength)
        this.currentChunksLength += chunk.length
        score += chunk.length * PlayerConsts.POINTS_INDEX
        chunks.push(chunk)
      } while (score < length)
      this._levelBuilder.generateContent(chunks)
      this.hasInitialLevel = true
    } catch (error) {
      this.logger.error('Error generating game content:', error)
    }
  }

  onScoreUpdated(currentScore: number): void {
    if (!this.hasInitialLevel) return
    if (this.initiatingInProgress) return
    this.initiatingInProgress = true

    if (currentScore >= this.nextScoreThreshold) {
      this.getNextChunks(5000)
      this.nextScoreThreshold += 5000
    }

    this.initiatingInProgress = false
  }

  public get levelBuilder(): LevelBuilder {
    return this._levelBuilder
  }
}
