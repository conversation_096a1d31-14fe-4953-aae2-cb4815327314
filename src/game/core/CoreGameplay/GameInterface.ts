import { GameBuilder } from '@/game/core/CoreGameplay/GameBuilder'
import {
  GameSessionManager,
  InteractionType,
  type EntityInteraction,
  type SessionProperties
} from '@/game/core/MapGen/GameSessionManager'
import { MapGenerator } from '@/game/core/MapGen/MapGenerator'
import { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'
import type { ChunkInfo } from './Platforms/BasePlatformOptions'

export class GameInterface {
  private gameBuilder!: GameBuilder
  private sessionManager: GameSessionManager
  private mapGenerator!: MapGenerator
  private scene: GameScene
  private logger: Logger = logger
  private multiplier: number = 0
  private mobDeathInteractions: Array<EntityInteraction> = []

  constructor(scene: GameScene, builder: GameBuilder) {
    this.sessionManager = new GameSessionManager()
    this.gameBuilder = builder
    this.scene = scene
  }

  get currentMultiplier(): number {
    return this.multiplier > 0 ? this.multiplier : 1
  }

  async initGame(
    hasTutorial: boolean,
    hasMobsTutorial: boolean,
    playerLeague: number | undefined,
    boosters: number[],
    controlMode: number = 0
  ): Promise<void> {
    try {
      const sessionProps: SessionProperties = await this.sessionManager.createSession(
        hasTutorial,
        hasMobsTutorial,
        playerLeague,
        boosters,
        controlMode
      )

      if (__DEV__)
        console.log(
          'Game Interface',
          `Received Seeds: OuterSeed: ${sessionProps.outerSeed.map(v => v.toString())}, InnerSeed: ${sessionProps.innerSeed.map(v => v.toString())}`
        )

      this.initializeSessionData(sessionProps, hasTutorial, hasMobsTutorial, playerLeague)

      await this.gameBuilder.buildGame(this.mapGenerator, this)
      this.multiplier = sessionProps.multiplier

      console.log('Game successfully initialized with server seeds.')
    } catch (error) {
      console.error('Error initializing game session:', error)
    }
  }

  private initializeSessionData(
    sessionProps: SessionProperties,
    hasTutorial: boolean,
    hasMobsTutorial: boolean,
    playerLeague: number | undefined
  ): void {
    if (__DEV__) {
      console.log('sessionProps', { ...sessionProps })
    }

    if (hasTutorial) {
      this.mapGenerator = new MapGenerator(sessionProps, __TUTORIAL_CONFIG_DATA__)
    } else if (hasMobsTutorial || playerLeague! <= 2) {
      this.mapGenerator = new MapGenerator(sessionProps, __MOBS_TUTORIAL_CONFIG_DATA__)
    } else if (playerLeague === 3) {
      this.mapGenerator = new MapGenerator(sessionProps, __LEAGUE_3_CONFIG_DATA__)
    } else {
      this.mapGenerator = new MapGenerator(sessionProps)
    }
  }

  registerInteraction(info: ChunkInfo, interactionType: InteractionType, x: number, y: number) {
    const interaction: EntityInteraction = {
      chunkIndex: info.index,
      entityIndex: info.platformIndex,
      interactionType,
      x: Math.round(x * 100) / 100,
      y: Math.round(-y)
    }
    if (interaction.interactionType != InteractionType.MobCollideDeath) {
      this.sessionManager.registerInteraction(interaction, this.scene.getCurrentScore())
    } else {
      this.mobDeathInteractions.push(interaction)
    }
  }

  getActiveSessionInfo(): SessionProperties | undefined {
    return this.sessionManager.getActiveSession()
  }

  clearCachedInteractions() {
    this.mobDeathInteractions = []
  }

  registerGameEnd(score: number) {
    if (this.mobDeathInteractions.length == 1) {
      this.mobDeathInteractions.forEach(interaction => {
        this.sessionManager.registerInteraction(interaction, score)
      })
      this.mobDeathInteractions = []
    } else {
      this.sessionManager.registerGameEnd(score)
    }
  }
}
