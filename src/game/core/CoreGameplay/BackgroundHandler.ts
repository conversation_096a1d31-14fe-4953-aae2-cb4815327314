import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'

export class BackgroundHandler {
  private background!: Phaser.GameObjects.Image
  private logger: Logger = logger
  private backgrounds: string[] = ['bg01', 'bg02', 'bg03']

  constructor(private scene: GameScene) {
    this.cheatBack = this.cheatBack.bind(this)
    this.createBackground = this.createBackground.bind(this)
  }

  createRandomBackground() {
    this.createBackground(getRandomElement(this.backgrounds))
  }

  createBackground(textureName: string): void {
    try {
      if (this.background) {
        if (this.background.texture.key !== textureName) {
          this.background.setTexture(textureName)
        }
      } else {
        this.background = this.scene.add.image(0, 0, textureName).setOrigin(0.5, 0.5)
        this.background.setScrollFactor(0)
        this.background.setDepth(DepthOrder.Background)
      }

      this.scaleAndPositionBackground()
    } catch (error: any) {
      this.logger.error('Error in createBackground:', error)
    }
  }

  setBackground(textureName: string): void {
    this.createBackground(textureName)
  }

  cheatBack(): void {
    try {
      if (!this.background?.texture?.key) {
        return
      }

      const currentTextureKey = this.background.texture.key
      let currentIndex = this.backgrounds.indexOf(currentTextureKey)

      if (currentIndex === -1) {
        this.logger.warn('Current texture key not found, defaulting to first background.')
        currentIndex = 0
      }

      const nextIndex = (currentIndex + 1) % this.backgrounds.length

      this.createBackground(this.backgrounds[nextIndex])
    } catch (error: any) {
      this.logger.error('Error in cheatBack:', error)
    }
  }

  private scaleAndPositionBackground(): void {
    this.logger.log('Background Handler', 'scale camera ', this.background.texture)
    const defaultCamera = this.scene.cameras.default
    const mainCamera = this.scene.cameras.main

    const cameraZoom = mainCamera.zoom
    const viewWidth = mainCamera.width / cameraZoom
    const viewHeight = mainCamera.height / cameraZoom

    const imageRatio = this.background.width / this.background.height
    const viewRatio = viewWidth / viewHeight

    if (viewRatio > imageRatio) {
      this.background.setScale(viewWidth / this.background.width)
    } else {
      this.background.setScale(viewHeight / this.background.height)
    }

    const cameraCenterX = defaultCamera.centerX
    const cameraCenterY = defaultCamera.centerY

    this.background.setPosition(cameraCenterX, cameraCenterY)

    this.logger.log('Background Handler', 'Background centered on:', cameraCenterX, cameraCenterY)
  }
}

function getRandomElement<T>(array: T[]): T {
  const index = Math.floor(Math.random() * array.length)
  return array[index]
}
