import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import type { EntityType } from '../../MapGen/MapGenerator'
import { MobDynamicHorizontalBigConsts } from '../Constants/MobConsts'
import type { EnemyOptions } from './EnemyOptions'

export class MobDynamicHorizontalBig extends BaseEnemy {
  private movementTween!: Phaser.Tweens.Tween | null

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalBigConsts.HEALTH)
  }

  protected createEntity(x: number, y: number, type: EntityType): void {
    super.createEntity(x + MobDynamicHorizontalBigConsts.SPAWN_OFFSET_X, y, type)
    this.initMovement()
  }

  private initMovement(): void {
    const startX = this.sprite!.x
    const endX = startX + MobDynamicHorizontalBigConsts.JUMP_DISTANCE

    this.jump(
      startX,
      endX,
      MobDynamicHorizontalBigConsts.JUMP_DURATION,
      MobDynamicHorizontalBigConsts.JUMP_HEIGHT,
      () => {
        this.scene?.time.delayedCall(MobDynamicHorizontalBigConsts.DELAY_CALL_DURATION, () => {
          this.jump(
            endX,
            startX,
            MobDynamicHorizontalBigConsts.JUMP_DURATION,
            MobDynamicHorizontalBigConsts.JUMP_HEIGHT,
            () => {
              this.scene?.time.delayedCall(
                MobDynamicHorizontalBigConsts.DELAY_CALL_DURATION,
                () => {
                  this.initMovement()
                }
              )
            }
          )
        })
      }
    )
  }

  private jump(
    startX: number,
    endX: number,
    duration: number,
    jumpHeight: number,
    onComplete: () => void
  ): void {
    const startY = this.sprite!.y

    const movingRight = endX > startX
    this.sprite!.flipX = !movingRight

    this.movementTween =
      this.scene?.tweens.add({
        targets: this.sprite,
        x: endX,
        duration: duration,
        ease: 'Sine.easeIn',
        onUpdate: tween => {
          const progress = tween.progress
          const height = -4 * jumpHeight * Math.pow(progress - 0.5, 2) + jumpHeight
          this.sprite!.y = startY - height
          this.spriteCollider!.x =
            this.sprite!.x + MobDynamicHorizontalBigConsts.SPRITE_COLLIDER_OFFSET_X
          this.spriteCollider!.y =
            this.sprite!.y + MobDynamicHorizontalBigConsts.SPRITE_COLLIDER_OFFSET_Y
        },
        onComplete: () => {
          if (this.movementTween) {
            clearTween(this.movementTween)
            this.movementTween = null
          }
          this.sprite!.x = endX
          this.sprite!.y = startY
          if (onComplete) {
            onComplete()
          }
        }
      }) ?? null
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    if (this.movementTween) this.movementTween.stop()
    super.defeat(wasStomped)
    this.setHealth(MobDynamicHorizontalBigConsts.HEALTH)
  }

  reset(options: EnemyOptions): void {
    if (this.movementTween) this.movementTween.stop()

    super.reset(options)

    this.initMovement()
  }
}
