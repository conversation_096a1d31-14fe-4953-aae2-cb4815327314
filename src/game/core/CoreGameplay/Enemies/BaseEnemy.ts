import Sprite = Phaser.GameObjects.Sprite
import { achievements } from '@/game/core/CoreGameplay/Achievements/GameplayAchievementsHandler.ts'
import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { ChunkInfo } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { InteractionType } from '@/game/core/MapGen/GameSessionManager'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import { Vector2, type SpineGameObject } from '@esotericsoftware/spine-phaser'
import { addComponent, addEntity, removeEntity } from 'bitecs'
import Phaser from 'phaser'
import { Enemy, Health, Position } from '../../ecs/Components/EntityComponents'
import { getGameWorld } from '../../ecs/GameWorld'
import type { EnemyOptions } from './EnemyOptions'

export class BaseEnemy {
  protected sprite: Sprite | null = null
  protected body: Phaser.Physics.Arcade.Sprite | null = null
  protected spineObject: SpineGameObject | null = null
  protected spriteCollider: Phaser.Physics.Arcade.Body | null = null
  protected mobGroup: Phaser.Physics.Arcade.StaticGroup
  protected scene: Phaser.Scene | null = null
  protected gameInterface: GameInterface | undefined
  protected chunkInfo: ChunkInfo
  private tintTimer?: Phaser.Time.TimerEvent
  public entity: number

  public isActive!: boolean
  public nextFree: this | null = null

  constructor(options: EnemyOptions) {
    const { mobGroup, scene, position, entityType, chunkInfo, gameInterface } = options

    this.mobGroup = mobGroup
    this.scene = scene
    this.chunkInfo = chunkInfo
    this.gameInterface = gameInterface

    this.entity = addEntity(getGameWorld())

    addComponent(getGameWorld(), Position, this.entity)
    addComponent(getGameWorld(), Health, this.entity)
    addComponent(getGameWorld(), Enemy, this.entity)

    Position.x[this.entity] = options.position.x
    Position.y[this.entity] = options.position.y
    Health.value[this.entity] = 1
    Enemy.isDefeated[this.entity] = 0
    Enemy.canBeKilled[this.entity] = 1
    Enemy.mobType[this.entity] = options.entityType
    Enemy.tintTimer[this.entity] = 0
    Enemy.proximityToTriggerBehaviour[this.entity] = 400

    this.createEntity(position.x, position.y, entityType)
    this.scene.physics.add.existing(this.sprite!)
  }

  protected createEntity(x: number, y: number, type: EntityType): void {
    this.sprite = this.mobGroup.create(x, y, AtlasNames.ENV, `${type.toString()}.png`)
    this.sprite?.setDepth(DepthOrder.Enemies)
    this.sprite?.setOrigin(0, 0)

    this.adjustScale()
    this.body = this.sprite as Phaser.Physics.Arcade.Sprite
    this.body.setImmovable(true)
    this.enableCollision()
    this.body.refreshBody()

    this.spriteCollider = this.sprite?.body as Phaser.Physics.Arcade.Body
    this.spriteCollider.setSize(
      ENTITIES_SIZES[this.entityType()][0],
      ENTITIES_SIZES[this.entityType()][1]
    )
  }

  setHealth(hp: number) {
    Health.value[this.entity] = hp
  }

  getHealth(): number {
    return Health.value[this.entity]
  }

  setProximityThreshold(threshold: number) {
    Enemy.proximityToTriggerBehaviour[this.entity] = threshold
  }

  getProximityThreshold(): number {
    const proximity = Enemy.proximityToTriggerBehaviour[this.entity]
    return proximity
  }

  triggerInPlayerProximityBehaviour() {
    if (!this.sprite || Enemy.isDefeated[this.entity]) return
    console.log('triggered')
  }

  reset(options: EnemyOptions): void {
    const { mobGroup, position, entityType, chunkInfo, gameInterface } = options

    this.mobGroup = mobGroup
    this.chunkInfo = chunkInfo
    this.gameInterface = gameInterface

    Enemy.mobType[this.entity] = entityType
    Enemy.isDefeated[this.entity] = 0
    Position.x[this.entity] = options.position.x
    Position.y[this.entity] = options.position.y
    if (this.sprite) {
      this.scene?.tweens.killTweensOf(this.sprite)
      this.sprite.destroy(true)
    }

    if (this.spineObject) {
      this.spineObject.setVisible(true)
    }

    this.createEntity(position.x, position.y, entityType)
  }

  returnToPool(): void {
    if (!this.sprite || !this.sprite.visible) return

    this.setActive(false)
    this.setVisible(false)

    if (this.spineObject) {
      this.spineObject.setVisible(false)
    }

    if (this.body && this.body.body) {
      this.body.body.enable = false
      this.body.body.checkCollision.none = true
    }

    this.mobGroup.remove(this.sprite!)
  }

  protected defeat(wasStomped: boolean): void {
    achievements.checkAchievement(8)
    Enemy.isDefeated[this.entity] = 1

    if (this.scene && this.body) {
      this.scene.tweens.killTweensOf(this.body)
    }
    if (this.tintTimer) {
      this.tintTimer.remove()
      this.tintTimer = undefined
    }

    this.resetSlotTints()
    this.spineObject?.animationState.clearTracks()

    this.returnToPool()
  }

  triggerHit(bulletPos: Vector2): boolean {
    if (!Enemy.canBeKilled[this.entity]) {
      return false
    }
    Health.value[this.entity] -= 1

    if (Health.value[this.entity] < 0) return false
    this.applyTintEffect()

    if (Health.value[this.entity] == 0) {
      this.disableCollider()

      this.scene?.time.delayedCall(99, () => {
        this.defeat(false)
      })
      return true
    } else {
      this.playHit()
      return false
    }
  }

  playHit(): void {}

  destroyMob(): void {
    this.setActive(false)
    this.setVisible(false)
    if (this.spineObject) {
      this.spineObject.setVisible(false)
    }
    if (this.sprite) {
      this.scene?.tweens.killTweensOf(this.sprite)
      this.sprite.removeAllListeners()
    }
    this.scene?.events.once('postupdate', () => {
      try {
        if (
          this.mobGroup &&
          (!('active' in this.mobGroup) || (this.mobGroup as any).active) &&
          this.sprite &&
          this.sprite.active
        ) {
          if (typeof this.mobGroup.contains === 'function') {
            if (this.mobGroup.contains(this.sprite)) {
              this.mobGroup.remove(this.sprite)
            }
          } else if (
            this.mobGroup.children &&
            this.mobGroup.children.entries &&
            Array.isArray(this.mobGroup.children.entries)
          ) {
            if (this.mobGroup.children.entries.indexOf(this.sprite) !== -1) {
              this.mobGroup.remove(this.sprite)
            }
          }
        }
      } catch (error) {
        console.error('Error removing sprite from mobGroup:', error)
      }
      if (this.sprite) {
        this.sprite.destroy(true)
        this.sprite = null
      }
      if (this.body) {
        this.body.destroy(true)
        this.body = null
      }
      if (this.spriteCollider) {
        this.spriteCollider.destroy()
        this.spriteCollider = null
      }
      if (this.spineObject) {
        this.spineObject?.destroy(true)
        this.spineObject = null
      }
      this.scene = null
    })
    const world = getGameWorld()
    removeEntity(world, this.entity)
    this.entity = -1
  }

  private applyTintEffect(): void {
    if (this.tintTimer) {
      this.tintTimer.remove()
      this.tintTimer = undefined
    }

    this.sprite?.setTint(0xff6161)

    this.tintTimer = this.scene?.time.delayedCall(100, () => {
      this.sprite?.clearTint()
      this.tintTimer = undefined
    })
  }

  private resetSlotTints(): void {
    this.sprite?.clearTint()
  }

  onCollideWithPlayer(playerPos: Vector2, shouldKillMob: boolean) {
    if (shouldKillMob) {
      this.defeat(true)
    } else {
      this.gameInterface?.registerInteraction(
        this.chunkInfo,
        InteractionType.MobCollideDeath,
        this.sprite!.x,
        this.sprite!.y
      )
    }
  }

  syncPosition(x: number) {
    this.sprite!.x = x
    this.spriteCollider!.x = x
  }

  setActive(active: boolean): this {
    if (this.sprite) {
      this.sprite.setActive(active)
    }
    return this
  }

  setVisible(visible: boolean): this {
    if (this.sprite) {
      this.sprite.setVisible(visible)
    }
    return this
  }

  getSprite(): Sprite {
    return this.sprite!
  }

  entityType(): EntityType {
    const mobType = Enemy.mobType[this.entity] as EntityType
    return mobType
  }

  getMobPosition(): Vector2 {
    return new Vector2(this.sprite?.x, this.sprite?.y)
  }

  protected adjustScale() {
    const originalWidth = this.sprite!.width
    const originalHeight = this.sprite!.height
    const [targetWidth, targetHeight] = ENTITIES_SIZES[this.entityType()]

    const scaleX = targetWidth / originalWidth
    const scaleY = targetHeight / originalHeight
    const finalScale = Math.min(scaleX, scaleY)
    this.sprite?.setScale(finalScale)
    this.sprite?.setDisplaySize(targetWidth, targetHeight)
  }

  protected enableCollision() {
    this.body!.body!.enable = true
    this.body!.body!.checkCollision.up = true
    this.body!.body!.checkCollision.right = true
    this.body!.body!.checkCollision.left = true
    this.body!.body!.checkCollision.down = true
  }

  public disableCollider() {
    this.spriteCollider!.enable = false
  }

  public makeColliderCircle(multiplier: number) {
    const radius = Math.max(
      ENTITIES_SIZES[this.entityType()][0],
      ENTITIES_SIZES[this.entityType()][1]
    )

    this.spriteCollider?.setCircle(radius * multiplier)
    this.spriteCollider?.setOffset(0, 0)
  }

  public getSpriteColliderCenter(): Vector2 {
    if (!this.spriteCollider) {
      throw new Error('Sprite collider is not initialized')
    }
    const centerX = this.spriteCollider.x + this.spriteCollider.width / 2
    const centerY = this.spriteCollider.y + this.spriteCollider.height / 2
    return new Vector2(centerX, centerY)
  }

  protected registerMobsDeath(wasStomped: boolean) {
    if (wasStomped) {
      this.gameInterface?.registerInteraction(
        this.chunkInfo,
        InteractionType.MobCollideAlive,
        this.sprite!.x,
        this.sprite!.y
      )
    } else {
      this.gameInterface?.registerInteraction(
        this.chunkInfo,
        InteractionType.MobCollideBullet,
        this.sprite!.x,
        this.sprite!.y
      )
    }
  }
}
