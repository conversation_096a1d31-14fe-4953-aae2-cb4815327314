import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import { ENTITIES_SIZES, type EntityType } from '../../MapGen/MapGenerator'
import { MobDynamicHorizontalHugeConsts } from '../Constants/MobConsts'
import type { EnemyOptions } from './EnemyOptions'

export class MobDynamicHorizontalHuge extends BaseEnemy {
  private movementTween!: Phaser.Tweens.Tween | null
  private shakeTween!: Phaser.Tweens.Tween | null

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalHugeConsts.HEALTH)
  }

  protected createEntity(x: number, y: number, type: EntityType): void {
    super.createEntity(x, y, type)
    this.initMovement()
    this.initShake()

    this.spriteCollider = this.sprite!.body as Phaser.Physics.Arcade.Body
    this.spriteCollider.setSize(
      ENTITIES_SIZES[this.entityType()][0],
      ENTITIES_SIZES[this.entityType()][1]
    )
  }

  private initMovement(): void {
    const moveDistance = MobDynamicHorizontalHugeConsts.MOVE_DISTANCE
    const duration = MobDynamicHorizontalHugeConsts.MOVE_DURATION
    const originalX = this.sprite!.x

    this.movementTween = this.scene
      ? this.scene.tweens.add({
          targets: this.sprite,
          x: {
            from: originalX - moveDistance,
            to: originalX + moveDistance
          },
          duration: duration,
          ease: 'Sine.easeInOut',
          yoyo: true,
          repeat: -1,
          onUpdate: () => {
            const cameraLeft = this.scene!.cameras.main.worldView.left
            const cameraRight = this.scene!.cameras.main.worldView.right
            if (this.sprite!.x < cameraLeft + moveDistance) {
              this.sprite!.x = cameraLeft + moveDistance
            }
            if (this.sprite!.x > cameraRight - moveDistance) {
              this.sprite!.x = cameraRight - moveDistance
            }
            this.spriteCollider!.x = this.sprite!.x
            this.spriteCollider!.y = this.sprite!.y
          }
        })
      : null
  }

  private initShake(): void {
    this.shakeTween = this.scene
      ? this.scene.tweens.add({
          targets: this.sprite,
          y: {
            from: this.sprite!.y - MobDynamicHorizontalHugeConsts.SHAKE_DISTANCE,
            to: this.sprite!.y + MobDynamicHorizontalHugeConsts.SHAKE_DISTANCE
          },
          duration: MobDynamicHorizontalHugeConsts.SHAKE_DURATION,
          ease: 'Sine.easeInOut',
          yoyo: true,
          repeat: -1
        })
      : null
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    if (this.movementTween) clearTween(this.movementTween)
    if (this.shakeTween) clearTween(this.shakeTween)

    this.movementTween = null
    this.shakeTween = null

    super.defeat(wasStomped)
    this.setHealth(MobDynamicHorizontalHugeConsts.HEALTH)
  }

  reset(options: EnemyOptions): void {
    if (this.movementTween) clearTween(this.movementTween)
    if (this.shakeTween) clearTween(this.shakeTween)

    super.reset(options)
    this.initMovement()
    this.initShake()
  }
}
