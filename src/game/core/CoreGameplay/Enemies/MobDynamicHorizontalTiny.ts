import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { Enemy } from '../../ecs/Components/EntityComponents'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDynamicHorizontalTinyConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, SunnyAnimations } from '../Player/PlayerStates/States/SpineAnimations'
import type { EnemyOptions } from './EnemyOptions'

export class MobDynamicHorizontalTiny extends BaseEnemy {
  private movementTween: Phaser.Tweens.Tween | null = null

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalTinyConsts.HEALTH)
    this.createSpineObject()
    this.initMovement()
  }

  private createSpineObject(): void {
    if (this.spineObject) {
      this.spineObject?.animationState.clearTracks()
      this.spineObject?.animationState.clearListeners()
      this.spineObject?.destroy()
    }
    const cam = this.scene!.cameras.main
    const centerX = cam.worldView.left + cam.width / 2

    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + SunnyAnimations.X_OFFSET,
      this.sprite!.y + SunnyAnimations.Y_OFFSET,
      MobDataSetKeys.SUNNY_DATA,
      MobDataSetKeys.SUNNY_ATLAS
    ).setDepth(DepthOrder.Enemies)

    this.spineObject?.animationState.setAnimation(0, SunnyAnimations.OUTLINE_COLOR, true)
    this.spineObject?.animationState.setAnimation(1, SunnyAnimations.IDLE, true)
    this.spineObject?.setScale(SunnyAnimations.SCALE)

    this.spineObject?.setScale(
      this.sprite!.x > centerX ? -SunnyAnimations.SCALE : SunnyAnimations.SCALE,
      this.spineObject.scaleY
    )

    this.sprite!.setAlpha(0)
  }

  private initMovement(): void {
    const moveDistance = MobDynamicHorizontalTinyConsts.MOVE_DISTANCE
    const duration = MobDynamicHorizontalTinyConsts.DURATION
    const originalX = this.sprite!.x

    this.movementTween =
      this.scene?.tweens.add({
        targets: this.sprite,
        x: { from: originalX - moveDistance, to: originalX + moveDistance },
        duration,
        ease: 'Back.easeInOut',
        yoyo: true,
        repeat: -1,
        onUpdate: () => {
          this.spineObject!.x = this.sprite!.x + SunnyAnimations.X_OFFSET
          this.spriteCollider!.x = this.sprite!.x
          this.spriteCollider!.y = this.sprite!.y
        }
      }) ?? null
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    this.disableCollider()
    this.spineObject?.animationState.setAnimation(1, SunnyAnimations.DEATH, false)
    this.scene?.time.delayedCall(
      950,
      () => {
        super.defeat(wasStomped)
        if (this.movementTween) this.movementTween.stop()
      },
      [],
      this
    )
    this.setHealth(MobDynamicHorizontalTinyConsts.HEALTH)
  }

  override reset(options: EnemyOptions): void {
    if (this.movementTween) this.movementTween.stop()
    super.reset(options)
    this.enableCollision()

    this.sprite?.setAlpha(0)
    const sprite = this.getSprite()
    const { x, y } = sprite

    if (!this.spineObject) return
    const cam = this.scene!.cameras.main
    const centerX = cam.worldView.left + cam.width / 2

    this.spineObject.x = x + SunnyAnimations.X_OFFSET
    this.spineObject.y = y + SunnyAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(0, SunnyAnimations.OUTLINE_COLOR, true)
    this.spineObject?.animationState.setAnimation(1, SunnyAnimations.IDLE, true)

    this.spineObject?.setScale(
      this.sprite!.x > centerX ? -SunnyAnimations.SCALE : SunnyAnimations.SCALE,
      this.spineObject.scaleY
    )

    this.initMovement()
  }

  override triggerInPlayerProximityBehaviour() {
    if (!this.sprite || Enemy.isDefeated[this.entity]) return
    this.spineObject?.animationState.setAnimation(1, SunnyAnimations.IDLE_2, true)
  }
}
