import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import type { EntityType } from '../../MapGen/MapGenerator'
import { MobDynamicVerticalConsts } from '../Constants/MobConsts'
import type { EnemyOptions } from './EnemyOptions'

export class MobDynamicVertical extends BaseEnemy {
  private movementTween!: Phaser.Tweens.Tween | null
  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicVerticalConsts.HEALTH)
  }

  protected createEntity(x: number, y: number, type: EntityType): void {
    super.createEntity(x, y, type)
    this.initMovement()
  }

  private initMovement(): void {
    const moveDistance = MobDynamicVerticalConsts.MOVE_DISTANCE
    const duration = MobDynamicVerticalConsts.DURATION

    const originalX = this.sprite!.x

    this.movementTween =
      this.scene?.tweens.add({
        targets: this.sprite,
        x: {
          from: originalX - moveDistance,
          to: originalX + moveDistance
        },
        duration: duration,
        ease: 'Sine.easeInOut',
        yoyo: true,
        repeat: -1,
        onUpdate: () => {
          const cameraLeft = this.scene!.cameras.main.worldView.left
          const cameraRight = this.scene!.cameras.main.worldView.right
          if (this.sprite!.x < cameraLeft + moveDistance) {
            this.sprite!.x = cameraLeft + moveDistance
          }
          if (this.sprite!.x > cameraRight - moveDistance) {
            this.sprite!.x = cameraRight - moveDistance
          }
          this.spriteCollider!.x =
            this.sprite!.x + MobDynamicVerticalConsts.SPRITE_COLLIDER_OFFSET_X
          this.spriteCollider!.y =
            this.sprite!.y + MobDynamicVerticalConsts.SPRITE_COLLIDER_OFFSET_Y
        }
      }) ?? null
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    if (this.movementTween) clearTween(this.movementTween)
    this.movementTween = null

    super.defeat(wasStomped)
    this.setHealth(MobDynamicVerticalConsts.HEALTH)
  }

  reset(options: EnemyOptions): void {
    if (this.movementTween) clearTween(this.movementTween)
    this.movementTween = null

    super.reset(options)
    this.initMovement()
  }
}
