import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { EnemyOptions } from '@/game/core/CoreGameplay/Enemies/EnemyOptions'
import { Enemy } from '../../ecs/Components/EntityComponents'
import { DepthOrder } from '../Constants/DephOrdering'
import { BlackHoleAnimations, MobDataSetKeys } from '../Player/PlayerStates/States/SpineAnimations'

export class BlackHole extends BaseEnemy {
  constructor(options: EnemyOptions) {
    super(options)
    Enemy.canBeKilled[this.entity] = 0
    this.createSpineObject()
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + BlackHoleAnimations.X_OFFSET,
      this.sprite!.y + BlackHoleAnimations.Y_OFFSET,
      MobDataSetKeys.BLACKHOLE_DATA,
      MobDataSetKeys.BLACKHOLE_ATLAS
    )

    this.spineObject?.setScale(0.9)

    this.makeColliderCircle(0.45)

    this.spineObject.animationState.timeScale = Phaser.Math.FloatBetween(0.2, 0.3)
    this.spineObject?.animationState.setAnimation(1, BlackHoleAnimations.T0_IDLE, true)
    this.spineObject?.animationState.setAnimation(2, BlackHoleAnimations.T1_IDLE, true)
    this.spineObject?.animationState.setAnimation(2, BlackHoleAnimations.T2_IDLE, true)
    this.spineObject?.setDepth(DepthOrder.Enemies)

    this.sprite?.setAlpha(0)
  }

  override reset(options: EnemyOptions): void {
    super.reset(options)
    this.sprite?.setAlpha(0)
    const sprite = this.getSprite()
    const { x, y } = sprite

    this.makeColliderCircle(0.45)

    this.spineObject!.x = x + BlackHoleAnimations.X_OFFSET
    this.spineObject!.y = y + BlackHoleAnimations.Y_OFFSET

    this.spineObject!.animationState.timeScale = Phaser.Math.FloatBetween(0.2, 0.3)
    this.spineObject?.animationState.setAnimation(1, BlackHoleAnimations.T0_IDLE, true)
    this.spineObject?.animationState.setAnimation(2, BlackHoleAnimations.T1_IDLE, true)
    this.spineObject?.animationState.setAnimation(2, BlackHoleAnimations.T2_IDLE, true)
  }
}
