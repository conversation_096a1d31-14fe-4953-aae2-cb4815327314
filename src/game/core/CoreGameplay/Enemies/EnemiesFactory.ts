import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { BlackHole } from '@/game/core/CoreGameplay/Enemies/BlackHole'
import { MobDynamicHorizontalBig } from '@/game/core/CoreGameplay/Enemies/MobDynamicHorizontalBig'
import { MobDynamicHorizontalMiddle } from '@/game/core/CoreGameplay/Enemies/MobDynamicHorizontalMiddle'
import { MobDynamicHorizontalSmall } from '@/game/core/CoreGameplay/Enemies/MobDynamicHorizontalSmall'
import { MobDynamicHorizontalTiny } from '@/game/core/CoreGameplay/Enemies/MobDynamicHorizontalTiny'
import { MobStatic } from '@/game/core/CoreGameplay/Enemies/MobStatic'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { PoolSizeConsts } from '../Constants/PoolConsts'
import type { EnemyOptions } from './EnemyOptions'
import { EnemyPool } from './EnemyPool'
import { MobDynamicHorizontalHuge } from './MobDynamicHorizontalHuge'
import { MobDynamicHorizontalVertical } from './MobDynamicHorizontalVertical'
import { MobDynamicVertical } from './MobDynamicVertical'
import { MobStaticVertical } from './MobStaticVertical'
import { ProximityChecker } from './ProximityChecker'
import { UFO } from './UFO'
import { UFOManager } from './UFOManager'

export class EnemiesFactory {
  private mobPools: Map<EntityType, EnemyPool<BaseEnemy>>
  private ufoManager!: UFOManager
  private proximityChecker!: ProximityChecker
  private allActiveMobs: Set<BaseEnemy> = new Set()

  constructor(private scene: GameScene) {
    this.ufoManager = new UFOManager(scene)
    this.mobPools = new Map<EntityType, EnemyPool<BaseEnemy>>()
    this.initializePool(EntityType.MobStatic, MobStatic, PoolSizeConsts.STATIC_MOB_POOL_SIZE)
    this.initializePool(EntityType.MobBlackHole, BlackHole, PoolSizeConsts.BLACKHOLE_MOB_POOL_SIZE)
    this.initializePool(
      EntityType.MobDynamicHorizontalTiny,
      MobDynamicHorizontalTiny,
      PoolSizeConsts.TINY_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicHorizontalSmall,
      MobDynamicHorizontalSmall,
      PoolSizeConsts.SMALL_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicHorizontalMiddle,
      MobDynamicHorizontalMiddle,
      PoolSizeConsts.MIDDLE_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicHorizontalBig,
      MobDynamicHorizontalBig,
      PoolSizeConsts.BIG_MOB_POOL_SIZE
    )
    this.initializePool(EntityType.MobUFO, UFO, PoolSizeConsts.UFO_MOB_POOL_SIZE)
    this.initializePool(
      EntityType.MobStaticVertical,
      MobStaticVertical,
      PoolSizeConsts.VERTICAL_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicVertical,
      MobDynamicVertical,
      PoolSizeConsts.DYNAMIC_VERTICAL_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicHorizontalHuge,
      MobDynamicHorizontalHuge,
      PoolSizeConsts.HUGE_MOB_POOL_SIZE
    )
    this.initializePool(
      EntityType.MobDynamicHorizontalVertical,
      MobDynamicHorizontalVertical,
      PoolSizeConsts.HORIZONTAL_VERTICAL_MOB_POOL_SIZE
    )

    this.scene.time.delayedCall(500, () => {
      this.ufoManager.cachePlayer()
      this.proximityChecker = new ProximityChecker(scene, this.allActiveMobs)
    })
  }

  private initializePool(
    entityType: EntityType,
    EnemyClass: new (options: EnemyOptions) => BaseEnemy,
    sizeLimit: number
  ): void {
    const pool = new EnemyPool<BaseEnemy>(this.scene, EnemyClass, sizeLimit)
    this.mobPools.set(entityType, pool)
  }

  async preloadMobs(): Promise<void> {
    const preloadPromises = Array.from(this.mobPools.entries()).map(([type, pool]) => {
      return pool.preload(1, type)
    })
    await Promise.all(preloadPromises)
  }

  public createMob(options: EnemyOptions): BaseEnemy {
    const pool = this.mobPools.get(options.entityType)
    if (!pool) {
      throw new Error(`Invalid MobType: ${options.entityType}`)
    }

    const mob = pool.get(options)
    this.allActiveMobs.add(mob)

    if (mob instanceof UFO) {
      this.ufoManager.addMob(mob)
    }

    return mob
  }

  public returnMobToPool(mob: BaseEnemy): void {
    const pool = this.mobPools.get(mob.entityType())
    if (pool) {
      pool.return(mob)
      this.allActiveMobs.delete(mob)
      this.proximityChecker.removeMobFromTriggered(mob)

      if (mob instanceof UFO) {
        this.ufoManager.removeMob(mob)
      }
    } else {
      throw new Error(`Invalid MobType: ${mob.entityType()}`)
    }
  }

  clearEnemyPools(): void {
    this.mobPools?.forEach(pool => pool.clear())
    this.proximityChecker?.reset()
    this.allActiveMobs?.clear()
    this.ufoManager?.clear()
  }
}
