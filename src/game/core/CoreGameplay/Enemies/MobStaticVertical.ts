import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { Enemy, Health } from '../../ecs/Components/EntityComponents'
import { clearTween } from '../../HelperFunctions'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobStaticVerticalConsts } from '../Constants/MobConsts'
import { FlegmaFlexAnimations, MobDataSetKeys } from '../Player/PlayerStates/States/SpineAnimations'
import type { EnemyOptions } from './EnemyOptions'

export class MobStaticVertical extends BaseEnemy {
  private movementTween: Phaser.Tweens.Tween | null = null

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobStaticVerticalConsts.HEALTH)
    this.createSpineObject()
    this.initMovement()
  }

  protected createSpineObject(): void {
    if (this.spineObject) {
      this.spineObject?.animationState.clearTracks()
      this.spineObject?.animationState.clearListeners()
      this.spineObject?.destroy()
    }

    this.spineObject = this.scene!.add.spine(
      this.sprite!.x,
      this.sprite!.y,
      MobDataSetKeys.FLEGMA_FLEX_DATA,
      MobDataSetKeys.FLEGMA_FLEX_ATLAS
    )

    this.spineObject?.animationState.setAnimation(0, FlegmaFlexAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, FlegmaFlexAnimations.T1_IDLE, true)

    this.spineObject?.setScale(0.5)

    this.spineObject?.setDepth(DepthOrder.Enemies)
    this.sprite?.setAlpha(0)
  }

  private initMovement(): void {
    const startX = this.sprite!.x

    // this.jump(
    //   startX,
    //   MobStaticVerticalConsts.JUMP_DURATION,
    //   MobStaticVerticalConsts.JUMP_HEIGHT,
    //   () => {
    //     this.scene!.time.delayedCall(MobStaticVerticalConsts.DELAY_CALL_DURATION, () => {
    //       this.initMovement()
    //     })
    //   }
    // )
  }

  private jump(startX: number, duration: number, jumpHeight: number, onComplete: () => void): void {
    const startY = this.sprite!.y
    this.movementTween =
      this.scene?.tweens.add({
        targets: this.sprite,
        x: startX,
        duration: duration,
        ease: 'Sine.easeIn',
        onUpdate: tween => {
          const progress = tween.progress
          const height = -4 * jumpHeight * Math.pow(progress - 0.5, 2) + jumpHeight
          this.sprite!.y = startY - height
          this.spriteCollider!.x = this.sprite!.x + MobStaticVerticalConsts.COLLIDER_OFFSET_X
          this.spriteCollider!.y = this.sprite!.y + MobStaticVerticalConsts.COLLIDER_OFFSET_Y
          if (this.spineObject) {
            this.spineObject.x = this.sprite!.x + FlegmaFlexAnimations.X_OFFSET
            this.spineObject.y = this.sprite!.y + FlegmaFlexAnimations.Y_OFFSET
          }
        },
        onComplete: () => {
          if (this.movementTween) {
            clearTween(this.movementTween)
            this.movementTween = null
          }
          this.sprite!.x = startX
          this.sprite!.y = startY
          if (this.spineObject) {
            this.spineObject.x = this.sprite!.x + FlegmaFlexAnimations.X_OFFSET
            this.spineObject.y = this.sprite!.y + FlegmaFlexAnimations.Y_OFFSET
          }
          if (onComplete) {
            onComplete()
          }
        }
      }) ?? null
  }

  override triggerInPlayerProximityBehaviour(): void {
    if (!this.sprite || Enemy.isDefeated[this.entity]) return
    this.spineObject?.animationState.setAnimation(1, FlegmaFlexAnimations.T1_IDLE2, true)
  }

  override triggerHit(): boolean {
    if (!this.spineObject) return false

    Health.value[this.entity] -= 1

    if (Health.value[this.entity] < 0) {
      return false
    }

    this.spineObject.animationState.clearListeners()

    if (Health.value[this.entity] === 1) {
      this.spineObject.animationState.setAnimation(1, FlegmaFlexAnimations.T1_HIT, false)
      this.spineObject.animationState.addAnimation(1, FlegmaFlexAnimations.T1_IDLE3, true, 0)
    } else if (Health.value[this.entity] === 0) {
      this.defeat(false)
      return true
    }
    return false
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    this.disableCollider()
    this.spineObject?.animationState.setAnimation(1, FlegmaFlexAnimations.T1_DEATH, false)
    this.scene?.time.delayedCall(
      950,
      () => {
        super.defeat(wasStomped)
        if (this.movementTween) this.movementTween.stop()
      },
      [],
      this
    )
    this.setHealth(MobStaticVerticalConsts.HEALTH)
  }

  override reset(options: EnemyOptions): void {
    if (this.movementTween) this.movementTween.stop()
    super.reset(options)
    this.enableCollision()

    this.sprite?.setAlpha(0)
    const sprite = this.getSprite()
    const { x, y } = sprite

    this.initMovement()

    if (!this.spineObject) return
    this.spineObject.x = x + FlegmaFlexAnimations.X_OFFSET
    this.spineObject.y = y + FlegmaFlexAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(0, FlegmaFlexAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, FlegmaFlexAnimations.T1_IDLE, true)
  }
}
