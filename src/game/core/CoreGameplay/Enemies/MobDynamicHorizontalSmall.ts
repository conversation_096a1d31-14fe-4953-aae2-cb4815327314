import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { EnemyOptions } from '@/game/core/CoreGameplay/Enemies/EnemyOptions'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDynamicHorizontalSmallConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, SimpyAnimations } from '../Player/PlayerStates/States/SpineAnimations'

export class MobDynamicHorizontalSmall extends BaseEnemy {
  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalSmallConsts.HEALTH)
    this.createSpineObject()
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + SimpyAnimations.X_OFFSET,
      this.sprite!.y + SimpyAnimations.Y_OFFSET,
      MobDataSetKeys.SIMPY_DATA,
      MobDataSetKeys.SIMPY_ATLAS
    )
    this.spineObject?.setScale(SimpyAnimations.SCALE)
    //this.makeColliderCircle(MobDynamicHorizontalSmallConsts.COLLIDER_SCALE)
    this.spriteCollider?.setOffset(
      MobDynamicHorizontalSmallConsts.COLLIDER_OFFSET_X,
      MobDynamicHorizontalSmallConsts.COLLIDER_OFFSET_Y
    )
    this.spineObject?.setOrigin(0, 0)

    this.spineObject?.animationState.setAnimation(0, SimpyAnimations.OUTLINE_COLOR_2, true)
    this.spineObject?.animationState.setAnimation(1, SimpyAnimations.IDLE, true)

    this.spineObject?.setDepth(DepthOrder.Enemies)

    this.sprite?.setAlpha(0)
  }

  override triggerInPlayerProximityBehaviour() {
    super.triggerInPlayerProximityBehaviour()
    this.spineObject?.animationState.setAnimation(2, SimpyAnimations.REACTION, false)
    this.spineObject?.animationState.addListener({
      complete: trackEntry => {
        if (trackEntry.animation?.name === SimpyAnimations.REACTION) {
          this.spineObject?.animationState.addAnimation(2, SimpyAnimations.IDLE_2, true)
        }
      }
    })
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    this.disableCollider()
    this.spineObject?.animationState.setAnimation(3, SimpyAnimations.DEATH_2, false)
    this.scene?.time.delayedCall(1000, () => {
      super.defeat(wasStomped)
      this.setHealth(MobDynamicHorizontalSmallConsts.HEALTH)
    })
  }

  override reset(options: EnemyOptions): void {
    super.reset(options)
    this.sprite?.setAlpha(0)
    const sprite = this.getSprite()
    const { x, y } = sprite

    //this.makeColliderCircle(MobDynamicHorizontalSmallConsts.COLLIDER_SCALE)
    this.spriteCollider?.setOffset(
      MobDynamicHorizontalSmallConsts.COLLIDER_OFFSET_X,
      MobDynamicHorizontalSmallConsts.COLLIDER_OFFSET_Y
    )

    this.spineObject!.x = x + SimpyAnimations.X_OFFSET
    this.spineObject!.y = y + SimpyAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(0, SimpyAnimations.OUTLINE_COLOR_2, true)
    this.spineObject?.animationState.setAnimation(1, SimpyAnimations.IDLE, true)
  }
}
