import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import Phaser from 'phaser'
import type { EnemyOptions } from './EnemyOptions'

export class EnemyPool<T extends BaseEnemy & { isActive: boolean; nextFree: T | null }> {
  private readonly pool: Set<T> = new Set()
  private freeListHead: T | null = null
  private freeListCount: number = 0

  private readonly freePoolLimit: number

  private readonly scene: Phaser.Scene
  private readonly EnemyClass: new (options: EnemyOptions) => T

  private totalTakenFromPool: number = 0
  private totalCreatedNew: number = 0

  constructor(
    scene: Phaser.Scene,
    EnemyClass: new (options: EnemyOptions) => T,
    freePoolLimit: number
  ) {
    this.scene = scene
    this.EnemyClass = EnemyClass
    this.freePoolLimit = freePoolLimit
  }

  async preload(count: number, entityType: EntityType): Promise<void> {
    const dummyGroup = this.scene.physics.add.staticGroup()
    const dummyInterface = undefined

    for (let i = 0; i < count; i++) {
      const options: EnemyOptions = {
        mobGroup: dummyGroup,
        scene: this.scene as GameScene,
        position: { x: -1000, y: 1000 },
        entityType,
        chunkInfo: {
          id: 0,
          index: -1,
          platformIndex: -1,
          length: 0,
          prevChunkCumulativeLenght: 0,
          generation: 0
        },
        gameInterface: dummyInterface,
        customParameters: {}
      }

      const enemy = new this.EnemyClass(options)
      enemy.returnToPool()
      enemy.isActive = false
      enemy.nextFree = null
      this.pool.add(enemy)

      // Add enemy to the free list.
      enemy.nextFree = this.freeListHead
      this.freeListHead = enemy
      this.freeListCount++
    }
  }

  get(options: EnemyOptions): T {
    let enemy: T
    if (this.freeListHead) {
      enemy = this.freeListHead
      this.freeListHead = enemy.nextFree
      enemy.nextFree = null
      this.freeListCount--
      enemy.isActive = true
      enemy.reset(options)
      this.totalTakenFromPool++
    } else {
      enemy = new this.EnemyClass(options)
      enemy.isActive = true
      enemy.nextFree = null
      this.pool.add(enemy)
      enemy.reset(options)
      this.totalCreatedNew++
    }
    return enemy
  }

  return(enemy: T): void {
    enemy.returnToPool()
    enemy.isActive = false

    if (this.freeListCount < this.freePoolLimit) {
      enemy.nextFree = this.freeListHead
      this.freeListHead = enemy
      this.freeListCount++
    } else {
      this.pool.delete(enemy)
      enemy.destroyMob()
    }
  }

  getAll(): T[] {
    return Array.from(this.pool)
  }

  clear(): void {
    this.pool.forEach(enemy => enemy.destroyMob())
    this.pool.clear()
    this.freeListHead = null
    this.freeListCount = 0
  }
}
