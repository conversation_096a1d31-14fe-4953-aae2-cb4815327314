import { <PERSON>Enemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import { Enemy } from '../../ecs/Components/EntityComponents'
import { DepthOrder } from '../Constants/DephOrdering'
import { UFOConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, UFOAnimations } from '../Player/PlayerStates/States/SpineAnimations'
import type { EnemyOptions } from './EnemyOptions'

export class UFO extends BaseEnemy {
  public lightCollider: Phaser.Physics.Arcade.Body | null = null

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(UFOConsts.HEALTH)
    this.createSpineObject()
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + UFOAnimations.X_OFFSET,
      this.sprite!.y + UFOAnimations.Y_OFFSET,
      MobDataSetKeys.UFO_DATA,
      MobDataSetKeys.UFO_ATLAS
    )

    this.spineObject?.setScale(0.7)

    this.spineObject?.animationState.setAnimation(0, UFOAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, UFOAnimations.T1_IDLE, true)
    this.spineObject?.setDepth(DepthOrder.Enemies)

    this.sprite?.setAlpha(0)
  }

  protected createEntity(x: number, y: number, type: EntityType): void {
    super.createEntity(x, y, type)

    this.spriteCollider!.setSize(
      ENTITIES_SIZES[this.entityType()][0] * 0.7,
      ENTITIES_SIZES[this.entityType()][1] * 0.5
    )

    this.spriteCollider?.setOffset(
      UFOConsts.SPRITE_COLLIDER_OFFSET_X,
      UFOConsts.SPRITE_COLLIDER_OFFSET_Y
    )

    this.lightCollider =
      this.scene?.physics.add.body(
        x + UFOConsts.LIGHT_COLLIDER_OFFSET_X,
        y + ENTITIES_SIZES[this.entityType()][1] * UFOConsts.SPRITE_COLLIDER_SCALE_Y,
        ENTITIES_SIZES[this.entityType()][0] * UFOConsts.LIGHT_COLLIDER_SCALE_X,
        ENTITIES_SIZES[this.entityType()][1] * UFOConsts.LIGHT_COLLIDER_SCALE_Y
      ) ?? null
  }

  override reset(options: EnemyOptions): void {
    super.reset(options)

    this.enableCollision()
    const sprite = this.getSprite()
    const { x, y } = sprite
    this.sprite?.setAlpha(0)

    this.spriteCollider?.setOffset(
      UFOConsts.SPRITE_COLLIDER_OFFSET_X,
      UFOConsts.SPRITE_COLLIDER_OFFSET_Y
    )

    this.spineObject!.x = x + UFOAnimations.X_OFFSET
    this.spineObject!.y = y + UFOAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(0, UFOAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, UFOAnimations.T1_IDLE, true)
  }

  override destroyMob(): void {
    if (this.lightCollider) {
      this.lightCollider.destroy()
    }
    super.destroyMob()
  }

  enableLightCollision(shouldCollide: boolean) {
    this.lightCollider!.enable = shouldCollide
  }

  override triggerInPlayerProximityBehaviour(): void {
    if (!this.sprite || Enemy.isDefeated[this.entity]) return
    this.spineObject?.animationState.setAnimation(1, UFOAnimations.T1_IDLE2, true)
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    this.disableCollider()
    if (this.lightCollider) {
      this.lightCollider.destroy()
      this.lightCollider = null
    }
    this.spineObject?.animationState.setAnimation(1, UFOAnimations.T1_Death, false)
    this.scene?.time.delayedCall(
      850,
      () => {
        super.defeat(wasStomped)
      },
      [],
      this
    )
    this.setHealth(UFOConsts.HEALTH)
  }

  getLightColliderTopCenter(): Phaser.Math.Vector2 {
    return new Phaser.Math.Vector2(
      this.lightCollider!.x + this.lightCollider!.width / 2,
      this.lightCollider!.y
    )
  }
}
