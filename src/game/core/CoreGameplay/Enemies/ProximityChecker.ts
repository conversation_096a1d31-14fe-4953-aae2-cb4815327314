import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'

export class ProximityChecker {
  private triggeredProximityMobs: Set<BaseEnemy> = new Set()

  constructor(
    private scene: GameScene,
    private allActiveMobs: Set<BaseEnemy>
  ) {
    this.scene.events.on('current-score-changed', this.handleProximityCheck, this)
  }

  private handleProximityCheck(): void {
    try {
      const player = this.scene.getPlayer()
      if (!player) return

      const px = player.x
      const py = player.y
      const invalidMobs: BaseEnemy[] = []

      for (const mob of this.allActiveMobs) {
        const sprite = mob.getSprite()
        if (!sprite) {
          invalidMobs.push(mob)
          continue
        }

        if (mob.entityType() === EntityType.MobBlackHole) {
          continue
        }

        const dx = px - sprite.x
        const dy = py - sprite.y
        const distanceSq = dx * dx + dy * dy

        const threshold = mob.getProximityThreshold?.() || 350
        if (distanceSq <= threshold * threshold) {
          if (!this.triggeredProximityMobs.has(mob)) {
            mob.triggerInPlayerProximityBehaviour()
            this.triggeredProximityMobs.add(mob)
          }
        }

        const aimbotThreshold = 550
        if (distanceSq <= aimbotThreshold * aimbotThreshold) {
          this.scene.events.emit('aimbot:shoot', mob)
        }
      }

      for (const mob of invalidMobs) {
        this.allActiveMobs.delete(mob)
        this.triggeredProximityMobs.delete(mob)
      }
    } catch (error: any) {
      console.error(`Error in ProximityChecker: ${error.message}`)
    }
  }

  public reset(): void {
    this.triggeredProximityMobs.clear()
  }

  public removeMobFromTriggered(mob: BaseEnemy): void {
    this.triggeredProximityMobs.delete(mob)
  }
}
