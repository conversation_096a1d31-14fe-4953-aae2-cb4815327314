import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { EnemyOptions } from '@/game/core/CoreGameplay/Enemies/EnemyOptions'
import { Enemy, Health } from '../../ecs/Components/EntityComponents'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobStaticConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, SlippyAnimations } from '../Player/PlayerStates/States/SpineAnimations'

export class MobStatic extends BaseEnemy {
  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobStaticConsts.HEALTH)
    this.createSpineObject()
  }

  override reset(options: EnemyOptions): void {
    super.reset(options)

    this.enableCollision()
    const sprite = this.getSprite()
    const { x, y } = sprite
    this.sprite?.setAlpha(0)
    this.spineObject!.x = x + SlippyAnimations.X_OFFSET
    this.spineObject!.y = y + SlippyAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(0, SlippyAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, SlippyAnimations.T1_IDLE, true)
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + SlippyAnimations.X_OFFSET,
      this.sprite!.y + SlippyAnimations.Y_OFFSET,
      MobDataSetKeys.SLIPPY_DATA,
      MobDataSetKeys.SLIPPY_ATLAS
    )

    const finalScale = this.sprite!.scale
    this.spineObject?.setScale(finalScale)

    this.spineObject?.animationState.setAnimation(0, SlippyAnimations.BLUE_OUTLINE, true)
    this.spineObject?.animationState.setAnimation(1, SlippyAnimations.T1_IDLE, true)
    this.spineObject?.setDepth(DepthOrder.Enemies)

    this.sprite?.setAlpha(0)
  }

  override triggerInPlayerProximityBehaviour(): void {
    if (!this.sprite || Enemy.isDefeated[this.entity]) return
    this.spineObject?.animationState.setAnimation(1, SlippyAnimations.T1_IDLE2, true)
  }

  override triggerHit(): boolean {
    if (!this.spineObject) return false

    Health.value[this.entity] -= 1

    if (Health.value[this.entity] < 0) {
      return false
    }

    this.spineObject?.animationState.clearListeners()

    if (Health.value[this.entity] === 1) {
      this.spineObject?.animationState.setAnimation(1, SlippyAnimations.T1_HIT2, false)
      this.spineObject?.animationState.addAnimation(1, SlippyAnimations.T1_IDLE3, true, 0)
      return true
    } else if (Health.value[this.entity] === 0) {
      this.defeat(false)
    }
    return false
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    this.disableCollider()
    this.spineObject?.animationState.setAnimation(1, SlippyAnimations.T1_DEATH2, false)
    this.scene?.time.delayedCall(
      950,
      () => {
        super.defeat(wasStomped)
      },
      [],
      this
    )
    this.setHealth(MobStaticConsts.HEALTH)
  }
}
