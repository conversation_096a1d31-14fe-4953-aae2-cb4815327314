import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDynamicHorizontalVerticalConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, SigmaAnimations } from '../Player/PlayerStates/States/SpineAnimations'
import type { EnemyOptions } from './EnemyOptions'

export class MobDynamicHorizontalVertical extends BaseEnemy {
  private movementTween!: Phaser.Tweens.Tween | null
  public hasMovementStarted: boolean = false

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalVerticalConsts.HEALTH)
    this.setProximityThreshold(MobDynamicHorizontalVerticalConsts.PROXIMITY_TO_TRIGGER_BEHAVIOUR)
    this.createSpineObject()
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x + SigmaAnimations.X_OFFSET,
      this.sprite!.y + SigmaAnimations.Y_OFFSET,
      MobDataSetKeys.SIGMA_DATA,
      MobDataSetKeys.SIGMA_ATLAS
    )
    this.spineObject?.setScale(SigmaAnimations.SCALE)
    this.spineObject?.setDepth(DepthOrder.Enemies)
    this.sprite?.setAlpha(0)
    this.spineObject?.animationState.setAnimation(4, 'animation', true)
  }

  override triggerInPlayerProximityBehaviour(): void {
    super.triggerInPlayerProximityBehaviour()
    if (this.hasMovementStarted) {
      return
    }
    this.hasMovementStarted = true
    this.moveHorizontallyAndUpwards(true)
  }

  private moveHorizontallyAndUpwards(moveRight: boolean): void {
    if (!this.sprite!.active) {
      return
    }

    const cameraLeft =
      this.scene!.cameras.main.worldView.left +
      MobDynamicHorizontalVerticalConsts.CAMERA_LEFT_OFFSET
    const cameraRight =
      this.scene!.cameras.main.worldView.right -
      MobDynamicHorizontalVerticalConsts.CAMERA_RIGHT_OFFSET
    const cameraTop =
      this.scene!.cameras.main.worldView.top + MobDynamicHorizontalVerticalConsts.CAMERA_TOP_OFFSET
    const cameraBottom =
      this.scene!.cameras.main.worldView.bottom +
      MobDynamicHorizontalVerticalConsts.CAMERA_BOTTOM_OFFSET

    const targetX = moveRight ? cameraRight : cameraLeft
    const targetY = Phaser.Math.Clamp(
      this.sprite!.y - MobDynamicHorizontalVerticalConsts.MOVE_UPWARD_DISTANCE,
      cameraTop,
      cameraBottom
    )

    this.movementTween =
      this.scene?.tweens.add({
        targets: this.sprite,
        x: targetX,
        y: targetY,
        duration: MobDynamicHorizontalVerticalConsts.TWEEN_DURATION,
        ease: 'Linear',
        onUpdate: () => {
          const newX = this.sprite!.x
          const newY = this.sprite!.y
          this.spriteCollider!.x = newX
          this.spriteCollider!.y = newY
          if (!this.spineObject) return
          this.spineObject.x = newX + SigmaAnimations.X_OFFSET
          this.spineObject.y = newY + SigmaAnimations.Y_OFFSET
        },
        onComplete: () => {
          this.moveHorizontallyAndUpwards(!moveRight)
        }
      }) ?? null
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    if (this.movementTween) clearTween(this.movementTween)
    this.movementTween = null

    const sprite = this.getSprite()
    const { x, y } = sprite
    this.spineObject!.x = x + SigmaAnimations.X_OFFSET
    this.spineObject!.y = y + SigmaAnimations.Y_OFFSET

    super.defeat(wasStomped)
    this.setHealth(MobDynamicHorizontalVerticalConsts.HEALTH)
    this.hasMovementStarted = false
  }

  override reset(options: EnemyOptions): void {
    super.reset(options)

    if (this.movementTween) clearTween(this.movementTween)
    this.movementTween = null

    const sprite = this.getSprite()
    const { x, y } = sprite
    this.spineObject!.x = x + SigmaAnimations.X_OFFSET
    this.spineObject!.y = y + SigmaAnimations.Y_OFFSET

    this.hasMovementStarted = false
    this.spineObject?.animationState.setAnimation(4, 'animation', true)
  }
}
