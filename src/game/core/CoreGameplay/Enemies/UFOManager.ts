import type { GameScene } from '@/game/core/scenes/GameScene'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { UFO } from './UFO'

export class UFOManager {
  private scene: GameScene
  private mobs: UFO[] = []
  private player!: Phaser.Physics.Arcade.Sprite | null

  constructor(scene: GameScene) {
    this.scene = scene
    this.scene.events.on('update', this.update, this)
  }

  public cachePlayer(): void {
    this.player = this.scene.getPlayer()
  }

  private update(): void {
    if (this.player && this.player.body) {
      this.mobs.forEach(mob => {
        if (mob.lightCollider && this.player) {
          this.scene.physics.world.overlap(
            this.player,
            mob.lightCollider as Phaser.Physics.Arcade.Body,
            () => {
              mob.enableLightCollision(false)
              this.scene.events.emit(
                GAME_EVENTS.PLAYER_TOUCHED_LIGHT,
                mob,
                mob.getLightColliderTopCenter()
              )
            }
          )
        }
      })
    }
  }

  addMob(mob: UFO): void {
    if (!this.mobs.includes(mob)) {
      this.mobs.push(mob)
    }
  }

  removeMob(mob: UFO): void {
    this.mobs = this.mobs.filter(m => m !== mob)
  }

  clear(): void {
    this.mobs = []
  }
}
