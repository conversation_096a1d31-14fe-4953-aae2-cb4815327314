import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { ChunkInfo } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters.ts'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import Phaser from 'phaser'

export interface EnemyOptions {
  mobGroup: Phaser.Physics.Arcade.StaticGroup
  scene: GameScene
  position: { x: number; y: number }
  entityType: EntityType
  chunkInfo: ChunkInfo
  gameInterface: GameInterface | undefined
  customParameters: CustomParameters
}
