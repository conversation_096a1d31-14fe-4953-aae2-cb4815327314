import { GameViewConsts } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { EnemyOptions } from '@/game/core/CoreGameplay/Enemies/EnemyOptions'
import { SpineGameObject } from '@esotericsoftware/spine-phaser'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDynamicHorizontalMiddleConsts } from '../Constants/MobConsts'
import { MobDataSetKeys, RostyAnimations } from '../Player/PlayerStates/States/SpineAnimations'

export class MobDynamicHorizontalMiddle extends BaseEnemy {
  private movementTween: Phaser.Tweens.Tween | null = null
  private shouldMoveHalfScreen: boolean = false
  private previousX: number = 0
  private isDefeated: boolean = false

  constructor(options: EnemyOptions) {
    super(options)
    this.setHealth(MobDynamicHorizontalMiddleConsts.HEALTH)
    this.shouldMoveHalfScreen = options.customParameters?.mobHalfScreenMovement || false
    this.createSpineObject()
    this.startTweener()
  }

  protected createSpineObject(): void {
    this.spineObject = this.scene!.add.spine(
      this.sprite!.x,
      this.sprite!.y,
      MobDataSetKeys.ROSTY_DATA,
      MobDataSetKeys.ROSTY_ATLAS
    )
    this.spineObject.setScale(RostyAnimations.SCALE)
    this.spineObject.animationState.setAnimation(0, RostyAnimations.OUTLINE_COLOR_1, true)
    this.spineObject.animationState.setAnimation(1, RostyAnimations.IDLE, true)
    this.spineObject.setDepth(DepthOrder.Enemies)
    this.sprite?.setAlpha(0)
  }

  override defeat(wasStomped: boolean): void {
    this.registerMobsDeath(wasStomped)
    if (this.movementTween) {
      this.scene?.tweens.killTweensOf(this.getSprite())
      this.movementTween = null
    }

    this.isDefeated = true

    const sprite = this.getSprite()
    const { x, y } = sprite
    this.spineObject!.x = x
    this.spineObject!.y = y + RostyAnimations.Y_OFFSET

    this.spineObject?.animationState.setAnimation(3, RostyAnimations.DIE_2, false)

    this.scene?.time.delayedCall(1000, () => {
      super.defeat(wasStomped)
      this.setHealth(MobDynamicHorizontalMiddleConsts.HEALTH)
      if (this.movementTween) clearTween(this.movementTween)
      this.movementTween = null
      this.spineObject?.animationState.clearTracks()
    })
  }

  override reset(options: EnemyOptions): void {
    if (this.movementTween) clearTween(this.movementTween)
    this.movementTween = null

    super.reset(options)
    this.sprite?.setAlpha(0)
    this.isDefeated = false

    this.spineObject?.animationState.setAnimation(0, RostyAnimations.OUTLINE_COLOR_1, true)
    this.spineObject?.animationState.setAnimation(1, RostyAnimations.IDLE, true)

    this.shouldMoveHalfScreen = options.customParameters?.mobHalfScreenMovement || false
    this.previousX = this.sprite!.x
    this.startTweener()
  }

  public startTweener() {
    let randomDuration = Phaser.Math.Between(
      MobDynamicHorizontalMiddleConsts.RANDOM_DURATION_MIN,
      MobDynamicHorizontalMiddleConsts.RANDOM_DURATION_MAX
    )
    if (this.shouldMoveHalfScreen) {
      randomDuration = randomDuration / MobDynamicHorizontalMiddleConsts.HALF_SCREEN_DIVISOR
    }

    const sprite = this.getSprite()

    this.movementTween = createMobMovementTween(
      this.scene!,
      sprite,
      this.spineObject!,
      this.spriteCollider!,
      randomDuration,
      this.shouldMoveHalfScreen,
      this.previousX,
      this.entity,
      this.isDefeated
    )

    this.movementTween.play()
  }

  override triggerInPlayerProximityBehaviour() {
    super.triggerInPlayerProximityBehaviour()
    if (this.isDefeated) return
    this.spineObject?.animationState.setAnimation(2, RostyAnimations.KICKING_AIR, false)
    this.spineObject?.animationState.addListener({
      complete: trackEntry => {
        if (trackEntry.animation?.name === RostyAnimations.KICKING_AIR) {
          this.spineObject?.animationState.addAnimation(1, RostyAnimations.IDLE, true)
          this.spineObject?.animationState.addAnimation(2, RostyAnimations.IDLE_LOOKING_DOWN, true)
        }
      }
    })
  }
}

export function createMobMovementTween(
  scene: Phaser.Scene,
  sprite: Phaser.GameObjects.Sprite,
  spineObject: SpineGameObject,
  collider: Phaser.Physics.Arcade.Body,
  movementDuration: number,
  shouldMoveHalfScreen: boolean,
  previousX: number,
  entity: number,
  isDefeated: boolean
): Phaser.Tweens.Tween {
  const camera = scene.cameras.main
  const spriteWidth = sprite.displayWidth
  const startX = camera.worldView.left + collider.width
  const endX = camera.worldView.right - collider.width
  const screenCenter = GameViewConsts.REF_WIDTH / 2

  let initialX, finalX
  if (shouldMoveHalfScreen) {
    if (sprite.x < screenCenter) {
      initialX = startX
      finalX = screenCenter - spriteWidth
    } else {
      initialX = screenCenter
      finalX = endX
    }
  } else {
    const startFromRight = entity % 2 === 0
    initialX = startFromRight ? endX : startX
    finalX = startFromRight ? startX : endX
  }

  const tweenConfig = {
    targets: sprite,
    x: {
      from: initialX,
      to: finalX
    },
    ease: 'Sine.easeInOut',
    duration: movementDuration,
    yoyo: true,
    repeat: -1,
    onUpdate: () => {
      updateSpinePositionAndDirection(
        sprite,
        spineObject,
        collider,
        previousX,
        collider.width / 2,
        isDefeated
      )
      previousX = sprite.x
    }
  }

  return scene.tweens.add(tweenConfig)
}

function updateSpinePositionAndDirection(
  sprite: Phaser.GameObjects.Sprite,
  spineObject: SpineGameObject,
  collider: Phaser.Physics.Arcade.Body,
  previousX: number,
  bodyOffset: number,
  isDefeated: boolean
): void {
  if (isDefeated) return
  if (!spineObject) return
  spineObject.x = sprite.x
  spineObject.y = sprite.y + RostyAnimations.Y_OFFSET
  collider.x = sprite.x - bodyOffset

  if (sprite.x > previousX) {
    spineObject.scaleX = -Math.abs(spineObject.scaleX)
  } else if (sprite.x < previousX) {
    spineObject.scaleX = Math.abs(spineObject.scaleX)
  }
}
