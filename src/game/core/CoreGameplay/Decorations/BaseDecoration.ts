import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering.ts'
import Image = Phaser.GameObjects.Image

export class BaseDecoration {
  protected readonly textureKey: string
  protected readonly image: Image

  constructor(textureKey: string, scene: Phaser.Scene, position: { x: number; y: number }) {
    this.textureKey = textureKey
    this.image = scene.add
      .image(position.x, position.y, 'decor', `${textureKey}.png`)
      .setOrigin(0, 0)
      .setDepth(DepthOrder.Decorations)
  }

  public checkIfOutsideScreen(lowestScreenPoint: number): boolean {
    return this.image.y > lowestScreenPoint
  }

  public destroy(): void {
    this.image.destroy()
  }
}
