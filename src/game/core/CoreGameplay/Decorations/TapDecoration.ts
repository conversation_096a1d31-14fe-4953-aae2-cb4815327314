import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class TapDecoration extends BaseDecoration {
  private readonly tapDecoration: Phaser.GameObjects.Image
  private readonly tapDecorationText: Phaser.GameObjects.Image
  private readonly tapDecorationArea: Phaser.GameObjects.Image
  private tapTween: Phaser.Tweens.Tween | null = null
  private readonly s: Phaser.Scene = this.image.scene

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.tapDecoration = this.image
    this.s = scene
    this.tapDecoration.setOrigin(0.5, 0.5)
    this.tapDecoration.x += this.tapDecoration.width
    this.tapDecoration.y += this.tapDecoration.height / 2

    this.tapDecorationText = this.s.add.image(
      this.tapDecoration.x,
      this.tapDecoration.y + 100,
      AtlasNames.DECOR,
      `${texture}_text.png`
    )
    this.tapDecorationArea = this.s.add.image(
      this.tapDecoration.x,
      this.tapDecoration.y,
      AtlasNames.DECOR,
      `${texture}_area.png`
    )
    this.tapDecorationArea.setScale(0)
    this.createTapAnimation()
  }

  private createTapAnimation(): void {
    this.tapTween = this.s.tweens.add({
      targets: this.tapDecoration,
      scale: { from: 1, to: 1.2 },
      duration: 600,
      yoyo: true,
      ease: 'Sine.easeInOut',
      repeat: -1,
      delay: 300
    })

    this.s.tweens.add({
      targets: this.tapDecorationArea,
      scale: { from: 0, to: 1 },
      duration: 600,
      yoyo: true,
      ease: 'Back.easeOut',
      repeat: -1,
      delay: 1000
    })
  }

  public checkIfOutsideScreen(lowestScreenPoint: number): boolean {
    return this.tapDecoration.y > lowestScreenPoint
  }

  public destroy(): void {
    if (this.tapTween) {
      this.tapTween.stop()
      this.tapTween.remove()
      this.tapTween = null
    }
    this.tapDecoration.destroy()
    this.tapDecorationText.destroy()
    this.tapDecorationArea.destroy()
  }
}
