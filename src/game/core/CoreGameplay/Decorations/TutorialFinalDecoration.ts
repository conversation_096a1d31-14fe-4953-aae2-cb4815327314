import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class TutorialFinalDecoration extends BaseDecoration {
  private readonly tutorialFinalDecoration: Phaser.GameObjects.Image

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.tutorialFinalDecoration = this.image
    this.tutorialFinalDecoration.setOrigin(0.5, 0.5)
    this.tutorialFinalDecoration.x += this.tutorialFinalDecoration.width
    this.tutorialFinalDecoration.y += this.tutorialFinalDecoration.height * 4
  }

  public destroy(): void {
    this.tutorialFinalDecoration.destroy()
  }
}
