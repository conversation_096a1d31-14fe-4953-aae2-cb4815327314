import { AirdropDecoration } from '@/game/core/CoreGameplay/Decorations/AirdropDecoration.ts'
import { AvoidDecoration } from '@/game/core/CoreGameplay/Decorations/AvoidDecoration.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'
import { BoosterDecoration } from '@/game/core/CoreGameplay/Decorations/BoosterDecoration.ts'
import { HyroscopeAccessDecoration } from '@/game/core/CoreGameplay/Decorations/HyroscopeAccessDecoration.ts'
import { TapDecoration } from '@/game/core/CoreGameplay/Decorations/TapDecoration.ts'
import { TutorialFinalDecoration } from '@/game/core/CoreGameplay/Decorations/TutorialFinalDecoration.ts'
import type { SpriteConfig } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions.ts'
import { EntityType } from '@/game/core/MapGen/MapGenerator.ts'

export class DecorationsBuilder {
  private readonly scene: Phaser.Scene
  private readonly decorations: BaseDecoration[] = []
  private readonly animatedDecorationsKeys: string[] = []

  constructor(scene: Phaser.Scene) {
    this.scene = scene
    this.animatedDecorationsKeys = ['0001', '0002', '0003', '0004', '0005', '0006']
  }

  public createDecoration(type: EntityType, spriteConfig: SpriteConfig): BaseDecoration {
    const { position, texture } = spriteConfig
    let decoration: BaseDecoration | null = null
    if (this.animatedDecorationsKeys.includes(texture)) {
      switch (texture) {
        case '0001':
          decoration = new HyroscopeAccessDecoration(this.scene, position, texture)
          break
        case '0002':
          decoration = new TapDecoration(this.scene, position, texture)
          break
        case '0003':
          decoration = new AvoidDecoration(this.scene, position, texture)
          break
        case '0004':
          decoration = new AirdropDecoration(this.scene, position, texture)
          break
        case '0005':
          decoration = new BoosterDecoration(this.scene, position, texture)
          break
        case '0006':
          decoration = new TutorialFinalDecoration(this.scene, position, texture)
          break
        default:
          console.error(`Unknown animated decoration key: ${texture}`)
          break
      }
    } else {
      decoration = new BaseDecoration(texture, this.scene, position)
    }

    if (decoration === null) {
      throw new Error('Decoration is null')
    }

    this.decorations.push(decoration!)
    return decoration
  }

  tryToDestroyDecoration(lowestScreenPoint: number) {
    if (this.decorations.length === 0) return

    this.decorations.forEach((decoration, index) => {
      if (decoration.checkIfOutsideScreen(lowestScreenPoint)) {
        decoration.destroy()
        this.decorations.splice(index, 1)
      }
    })
  }
}
