import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering.ts'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class AirdropDecoration extends BaseDecoration {
  private readonly airdropDecoration: Phaser.GameObjects.Image
  private readonly blinkSprite: Phaser.GameObjects.Image
  private readonly airdropText: Phaser.GameObjects.Image
  private blinkTween: Phaser.Tweens.Tween | null = null

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.airdropDecoration = this.image
    this.airdropDecoration.x += this.airdropDecoration.width / 2
    this.airdropDecoration.y += this.airdropDecoration.height / 2
    const maskShape = this.createCircularMask(scene)
    this.airdropDecoration.setOrigin(0.5, 0.5)
    this.airdropText = scene.add.image(
      this.airdropDecoration.x + 20,
      this.airdropDecoration.y + 70,
      AtlasNames.DECOR,
      `${texture}_text.png`
    )

    this.blinkSprite = scene.add.image(
      position.x - this.airdropDecoration.width / 2,
      position.y + this.airdropDecoration.height / 2,
      AtlasNames.DECOR,
      `${texture}_blink.png`
    )

    this.blinkSprite.setDepth(DepthOrder.Decorations + 1).setOrigin(0.5, 0.5)
    this.blinkSprite.setMask(new Phaser.Display.Masks.GeometryMask(scene, maskShape))

    this.createBlinkAnimation()
  }

  private createCircularMask(scene: Phaser.Scene): Phaser.GameObjects.Graphics {
    const maskShape = scene.add.graphics()
    const radius = this.airdropDecoration.width / 4.5
    maskShape
      .fillStyle(0xffffff)
      .setDepth(DepthOrder.Decorations + 1)
      .setAlpha(0)
    maskShape.fillCircle(
      this.airdropDecoration.x + this.airdropDecoration.width / 4 - 5,
      this.airdropDecoration.y + this.airdropDecoration.height / 4 - 5,
      radius
    )
    return maskShape
  }

  private createBlinkAnimation(): void {
    this.blinkTween = this.blinkSprite.scene.tweens.add({
      targets: this.blinkSprite,
      x: this.airdropDecoration.x + this.airdropDecoration.width / 2,
      duration: 1000,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  public checkIfOutsideScreen(lowestScreenPoint: number): boolean {
    return this.airdropDecoration.y > lowestScreenPoint
  }

  public destroy(): void {
    if (this.blinkTween) {
      this.blinkTween.stop()
      this.blinkTween.remove()
      this.blinkTween = null
    }
    this.airdropDecoration.destroy()
    this.blinkSprite.destroy()
    this.airdropText.destroy()
  }
}
