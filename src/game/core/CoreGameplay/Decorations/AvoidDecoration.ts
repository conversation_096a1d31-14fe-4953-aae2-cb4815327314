import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class AvoidDecoration extends BaseDecoration {
  private readonly avoidDecoration: Phaser.GameObjects.Image
  private readonly s: Phaser.Scene = this.image.scene
  private readonly avoidDecorationText: Phaser.GameObjects.Image
  private pulseTween: Phaser.Tweens.Tween | null = null

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.avoidDecoration = this.image
    this.avoidDecoration.setOrigin(0.5, 0.5)
    this.avoidDecoration.x += this.avoidDecoration.width / 2
    this.avoidDecoration.y += this.avoidDecoration.height / 2
    this.avoidDecorationText = this.s.add.image(
      this.avoidDecoration.x,
      this.avoidDecoration.y + this.avoidDecoration.height / 2 + 20,
      AtlasNames.DECOR,
      `${texture}_text.png`
    )

    this.createPulsateAnimation()
  }

  private createPulsateAnimation(): void {
    this.pulseTween = this.s.tweens.add({
      targets: this.avoidDecoration,
      scale: { from: 1, to: 1.2 },
      duration: 500,
      yoyo: true,
      ease: 'Sine.easeInOut',
      repeat: -1
    })
  }

  public checkIfOutsideScreen(lowestScreenPoint: number): boolean {
    return this.avoidDecoration.y > lowestScreenPoint
  }

  public destroy(): void {
    if (this.pulseTween) {
      this.pulseTween.stop()
      this.pulseTween.remove()
      this.pulseTween = null
    }
    this.avoidDecoration.destroy()
    this.avoidDecorationText.destroy()
  }
}
