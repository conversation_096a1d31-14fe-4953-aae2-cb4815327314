import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class BoosterDecoration extends BaseDecoration {
  private readonly boosterDecoration: Phaser.GameObjects.Image
  private readonly textures: string[]
  private textureIndex: number = 0
  private direction: number = 1
  private switchTween: Phaser.Tweens.Tween | null = null
  private readonly boosterText: Phaser.GameObjects.Image

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.boosterDecoration = this.image
    this.textures = [`${texture}.png`, `${texture}_2.png`, `${texture}_3.png`]
    this.boosterText = scene.add.image(
      position.x + this.boosterDecoration.width / 1.5,
      position.y + this.boosterDecoration.height * 1.2,
      AtlasNames.DECOR,
      `${texture}_text.png`
    )

    this.createSwitchTween()
  }

  private createSwitchTween(): void {
    this.switchTween = this.boosterDecoration.scene.tweens.add({
      targets: this.boosterDecoration,
      alpha: { from: 1, to: 1 },
      duration: 50,
      yoyo: true,
      repeat: -1,
      onYoyo: () => {
        this.textureIndex += this.direction

        if (this.textureIndex === this.textures.length - 1 || this.textureIndex === 0) {
          this.direction *= -1
        }

        this.boosterDecoration.setTexture(AtlasNames.DECOR, this.textures[this.textureIndex])
      },
      ease: 'Sine.easeInOut'
    })
  }

  public destroy(): void {
    if (this.switchTween) {
      this.switchTween.stop()
      this.switchTween.remove()
      this.switchTween = null
    }
    this.boosterDecoration.destroy()
    this.boosterText.destroy()
  }
}
