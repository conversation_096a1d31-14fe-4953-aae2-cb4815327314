import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'

export class HyroscopeAccessDecoration extends BaseDecoration {
  private readonly hyroscopeAccessDecoration: Phaser.GameObjects.Image
  private readonly text: Phaser.GameObjects.Image
  private readonly s: Phaser.Scene = this.image.scene
  private shakeTween: Phaser.Tweens.Tween | null = null

  constructor(scene: Phaser.Scene, position: { x: number; y: number }, texture: string) {
    super(texture, scene, position)
    this.hyroscopeAccessDecoration = this.image
    this.hyroscopeAccessDecoration.setOrigin(0.5, 0.5)
    this.hyroscopeAccessDecoration.x += this.hyroscopeAccessDecoration.width / 1.5
    this.text = this.s.add.image(
      this.hyroscopeAccessDecoration.x,
      this.hyroscopeAccessDecoration.y + this.hyroscopeAccessDecoration.height / 2 + 20,
      AtlasNames.DECOR,
      `${texture}_text.png`
    )

    this.createShakeEffect()
  }

  private createShakeEffect(): void {
    this.shakeTween = this.s.tweens.add({
      targets: this.hyroscopeAccessDecoration,
      angle: { from: -45, to: 45 },
      duration: 500,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  public checkIfOutsideScreen(lowestScreenPoint: number): boolean {
    return this.hyroscopeAccessDecoration.y > lowestScreenPoint
  }

  public destroy(): void {
    if (this.shakeTween) {
      this.shakeTween.stop()
      this.shakeTween.remove()
      this.shakeTween = null
    }
    this.hyroscopeAccessDecoration.destroy()
    this.text.destroy()
  }
}
