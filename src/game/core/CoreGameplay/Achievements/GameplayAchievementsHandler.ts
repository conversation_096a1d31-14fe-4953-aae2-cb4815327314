import { eventBus } from '@/services/eventBus/uiEventBus.ts'
import type { UnattainedAchievement } from '@/services/openapi'
import { GAME_EVENTS } from '@/shared/constants/uiEvents.ts'
import { cachedAchievementsData } from '@/shared/storage/cachedAchievementsData.ts'
import type { EventBus } from '@/shared/types'

export class CachedAchievementProgress {
  public id: number
  public currentProgress: number

  constructor(id: number, currentProgress: number) {
    this.id = id
    if (id === 3) {
      this.currentProgress = 0
    } else {
      this.currentProgress = currentProgress
    }
  }
}

export class GameplayAchievementsHandler {
  private achievementsList: Array<UnattainedAchievement> | undefined
  private uiEventBus: EventBus
  private cachedAchievements: Array<CachedAchievementProgress> | undefined
  private unlockedLevels: Map<number, number>

  constructor(uiEventBus: EventBus) {
    this.uiEventBus = uiEventBus
    this.cachedAchievements = []
    this.unlockedLevels = new Map()
  }

  initAchievements() {
    this.achievementsList = cachedAchievementsData.playerAchievements
    this.cachedAchievements = this.achievementsList?.map(
      achievement => new CachedAchievementProgress(achievement.id, achievement.currentProgress)
    )
    if (!this.achievementsList || this.achievementsList.length === 0) {
      console.log('No achievements to attain for current session')
    }
  }

  checkAchievement(achievementId: number, value: number = 1) {
    if (!this.achievementsList) return
    const achievement = this.achievementsList.find(a => a.id === achievementId)
    if (!achievement) {
      console.log('Achievement not found')
      return
    }
    const cachedAchievement = this.cachedAchievements?.find(a => a.id === achievementId)
    const currentProgress = cachedAchievement?.currentProgress ?? achievement.currentProgress
    const newProgress = currentProgress + value
    this.saveAchievementProgress(achievementId, newProgress)
    let currentUnlockedLevel = this.unlockedLevels.get(achievementId) || 0
    achievement.levels.forEach(level => {
      if (newProgress >= level.requirement && level.level > currentUnlockedLevel) {
        currentUnlockedLevel = level.level
        console.log('Achievement unlocked level', level.level)
        this.uiEventBus.emit(GAME_EVENTS.ACHIEVEMENT_UNLOCKED, achievement)
      }
    })
    this.unlockedLevels.set(achievementId, currentUnlockedLevel)
  }

  saveAchievementProgress(achievementId: number, newProgress: number) {
    if (!this.cachedAchievements) {
      console.log('No achievements to save progress for')
      return
    }
    const achievement = this.cachedAchievements.find(a => a.id === achievementId)
    if (!achievement) {
      console.log('Achievement not found')
      return
    }
    achievement.currentProgress = newProgress
  }
}

export const achievements = new GameplayAchievementsHandler(eventBus)
