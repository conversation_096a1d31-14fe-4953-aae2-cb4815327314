export class EnemyStatsHandler {
  private killedMobsCount: number = 0
  private scene: Phaser.Scene

  constructor(scene: Phaser.Scene) {
    this.scene = scene
    this.scene.events.on('enemyDefeat', this.incrementKilledMobsCount, this)
  }

  public getKilledMobsCount() {
    return this.killedMobsCount
  }

  incrementKilledMobsCount() {
    this.killedMobsCount++
  }

  destroy() {
    this.scene.events.off('enemyDefeat', this.incrementKilledMobsCount, this)
  }
}
