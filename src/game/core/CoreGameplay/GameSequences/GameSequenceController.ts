import type { CameraManager } from '@/game/core/CoreGameplay/CameraManager'
import type { GameBuilder } from '@/game/core/CoreGameplay/GameBuilder'
import { GameScene } from '../../scenes/GameScene'
import { ContinueGameSequence } from './ContinueGameSequence'
import { GameOverSequence } from './GameOverSequence'
import { MainSequence } from './MainSequence'
import { TutorialSequence } from './TutorialSequence'

export interface Sequence {
  type: SequenceType
  onStarted: () => void
  onEnded: () => void
  process: () => Promise<void>
}

export enum SequenceType {
  Tutorial,
  Main,
  GameOver,
  ContinueGame
}

export class GameSequenceController {
  private currentSequence: Sequence | null = null
  private gameScene: GameScene
  private readonly gameBuilder: GameBuilder
  private cameraManager: CameraManager

  constructor(gameScene: GameScene, gameBuilder: GameBuilder, cameraManager: CameraManager) {
    this.gameScene = gameScene
    this.gameBuilder = gameBuilder
    this.cameraManager = cameraManager
  }

  public async switchSequence(sequenceType: SequenceType): Promise<void> {
    if (this.currentSequence) {
      this.currentSequence.onEnded()
    }

    switch (sequenceType) {
      case SequenceType.Tutorial:
        this.currentSequence = new TutorialSequence()
        break
      case SequenceType.Main:
        this.currentSequence = new MainSequence()
        break
      case SequenceType.GameOver:
        this.currentSequence = new GameOverSequence(this.cameraManager, this.gameScene.UiEventBus)
        break
      case SequenceType.ContinueGame:
        this.currentSequence = new ContinueGameSequence(this.gameScene.UiEventBus)
        break
      default:
        throw new Error('[GameSequenceController] Unknown sequence type')
    }

    this.currentSequence.onStarted()
    await this.currentSequence.process()
  }

  public getCurrentSequence(): SequenceType | null {
    return this.currentSequence?.type!
  }
}
