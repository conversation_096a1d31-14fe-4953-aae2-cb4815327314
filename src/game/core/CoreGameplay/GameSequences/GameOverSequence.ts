import type { CameraManager } from '@/game/core/CoreGameplay/CameraManager'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import { SequenceType, type Sequence } from './GameSequenceController'

export class GameOverSequence implements Sequence {
  private cameraManager: CameraManager
  private uiEventBus: EventBus
  type: SequenceType = SequenceType.GameOver

  constructor(cameraManager: CameraManager, uiEventBus: EventBus) {
    this.cameraManager = cameraManager
    this.uiEventBus = uiEventBus
  }
  onStarted(): void {
    this.cameraManager.cameraController.moveToEndGame(() => {
      this.uiEventBus.emit(GAME_EVENTS.PLAYER_DIED)
    })
  }

  onEnded(): void {}
  async process(): Promise<void> {}
}
