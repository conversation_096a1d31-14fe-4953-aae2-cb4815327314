import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import { SequenceType, type Sequence } from './GameSequenceController'

export class ContinueGameSequence implements Sequence {
  private uiEventBus: EventBus
  type: SequenceType = SequenceType.GameOver

  constructor(uiEventBus: EventBus) {
    this.uiEventBus = uiEventBus
  }

  onStarted(): void {
    this.uiEventBus.emit(GAME_EVENTS.PLAYER_RESSURECTED)
  }

  onEnded(): void {}

  async process(): Promise<void> {}
}
