import { BoostersFactory } from '@/game/core/CoreGameplay/Boosters/BoostersFactory'
import { CameraManager } from '@/game/core/CoreGameplay/CameraManager'
import { CollectablesFactory } from '@/game/core/CoreGameplay/Collectables/CollectablesFactory'
import { DecorationsBuilder } from '@/game/core/CoreGameplay/Decorations/DecorationsBuilder.ts'
import { EnemiesFactory } from '@/game/core/CoreGameplay/Enemies/EnemiesFactory'
import { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { logger } from '@/shared/Logger'
import DIContainer from '../Core/DIContainer'
import { GameBuilder } from './GameBuilder'
import { GameSequenceController } from './GameSequences/GameSequenceController'
import { PlatformFactory } from './Platforms/PlatformFactory'

class GameContainer {
  private static instance: DIContainer | null = null

  static initialize(scene: GameScene): DIContainer {
    if (GameContainer.instance) {
      logger.log(
        'Game Container',
        'Existing GameContainer instance found. Destroying previous instance.'
      )

      GameContainer.instance = null
    }

    GameContainer.instance = new DIContainer()

    GameContainer.instance.register(PlatformFactory, [scene], true)
    GameContainer.instance.register(
      GameBuilder,
      [
        scene,
        PlatformFactory,
        BoostersFactory,
        EnemiesFactory,
        CollectablesFactory,
        DecorationsBuilder
      ],
      true
    )
    GameContainer.instance.register(
      GameSequenceController,
      [scene, GameBuilder, CameraManager],
      true
    )
    GameContainer.instance.register(BoostersFactory, [scene], true)
    GameContainer.instance.register(EnemiesFactory, [scene], true)
    GameContainer.instance.register(CollectablesFactory, [scene], true)
    GameContainer.instance.register(CameraManager, [scene], true)
    GameContainer.instance.register(GameInterface, [scene, GameBuilder], true)
    GameContainer.instance.register(DecorationsBuilder, [scene], true)

    logger.log('Game Container', 'Game DI Container initialized.')

    return GameContainer.instance
  }
}

export default GameContainer
