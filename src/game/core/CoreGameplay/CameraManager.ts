import { CameraController } from '@/game/core/CoreGameplay/CameraController'
import { GameScene } from '@/game/core/scenes/GameScene'

export class CameraManager {
  public cameraController!: CameraController

  constructor(private scene: GameScene) {
    this.scene.events.on('camera-bottom-changed', this.handleCameraBottomChanged, this)
  }

  createCamera(playerInstance: Phaser.Physics.Arcade.Sprite): void {
    this.cameraController = new CameraController(this.scene, playerInstance)
  }

  private handleCameraBottomChanged(): void {
    this.scene.GameBuilder.levelBuilder.destroyOffscreenObjects()
  }
}
