import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform'
import type {
  BasePlatformOptions,
  Position
} from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { PlatformFactory } from '@/game/core/CoreGameplay/Platforms/PlatformFactory'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'

export class PlatformPool<T extends BasePlatform & { isActive: boolean; nextFree: T | null }> {
  private readonly scene: GameScene
  private readonly factory: PlatformFactory
  private readonly gameInterface: GameInterface
  private readonly PlatformClass: new (options: BasePlatformOptions) => T

  private readonly pool: Set<T> = new Set()
  private freeListHead: T | null = null
  private freeListCount: number = 0

  private readonly freePoolLimit: number

  private totalTakenFromPool: number = 0
  private totalCreatedNew: number = 0

  constructor(
    scene: GameScene,
    factory: PlatformFactory,
    gameInterface: GameInterface,
    PlatformClass: new (options: BasePlatformOptions) => T,
    freePoolLimit: number
  ) {
    this.scene = scene
    this.factory = factory
    this.gameInterface = gameInterface
    this.PlatformClass = PlatformClass
    this.freePoolLimit = freePoolLimit
  }

  preload(count: number): void {
    const dummyGroup = this.scene.physics.add.staticGroup()

    for (let i = 0; i < count; i++) {
      const options: BasePlatformOptions = {
        factory: this.factory,
        entityType: EntityType.PlatformStatic,
        platformGroup: dummyGroup,
        scene: this.scene,
        spriteConfig: {
          position: { x: -1000, y: 1000 },
          texture: '1'
        },
        customParams: {},
        chunkInfo: {
          id: 0,
          index: 0,
          length: 0,
          prevChunkCumulativeLenght: 0,
          platformIndex: 0,
          generation: 0
        },
        gameInterface: this.gameInterface
      }
      const platform = new this.PlatformClass(options)
      platform.setActive(false).setVisible(false)

      platform.isActive = false
      platform.nextFree = null

      this.pool.add(platform)

      platform.nextFree = this.freeListHead
      this.freeListHead = platform
      this.freeListCount++
    }
  }

  get(options: BasePlatformOptions): T {
    let platform: T
    if (this.freeListHead) {
      platform = this.freeListHead
      this.freeListHead = platform.nextFree
      platform.nextFree = null
      this.freeListCount--
      platform.isActive = true
      platform.reset(options)
      this.totalTakenFromPool++
    } else {
      platform = new this.PlatformClass(options)
      platform.isActive = true
      platform.nextFree = null
      this.pool.add(platform)
      this.totalCreatedNew++

      if (options.entityType === EntityType.PlatformDynamicH) {
        platform.startTweener(options.customParams)
      }
      if (options.entityType === EntityType.PlatformDynamicV) {
        platform.startVerticalTweener(options.customParams, options.spriteConfig.position.y)
      }
    }

    return platform
  }

  return(platform: T): void {
    platform.returnToPool()
    platform.isActive = false

    if (this.freeListCount < this.freePoolLimit) {
      platform.nextFree = this.freeListHead
      this.freeListHead = platform
      this.freeListCount++
    } else {
      this.pool.delete(platform)
      platform.destroyPlatform()
    }
  }

  createRevivingPlatform(pos: Position, group: Phaser.Physics.Arcade.StaticGroup): T {
    const options: BasePlatformOptions = {
      factory: this.factory,
      entityType: EntityType.PlatformStatic,
      platformGroup: group,
      scene: this.scene,
      spriteConfig: {
        position: pos,
        texture: '1'
      },
      customParams: {},
      chunkInfo: {
        id: 0,
        index: 0,
        length: 0,
        prevChunkCumulativeLenght: 0,
        platformIndex: 0,
        generation: 0
      },
      gameInterface: this.gameInterface
    }
    return this.get(options)
  }

  clear(): void {
    this.pool.forEach(platform => platform.destroyPlatform())
    this.pool.clear()

    this.freeListHead = null
    this.freeListCount = 0
  }
}
