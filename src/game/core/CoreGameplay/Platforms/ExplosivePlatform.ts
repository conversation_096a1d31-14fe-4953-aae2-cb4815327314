import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { ExplosivePlatformConsts } from '../Constants/PlatformAnimationsConsts'
import { BasePlatform } from './BasePlatform'
import { ExplosivePlatformManager } from './Managers/ExplosivePlatformManager'

export class ExplosivePlatform extends BasePlatform {
  private manager: ExplosivePlatformManager
  public hasExploded: boolean = false
  public becameRed: boolean = false
  private explosionFrames = [
    'platform_explosive_02a',
    'platform_explosive_02b',
    'platform_explosive_02c',
    'platform_explosive_02d',
    'platform_explosive_02e',
    'platform_explosive_02f',
    'platform_explosive_02g'
  ]

  constructor(options: BasePlatformOptions) {
    super(options)
    this.manager = options.factory.explosivePlatformManager
    this.manager.addPlatform(this)
  }

  reset(options: BasePlatformOptions): void {
    super.reset(options)
    this.manager.addPlatform(this)
  }

  override destroyPlatform(): void {
    this.manager.removePlatform(this)
    super.destroyPlatform()
  }

  disablePlatform(): void {
    this.getSprite().setAlpha(0)
    if (this.body && this.body.body) {
      this.body.body.enable = false
      this.refreshBody()
    }
  }

  enablePlatform(): void {
    this.getSprite().setAlpha(1)
    if (this.body && this.body.body) {
      this.body.body.enable = true
    }
    this.refreshBody()
  }

  returnToPool() {
    this.hasExploded = false
    super.returnToPool()
  }

  interact(playerPos: Vector2): void {
    super.interact(playerPos)
    if (this.becameRed == true) return
    this.becomeRed()
  }

  becomeRed() {
    this.becameRed = true
    const frameDuration = ExplosivePlatformConsts.RED_FRAME_DURATION

    this.swapTexturesUntil('platform_explosive_02e', frameDuration, () => {
      this.explode()
    })
  }

  explode() {
    const frameDuration = ExplosivePlatformConsts.EXPLOSION_FRAME_DURATION
    const delay = ExplosivePlatformConsts.DELAY_BEFORE_EXPLOSION
    this.scene!.time.delayedCall(delay, () => {
      const lastFrames = this.explosionFrames.slice(-2)
      this.swapTextures(lastFrames, frameDuration, () => {
        this.hasExploded = true
        this.becameRed = false
        this.returnToPool()
      })
    })
  }

  private swapTextures(frames: string[], frameDuration: number, onComplete: () => void): void {
    const originalPosition = { x: this.image!.x, y: this.image!.y }

    frames.forEach((frame, index) => {
      this.scene!.time.delayedCall(index * frameDuration, () => {
        this.image!.setTexture(AtlasNames.ANIM, `explosive/${frame}.png`)
        this.image!.setPosition(originalPosition.x, originalPosition.y)
      })
    })

    this.scene!.time.delayedCall(frames.length * frameDuration, onComplete)
  }

  private swapTexturesUntil(
    targetFrame: string,
    frameDuration: number,
    onComplete: () => void
  ): void {
    const originalPosition = { x: this.image!.x, y: this.image!.y }

    for (let i = 0; i < this.explosionFrames.length; i++) {
      const frame = this.explosionFrames[i]

      this.scene!.time.delayedCall(i * frameDuration, () => {
        this.image!.setTexture(AtlasNames.ANIM, `explosive/${frame}.png`)
        this.image!.setPosition(originalPosition.x, originalPosition.y)
      })

      if (frame === targetFrame) {
        this.scene!.time.delayedCall((i + 1) * frameDuration, onComplete)
        break
      }
    }
  }
}
