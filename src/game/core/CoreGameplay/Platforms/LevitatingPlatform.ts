import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-core'
import { clearTween } from '../../HelperFunctions'
import { LevitatingPlatformConsts } from '../Constants/PlatformAnimationsConsts'
import { BasePlatform } from './BasePlatform'
import type { CustomParameters } from './CustomParameters'
import {
  createInitialPlatformMovementTween,
  createOscillatingPlatformTween
} from './PlatformTweens'

export class LevitatingPlatform extends BasePlatform {
  private movementTween: Phaser.Tweens.Tween | null = null
  private originalY!: number
  private currentYOffset: number = 0

  constructor(options: BasePlatformOptions) {
    super(options)
  }

  override reset(options: BasePlatformOptions): void {
    super.reset(options)
    this.image!.setDisplaySize(
      LevitatingPlatformConsts.DISPLAY_SIZE_X,
      LevitatingPlatformConsts.DISPLAY_SIZE_Y
    )
    this.originalY = this.image!.y
    this.currentYOffset = 0

    if (options.customParams.isVisible) {
      this.tryStartTweener(options.customParams)
    }
  }

  override interact(playerPos: Vector2): void {
    super.interact(playerPos)
    this.playMoveDownTween()
  }

  override returnToPool(): void {
    if (this.movementTween) {
      clearTween(this.movementTween)
      this.movementTween = null
    }

    if (this.platformTween) {
      clearTween(this.platformTween)
      this.platformTween = null
    }
    super.returnToPool()
  }

  private tryStartTweener(customParams: CustomParameters) {
    const randomDuration = customParams.duration
    const sprite = this.getSprite()

    if (randomDuration) {
      this.platformTween = createInitialPlatformMovementTween(
        this.scene!,
        sprite,
        randomDuration,
        () => {
          this.platformTween = createOscillatingPlatformTween(
            this.scene!,
            sprite,
            randomDuration,
            this.booster,
            this.mob,
            this.collectable
          )
        },
        this.booster,
        this.mob,
        this.collectable
      )
    }
  }

  private playMoveDownTween(): void {
    if (this.movementTween) {
      this.currentYOffset = this.image!.y - this.originalY
      this.movementTween.stop()
      this.movementTween = null
    }

    const distance = LevitatingPlatformConsts.MOVE_DOWN_DISTANCE
    this.currentYOffset += distance
    const targetY = this.originalY + this.currentYOffset

    this.movementTween = this.scene!.tweens.add({
      targets: this.image,
      y: targetY,
      duration: 500,
      ease: 'Power1',
      yoyo: false,
      onUpdate: () => {
        if (this.image!.body) {
          this.image!.body.position.y = this.image!.y
        }
      },
      onComplete: () => {
        if (this.currentYOffset > 0 && this.image!.y >= targetY) {
          this.startReturnToOriginalY()
        }
      },
      delay: 0
    })
  }

  private startReturnToOriginalY(): void {
    if (this.movementTween) {
      this.movementTween.stop()
      this.movementTween = null
    }

    const currentY = this.image!.y
    const distanceToOriginal = currentY - this.originalY

    this.movementTween = this.scene!.tweens.add({
      targets: this.image,
      y: this.originalY,
      duration: Math.abs(distanceToOriginal) * 6,
      ease: 'Sine.easeIn',
      onUpdate: () => {
        if (this.image!.body) {
          this.image!.body.position.y = this.image!.y
        }
      },
      onComplete: () => {
        this.currentYOffset = 0
      },
      delay: 0
    })
  }
}
