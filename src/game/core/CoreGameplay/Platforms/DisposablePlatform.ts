import { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform'
import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'

export class DisposablePlatform extends BasePlatform {
  constructor(options: BasePlatformOptions) {
    super(options)
  }

  reset(options: BasePlatformOptions) {
    super.reset(options)
    this.body!.body!.enable = true
    this.getSprite().setAlpha(1)
  }

  interact(playerPos: Vector2): void {
    super.interact(playerPos)
    const sprite = this.getSprite()
    this.body!.body!.enable = false

    sprite.play('bubbleAnim')

    sprite.once('animationcomplete', () => {
      this.scene?.tweens.add({
        targets: sprite,
        alpha: 0,
        duration: 150,
        onComplete: () => {
          this.returnToPool()
        }
      })
    })
  }

  dispose() {
    super.destroyPlatform()
  }
}
