import { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform'
import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { clearTween } from '../../HelperFunctions'

export class DynamicHorizontalPlatform extends BasePlatform {
  constructor(options: BasePlatformOptions) {
    super(options)
  }

  reset(options: BasePlatformOptions): void {
    if (this.platformTween) {
      clearTween(this.platformTween)
      this.platformTween = null
    }

    super.reset(options)
    this.startTweener(options.customParams)
    if (__DEV__) this.logger.log('Dynamic Platform', 'resetting dynamic platform')
  }

  async destroyPlatform(): Promise<void> {
    if (this.platformTween) {
      clearTween(this.platformTween)
      this.platformTween = null
    }
    if (__DEV__) this.logger.log('Dynamic Platform', 'destroy dynamic platform')
    await super.destroyPlatform()
  }
}
