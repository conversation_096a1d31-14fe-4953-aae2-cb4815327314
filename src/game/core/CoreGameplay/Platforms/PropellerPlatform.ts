import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { BasePlatform } from './BasePlatform'

export class PropellerPlatform extends BasePlatform {
  private floatAreaCollider: Phaser.Physics.Arcade.Body | null = null

  constructor(options: BasePlatformOptions) {
    super(options)

    if (this.body && this.body.body) {
      this.body.body.checkCollision.up = false
      this.body.refreshBody()
    }

    this.createFloatArea()

    if (this.floatAreaCollider) {
      options.factory.propellerPlatformManager.addFloatArea(this.floatAreaCollider)
    }
  }

  override reset(options: BasePlatformOptions): void {
    super.reset(options)

    if (this.floatAreaCollider) {
      this.floatAreaCollider.position.x = this.image!.x
      this.floatAreaCollider.position.y = this.image!.y - 200
      this.floatAreaCollider.enable = true
    }
  }

  override returnToPool(): void {
    super.returnToPool()

    if (this.floatAreaCollider) {
      this.floatAreaCollider.enable = false
    }
  }

  private createFloatArea(): void {
    const scene = this.image!.scene
    if (!scene) return

    this.floatAreaCollider = this.scene!.physics.add.body(this.image!.x, this.image!.y, 114, 200)
    this.floatAreaCollider.enable = false
    this.floatAreaCollider.checkCollision.none
  }
}

export interface FloatingAreaData {
  colliderTop: number
  colliderBottom: number
}
