import { GameViewConsts } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { ShiftingPlatform } from '@/game/core/CoreGameplay/Platforms/ShiftingPlatform'
import { ENTITIES_SIZES } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Depth } from '@esotericsoftware/spine-phaser'

export class ShiftingPlatformManager {
  private platformsByChunk: { [chunkIndex: number]: Set<ShiftingPlatform> } = {}
  private scene: GameScene
  private readonly platformWidth: number
  private readonly screenWidth: number
  private duration = 200

  constructor(scene: GameScene) {
    this.scene = scene
    const [w] = ENTITIES_SIZES[1]
    this.platformWidth = w
    this.screenWidth = GameViewConsts.REF_WIDTH
  }

  addPlatform(platform: ShiftingPlatform, chunkIndex: number): void {
    if (!this.platformsByChunk[chunkIndex]) {
      this.platformsByChunk[chunkIndex] = new Set()
    }
    this.platformsByChunk[chunkIndex].add(platform)
    this.startPlatformShake(platform)
  }

  removePlatform(platform: ShiftingPlatform, chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]
    if (!platforms || platforms.size === 0) {
      return
    }
    if (!platforms.delete(platform)) {
      console.warn(`Platform not found in chunk: ${chunkIndex}`, platform)
    }
  }

  startPlatformShake(platform: ShiftingPlatform): void {
    const sprite = platform.getSprite()
    this.scene.tweens.killTweensOf(sprite)

    if (!sprite.visible) {
      return
    }

    const body = sprite.body as Phaser.Physics.Arcade.StaticBody
    const shakeRange = 8
    const shakeDuration = Phaser.Math.Between(100, 250)
    const randomStartDelay = Phaser.Math.Between(0, 75)

    this.scene.time.delayedCall(randomStartDelay, () => {
      this.scene.tweens.add({
        targets: sprite,
        x: {
          value: `+=${shakeRange}`,
          duration: shakeDuration,
          ease: 'Sine.easeInOut',
          yoyo: true,
          repeat: -1
        },
        onUpdate: () => {
          if (body) {
            body.x = sprite.x
          }
        }
      })
    })
  }

  stopPlatformMovement(platform: ShiftingPlatform): void {
    const sprite = platform.getSprite()
    this.scene.tweens.killTweensOf(sprite)
  }

  handleInteraction(chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]
    if (!platforms || platforms.size === 0) return

    platforms.forEach(platform => {
      if (platform.isActive) {
        this.stopPlatformMovement(platform)
        let direction = platform.getDirection()
        if (direction === 0) {
          direction = Phaser.Math.Between(0, 1) === 0 ? -1 : 1
          platform.setDirection(direction)
        }
        const sprite = platform.getSprite()
        const body = sprite.body as Phaser.Physics.Arcade.StaticBody
        const distance = Phaser.Math.Between(this.platformWidth / 2, this.platformWidth * 2)
        let newX = sprite.x + direction * distance
        if (newX <= 0 || newX >= this.screenWidth - this.platformWidth) {
          direction = -direction
          platform.setDirection(direction)
          newX = sprite.x + direction * distance
          newX = Phaser.Math.Clamp(newX, 0, this.screenWidth - this.platformWidth)
        }
        newX = Phaser.Math.Clamp(newX, 0, this.screenWidth - this.platformWidth)

        const offsetAnimX = 48
        const offsetAnimY = 18

        const animSprite = this.scene.add.sprite(
          sprite.x + offsetAnimX,
          sprite.y + offsetAnimY,
          'anim',
          'Platform_Random_Move-01b.png'
        )

        animSprite.scale = 0.5
        animSprite.setDepth(Depth.Platform + 1)

        this.scene.tweens.add({
          targets: sprite,
          x: newX,
          duration: this.duration,
          ease: 'Linear',
          onUpdate: () => {
            if (body) {
              body.x = sprite.x
            }
            animSprite.x = sprite.x + offsetAnimX
            animSprite.y = sprite.y + offsetAnimY
          },
          onComplete: () => {
            this.startPlatformShake(platform)
          }
        })

        this.scene.tweens.add({
          targets: animSprite,
          alpha: 0,
          delay: platform.getRandomDelay(),
          duration: platform.getRandomDuration(),
          ease: 'Linear',
          onComplete: () => {
            animSprite.destroy(true)
          }
        })
      }
    })
  }

  clear(): void {
    this.platformsByChunk = {}
  }
}
