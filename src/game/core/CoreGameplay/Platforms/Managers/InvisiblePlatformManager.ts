import type { InvisiblePlatform } from '@/game/core/CoreGameplay/Platforms/InvisiblePlatform'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'

export class InvisiblePlatformManager {
  private platformsByChunk: { [chunkIndex: number]: Set<InvisiblePlatform> } = {}
  private scene: GameScene
  private logger: Logger = logger

  constructor(scene: GameScene) {
    this.scene = scene
  }

  addPlatform(platform: InvisiblePlatform, chunkIndex: number): void {
    if (!this.platformsByChunk[chunkIndex]) {
      this.platformsByChunk[chunkIndex] = new Set()
    }
    this.platformsByChunk[chunkIndex].add(platform)
  }

  removePlatform(platform: InvisiblePlatform, chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]
    if (!platforms || platforms.size === 0) {
      return
    }
    if (!platforms.delete(platform)) {
      if (__DEV__) this.logger.warn(`Platform not found in chunk: ${chunkIndex}`, platform)
    }
  }

  activateNextPlatform(currentPlatform: InvisiblePlatform, chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]
    if (!platforms || platforms.size === 0) return
    let foundCurrentPlatform = false
    for (const platform of platforms) {
      if (platform === currentPlatform) {
        foundCurrentPlatform = true
        continue
      }
      if (foundCurrentPlatform) {
        platform.customParams.isVisible = true
        platform.enablePlatform()
        break
      }
    }
    if (!foundCurrentPlatform) {
      if (__DEV__)
        this.logger.warn(
          'Interacted platform not found in the chunk platforms set',
          currentPlatform
        )
    }
  }

  clear(): void {
    this.platformsByChunk = {}
  }
}
