import type { GameScene } from '@/game/core/scenes/GameScene'
import { ExplosivePlatform } from '../ExplosivePlatform'

export class ExplosivePlatformManager {
  private platforms: Set<ExplosivePlatform> = new Set()
  private scene: GameScene
  private decorationPlatformsThreshold = window.innerHeight / 3.2

  constructor(scene: GameScene) {
    this.scene = scene
    this.scene.events.on('player-position-updated', this.handlePlayerPositionUpdated, this)
  }

  private handlePlayerPositionUpdated(position: { x: number; y: number }): void {
    const playerY = position.y

    this.platforms.forEach(platform => {
      if (platform.hasExploded) {
        return
      }

      const sprite = platform.getSprite()

      if (!sprite.visible || !sprite.active) {
        return
      }

      if (platform.customParams.isExplosiveAsDecoration?.valueOf() === true) {
        const platformWorldY = sprite.y
        const yDistance = playerY - platformWorldY

        if (yDistance <= this.decorationPlatformsThreshold) {
          platform.becomeRed()
          platform.hasExploded = true
        }
      }
    })
  }

  addPlatform(platform: ExplosivePlatform): void {
    if (!this.platforms.has(platform)) {
      this.platforms.add(platform)
    } else {
      if (__DEV__) console.log('Attempted to add duplicate platform', platform)
    }
  }

  removePlatform(platform: ExplosivePlatform): void {
    if (this.platforms.has(platform)) {
      this.platforms.delete(platform)
    } else {
      if (__DEV__) console.log('Attempted to remove non-existing platform', platform)
    }
  }

  clear(): void {
    this.platforms.clear()
  }
}
