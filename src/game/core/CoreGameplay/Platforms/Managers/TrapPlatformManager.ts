import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import { GameScene } from '../../../scenes/GameScene'

export class TrapPlatformManager {
  private traps: Phaser.Physics.Arcade.Group | null
  private scene: GameScene
  private player!: Phaser.Physics.Arcade.Sprite | null

  constructor(scene: GameScene) {
    this.scene = scene
    this.traps = this.scene.physics.add.group()
    this.scene.events.on('update', this.update, this)
    this.scene.events.once('shutdown', this.clear, this)
  }

  public cachePlayer(): void {
    this.player = this.scene.getPlayer()
  }

  public addTrap(trap: Phaser.Physics.Arcade.Sprite): void {
    if (this.traps && !this.traps.contains(trap)) {
      this.traps.add(trap)
    }
  }

  private update(): void {
    if (!this.traps || !this.traps.children || this.traps.getLength() === 0) {
      return
    }

    if (this.player && !this.player.active && !this.player.body) {
      return
    }

    this.traps.getChildren().forEach(child => {
      const trap = child as Phaser.Physics.Arcade.Sprite
      if (trap.active && this.player) {
        if (
          this.scene.physics &&
          this.scene.physics.world &&
          this.scene.physics.world.overlap(this.player, trap)
        ) {
          this.handleTrapOverlap()
        }
      }
    })
  }

  private handleTrapOverlap(): void {
    this.scene.events.emit(GAME_EVENTS.TRAP_TOUCHED)
  }

  clear() {
    if (this.traps) {
      try {
        // Destroy the group fully and remove its children from the scene
        this.traps.destroy(true)
      } catch (error) {
        console.warn('Error destroying traps group:', error)
      }
    }
    this.scene.events.off('update', this.update, this)
    this.traps = null
  }
}
