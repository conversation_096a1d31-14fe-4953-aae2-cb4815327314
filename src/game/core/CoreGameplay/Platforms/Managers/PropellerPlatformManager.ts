import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import { GameScene } from '../../../scenes/GameScene'

export class PropellerPlatformManager {
  private floatAreaBodies: Phaser.Physics.Arcade.Body[] = []
  private scene: GameScene
  private player!: Phaser.Physics.Arcade.Sprite | null
  private overlapStates: Map<Phaser.Physics.Arcade.Body, boolean> = new Map()

  constructor(scene: GameScene) {
    this.scene = scene
    this.scene.events.on('update', this.update, this)
  }

  public cachePlayer(): void {
    this.player = this.scene.getPlayer()
  }

  public addFloatArea(floatArea: Phaser.Physics.Arcade.Body): void {
    if (!this.floatAreaBodies.includes(floatArea)) {
      this.floatAreaBodies.push(floatArea)
      this.overlapStates.set(floatArea, false) // Initialize overlap state as false
    }
  }

  public reset(): void {
    this.floatAreaBodies = []
    this.overlapStates.clear()
  }

  private update(): void {
    if (
      this.floatAreaBodies.length === 0 ||
      !this.player ||
      !this.player.active ||
      !this.player.body
    ) {
      return
    }

    this.floatAreaBodies.forEach(body => {
      if (!this.player) return
      const isOverlapping = this.scene.physics.world.overlap(this.player, body)

      if (isOverlapping) {
        if (!this.overlapStates.get(body)) {
          this.handleFloatAreaEnter(body) // Trigger Enter
        }
        this.handleFloatAreaStay(body) // Trigger Stay
      } else if (this.overlapStates.get(body)) {
        this.handleFloatAreaExit(body) // Trigger Exit
      }

      this.overlapStates.set(body, isOverlapping)
    })
  }

  private handleFloatAreaEnter(body: Phaser.Physics.Arcade.Body): void {
    //this.scene.events.emit(GAME_EVENTS.FLOATING_AREA_ENTERED, body)
    console.log('Trigger enter')
  }

  private handleFloatAreaStay(body: Phaser.Physics.Arcade.Body): void {
    this.scene.events.emit(GAME_EVENTS.FLOATING_AREA_STAY, body)
    console.log('Trigger stay')
  }

  private handleFloatAreaExit(body: Phaser.Physics.Arcade.Body): void {
    console.log('Trigger exit')
    //this.scene.events.emit(GAME_EVENTS.FLOATING_AREA_EXITED, body)
  }
}
