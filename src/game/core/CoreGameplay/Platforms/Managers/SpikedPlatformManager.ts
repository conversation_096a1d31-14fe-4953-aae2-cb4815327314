import { SpikedPlatform } from '../SpikedPlatform'

export class SpikedPlatformManager {
  private platformsByChunk: { [chunkIndex: number]: SpikedPlatform[] } = {}

  constructor() {}

  addPlatform(platform: SpikedPlatform, chunkIndex: number): void {
    if (!this.platformsByChunk[chunkIndex]) {
      this.platformsByChunk[chunkIndex] = []
    }
    this.platformsByChunk[chunkIndex].push(platform)
  }

  removePlatform(platform: SpikedPlatform, chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]

    if (!platforms || platforms.length === 0) {
      return
    }

    const index = platforms.indexOf(platform)

    if (index > -1) {
      platforms.splice(index, 1)
    } else {
      if (__DEV__) console.warn(`Platform not found in chunk: ${chunkIndex}`, platform)
    }
  }

  toggleSpikesInAChunk(chunkIndex: number): void {
    const platforms = this.platformsByChunk[chunkIndex]
    if (!platforms || platforms.length === 0) return

    for (let i = 0; i < platforms.length; i++) {
      const platform = platforms[i]
      platform.toggleSpikeState()
    }
  }

  clear(): void {
    this.platformsByChunk = {}
  }
}
