import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { BasePlatform } from './BasePlatform'
import { ShiftingPlatformManager } from './Managers/ShiftingPlatformManager'

export class ShiftingPlatform extends BasePlatform {
  private manager: ShiftingPlatformManager
  private direction: number
  private durationRange: [number, number] = [180, 220]
  private delayRange: [number, number] = [50, 100]

  constructor(options: BasePlatformOptions) {
    super(options)
    this.manager = options.factory.shiftingPlatformManager
    this.direction = 1
    this.manager.addPlatform(this, options.chunkInfo.index)
  }

  reset(options: BasePlatformOptions): void {
    super.reset(options)
    this.direction = 1
    this.manager.addPlatform(this, options.chunkInfo.index)
  }

  getRandomDuration(): number {
    const [min, max] = this.durationRange
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  getRandomDelay(): number {
    const [min, max] = this.delayRange
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  override destroyPlatform(): void {
    this.manager.removePlatform(this, this.chunkInfo.index)
    super.destroyPlatform()
  }

  returnToPool() {
    this.manager.removePlatform(this, this.chunkInfo.index)
    super.returnToPool()
  }

  interact(playerPos: Vector2): void {
    if (!this.isActive) return
    super.interact(playerPos)
    this.manager.handleInteraction(this.chunkInfo.index)
  }

  getDirection(): number {
    return this.direction
  }

  setDirection(direction: number): void {
    this.direction = direction
  }
}
