import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { BoosterPlatformConsts } from '../Constants/PlatformAnimationsConsts'
import { BasePlatform } from './BasePlatform'

export class BoosterPlatform extends BasePlatform {
  constructor(options: BasePlatformOptions) {
    super(options)
    if (this.body && this.body.body) {
      this.body.body.checkCollision.up = false
      this.body.refreshBody()
    }
  }

  override reset(options: BasePlatformOptions): void {
    super.reset(options)
    this.image!.setDisplaySize(
      BoosterPlatformConsts.DISPLAY_SIZE_X,
      BoosterPlatformConsts.DISPLAY_SIZE_Y
    )
    this.image!.setDisplayOrigin(0, BoosterPlatformConsts.DISPLAY_OFFSET_Y)
  }
}
