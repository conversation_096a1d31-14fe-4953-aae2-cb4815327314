import Sprite = Phaser.GameObjects.Sprite
import type { BaseBooster } from '@/game/core/CoreGameplay/Boosters/BaseBooster'
import type { CollectableItemBase } from '@/game/core/CoreGameplay/Collectables/CollectableItemBase'
import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { BaseDecoration } from '@/game/core/CoreGameplay/Decorations/BaseDecoration.ts'
import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type {
  BasePlatformOptions,
  ChunkInfo,
  SpriteConfig
} from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters'
import type { PlatformFactory } from '@/game/core/CoreGameplay/Platforms/PlatformFactory'
import { InteractionType } from '@/game/core/MapGen/GameSessionManager'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'
import { HapticsService, hapticsService } from '@/shared/haptics/hapticsService.ts'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { addComponent, addEntity, removeEntity } from 'bitecs'
import Phaser from 'phaser'
import { clearTween } from '../../HelperFunctions'
import { Original, Platform, Position } from '../../ecs/Components/EntityComponents'
import { getGameWorld } from '../../ecs/GameWorld'
import { BasePlatformConsts, ShakeConstants } from '../Constants/PlatformAnimationsConsts'
import {
  createInitialPlatformMovementTween,
  createOscillatingPlatformTween
} from './PlatformTweens'

export class BasePlatform {
  public image: Sprite | null = null
  public body: Phaser.Physics.Arcade.Sprite | null = null
  protected collider: Phaser.Physics.Arcade.Body | null = null
  protected scene: GameScene | null = null
  protected platformGroup!: Phaser.Physics.Arcade.StaticGroup
  public customParams!: CustomParameters
  protected platformTween: Phaser.Tweens.Tween | null = null
  protected logger: Logger = logger
  public chunkInfo!: ChunkInfo
  protected booster: BaseBooster | null = null
  protected mob: BaseEnemy | null = null
  protected collectable: CollectableItemBase | null = null
  protected factory!: PlatformFactory
  protected gameInterface!: GameInterface
  protected shakeTween: Phaser.Tweens.Tween | null = null
  protected oscillatingTween: Phaser.Tweens.Tween | null = null
  protected decoration: BaseDecoration | null = null
  protected haptics: HapticsService = hapticsService
  protected entityType!: EntityType
  protected entity: number = -1
  protected originalX?: number

  public isActive!: boolean
  public nextFree: this | null = null

  constructor(options: BasePlatformOptions) {
    this.initializeEntityAndProperties(options)
    this.createOrUpdatePlatformSprite(options.spriteConfig)
    this.configurePhysics()
  }

  private initializeEntityAndProperties(options: BasePlatformOptions) {
    if (this.entity === -1) {
      this.entity = addEntity(getGameWorld())
      addComponent(getGameWorld(), Position, this.entity)
      addComponent(getGameWorld(), Original, this.entity)
      addComponent(getGameWorld(), Platform, this.entity)
    }

    this.initializeComponentData()

    const { factory, entityType, platformGroup, scene, customParams, chunkInfo, gameInterface } =
      options
    this.factory = factory
    this.entityType = entityType
    this.platformGroup = platformGroup
    this.scene = scene
    this.customParams = customParams
    this.chunkInfo = chunkInfo
    this.gameInterface = gameInterface
  }

  private initializeComponentData() {
    Position.x[this.entity] = 0
    Position.y[this.entity] = 0
    Original.X[this.entity] = 0
    Original.Y[this.entity] = 0
    Original.ScaleX[this.entity] = 0
    Original.ScaleY[this.entity] = 0
    Platform.isActive[this.entity] = 1
    Platform.shouldAnimateTouch[this.entity] = 1
    Platform.shouldTrackInteraction[this.entity] = 1
  }

  private createOrUpdatePlatformSprite(spriteConfig: SpriteConfig): void {
    const { position, texture } = spriteConfig
    const { x, y } = position
    const currentTexture = texture === '0' || texture === '' ? '1' : texture

    if (!this.image || !this.image.texture || !this.image.frame) {
      this.image = this.platformGroup.create(x, y, AtlasNames.PLATFORMS, `${currentTexture}.png`)
      this.image?.setOrigin(0, 0)
      this.image?.setDepth(DepthOrder.Platforms)
    } else {
      if (
        this.image.texture.key !== AtlasNames.PLATFORMS ||
        this.image.frame.name !== `${currentTexture}.png`
      ) {
        try {
          this.image.setTexture(AtlasNames.PLATFORMS, `${currentTexture}.png`)
        } catch (err) {
          console.warn('Failed to set texture on existing sprite, recreating sprite', err)
          this.image.destroy()
          this.image = this.platformGroup.create(
            x,
            y,
            AtlasNames.PLATFORMS,
            `${currentTexture}.png`
          )
          this.image?.setOrigin(0, 0)
          this.image?.setDepth(DepthOrder.Platforms)
        }
      }
      if (this.image?.x !== x || this.image?.y !== y) {
        this.image?.setPosition(x, y)
        this.originalX = x
      }
    }

    const [w, h] = ENTITIES_SIZES[this.entityType]
    if (this.image?.displayWidth !== w || this.image?.displayHeight !== h) {
      this.image?.setDisplaySize(w, h)
    }
    this.startHorizontalTween()
  }

  private configurePhysics(): void {
    this.body = this.image as Phaser.Physics.Arcade.Sprite
    this.scene?.physics.add.existing(this.image!)
    this.collider = this.body.body as Phaser.Physics.Arcade.Body

    this.collider.checkCollision.up = true
    this.collider.checkCollision.down = false
    this.collider.checkCollision.left = false
    this.collider.checkCollision.right = false

    this.body.refreshBody()
    this.setBool(Platform.shouldTrackInteraction, true)
  }

  reset(options: BasePlatformOptions): void {
    this.initializeEntityAndProperties(options)
    this.createOrUpdatePlatformSprite(options.spriteConfig)
    this.setActive(true)
    this.setVisible(true)
    if (this.collider) {
      this.collider.enable = true
    }
    this.refreshBody()
    if (!this.platformGroup.contains(this.image!)) {
      this.platformGroup.add(this.image!)
    }
  }

  setActive(active: boolean): this {
    this.image?.setActive(active)
    return this
  }

  setVisible(visible: boolean): this {
    this.image?.setVisible(visible)
    return this
  }

  addBooster(booster: BaseBooster) {
    this.booster = booster
    this.booster.syncPosition(this.image!.x)
  }

  addMob(mob: BaseEnemy) {
    this.mob = mob
  }

  addCollectable(collectable: CollectableItemBase) {
    this.collectable = collectable
    this.collectable.syncPosition(this.image!.x)
  }

  addDecoration(decoration: BaseDecoration) {
    this.decoration = decoration
  }

  interact(playerPos: Vector2) {
    this.shakePlatform()
    if (this.getType() != EntityType.PlatformWreckable) {
      this.haptics.triggerImpactHapticEvent('soft')
    }

    if (__DEV__) console.log(EntityType[this.getType()])

    if (!this.getBool(Platform.shouldTrackInteraction)) return
    this.gameInterface.registerInteraction(
      this.chunkInfo,
      InteractionType.PlatformCollide,
      this.image!.x,
      this.image!.y
    )
  }

  dontSendPlatformInteraction() {
    this.setBool(Platform.shouldTrackInteraction, false)
  }

  private storeOriginalScaleAndPosition(): void {
    if (Original.ScaleX[this.entity] === 0 || Original.ScaleY[this.entity] === 0) {
      Original.ScaleX[this.entity] = this.image!.scaleX
      Original.ScaleY[this.entity] = this.image!.scaleY
      Original.Y[this.entity] = this.image!.y
    }
  }

  private resetShakeProperties(): void {
    const originalY = Original.Y[this.entity]
    const originalScaleX = Original.ScaleX[this.entity]
    const originalScaleY = Original.ScaleY[this.entity]

    this.image!.y = originalY
    this.image!.scaleX = originalScaleX
    this.image!.scaleY = originalScaleY

    if (this.collider) {
      this.collider.y = originalY
    }

    this.shakeTween = null
    this.oscillatingTween = null
  }

  refreshBody(): void {
    const [w, h] = ENTITIES_SIZES[this.entityType]
    if (this.image?.displayWidth !== w || this.image?.displayHeight !== h) {
      this.image?.setDisplaySize(w, h)
    }
    if (this.body && this.body.body && typeof this.body.refreshBody === 'function') {
      try {
        this.body.refreshBody()
      } catch (err) {
        console.warn('Failed to refresh physics body:', err)
      }
    }
  }

  setChunkPositions(start: number, end: number) {}

  protected startHorizontalTween(): void {
    if (this.oscillatingTween) {
      clearTween(this.oscillatingTween)
      this.oscillatingTween = null
    }
    this.scene?.tweens.killTweensOf(this.getSprite())
    const randomDelay = Phaser.Math.Between(0, 1000)
    const sprite = this.getSprite()
    if (sprite === null) return
    if (!this.scene) return
    this.oscillatingTween = this.scene.tweens.add({
      targets: sprite,
      x: sprite.x + 7,
      ease: 'Sine.easeInOut',
      duration: 3000,
      yoyo: true,
      delay: randomDelay,
      repeat: -1,
      onUpdate: () => {
        if (!this.getSprite().active) return
        if (this.collider) this.collider.x = sprite.x
      }
    })
  }

  public returnToPool(): void {
    if (!this.getBool(Platform.isActive)) return
    this.setBool(Platform.isActive, false)
    this.setActive(false)
    this.setVisible(false)
    if (this.collider) {
      this.collider.enable = false
    }

    if (this.oscillatingTween) {
      this.oscillatingTween.stop()
      this.scene?.tweens.remove(this.oscillatingTween)
      this.oscillatingTween.remove()
      this.oscillatingTween.destroy()
      this.oscillatingTween = null
    }

    this.clearAllTweens()
    this.booster = null
    this.mob = null
    this.collectable = null
    this.decoration = null
    this.initializeComponentData()
  }

  private clearAllTweens(): void {
    const stopAndRemoveTween = (tween: Phaser.Tweens.Tween | null) => {
      if (tween) {
        tween.stop()
        this.scene?.tweens.remove(tween)
        tween.remove()
        tween.destroy()
      }
    }
    stopAndRemoveTween(this.shakeTween)
    stopAndRemoveTween(this.oscillatingTween)
    stopAndRemoveTween(this.platformTween)
    this.shakeTween = null
    this.oscillatingTween = null
    this.platformTween = null
  }

  setPosition(x: number, y: number) {
    Position.x[this.entity] = x
    Position.y[this.entity] = y
  }

  update() {}

  getSprite(): Sprite {
    return this.image!
  }

  getType(): EntityType {
    return this.entityType
  }

  getBooster(): BaseBooster | null {
    return this.booster
  }

  getMob(): BaseEnemy | null {
    return this.mob
  }

  getCollectable(): CollectableItemBase | null {
    return this.collectable
  }

  isPlatformSpriteActive(): boolean {
    return this.getBool(Platform.isActive)
  }

  protected setBool(component: Uint8Array, value: boolean): void {
    component[this.entity] = value ? 1 : 0
  }

  protected getBool(component: Uint8Array): boolean {
    return component[this.entity] === 1
  }

  private updatePosition(): void {
    if (!this.image) return
    const { x, y } = this.image
    Position.x[this.entity] = x
    Position.y[this.entity] = y
    if (this.collider) {
      this.collider.x = x
      this.collider.y = y
    }
  }

  private shakePlatform(): void {
    if (!this.getBool(Platform.shouldAnimateTouch) || this.shakeTween) return

    this.storeOriginalScaleAndPosition()
    const { SHAKE_AMPLITUDE, SCALE_AMPLITUDE, DURATION } = BasePlatformConsts
    const {
      AMPLITUDE_MULTIPLIER_UP,
      AMPLITUDE_MULTIPLIER_DOWN,
      SCALE_MULTIPLIER,
      DURATION_OFFSET
    } = ShakeConstants

    const originalY = Original.Y[this.entity]
    const originalScaleX = Original.ScaleX[this.entity]
    const originalScaleY = Original.ScaleY[this.entity]

    this.shakeTween = this.scene!.tweens.add({
      targets: this.image,
      props: {
        y: {
          value: [
            originalY + SHAKE_AMPLITUDE,
            originalY - SHAKE_AMPLITUDE * AMPLITUDE_MULTIPLIER_UP,
            originalY + SHAKE_AMPLITUDE * AMPLITUDE_MULTIPLIER_DOWN,
            originalY
          ],
          duration: DURATION,
          ease: 'Quad.easeInOut'
        },
        scaleX: {
          value: [
            originalScaleX + SCALE_AMPLITUDE,
            originalScaleX - SCALE_AMPLITUDE,
            originalScaleX + SCALE_AMPLITUDE * SCALE_MULTIPLIER,
            originalScaleX
          ],
          duration: DURATION,
          ease: 'Cubic.easeInOut'
        },
        scaleY: {
          value: [
            originalScaleY - SCALE_AMPLITUDE,
            originalScaleY + SCALE_AMPLITUDE,
            originalScaleY - SCALE_AMPLITUDE * SCALE_MULTIPLIER,
            originalScaleY
          ],
          duration: DURATION + DURATION_OFFSET,
          ease: 'Cubic.easeInOut'
        }
      },
      onUpdate: () => {
        if (!this.image?.active) return
        this.updatePosition()
      },
      onComplete: () => {
        this.resetShakeProperties()
        this.shakeTween = null
      }
    })
  }

  startVerticalTweener(customParams: CustomParameters, startY: number) {
    const sprite = this.getSprite()

    let distance = 100
    if (customParams.distance) {
      distance = Phaser.Math.Between(customParams.distance[0], customParams.distance[1]!) || 100
    }

    const duration = customParams.duration || 1000
    if (__DEV__) console.info('Dynamic Vertical:', customParams.duration, customParams.distance)
    if (__DEV__) console.info(`Dynamic Vertical Start Y: ${startY}, Distance: ${distance}`)

    this.platformTween = this.scene!.tweens.add({
      targets: sprite,
      y: { from: startY, to: startY - distance },
      ease: 'Linear',
      duration: duration,
      yoyo: true,
      repeat: -1,
      onUpdate: () => {
        if (!sprite.active) {
          return
        }
        if (this.collider) {
          this.collider.y = sprite.y
        }
      }
    })
  }

  public startTweener(customParams: CustomParameters) {
    const randomDuration = customParams?.duration ?? 3000
    const sprite = this.getSprite()

    if (this.platformTween) {
      clearTween(this.platformTween)
      this.platformTween = null
    }

    this.platformTween = createInitialPlatformMovementTween(
      this.scene!,
      sprite,
      randomDuration,
      () => {
        this.platformTween = createOscillatingPlatformTween(
          this.scene!,
          sprite,
          randomDuration,
          this.booster,
          this.mob,
          this.collectable
        )
      },
      this.booster,
      this.mob,
      this.collectable
    )
  }

  destroyPlatform(): void {
    this.scene?.tweens.killTweensOf(this.image!)

    if (this.platformTween) {
      this.platformTween.stop()
      this.platformTween.remove()
      this.platformTween = null
    }
    if (this.shakeTween) {
      this.shakeTween.stop()
      this.shakeTween.remove()
      this.shakeTween = null
    }

    if (this.oscillatingTween) {
      this.oscillatingTween.stop()
      this.scene?.tweens.remove(this.oscillatingTween)
      this.oscillatingTween.remove()
      this.oscillatingTween.destroy()
      this.oscillatingTween = null
    }

    if (this.booster) {
      this.booster.returnToPool()
      this.booster = null
    }
    if (this.mob) {
      this.mob.returnToPool()
      this.mob = null
    }
    if (this.collectable) {
      this.collectable.returnToPool()
      this.collectable = null
    }
    if (this.decoration) {
      this.decoration.destroy()
      this.decoration = null
    }

    this.nextFree = null

    this.scene?.events.once('postupdate', () => {
      if (this.image) {
        this.image.destroy(true)
        this.image = null
      }
      if (this.body) {
        this.body.destroy(true)
        this.body = null
      }
      if (this.collider) {
        this.collider.destroy()
        this.collider = null
      }
      this.scene = null
    })

    const world = getGameWorld()
    removeEntity(world, this.entity)
    this.entity = -1
  }
}
