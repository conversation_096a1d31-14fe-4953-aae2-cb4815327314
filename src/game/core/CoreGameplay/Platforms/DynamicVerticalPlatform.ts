import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { Platform } from '../../ecs/Components/EntityComponents'
import { BasePlatform } from './BasePlatform'

export class DynamicVerticalPlatform extends BasePlatform {
  constructor(options: BasePlatformOptions) {
    super(options)
    this.setBool(Platform.shouldAnimateTouch, false)
  }

  reset(options: BasePlatformOptions): void {
    if (this.platformTween) {
      this.platformTween.stop()
      this.platformTween = null
    }

    super.reset(options)
    this.startVerticalTweener(options.customParams, options.spriteConfig.position.y)
    this.setBool(Platform.shouldAnimateTouch, false)
    if (__DEV__) console.log('Dynamic Platform', 'resetting dynamic vertical platform')
  }

  async destroyPlatform(): Promise<void> {
    if (this.platformTween) {
      this.platformTween.stop()
      this.platformTween = null
    }
    if (__DEV__) console.log('Dynamic Platform', 'destroy dynamic vertical platform')
    await super.destroyPlatform()
  }
}
