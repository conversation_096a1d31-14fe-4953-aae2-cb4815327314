import type { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform'

export class PlatformsDebugViewController {
  private platforms: BasePlatform[] = []
  private currentIndex: number = -1

  trySwitchPlatformsView() {
    const textures: string[] = ['1', '2', '3']
    this.currentIndex = (this.currentIndex + 1) % textures.length
    if (this.currentIndex > textures.length) {
      this.currentIndex = 0
    }
    this.platforms.forEach(p => {
      const type = p.getType().toString()
      const sprite = p.getSprite()
      if (this.currentIndex == -1) {
        sprite.setTexture(type)
        this.currentIndex = -1
      } else {
        try {
          sprite.setTexture(`${type}_${textures[this.currentIndex]}`)
        } catch (error: any) {
          console.log('this sprite doesnt have test texture')
        }
      }
    })

    console.log(this.currentIndex)
  }

  setPlatformGroup(platforms: BasePlatform[]) {
    this.platforms = platforms
  }
}
