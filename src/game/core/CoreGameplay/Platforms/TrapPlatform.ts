import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { Position } from '../../ecs/Components/EntityComponents'
import { TrapPlatformConsts } from '../Constants/PlatformAnimationsConsts'
import { BasePlatform } from './BasePlatform'

export class TrapPlatform extends BasePlatform {
  private trapImageKey = '101.png'
  private trapSprite: Phaser.Physics.Arcade.Sprite | null = null
  private trapSpriteCollider: Phaser.Physics.Arcade.Body | null = null

  constructor(options: BasePlatformOptions) {
    super(options)

    this.createTrap()
    if (this.trapSprite) {
      this.trapSprite.setActive(false).setVisible(false)
      options.factory.trapPlatformManager.addTrap(this.trapSprite)
    }
  }

  reset(options: BasePlatformOptions): void {
    super.reset(options)
    if (!this.trapSprite) {
      return
    }

    const positionX = Position.x[this.entity] + TrapPlatformConsts.TRAP_POSITION_OFFSET_X
    const positionY = Position.y[this.entity] + TrapPlatformConsts.TRAP_POSITION_OFFSET_Y

    this.trapSprite.setPosition(positionX, positionY)
    this.trapSprite.setActive(true).setVisible(true)

    if (this.trapSpriteCollider) {
      this.trapSpriteCollider.enable = true
    }
  }

  returnToPool(): void {
    super.returnToPool()

    if (!this.trapSprite) return
    this.trapSprite.setActive(false).setVisible(false)
    if (this.trapSpriteCollider) {
      this.trapSpriteCollider.setEnable(false)
    }
  }

  private createTrap(): void {
    const scene = this.image!.scene
    if (!scene) return
    this.trapSprite = scene.physics.add.sprite(0, 0, AtlasNames.ENV, this.trapImageKey)

    this.trapSprite.setDisplaySize(
      TrapPlatformConsts.TRAP_DISPLAY_SIZE_X,
      TrapPlatformConsts.TRAP_DISPLAY_SIZE_Y
    )

    this.trapSpriteCollider = this.trapSprite.body as Phaser.Physics.Arcade.Body

    if (this.trapSpriteCollider) {
      this.trapSpriteCollider.checkCollision.none = false
      this.trapSpriteCollider.enable = true
      this.trapSpriteCollider.setImmovable(true)
      this.trapSprite.refreshBody()
    }

    this.trapSprite.setDepth(TrapPlatformConsts.TRAP_DEPTH)
  }
}
