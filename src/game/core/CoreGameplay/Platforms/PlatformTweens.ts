import { logger } from '@/shared/Logger'
import type { Scene } from 'phaser'
import type { GameScene } from '../../scenes/GameScene'
import type { BaseBooster } from '../Boosters/BaseBooster'
import type { CollectableItemBase } from '../Collectables/CollectableItemBase'
import { GameViewConsts } from '../Constants/GameViewConsts'
import type { BaseEnemy } from '../Enemies/BaseEnemy'

export function createInitialPlatformMovementTween(
  scene: GameScene,
  sprite: Phaser.GameObjects.Sprite,
  baseDuration: number,
  onCompleteCallback: () => void,
  booster: BaseBooster | null,
  mob: BaseEnemy | null,
  collectable: CollectableItemBase | null
): Phaser.Tweens.Tween {
  const body = sprite.body as Phaser.Physics.Arcade.StaticBody
  const camera = scene.cameras.main

  const screenRight = GameViewConsts.REF_WIDTH
  const screenCenter = screenRight / 2
  const screenLeft = camera.worldView.left
  const spriteWidth = sprite.displayWidth
  if (__DEV__) logger.info(`camera right pos: ${screenRight}, left pos: ${screenLeft}`)

  let distanceToTarget: number
  let targetX: number

  if (sprite.x < screenCenter) {
    distanceToTarget = screenRight - sprite.x - spriteWidth
    targetX = screenRight - spriteWidth
  } else {
    distanceToTarget = sprite.x - screenLeft
    targetX = screenLeft
  }

  const adjustedDuration = (distanceToTarget / camera.worldView.width) * baseDuration

  const tweenConfig = {
    targets: sprite,
    x: targetX,
    duration: adjustedDuration,
    ease: 'Linear',
    yoyo: false,
    repeat: 0,
    onUpdate: () => {
      if (!sprite.active) {
        return
      }

      if (booster) {
        booster.syncPosition(sprite.x)
      }
      if (mob) {
        mob.syncPosition(sprite.x)
      }

      if (collectable) {
        collectable.syncPosition(sprite.x)
      }

      body.x = sprite.x
    },
    onComplete: onCompleteCallback
  }

  return scene.tweens.add(tweenConfig)
}

export function createOscillatingPlatformTween(
  scene: Scene,
  sprite: Phaser.GameObjects.Sprite,
  duration: number,
  booster: BaseBooster | null,
  mob: BaseEnemy | null,
  collectable: CollectableItemBase | null
): Phaser.Tweens.Tween {
  const body = sprite.body as Phaser.Physics.Arcade.StaticBody
  const camera = scene.cameras.main

  const screenRight = GameViewConsts.REF_WIDTH
  const screenLeft = camera.worldView.left
  const spriteWidth = sprite.displayWidth

  let initialTargetX: number
  if (sprite.x < screenRight / 2) {
    initialTargetX = screenRight - spriteWidth
  } else {
    initialTargetX = screenLeft
  }

  const tweenConfig = {
    targets: sprite,
    x: initialTargetX,
    duration: duration,
    ease: 'Linear',
    yoyo: true,
    repeat: -1,
    onUpdate: () => {
      if (!sprite.active) {
        return
      }

      if (booster) {
        booster.syncPosition(sprite.x)
      }
      if (mob) {
        mob.syncPosition(sprite.x)
      }

      if (collectable) {
        collectable.syncPosition(sprite.x)
      }
      body.x = sprite.x
    },
    onYoyo: () => {
      if (sprite.x <= screenLeft) {
        tweenConfig.x = screenRight - spriteWidth
      } else if (sprite.x >= screenRight - spriteWidth) {
        tweenConfig.x = screenLeft
      }
    }
  }

  return scene.tweens.add(tweenConfig)
}
