import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { BasePlatform } from './BasePlatform'
import { InvisiblePlatformManager } from './Managers/InvisiblePlatformManager'

export class InvisiblePlatform extends BasePlatform {
  private manager: InvisiblePlatformManager
  private blinkTween: Phaser.Tweens.Tween | null = null

  constructor(options: BasePlatformOptions) {
    super(options)
    this.manager = options.factory.invisiblePlatformManager
    const chunkIndex = options.chunkInfo.index
    this.manager.addPlatform(this, chunkIndex)

    if (!this.customParams.isVisible) {
      this.disablePlatform()
    } else {
      this.enablePlatform()
    }
  }

  reset(options: BasePlatformOptions): void {
    super.reset(options)
    const chunkIndex = options.chunkInfo.index
    this.manager.addPlatform(this, chunkIndex)

    if (!this.customParams.isVisible) {
      this.disablePlatform()
    } else {
      this.enablePlatform()
    }
  }

  override destroyPlatform(): void {
    this.manager.removePlatform(this, this.chunkInfo.index)
    super.destroyPlatform()
  }

  disablePlatform(): void {
    this.getSprite().setAlpha(0)
    this.stopBlinking()
    if (this.body && this.body.body) {
      this.body.body.enable = false
      this.refreshBody()
    }
  }

  enablePlatform(): void {
    this.getSprite().setAlpha(1)
    if (this.body && this.body.body) {
      this.body.body.enable = true
    }
    this.refreshBody()
    this.startBlinking()
  }

  returnToPool() {
    super.returnToPool()
    this.stopBlinking()
  }

  interact(playerPos: Vector2): void {
    super.interact(playerPos)
    const chunkIndex = this.chunkInfo.index
    this.manager.activateNextPlatform(this, chunkIndex)
    this.manager.removePlatform(this, chunkIndex)
    this.customParams.isVisible = false
    this.stopBlinking()

    this.scene!.tweens.add({
      targets: this.getSprite(),
      alpha: 0,
      duration: 5 * (1000 / 60),
      ease: 'Linear',
      onComplete: () => {
        this.customParams.isVisible = false
        this.disablePlatform()
      }
    })
  }

  private startBlinking(): void {
    if (this.blinkTween) {
      this.blinkTween.stop()
    }

    this.blinkTween = this.scene!.tweens.add({
      targets: this.getSprite(),
      alpha: { from: 1, to: 0.5 },
      duration: 5 * (1000 / 60),
      yoyo: true,
      repeat: -1,
      ease: 'Linear',
      onYoyo: () => {
        const sprite = this.getSprite()
        if (sprite.tintTopLeft === 0xffffff) {
          sprite.clearTint()
        } else {
          sprite.setTint(0xffffff)
        }
      }
    })
  }

  private stopBlinking(): void {
    if (this.blinkTween) {
      this.blinkTween.stop()
      this.blinkTween = null
      this.getSprite().clearTint()
      this.getSprite().setAlpha(1)
    }
  }
}
