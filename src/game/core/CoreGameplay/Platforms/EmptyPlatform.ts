import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters'
import { BasePlatform } from './BasePlatform'

export class EmptyPlatform extends BasePlatform {
  constructor(options: BasePlatformOptions) {
    super(options)
  }

  private makeInvisibleAndNonInteractive(): void {
    this.image!.setDepth(-1)
    this.image!.setVisible(false)
    this.body!.body!.enable = false
  }

  reset(options: BasePlatformOptions): void {
    super.reset(options)
    this.makeInvisibleAndNonInteractive()
  }

  interact(): void {}

  update(): void {}

  startTweener(customParams: CustomParameters): void {}
}
