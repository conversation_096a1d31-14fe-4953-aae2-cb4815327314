import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { BasePlatform } from './BasePlatform'
import { SpikedPlatformManager } from './Managers/SpikedPlatformManager'

export class SpikedPlatform extends BasePlatform {
  private manager: SpikedPlatformManager
  private isSpiked: boolean
  private noSpikeTexture: string = '61.png'
  private spikeTexture: string = '60.png'

  constructor(options: BasePlatformOptions) {
    super(options)
    this.manager = options.factory.spikedPlatformManager
    const chunkIndex = options.chunkInfo.index
    this.manager.addPlatform(this, chunkIndex)
    this.isSpiked = options.customParams.isVisible || false

    this.setupSpikedPlatform()
  }

  public IsSpiked(): boolean {
    return this.isSpiked
  }

  public toggleSpikeState() {
    this.isSpiked = !this.isSpiked
    this.setupSpikedPlatform()
  }

  private setupSpikedPlatform() {
    if (this.isSpiked) {
      this.spikesUp()
    } else {
      this.spikesDown()
    }
  }

  private spikesUp(): void {
    this.getSprite().setTexture(AtlasNames.ENV, this.spikeTexture)
  }

  private spikesDown(): void {
    this.getSprite().setTexture(AtlasNames.ENV, this.noSpikeTexture)
  }

  override reset(options: BasePlatformOptions): void {
    super.reset(options)
    const chunkIndex = options.chunkInfo.index
    this.isSpiked = options.customParams.isVisible || false
    this.manager.addPlatform(this, chunkIndex)

    this.setupSpikedPlatform()
  }

  override returnToPool() {
    const chunkIndex = this.chunkInfo.index
    this.manager.removePlatform(this, chunkIndex)
    super.returnToPool()
  }

  override interact(playerPos: Vector2): void {
    super.interact(playerPos)
    const chunkIndex = this.chunkInfo.index
    this.manager.toggleSpikesInAChunk(chunkIndex)
  }
}
