import type { BasePlatformOptions } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { clearTween } from '../../HelperFunctions'
import { WreckableConsts } from '../Constants/PlatformAnimationsConsts'
import { BasePlatform } from './BasePlatform'
import {
  createInitialPlatformMovementTween,
  createOscillatingPlatformTween
} from './PlatformTweens'

export class WreckablePlatform extends BasePlatform {
  private wrecked: boolean = false

  constructor(options: BasePlatformOptions) {
    super(options)
  }

  reset(options: BasePlatformOptions): void {
    if (this.platformTween) {
      this.platformTween.stop()
    }

    this.wrecked = false
    const sprite = this.getSprite()
    sprite.setAlpha(1)
    sprite.setPosition(options.spriteConfig.position.x, options.spriteConfig.position.y)

    super.reset(options)
    this.tryStartTweener(options.customParams)
  }

  interact(playerPos: Vector2) {
    if (this.isActive) {
      super.interact(playerPos)
      this.destroyPlatform()
    }
  }

  private tryStartTweener(customParams: CustomParameters) {
    const randomDuration = customParams.duration
    const sprite = this.getSprite()

    if (randomDuration) {
      this.platformTween = createInitialPlatformMovementTween(
        this.scene!,
        sprite,
        randomDuration,
        () => {
          this.platformTween = createOscillatingPlatformTween(
            this.scene!,
            sprite,
            randomDuration,
            this.booster,
            this.mob,
            this.collectable
          )
        },
        this.booster,
        this.mob,
        this.collectable
      )
    }
  }

  async playWreck(): Promise<void> {
    if (!this.isActive || !this.body || !this.body.body) return

    return new Promise<void>(resolve => {
      this.body!.body!.enable = false
      const sprite = this.getSprite()

      sprite.play('crashAnim')

      const fadeStartDelay = WreckableConsts.FALL_DURATION * WreckableConsts.FADE_START_MULTIPLIER
      const fadeDuration = WreckableConsts.FALL_DURATION * WreckableConsts.FADE_DURATION_MULTIPLIER

      this.scene!.tweens.add({
        targets: sprite,
        y: sprite.y + WreckableConsts.FALL_DISTANCE,
        ease: 'Cubic.easeOut',
        duration: WreckableConsts.FALL_DURATION,
        onComplete: () => {
          resolve()
        }
      })

      this.scene!.tweens.add({
        targets: sprite,
        alpha: 0,
        ease: 'Linear',
        delay: fadeStartDelay,
        duration: fadeDuration
      })
    })
  }

  async destroyPlatform(): Promise<void> {
    if (!this.wrecked) {
      this.wrecked = true

      await this.playWreck()

      if (this.platformTween) {
        clearTween(this.platformTween)
        this.platformTween = null
      }
      super.returnToPool()
    }
  }
}
