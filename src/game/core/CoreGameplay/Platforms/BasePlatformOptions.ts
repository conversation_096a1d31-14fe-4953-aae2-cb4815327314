import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters'
import type { PlatformFactory } from '@/game/core/CoreGameplay/Platforms/PlatformFactory'
import type { Chunk, EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'

export interface Position {
  x: number
  y: number
}

export interface ChunkInfo extends Omit<Chunk, 'components'> {
  platformIndex: number
}

export interface SpriteConfig {
  position: Position
  texture: string
}

export interface BasePlatformOptions {
  factory: PlatformFactory
  entityType: EntityType
  platformGroup: Phaser.Physics.Arcade.StaticGroup
  scene: GameScene
  spriteConfig: SpriteConfig
  customParams: CustomParameters
  chunkInfo: ChunkInfo
  gameInterface: GameInterface
}
