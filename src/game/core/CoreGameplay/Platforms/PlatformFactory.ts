import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform'
import type {
  BasePlatformOptions,
  Position
} from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { DisposablePlatform } from '@/game/core/CoreGameplay/Platforms/DisposablePlatform'
import { DynamicHorizontalPlatform } from '@/game/core/CoreGameplay/Platforms/DynamicHorizontalPlatform'
import { DynamicVerticalPlatform } from '@/game/core/CoreGameplay/Platforms/DynamicVerticalPlatform'
import { EmptyPlatform } from '@/game/core/CoreGameplay/Platforms/EmptyPlatform'
import { InvisiblePlatform } from '@/game/core/CoreGameplay/Platforms/InvisiblePlatform'
import { PlatformPool } from '@/game/core/CoreGameplay/Platforms/PlatformPool'
import { ShiftingPlatform } from '@/game/core/CoreGameplay/Platforms/ShiftingPlatform'
import { StaticPlatform } from '@/game/core/CoreGameplay/Platforms/StaticPlatform'
import { WreckablePlatform } from '@/game/core/CoreGameplay/Platforms/WreckablePlatform'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { PoolSizeConsts } from '../Constants/PoolConsts'
import { BoosterPlatform } from './BoosterPlatform'
import { ExplosivePlatform } from './ExplosivePlatform'
import { IcePlatform } from './IcePlatform'
import { LevitatingPlatform } from './LevitatingPlatform'
import { ExplosivePlatformManager } from './Managers/ExplosivePlatformManager'
import { InvisiblePlatformManager } from './Managers/InvisiblePlatformManager'
import { PropellerPlatformManager } from './Managers/PropellerPlatformManager'
import { ShiftingPlatformManager } from './Managers/ShiftingPlatformManager'
import { SpikedPlatformManager } from './Managers/SpikedPlatformManager'
import { TrapPlatformManager } from './Managers/TrapPlatformManager'
import { PropellerPlatform } from './PropellerPlatform'
import { SpikedPlatform } from './SpikedPlatform'
import { TrapPlatform } from './TrapPlatform'

export class PlatformFactory {
  private staticPlatformPool: PlatformPool<StaticPlatform>
  private dynamicHorizontalPlatformPool: PlatformPool<DynamicHorizontalPlatform>
  private wreckablePlatformPool: PlatformPool<WreckablePlatform>
  private disposablePlatformPool: PlatformPool<DisposablePlatform>
  private dynamicVerticalPlatformPool: PlatformPool<DynamicVerticalPlatform>
  private shiftingPlatformPool: PlatformPool<ShiftingPlatform>
  private invisiblePlatformPool: PlatformPool<InvisiblePlatform>
  private explosivePlatformPool: PlatformPool<ExplosivePlatform>
  private spikedPlatformPool: PlatformPool<SpikedPlatform>
  private emptyPlatformPool: PlatformPool<EmptyPlatform>
  private icePlatformPool: PlatformPool<IcePlatform>
  private boosterPlatformPool: PlatformPool<BoosterPlatform>
  private levitatingPlatformPool: PlatformPool<LevitatingPlatform>
  private trapPlatformPool: PlatformPool<TrapPlatform>
  private propellerPlatformPool: PlatformPool<PropellerPlatform>

  public shiftingPlatformManager: ShiftingPlatformManager
  public invisiblePlatformManager: InvisiblePlatformManager
  public explosivePlatformManager: ExplosivePlatformManager
  public spikedPlatformManager: SpikedPlatformManager
  public trapPlatformManager: TrapPlatformManager
  public propellerPlatformManager: PropellerPlatformManager

  constructor(scene: GameScene, gameInterface: GameInterface) {
    this.shiftingPlatformManager = new ShiftingPlatformManager(scene)
    this.invisiblePlatformManager = new InvisiblePlatformManager(scene)
    this.explosivePlatformManager = new ExplosivePlatformManager(scene)
    this.trapPlatformManager = new TrapPlatformManager(scene)
    this.propellerPlatformManager = new PropellerPlatformManager(scene)
    this.spikedPlatformManager = new SpikedPlatformManager()

    this.staticPlatformPool = new PlatformPool<StaticPlatform>(
      scene,
      this,
      gameInterface,
      StaticPlatform,
      PoolSizeConsts.STATIC_PLATFORM_POOL_SIZE
    )
    this.dynamicHorizontalPlatformPool = new PlatformPool<DynamicHorizontalPlatform>(
      scene,
      this,
      gameInterface,
      DynamicHorizontalPlatform,
      PoolSizeConsts.HORIZONTAL_PLATFORM_POOL_SIZE
    )
    this.wreckablePlatformPool = new PlatformPool<WreckablePlatform>(
      scene,
      this,
      gameInterface,
      WreckablePlatform,
      PoolSizeConsts.WRECKABLE_PLATFORM_POOL_SIZE
    )
    this.disposablePlatformPool = new PlatformPool<DisposablePlatform>(
      scene,
      this,
      gameInterface,
      DisposablePlatform,
      PoolSizeConsts.DISPOSABLE_PLATFORM_POOL_SIZE
    )
    this.dynamicVerticalPlatformPool = new PlatformPool<DynamicVerticalPlatform>(
      scene,
      this,
      gameInterface,
      DynamicVerticalPlatform,
      PoolSizeConsts.VERTICAL_PLATFORM_POOL_SIZE
    )
    this.shiftingPlatformPool = new PlatformPool<ShiftingPlatform>(
      scene,
      this,
      gameInterface,
      ShiftingPlatform,
      PoolSizeConsts.SHIFTING_PLATFORM_POOL_SIZE
    )
    this.invisiblePlatformPool = new PlatformPool<InvisiblePlatform>(
      scene,
      this,
      gameInterface,
      InvisiblePlatform,
      PoolSizeConsts.INVISIBLE_PLATFORM_POOL_SIZE
    )
    this.emptyPlatformPool = new PlatformPool<EmptyPlatform>(
      scene,
      this,
      gameInterface,
      EmptyPlatform,
      PoolSizeConsts.EMPTY_PLATFORM_POOL_SIZE
    )
    this.explosivePlatformPool = new PlatformPool<ExplosivePlatform>(
      scene,
      this,
      gameInterface,
      ExplosivePlatform,
      PoolSizeConsts.EXPLOSIVE_PLATFORM_POOL_SIZE
    )
    this.spikedPlatformPool = new PlatformPool<SpikedPlatform>(
      scene,
      this,
      gameInterface,
      SpikedPlatform,
      PoolSizeConsts.SPIKED_PLATFORM_POOL_SIZE
    )
    this.icePlatformPool = new PlatformPool<IcePlatform>(
      scene,
      this,
      gameInterface,
      IcePlatform,
      PoolSizeConsts.ICE_PLATFORM_POOL_SIZE
    )
    this.boosterPlatformPool = new PlatformPool<BoosterPlatform>(
      scene,
      this,
      gameInterface,
      BoosterPlatform,
      PoolSizeConsts.BOOSTER_PLATFORM_POOL_SIZE
    )
    this.levitatingPlatformPool = new PlatformPool<LevitatingPlatform>(
      scene,
      this,
      gameInterface,
      LevitatingPlatform,
      PoolSizeConsts.LEVITATING_PLATFORM_POOL_SIZE
    )
    this.trapPlatformPool = new PlatformPool<TrapPlatform>(
      scene,
      this,
      gameInterface,
      TrapPlatform,
      PoolSizeConsts.TRAP_PLATFORM_POOL_SIZE
    )

    this.propellerPlatformPool = new PlatformPool<PropellerPlatform>(
      scene,
      this,
      gameInterface,
      PropellerPlatform,
      PoolSizeConsts.PROPELLER_PLATFORM_POOL_SIZE
    )

    scene.time.delayedCall(500, () => {
      this.trapPlatformManager.cachePlayer()
      this.propellerPlatformManager.cachePlayer()
    })
  }

  async preloadPlatforms(): Promise<void> {
    await Promise.all([
      this.staticPlatformPool.preload(100),
      this.dynamicHorizontalPlatformPool.preload(20),
      this.wreckablePlatformPool.preload(20),
      this.disposablePlatformPool.preload(20),
      this.dynamicVerticalPlatformPool.preload(10),
      this.shiftingPlatformPool.preload(10),
      this.invisiblePlatformPool.preload(10),
      this.emptyPlatformPool.preload(10),
      this.explosivePlatformPool.preload(20)
      // this.spikedPlatformPool.preload(10),
      // this.icePlatformPool.preload(10),
      // this.boosterPlatformPool.preload(10),
      // this.levitatingPlatformPool.preload(10),
      // this.trapPlatformPool.preload(10),
      // this.propellerPlatformPool.preload(10)
    ])
  }

  createPlatform(options: BasePlatformOptions): BasePlatform {
    let platform: BasePlatform

    switch (options.entityType) {
      case EntityType.PlatformStatic:
        platform = this.staticPlatformPool.get(options)
        break
      case EntityType.PlatformDynamicH:
        platform = this.dynamicHorizontalPlatformPool.get(options)
        break
      case EntityType.PlatformWreckable:
        platform = this.wreckablePlatformPool.get(options)
        break
      case EntityType.PlatformDisposable:
        platform = this.disposablePlatformPool.get(options)
        break
      case EntityType.PlatformDynamicV:
        platform = this.dynamicVerticalPlatformPool.get(options)
        break
      case EntityType.PlatformShifting:
        platform = this.shiftingPlatformPool.get(options)
        break
      case EntityType.PlatformInvisible:
        platform = this.invisiblePlatformPool.get(options)
        break
      case EntityType.PlatformEmpty:
        platform = this.emptyPlatformPool.get(options)
        break
      case EntityType.PlatformExplosive:
        platform = this.explosivePlatformPool.get(options)
        break
      case EntityType.PlatformSpiked:
        platform = this.spikedPlatformPool.get(options)
        break
      case EntityType.PlatformIce:
        platform = this.icePlatformPool.get(options)
        break
      case EntityType.PlatformBooster:
        platform = this.boosterPlatformPool.get(options)
        break
      case EntityType.PlatformLevitating:
        platform = this.levitatingPlatformPool.get(options)
        break
      case EntityType.PlatformTrap:
        platform = this.trapPlatformPool.get(options)
        break
      case EntityType.PlatformPropeller:
        platform = this.propellerPlatformPool.get(options)
        break
      default:
        throw new Error(`Invalid platform type: ${options.entityType}`)
    }

    return platform
  }

  returnPlatformToPool(platform: BasePlatform): void {
    switch (platform.getType()) {
      case EntityType.PlatformStatic:
        this.staticPlatformPool.return(platform as StaticPlatform)
        break
      case EntityType.PlatformDynamicH:
        this.dynamicHorizontalPlatformPool.return(platform as DynamicHorizontalPlatform)
        break
      case EntityType.PlatformWreckable:
        this.wreckablePlatformPool.return(platform as WreckablePlatform)
        break
      case EntityType.PlatformDisposable:
        this.disposablePlatformPool.return(platform as DisposablePlatform)
        break
      case EntityType.PlatformDynamicV:
        this.dynamicVerticalPlatformPool.return(platform as DynamicVerticalPlatform)
        break
      case EntityType.PlatformShifting:
        this.shiftingPlatformPool.return(platform as ShiftingPlatform)
        break
      case EntityType.PlatformInvisible:
        this.invisiblePlatformPool.return(platform as InvisiblePlatform)
        break
      case EntityType.PlatformEmpty:
        this.emptyPlatformPool.return(platform as EmptyPlatform)
        break
      case EntityType.PlatformExplosive:
        this.explosivePlatformPool.return(platform as ExplosivePlatform)
        break
      case EntityType.PlatformSpiked:
        this.spikedPlatformPool.return(platform as SpikedPlatform)
        break
      case EntityType.PlatformIce:
        this.icePlatformPool.return(platform as IcePlatform)
        break
      case EntityType.PlatformBooster:
        this.boosterPlatformPool.return(platform as BoosterPlatform)
        break
      case EntityType.PlatformLevitating:
        this.levitatingPlatformPool.return(platform as LevitatingPlatform)
        break
      case EntityType.PlatformTrap:
        this.trapPlatformPool.return(platform as TrapPlatform)
        break
      case EntityType.PlatformPropeller:
        this.propellerPlatformPool.return(platform as PropellerPlatform)
        break
      default:
        throw new Error(`Invalid platform type: ${platform.getType()}`)
    }
  }

  createRevivingPlatform(pos: Position, group: Phaser.Physics.Arcade.StaticGroup): BasePlatform {
    return this.staticPlatformPool.createRevivingPlatform(pos, group)
  }

  clearPlatformPools(): void {
    this.staticPlatformPool.clear()
    this.dynamicHorizontalPlatformPool.clear()
    this.wreckablePlatformPool.clear()
    this.disposablePlatformPool.clear()
    this.dynamicVerticalPlatformPool.clear()
    this.shiftingPlatformPool.clear()
    this.invisiblePlatformPool.clear()
    this.emptyPlatformPool.clear()
    this.explosivePlatformPool.clear()
    this.spikedPlatformPool.clear()
    this.icePlatformPool.clear()
    this.boosterPlatformPool.clear()
    this.levitatingPlatformPool.clear()
    this.trapPlatformPool.clear()
    this.propellerPlatformPool.clear()

    this.shiftingPlatformManager.clear()
    this.invisiblePlatformManager.clear()
    this.explosivePlatformManager.clear()
    this.spikedPlatformManager.clear()
  }
}
