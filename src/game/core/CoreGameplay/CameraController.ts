import { GameViewConsts } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { Cameras } from 'phaser'
import { GameScene } from '../scenes/GameScene'

export class CameraController {
  private readonly camera: Cameras.Scene2D.Camera
  private readonly target: Phaser.GameObjects.Sprite
  private readonly verticalThreshold: number
  private positionThreshold: number = 110
  private isGameLoopStarted: boolean = false
  private endGameInvoked: boolean = false
  private playerRespawned: boolean = false

  constructor(
    private scene: GameScene,
    target: Phaser.GameObjects.Sprite
  ) {
    this.camera = this.scene.cameras.main
    this.target = target
    this.isGameLoopStarted = true

    this.initializeCameraPosition()
    this.verticalThreshold = this.calculateVerticalThreshold()
    this.setPlayerRespawnDelay(1500)

    this.scene.events.on('player-moved', (data: { y: number }) => {
      this.followPlayer()
    })
  }

  private initializeCameraPosition(): void {
    this.camera.scrollY = this.target.y - this.camera.height / this.camera.zoom
  }

  private calculateVerticalThreshold(): number {
    return this.camera.height * GameViewConsts.CAMERA_THRESHOLD
  }

  private setPlayerRespawnDelay(delay: number): void {
    this.scene.time.delayedCall(delay, () => {
      this.playerRespawned = true
    })
  }

  private followPlayer(): void {
    if (!this.isGameLoopStarted || !this.target.body) return
    const targetY = this.target.y - this.verticalThreshold
    const deltaTime = this.scene.game.loop.delta

    if (this.shouldUpdateScrollY(targetY)) {
      this.followPlayerWithTween(targetY, deltaTime)
    }

    if (this.shouldInvokeEndGame()) {
      this.scene.invokeEndGame(true)
      this.startGameLoop(false)
      this.endGameInvoked = true
    }
  }

  private followPlayerWithTween(targetY: number, deltaTime: number): void {
    const lerpFactor = 0.05
    const normalizedLerp = lerpFactor * (deltaTime / (1000 / 60))

    this.camera.scrollY = Phaser.Math.Linear(this.camera.scrollY, targetY, normalizedLerp)

    this.sendCameraBottomPosition()
  }

  private shouldUpdateScrollY(targetY: number): boolean {
    return (
      targetY < this.camera.scrollY &&
      Math.abs(targetY - this.camera.scrollY) > this.positionThreshold
    )
  }

  private shouldInvokeEndGame(): boolean {
    if (this.endGameInvoked || !this.playerRespawned) return false

    const cameraBottom = this.camera.worldView.bottom
    const acceptableThreshold = this.camera.height * 0.1
    const playerBottom = this.target.getBounds().bottom

    return playerBottom > cameraBottom + acceptableThreshold
  }

  private sendCameraBottomPosition(): void {
    const bottomPosition = this.camera.scrollY + this.camera.height / this.camera.zoom
    this.scene.events.emit('camera-bottom-changed', bottomPosition)
  }

  public moveBackToGame(onComplete?: () => void): void {
    this.scene.tweens.add({
      targets: this.camera,
      scrollY: this.camera.scrollY - this.camera.displayHeight * 2,
      ease: 'Sine.linear',
      duration: 1000,
      onComplete: () => onComplete?.()
    })
  }

  public trackEndGameInvoked(shouldTrack: boolean): void {
    this.endGameInvoked = shouldTrack
  }

  public moveToEndGame(onComplete?: () => void): void {
    const scrollDistance = this.camera.displayHeight * 2
    const playerFallSpeed = 2000
    const duration = (scrollDistance / playerFallSpeed) * 1000 * 0.5

    this.scene.time.delayedCall(300, () => {
      this.scene.tweens.add({
        targets: this.camera,
        scrollY: this.camera.scrollY + scrollDistance,
        ease: 'Quadratic.Out',
        duration: duration,
        onComplete: () => onComplete?.()
      })
    })
  }

  public instantMoveToEndGame(): void {
    this.scene.invokeEndGame(false)
    this.startGameLoop(false)
    this.endGameInvoked = true

    const scrollDistance = this.camera.displayHeight * 2
    this.moveCamera(this.camera.scrollY + scrollDistance, 'Quadratic.Out', 10)
    this.sendCameraBottomPosition()
  }

  public instantMoveToStartGame(revivePoint: number): void {
    this.moveCamera(revivePoint, 'Quadratic.Out', 10)
    this.sendCameraBottomPosition()
  }

  private moveCamera(
    targetScrollY: number,
    ease: string,
    duration: number,
    onComplete?: () => void
  ): void {
    this.scene.tweens.add({
      targets: this.camera,
      scrollY: targetScrollY,
      ease: ease,
      duration: duration,
      onComplete: () => onComplete?.()
    })
  }

  public startGameLoop(startLoop: boolean): void {
    this.isGameLoopStarted = startLoop
  }
}
