import type { BaseBooster } from '@/game/core/CoreGameplay/Boosters/BaseBooster.ts'
import type { CollectableItemBase } from '@/game/core/CoreGameplay/Collectables/CollectableItemBase.ts'
import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy.ts'
import type { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform.ts'
import type { Scene } from 'phaser'

export class Interactables {
  private readonly platformsGroup: Phaser.Physics.Arcade.StaticGroup
  private readonly boostersGroup: Phaser.Physics.Arcade.StaticGroup
  private readonly mobsGroup: Phaser.Physics.Arcade.StaticGroup
  private readonly collectablesGroup: Phaser.Physics.Arcade.StaticGroup

  private platforms: BasePlatform[] = []
  private boosters: BaseBooster[] = []
  private mobs: BaseEnemy[] = []
  private collectables: CollectableItemBase[] = []

  constructor(scene: Scene) {
    this.platformsGroup = scene.physics.add.staticGroup()
    this.boostersGroup = scene.physics.add.staticGroup()
    this.mobsGroup = scene.physics.add.staticGroup()
    this.collectablesGroup = scene.physics.add.staticGroup()
  }

  addPlatform(platform: BasePlatform): void {
    this.platforms.push(platform)
  }

  addBooster(booster: BaseBooster): void {
    this.boosters.push(booster)
  }

  addMob(mob: BaseEnemy): void {
    this.mobs.push(mob)
  }

  addCollectable(collectable: CollectableItemBase): void {
    this.collectables.push(collectable)
  }

  get PlatformsGroup(): Phaser.Physics.Arcade.StaticGroup {
    return this.platformsGroup
  }

  get BoostersGroup(): Phaser.Physics.Arcade.StaticGroup {
    return this.boostersGroup
  }

  get MobsGroup(): Phaser.Physics.Arcade.StaticGroup {
    return this.mobsGroup
  }

  get CollectablesGroup(): Phaser.Physics.Arcade.StaticGroup {
    return this.collectablesGroup
  }

  get Platforms(): BasePlatform[] {
    return this.platforms
  }

  get Boosters(): BaseBooster[] {
    return this.boosters
  }

  get Mobs(): BaseEnemy[] {
    return this.mobs
  }

  get Collectables(): CollectableItemBase[] {
    return this.collectables
  }

  filterMobs(predicate: (mob: BaseEnemy) => boolean): BaseEnemy[] {
    return this.mobs.filter(predicate)
  }

  filterPlatforms(predicate: (platform: BasePlatform) => boolean): BasePlatform[] {
    return this.platforms.filter(predicate)
  }

  filterBoosters(predicate: (booster: BaseBooster) => boolean): BaseBooster[] {
    return this.boosters.filter(predicate)
  }

  filterCollectables(
    predicate: (collectable: CollectableItemBase) => boolean
  ): CollectableItemBase[] {
    return this.collectables.filter(predicate)
  }
}
