export const WreckableConsts = {
  FALL_DISTANCE: 300, // Total distance to fall
  FALL_DURATION: 840, // Fall duration
  FADE_START_MULTIPLIER: 0.5, // Start fading after half the fall
  FADE_DURATION_MULTIPLIER: 0.25, // Duration of fade-out
  TEXTURE_SWAP_DELAY_1: 50, // Delay between first and second texture swap
  TEXTURE_SWAP_DELAY_2: 75, // Delay between second and third texture swap
  TEXTURE_SWAP_DELAY_3: 100 // Delay between third and final texture swap
}

export const ShakeConstants = {
  AMPLITUDE_MULTIPLIER_UP: 1.4,
  AMPLITUDE_MULTIPLIER_DOWN: 0.8,
  SCALE_MULTIPLIER: 0.5,
  DURATION_OFFSET: 50
}

export const BasePlatformConsts = {
  DURATION: 500,
  SHAKE_AMPLITUDE: 10,
  SCALE_AMPLITUDE: 0.01
}

export const ExplosivePlatformConsts = {
  RED_FRAME_DURATION: 35,
  EXPLOSION_FRAME_DURATION: 85,
  DELAY_BEFORE_EXPLOSION: 1530
}

export const BoosterPlatformConsts = {
  DISPLAY_SIZE_X: 114,
  DISPLAY_SIZE_Y: 44,
  DISPLAY_OFFSET_Y: 10
}

export const LevitatingPlatformConsts = {
  DISPLAY_SIZE_X: 120,
  DISPLAY_SIZE_Y: 46,
  MOVE_DOWN_DISTANCE: 175
}

export const TrapPlatformConsts = {
  TRAP_DISPLAY_SIZE_X: 45,
  TRAP_DISPLAY_SIZE_Y: 40,
  TRAP_POSITION_OFFSET_X: 59,
  TRAP_POSITION_OFFSET_Y: -20,
  TRAP_DEPTH: 1
}
