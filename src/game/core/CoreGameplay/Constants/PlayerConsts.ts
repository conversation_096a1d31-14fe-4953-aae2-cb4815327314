import { Vector2 } from '@esotericsoftware/spine-phaser'

export const PlayerConsts = {
  POINTS_INDEX: 0.5,
  GRAVITY: 1568,
  JUMP_VELOCITY: -1080,
  ORIGIN_DISTANCE: -1300,
  WRAP_OFFSET: 30,
  VISUALS_OFFSET: new Vector2(0, 40),
  FIXED_TIME_STEP: 1 / 60,
  START_POSITION: new Vector2(0, -200),
  LEGS_OFFSET: 20,
  DISPLAY_SIZE: new Vector2(92, 90),
  COLLIDER_WIDTH: 2,
  COLLIDER_HEIGHT: 0.7,
  INIT_JUMP_VELOCITY: 1.3,
  MAX_PLAYER_VELOCITY: 900,
  PLAYER_JUMPING_OFFSET: -75,
  FLIP_DELAY: 50,
  FLIP_THRESHOLD: 1,
  WRECKABLE_DESTRUCTION_THRESHHOLD: 20,
  LAUNCH_HEIGHT_ON_RESPAWN: 3000,
  SPRITE_OFFSET_Y: 300,
  BODY_COLLIDER_OFFSET_X: 10,
  SPINE_SCALE_MULTIPLIER: 0.55,
  NEW_SPINE_SCALE_MULTIPLIER: 1,
  IMMORTALITY_DURATION_JETPACK: 850,
  IMMORTALITY_DURATION_PROPELLER: 750,
  MAGNETIC_FIELD_SIZE_SMALL: 300,
  MAGNETIC_FIELD_SIZE_BIG: 450
}

export const InputConsts = {
  SWIPE_SENSITIVITY: 2.5,
  MAX_VELOCITY: 350,
  ANGULAR_VELOCITY_IOS: 0.9,
  ANGULAR_VELOCITY: 0.8,
  SMOOTHING_FACTOR_IOS: 0.35,
  SMOOTHING_FACTOR_SWIPE: 0.35,
  SMOOTHING_FACTOR: 0.2,
  DECELERATION_FACTOR: 0.75,
  POINTER_STALE_THRESHOLD: 50,
  KEYBOARD_MULTIPLIER: 50,
  SWIPE_DELTAS_MAX_LENGTH: 5,
  DOUBLE_TAP_THRESHOLD: 300
}

export const SwipeEmitterConsts = {
  LIFESPAN: 900,
  SPEED_MIN: 250,
  SPEED_MAX: 350,
  ANGLE_MIN: 0,
  ANGLE_MAX: 360,
  RADIAL: true,
  SCALE_START: 0.6,
  SCALE_END: 0,
  ALPHA_START: 1,
  ALPHA_END: 0,
  BLEND_MODE: 'ADD',

  EMIT_ZONE_TYPE: 'edge' as const,
  EMIT_ZONE_RADIUS: 50,
  EMIT_ZONE_QUANTITY: 1,

  QUANTITY: 1,
  FREQUENCY: -1 as const,

  START_BURST_COUNT: 5,
  MOVE_BURST_COUNT: 0.5,

  Y_EMIT_OFFSET: 25
}
