export const MobStaticVerticalConsts = {
  HEALTH: 2,
  SPAWN_OFFSET_Y: 115,
  SPAWN_OFFSET_X: 7,
  JUMP_HEIGHT: 50,
  JUMP_DURATION: 1000,
  DELAY_CALL_DURATION: 33,
  COLLIDER_OFFSET_X: 0,
  COLLIDER_OFFSET_Y: 0
}

export const MobDynamicHorizontalBigConsts = {
  HEALTH: 1,
  SPAWN_OFFSET_X: 75,
  JUMP_DISTANCE: 75,
  JUMP_HEIGHT: 75,
  JUMP_DURATION: 1000,
  DELAY_CALL_DURATION: 33,
  SPRITE_COLLIDER_OFFSET_X: 30,
  SPRITE_COLLIDER_OFFSET_Y: 20
}

export const MobDynamicHorizontalHugeConsts = {
  HEALTH: 3,
  MOVE_DISTANCE: 25,
  MOVE_DURATION: 450,
  SHAKE_DISTANCE: 5,
  SHAKE_DURATION: 150
}

export const MobDynamicHorizontalMiddleConsts = {
  HEALTH: 1,
  RA<PERSON>OM_DURATION_MIN: 3000,
  RANDOM_DURATION_MAX: 3400,
  HALF_SCREEN_DIVISOR: 2
}

export const MobDynamicHorizontalSmallConsts = {
  HEALTH: 1,
  COLLIDER_OFFSET_X: 0,
  COLLIDER_OFFSET_Y: 0,
  COLLIDER_SCALE: 0.35
}

export const MobDynamicHorizontalTinyConsts = {
  HEALTH: 1,
  MOVE_DISTANCE: 25,
  DURATION: 650,
  COLLIDER_SCALE: 0.5,
  COLLIDER_OFFSET_X: 15,
  COLLIDER_UPDATE_OFFSET_X: 10,
  COLLIDER_UPDATE_OFFSET_Y: 10
}

export const MobDynamicHorizontalVerticalConsts = {
  HEALTH: 5,
  PROXIMITY_TO_TRIGGER_BEHAVIOUR: 750,
  CAMERA_LEFT_OFFSET: 50,
  CAMERA_RIGHT_OFFSET: 50,
  CAMERA_TOP_OFFSET: -250,
  CAMERA_BOTTOM_OFFSET: 50,
  MOVE_UPWARD_DISTANCE: 350,
  TWEEN_DURATION: 1500
}

export const MobDynamicVerticalConsts = {
  HEALTH: 1,
  MOVE_DISTANCE: 25,
  DURATION: 220,
  SPRITE_COLLIDER_OFFSET_X: 20,
  SPRITE_COLLIDER_OFFSET_Y: 10
}

export const MobStaticConsts = {
  HEALTH: 2
}

export const UFOConsts = {
  HEALTH: 1,
  SPRITE_COLLIDER_SCALE_X: 0.9,
  SPRITE_COLLIDER_SCALE_Y: 0.3,
  SPRITE_COLLIDER_OFFSET_X: 26,
  SPRITE_COLLIDER_OFFSET_Y: -40,
  LIGHT_COLLIDER_OFFSET_X: 26,
  LIGHT_COLLIDER_SCALE_X: 0.7,
  LIGHT_COLLIDER_SCALE_Y: 0.75
}
