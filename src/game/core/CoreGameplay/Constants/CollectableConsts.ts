export const TicketPickupAnimationConsts: AnimationConfig = {
  font: '38px Nunito',
  color: '#ffffff',
  stroke: '#000000',
  strokeThickness: 3,
  origin: 0.5,
  floatUpDistanceY: 155,
  alpha: 0,
  duration: 650,
  ease: 'Quad.easeInOut',
  delay: 0,
  value: '+1',
  depth: 55,
  spawnOffsetX: 30,
  spawnOffsetY: -20
}

export const TonPickupAnimationConsts: AnimationConfig = {
  font: '38px Nunito',
  color: '#ffffff',
  stroke: '#000000',
  strokeThickness: 3,
  origin: 0.5,
  floatUpDistanceY: 155,
  alpha: 0,
  duration: 650,
  ease: 'Quad.easeInOut',
  delay: 0,
  value: '+1',
  depth: 55,
  spawnOffsetX: 30,
  spawnOffsetY: -20
}

export const CustomCoinAnimationConfig: AnimationConfig = {
  font: '38px Nunito',
  color: '#ffffff',
  stroke: '#000000',
  strokeThickness: 3,
  origin: 0.5,
  floatUpDistanceY: 155,
  alpha: 0,
  duration: 650,
  ease: 'Quad.easeInOut',
  delay: 0,
  value: '+1',
  depth: 55,
  spawnOffsetX: 30,
  spawnOffsetY: -20
}

export interface AnimationConfig {
  font: string
  color: string
  stroke: string
  strokeThickness: number
  origin: number
  floatUpDistanceY: number
  alpha: number
  duration: number
  ease: string
  delay: number
  value: string
  depth: number
  spawnOffsetX: number
  spawnOffsetY: number
}
