import { SpineGameObject, type Vector2 } from '@esotericsoftware/spine-phaser'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDataSetKeys, TicketAnimations } from '../Player/PlayerStates/States/SpineAnimations'
import { CollectableItemBase } from './CollectableItemBase'
import type { CollectableOptions } from './CollectableOptions'

export enum TicketSkins {
  OneTicket = 'Ticket_1',
  TwoTickets = 'Ticket_2',
  ThreeTickets = 'Ticket_3'
}

export class TicketCollectable extends CollectableItemBase {
  private spine: SpineGameObject | null = null
  private availableSkins: Set<string> = new Set(Object.values(TicketSkins))

  constructor(options: CollectableOptions) {
    super(options)

    this.createSpine(this.sprite.x, this.sprite.y)
    this.startIdleAnimation()
  }

  setSkin(skinName: TicketSkins) {
    if (this.availableSkins.has(skinName)) {
      this.spine?.skeleton.setSkinByName(skinName)

      this.spine?.skeleton.setSlotsToSetupPose()

      this.spine?.animationState.apply(this.spine?.skeleton)
    } else {
      console.warn(`Skin ${skinName} not found`)
    }
  }

  override reset(options: CollectableOptions): void {
    super.reset(options)
    if (this.spine) {
      this.spine?.setVisible(true)
      this.spine?.animationState.clearTracks()
      this.spine?.setPosition(
        options.position.x + TicketAnimations.X_OFFSET,
        options.position.y + TicketAnimations.Y_OFFSET
      )
    }
    this.startIdleAnimation()
  }

  override returnToPool(): void {
    if (this.spine) {
      this.spine?.setVisible(false)
      this.spine?.animationState.clearTracks()
    } else {
      console.warn('TicketCollectable.returnToPool: spine is null')
    }
    super.returnToPool()
  }

  override collect(playerPos: Vector2): void {
    super.collect(playerPos)
    this.spine?.animationState.setAnimation(2, TicketAnimations.T2_PICK_UP, false)
    this.returnToPoolWithDelay()
  }

  override syncPosition(x: number) {
    super.syncPosition(x)
    this.spine!.x = x + TicketAnimations.X_OFFSET + this.platformOffsetX
  }

  override syncSpineAndColliderPosition() {
    this.spine!.x = this.sprite.x + TicketAnimations.X_OFFSET
    this.spine!.y = this.sprite.y + TicketAnimations.Y_OFFSET
  }

  override destroyCollectable(): void {
    this.spine?.destroy()
    this.spine = null
    super.destroyCollectable()
  }

  private startIdleAnimation() {
    this.spine?.animationState.setAnimation(1, TicketAnimations.T1_RAYS, true)
    this.spine?.animationState.setAnimation(2, TicketAnimations.T2_IDLE, true)
  }

  private createSpine(x: number, y: number) {
    this.spine = this.scene!.add.spine(
      x + TicketAnimations.X_OFFSET,
      y + TicketAnimations.Y_OFFSET,
      MobDataSetKeys.TICKET_DATA,
      MobDataSetKeys.TICKET_ATLAS
    )

    const finalScale = this.sprite.scale * 0.7
    this.spine.setScale(finalScale)

    this.sprite?.setAlpha(0)
    this.spine?.setDepth(DepthOrder.Collectables)
  }
}
