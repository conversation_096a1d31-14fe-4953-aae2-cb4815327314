import { type GameScene } from '@/game/core/scenes/GameScene'

export interface JoystickConsts {
  OUTER_RADIUS: number
  INNER_RADIUS: number
  OUTER_ALPHA: number
  INNER_ALPHA: number
  OUTER_COLOR: number
  INNER_COLOR: number
  MAX_DISTANCE: number
  FADE_DURATION: number
  RETURN_DELAY: number
  RETURN_DURATION: number
}

export const JOYSTICK_CONSTS: JoystickConsts = {
  OUTER_RADIUS: 80,
  INNER_RADIUS: 25,
  OUTER_ALPHA: 0.3,
  INNER_ALPHA: 0.8,
  OUTER_COLOR: 0x666666,
  INNER_COLOR: 0xffffff,
  MAX_DISTANCE: 55,
  FADE_DURATION: 200,
  RETURN_DELAY: 500,
  RETURN_DURATION: 1000
}

export class TouchJoystick {
  private scene: GameScene
  private outerCircle: Phaser.GameObjects.Graphics | null = null
  private innerCircle: Phaser.GameObjects.Graphics | null = null
  private isActive = false
  private isTouching = false
  private startPosition = { x: 0, y: 0 }
  private currentPosition = { x: 0, y: 0 }
  private centerPosition = { x: 0, y: 0 }
  private normalizedInput = { x: 0, y: 0 }
  private returnTimer: Phaser.Time.TimerEvent | null = null
  private returnTween: Phaser.Tweens.Tween | null = null

  constructor(scene: GameScene) {
    this.scene = scene
    this.updateCenterPosition()
  }

  private updateCenterPosition(): void {
    const camera = this.scene.cameras.main
    if (!camera || !this.scene.sys.canvas) return

    const canvasWidth = this.scene.sys.canvas.width
    const canvasHeight = this.scene.sys.canvas.height
    const screenCenterX = canvasWidth / 2
    const screenCenterY = canvasHeight / 2 + 250
    const worldPoint = camera.getWorldPoint(screenCenterX, screenCenterY)

    this.centerPosition.x = worldPoint.x
    this.centerPosition.y = worldPoint.y
  }

  private clampToVisibleArea(x: number, y: number): { x: number; y: number } {
    const camera = this.scene.cameras.main
    if (!camera || !this.scene.sys.canvas) return { x, y }

    const canvasWidth = this.scene.sys.canvas.width
    const canvasHeight = this.scene.sys.canvas.height
    const margin = JOYSTICK_CONSTS.OUTER_RADIUS + 20
    const topLeft = camera.getWorldPoint(margin, margin)
    const bottomRight = camera.getWorldPoint(canvasWidth - margin, canvasHeight - margin)
    const clampedX = Math.max(topLeft.x, Math.min(bottomRight.x, x))
    const clampedY = Math.max(topLeft.y, Math.min(bottomRight.y, y))

    return { x: clampedX, y: clampedY }
  }

  public initialize(): void {
    this.updateCenterPosition()
    this.showAtPosition(this.centerPosition.x, this.centerPosition.y, false)
  }

  public updateCameraPosition(): void {
    if (!this.isActive || !this.outerCircle || !this.innerCircle) return

    this.updateCenterPosition()

    if (!this.isTouching && !this.returnTween) {
      const currentPos = { x: this.outerCircle.x, y: this.outerCircle.y }
      const clampedCurrentPos = this.clampToVisibleArea(currentPos.x, currentPos.y)
      const isOutsideVisible =
        Math.abs(currentPos.x - clampedCurrentPos.x) > 1 ||
        Math.abs(currentPos.y - clampedCurrentPos.y) > 1

      let targetX = this.centerPosition.x
      let targetY = this.centerPosition.y

      if (isOutsideVisible) {
        targetX = clampedCurrentPos.x
        targetY = clampedCurrentPos.y
      }

      const offsetX = targetX - this.outerCircle.x
      const offsetY = targetY - this.outerCircle.y

      if (Math.abs(offsetX) > 5 || Math.abs(offsetY) > 5) {
        if (this.returnTimer) {
          this.returnTimer.destroy()
          this.returnTimer = null
        }

        this.outerCircle.setPosition(targetX, targetY)
        this.innerCircle.setPosition(targetX, targetY)
        this.startPosition = { x: targetX, y: targetY }
        this.currentPosition = { x: targetX, y: targetY }
      }
    }
  }

  public show(x: number, y: number): void {
    this.showAtPosition(x, y, true)
  }

  private showAtPosition(x: number, y: number, isTouch: boolean): void {
    if (this.returnTimer) {
      this.returnTimer.destroy()
      this.returnTimer = null
    }

    if (this.returnTween) {
      this.returnTween.stop()
      this.returnTween = null
    }

    this.startPosition = { x, y }
    this.currentPosition = { x, y }
    this.isActive = true
    this.isTouching = isTouch

    if (this.outerCircle && this.innerCircle) {
      if (isTouch) {
        this.outerCircle.setPosition(x, y)
        this.innerCircle.setPosition(x, y)
      } else {
        this.scene.tweens.add({
          targets: [this.outerCircle, this.innerCircle],
          x: x,
          y: y,
          duration: JOYSTICK_CONSTS.FADE_DURATION,
          ease: 'Power2'
        })
      }
      return
    }

    this.outerCircle = this.scene.add.graphics()
    this.outerCircle.fillStyle(JOYSTICK_CONSTS.OUTER_COLOR, JOYSTICK_CONSTS.OUTER_ALPHA)
    this.outerCircle.fillCircle(0, 0, JOYSTICK_CONSTS.OUTER_RADIUS)
    this.outerCircle.setPosition(x, y)
    this.outerCircle.setDepth(1000)

    this.innerCircle = this.scene.add.graphics()
    this.innerCircle.fillStyle(JOYSTICK_CONSTS.INNER_COLOR, JOYSTICK_CONSTS.INNER_ALPHA)
    this.innerCircle.fillCircle(0, 0, JOYSTICK_CONSTS.INNER_RADIUS)
    this.innerCircle.setPosition(x, y)
    this.innerCircle.setDepth(1001)

    if (isTouch) {
      this.outerCircle.setAlpha(0)
      this.innerCircle.setAlpha(0)

      this.scene.tweens.add({
        targets: [this.outerCircle, this.innerCircle],
        alpha: [JOYSTICK_CONSTS.OUTER_ALPHA, JOYSTICK_CONSTS.INNER_ALPHA],
        duration: JOYSTICK_CONSTS.FADE_DURATION,
        ease: 'Power2'
      })
    }
  }

  public update(x: number, y: number): void {
    if (!this.isActive || !this.innerCircle || !this.outerCircle) return

    this.currentPosition = { x, y }

    const deltaX = x - this.startPosition.x
    const deltaY = y - this.startPosition.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    let knobX = x
    let knobY = y
    let baseX = this.startPosition.x
    let baseY = this.startPosition.y

    if (distance > JOYSTICK_CONSTS.MAX_DISTANCE) {
      const directionX = deltaX / distance
      const directionY = deltaY / distance

      baseX = x - directionX * JOYSTICK_CONSTS.MAX_DISTANCE
      baseY = y - directionY * JOYSTICK_CONSTS.MAX_DISTANCE
      knobX = baseX + directionX * JOYSTICK_CONSTS.MAX_DISTANCE
      knobY = baseY + directionY * JOYSTICK_CONSTS.MAX_DISTANCE

      this.startPosition = { x: baseX, y: baseY }
    }

    this.outerCircle.setPosition(baseX, baseY)
    this.innerCircle.setPosition(knobX, knobY)

    const finalDeltaX = knobX - baseX
    const finalDeltaY = knobY - baseY
    const finalDistance = Math.sqrt(finalDeltaX * finalDeltaX + finalDeltaY * finalDeltaY)

    if (finalDistance > 0) {
      this.normalizedInput.x = finalDeltaX / JOYSTICK_CONSTS.MAX_DISTANCE
      this.normalizedInput.y = finalDeltaY / JOYSTICK_CONSTS.MAX_DISTANCE
      this.normalizedInput.x = Math.max(-1, Math.min(1, this.normalizedInput.x))
      this.normalizedInput.y = Math.max(-1, Math.min(1, this.normalizedInput.y))
    } else {
      this.normalizedInput.x = 0
      this.normalizedInput.y = 0
    }
  }

  public hide(): void {
    if (!this.isActive) return

    this.isTouching = false
    this.normalizedInput = { x: 0, y: 0 }

    if (this.innerCircle && this.outerCircle) {
      this.innerCircle.setPosition(this.outerCircle.x, this.outerCircle.y)
    }

    this.returnTimer = this.scene.time.delayedCall(JOYSTICK_CONSTS.RETURN_DELAY, () => {
      this.returnToCenter()
    })
  }

  private returnToCenter(): void {
    if (!this.outerCircle || !this.innerCircle || this.isTouching) return

    this.updateCenterPosition()
    const clampedCenter = this.clampToVisibleArea(this.centerPosition.x, this.centerPosition.y)

    if (this.returnTimer) {
      this.returnTimer.destroy()
      this.returnTimer = null
    }

    this.returnTween = this.scene.tweens.add({
      targets: [this.outerCircle, this.innerCircle],
      x: clampedCenter.x,
      y: clampedCenter.y,
      duration: JOYSTICK_CONSTS.RETURN_DURATION,
      ease: 'Power2',
      onComplete: () => {
        this.startPosition = { x: clampedCenter.x, y: clampedCenter.y }
        this.currentPosition = { x: clampedCenter.x, y: clampedCenter.y }
        this.returnTween = null
      },
      onStop: () => {
        this.returnTween = null
      }
    })
  }

  public getInput(): { x: number; y: number } {
    return { ...this.normalizedInput }
  }

  public getIsActive(): boolean {
    return this.isActive
  }

  public destroy(): void {
    if (this.returnTimer) {
      this.returnTimer.destroy()
      this.returnTimer = null
    }

    if (this.returnTween) {
      this.returnTween.stop()
      this.returnTween = null
    }

    this.isActive = false
    this.isTouching = false
    this.normalizedInput = { x: 0, y: 0 }

    if (this.outerCircle && this.innerCircle) {
      this.outerCircle.destroy()
      this.innerCircle.destroy()
      this.outerCircle = null
      this.innerCircle = null
    }
  }
}
