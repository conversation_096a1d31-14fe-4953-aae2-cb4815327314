import type { BaseBooster } from '@/game/core/CoreGameplay/Boosters/BaseBooster'
import type { CollectableItemBase } from '@/game/core/CoreGameplay/Collectables/CollectableItemBase'
import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { Interactables } from '@/game/core/CoreGameplay/Interactables.ts'
import { Vector2 } from '@esotericsoftware/spine-phaser'
import { GameScene } from '../../scenes/GameScene'
import { MagnetFieldConsts } from '../Constants/MagnetConsts'
import { BasePlatform } from '../Platforms/BasePlatform'

export class PlayerCollisionDetector {
  private scene: GameScene
  private readonly interactables: Interactables
  private readonly onPlatformInteraction: (platform: BasePlatform) => void
  private readonly onBoosterInteraction: (
    booster: BaseBooster,
    collisionPos: Vector2 | undefined
  ) => void
  private readonly onEnemyCollision: (enemy: BaseEnemy) => void
  private readonly onCollectableInteraction: (
    collectable: CollectableItemBase,
    collisionPos: Vector2 | undefined
  ) => void
  private player!: Phaser.Physics.Arcade.Sprite
  private playerEntity!: number
  private attractedCollectables: Set<Phaser.Physics.Arcade.Sprite> = new Set()

  constructor(
    scene: GameScene,
    interactables: Interactables,
    onPlatformInteraction: (platform: BasePlatform) => void,
    onBoosterInteraction: (booster: BaseBooster, collisionPos: Vector2 | undefined) => void,
    onEnemyCollision: (enemy: BaseEnemy) => void,
    onCollectableCollision: (
      collectable: CollectableItemBase,
      collisionPos: Vector2 | undefined
    ) => void
  ) {
    this.scene = scene
    this.interactables = interactables
    this.onPlatformInteraction = onPlatformInteraction
    this.onBoosterInteraction = onBoosterInteraction
    this.onEnemyCollision = onEnemyCollision
    this.onCollectableInteraction = onCollectableCollision
  }

  public setupCollision(
    player: Phaser.Physics.Arcade.Sprite,
    magnet: Phaser.Physics.Arcade.Body | undefined,
    entity: number
  ): void {
    this.player = player
    this.playerEntity = entity
    const collisionConfig = [
      {
        group: this.interactables.PlatformsGroup,
        handler: this.handlePlatformCollision.bind(this)
      },
      { group: this.interactables.BoostersGroup, handler: this.handleBoostersCollision.bind(this) },
      { group: this.interactables.MobsGroup, handler: this.handleEnemyCollision.bind(this) },
      {
        group: this.interactables.CollectablesGroup,
        handler: this.handleCollectablesCollision.bind(this)
      }
    ]

    collisionConfig.forEach(config => {
      this.scene.physics.add.overlap(
        player,
        config.group,
        config.handler as Phaser.Types.Physics.Arcade.ArcadePhysicsCallback,
        undefined,
        this
      )
    })

    const magnetCollisionConfig = {
      group: this.interactables.CollectablesGroup,
      handler: this.handleMagnetCollectableCollision.bind(this)
    }

    if (!magnet) return

    this.scene.physics.add.overlap(
      magnet,
      magnetCollisionConfig.group,
      magnetCollisionConfig.handler as Phaser.Types.Physics.Arcade.ArcadePhysicsCallback,
      undefined,
      this
    )
  }

  private handleMagnetCollectableCollision(
    magnet: Phaser.GameObjects.GameObject,
    collectable: Phaser.GameObjects.GameObject
  ) {
    const collectableSprite = collectable as Phaser.Physics.Arcade.Sprite
    const baseCollectable = this.findBaseCollectableBySprite(collectableSprite)

    if (
      !baseCollectable ||
      collectableSprite.getData('isCollected') ||
      collectableSprite.getData('isBeingAttracted')
    ) {
      return
    }

    collectableSprite.setData('isBeingAttracted', true)
    this.attractedCollectables.add(collectableSprite)
  }

  public processAttractedCollectables(delta: number) {
    const playerPosition = new Phaser.Math.Vector2(this.player.x, this.player.y)

    this.attractedCollectables.forEach(collectable => {
      if (!collectable.active) {
        this.attractedCollectables.delete(collectable)
        return
      }

      const collectablePos = new Phaser.Math.Vector2(collectable.x, collectable.y)
      const direction = playerPosition.clone().subtract(collectablePos)
      const distance = direction.length()

      if (distance <= MagnetFieldConsts.COLLECTION_DISTANCE) {
        this.collectItem(collectable)
        return
      }

      direction.normalize()

      const baseSpeed = MagnetFieldConsts.ATTRACTION_SPEED

      const playerVelocityY = (this.player.body as Phaser.Physics.Arcade.Body)?.velocity?.y || 0
      const adjustedSpeed = baseSpeed + Math.abs(playerVelocityY) * 0.85
      const finalSpeed = adjustedSpeed

      const moveStep = direction.scale(finalSpeed * (delta / 1000))

      collectable.x += moveStep.x
      collectable.y += moveStep.y

      const baseItem = this.findBaseCollectableBySprite(collectable)
      baseItem?.syncSpineAndColliderPosition()
    })
  }

  private collectItem(collectable: Phaser.Physics.Arcade.Sprite) {
    if (collectable.getData('isCollected')) return

    const baseCollectable = this.findBaseCollectableBySprite(collectable)
    if (!baseCollectable) return

    const playerPos = new Phaser.Math.Vector2(this.player.x, this.player.y)

    this.onCollectableInteraction(baseCollectable, playerPos)
    baseCollectable.returnToPool()

    collectable.setData('isCollected', true)
    this.attractedCollectables.delete(collectable)
  }

  private handleCollision<T>(
    player: Phaser.GameObjects.GameObject,
    interactable: Phaser.GameObjects.GameObject,
    findBaseFunction: (sprite: Phaser.Physics.Arcade.Sprite) => T | null,
    interactionCallback: (base: T, position?: Vector2) => void,
    separate?: boolean
  ) {
    const interactableSprite = interactable as Phaser.Physics.Arcade.Sprite
    const baseInteractable = findBaseFunction(interactableSprite)
    const playerBody = player.body as Phaser.Physics.Arcade.Body

    if (separate) {
      this.scene.physics.world.separate(
        playerBody,
        interactableSprite.body as Phaser.Physics.Arcade.Body
      )
    }

    if (baseInteractable) {
      interactionCallback(baseInteractable, playerBody.position)
    }
  }

  private handlePlatformCollision(
    player: Phaser.GameObjects.GameObject,
    platform: Phaser.GameObjects.GameObject
  ) {
    this.handleCollision(
      player,
      platform,
      this.findBasePlatformBySprite.bind(this),
      (basePlatform: BasePlatform) => this.onPlatformInteraction(basePlatform)
    )
  }

  private handleBoostersCollision(
    player: Phaser.GameObjects.GameObject,
    booster: Phaser.GameObjects.GameObject
  ) {
    this.handleCollision(
      player,
      booster,
      this.findBaseBoosterBySprite.bind(this),
      (baseBooster: BaseBooster, collisionPos: Vector2 | undefined) =>
        this.triggerBoosterEffect(baseBooster, collisionPos),
      true
    )
  }

  private handleEnemyCollision(
    player: Phaser.GameObjects.GameObject,
    enemy: Phaser.GameObjects.GameObject
  ) {
    this.handleCollision(
      player,
      enemy,
      this.findBaseEnemyBySprite.bind(this),
      (baseEnemy: BaseEnemy) => this.onEnemyCollision(baseEnemy)
    )
  }

  private handleCollectablesCollision(
    player: Phaser.GameObjects.GameObject,
    collectable: Phaser.GameObjects.GameObject
  ) {
    const collectableSprite = collectable as Phaser.Physics.Arcade.Sprite
    if (collectableSprite.getData('isBeingAttracted')) {
      return
    }

    this.handleCollision(
      player,
      collectable,
      this.findBaseCollectableBySprite.bind(this),
      (baseCollectable: CollectableItemBase, collisionPos: Vector2 | undefined) =>
        this.onCollectableInteraction(baseCollectable, collisionPos)
    )
  }

  private triggerBoosterEffect(booster: BaseBooster, collisionPos: Vector2 | undefined) {
    this.onBoosterInteraction(booster, collisionPos)
  }

  private findBaseBySprite<T>(sprite: Phaser.Physics.Arcade.Sprite, items: T[]): T | null {
    for (const item of items) {
      if ((item as any).getSprite() === sprite) {
        return item
      }
    }
    return null
  }

  private findBasePlatformBySprite(sprite: Phaser.Physics.Arcade.Sprite): BasePlatform | null {
    return this.findBaseBySprite<BasePlatform>(sprite, this.interactables.Platforms)
  }

  private findBaseBoosterBySprite(sprite: Phaser.Physics.Arcade.Sprite): BaseBooster | null {
    return this.findBaseBySprite<BaseBooster>(sprite, this.interactables.Boosters)
  }

  private findBaseCollectableBySprite(
    sprite: Phaser.Physics.Arcade.Sprite
  ): CollectableItemBase | null {
    const baseCollectable = this.findBaseBySprite<CollectableItemBase>(
      sprite,
      this.interactables.Collectables
    )

    return baseCollectable
  }

  private findBaseEnemyBySprite(sprite: Phaser.Physics.Arcade.Sprite): BaseEnemy | null {
    return this.findBaseBySprite<BaseEnemy>(sprite, this.interactables.Mobs)
  }
}
