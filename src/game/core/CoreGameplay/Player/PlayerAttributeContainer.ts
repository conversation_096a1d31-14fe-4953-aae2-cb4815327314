import { Types, addEntity, defineComponent, type IWorld } from 'bitecs'

export enum Attribute {
  PlayerCreated = 'playerCreated',
  CanShoot = 'canShoot',
  StopBodyAndSpineSync = 'stopBodyAndSpineSync',
  HasLoggedJumpHeight = 'hasLoggedJumpHeight',
  PlayerCollisionsEnabled = 'playerCollisionsEnabled',
  IsDead = 'isDead',
  IsPlayerPassedFTUE = 'isPlayerPassedFTUE',
  IsBoosterActive = 'isBoosterActive',
  IsImmortal = 'isImmortal',
  IsJumping = 'isJumping',
  IsFalling = 'isFalling',
  IsBigJumpBoosterActive = 'isBigJumpBoosterActive',
  IsOnPropeller = 'isOnPropeller',
  IsShieldActive = 'isShieldActive',
  isBubbleActive = 'isBubbleActive'
}

type PlayerAttributesType = {
  canShoot: Uint8Array
  isDead: Uint8Array
  playerCreated: Uint8Array
  isBoosterActive: Uint8Array
  isImmortal: Uint8Array
  isJumping: Uint8Array
  isFalling: Uint8Array
  stopBodyAndSpineSync: Uint8Array
  hasLoggedJumpHeight: Uint8Array
  playerCollisionsEnabled: Uint8Array
  isBigJumpBoosterActive: Uint8Array
  isPlayerPassedFTUE: Uint8Array
  isOnPropeller: Uint8Array
  isShieldActive: Uint8Array
  isBubbleActive: Uint8Array
}

export const PlayerAttributes = defineComponent<PlayerAttributesType>({
  canShoot: Types.ui8,
  isDead: Types.ui8,
  playerCreated: Types.ui8,
  isBoosterActive: Types.ui8,
  isImmortal: Types.ui8,
  isJumping: Types.ui8,
  isFalling: Types.ui8,
  stopBodyAndSpineSync: Types.ui8,
  hasLoggedJumpHeight: Types.ui8,
  playerCollisionsEnabled: Types.ui8,
  isBigJumpBoosterActive: Types.ui8,
  isPlayerPassedFTUE: Types.ui8,
  isOnPropeller: Types.ui8,
  isShieldActive: Types.ui8,
  isBubbleActive: Types.ui8
})

export function getPlayerAttribute(attribute: Attribute, entity: number): boolean {
  return !!PlayerAttributes[attribute][entity]
}

export function setPlayerAttribute(attribute: Attribute, entity: number, value: boolean): void {
  PlayerAttributes[attribute][entity] = value ? 1 : 0
}

export function togglePlayerAttribute(attribute: Attribute, entity: number): void {
  const currentValue = getPlayerAttribute(attribute, entity)
  setPlayerAttribute(attribute, entity, !currentValue)
}

export function createPlayerEntity(world: IWorld): number {
  const playerEntity = addEntity(world)
  setPlayerAttribute(Attribute.CanShoot, playerEntity, false)
  setPlayerAttribute(Attribute.IsDead, playerEntity, false)
  setPlayerAttribute(Attribute.IsJumping, playerEntity, false)
  setPlayerAttribute(Attribute.IsFalling, playerEntity, false)
  setPlayerAttribute(Attribute.IsImmortal, playerEntity, false)
  setPlayerAttribute(Attribute.IsOnPropeller, playerEntity, false)
  setPlayerAttribute(Attribute.PlayerCreated, playerEntity, false)
  setPlayerAttribute(Attribute.IsBoosterActive, playerEntity, false)
  setPlayerAttribute(Attribute.IsPlayerPassedFTUE, playerEntity, false)
  setPlayerAttribute(Attribute.HasLoggedJumpHeight, playerEntity, false)
  setPlayerAttribute(Attribute.StopBodyAndSpineSync, playerEntity, false)
  setPlayerAttribute(Attribute.IsBigJumpBoosterActive, playerEntity, false)
  setPlayerAttribute(Attribute.PlayerCollisionsEnabled, playerEntity, true)
  setPlayerAttribute(Attribute.IsShieldActive, playerEntity, false)
  setPlayerAttribute(Attribute.isBubbleActive, playerEntity, false)
  return playerEntity
}
