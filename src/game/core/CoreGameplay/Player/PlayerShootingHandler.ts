import { BulletConsts } from '@/game/core/CoreGameplay/Constants/BulletConsts.ts'
import { InputConsts } from '@/game/core/CoreGameplay/Constants/PlayerConsts'
import type { Interactables } from '@/game/core/CoreGameplay/Interactables.ts'
import { UI_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import { SpineGameObject } from '@esotericsoftware/spine-phaser'
import { addComponent, addEntity, removeEntity } from 'bitecs'
import { EntityType } from '../../MapGen/MapGenerator'
import { Active, Position, Rotation, Velocity } from '../../ecs/Components/EntityComponents'
import { getGameWorld } from '../../ecs/GameWorld'
import { bulletQuery } from '../../ecs/Systems/BulletSystem'
import { type GameScene } from '../../scenes/GameScene'
import { DepthOrder } from '../Constants/DephOrdering'
import type { BaseEnemy } from '../Enemies/BaseEnemy'
import { Attribute, getPlayerAttribute, setPlayerAttribute } from './PlayerAttributeContainer'
import type { PlayerInputHandler } from './PlayerInputHandler'
import { MobDataSetKeys } from './PlayerStates/States/SpineAnimations'

export class PlayerShootingHandler {
  private scene: GameScene
  private player: Phaser.Physics.Arcade.Sprite
  private readonly interactables: Interactables
  private bulletPool: number[] = []
  private bulletEntityMap: Map<SpineGameObject, number> = new Map()
  private uiEventBus: EventBus
  private bulletsGroup: Phaser.Physics.Arcade.Group
  public bullets: Map<number, SpineGameObject> = new Map()
  private playerEntity: number
  private isGyroOn: boolean
  private lastTapTime = 0
  private inputHandler: PlayerInputHandler | null = null

  public readonly tapListener: (e: PointerEvent | TouchEvent) => void
  private static readonly BULLET_POSITION_OFFSET: number = 1000
  private static readonly MAX_BULLET_POOL_SIZE: number = 20
  private static readonly BULLET_SIZE: number = 50

  constructor(
    scene: GameScene,
    player: Phaser.Physics.Arcade.Sprite,
    interactables: Interactables,
    entity: number,
    uiEventBus: EventBus
  ) {
    this.scene = scene
    this.player = player
    this.interactables = interactables
    this.bulletsGroup = this.scene.physics.add.group()
    this.playerEntity = entity
    this.uiEventBus = uiEventBus
    this.tapListener = this.onTap.bind(this)
    this.addListener()
    this.isGyroOn = this.scene.IsGyroscopeControlMethod
    this.scene.input
      .keyboard!.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE)
      .on('down', this.shoot, this)
    this.checkForCollision()
    this.uiEventBus.on(UI_EVENTS.PAUSE, this.handleGamePause, this)
    this.uiEventBus.on(UI_EVENTS.UNPAUSE, this.handleGameResume, this)
  }

  public setInputHandler(inputHandler: PlayerInputHandler): void {
    this.inputHandler = inputHandler
  }

  private onTap(e: PointerEvent | TouchEvent): void {
    if (this.isGyroOn) {
      this.shoot(e)
      return
    }

    // If joystick is active and we have multiple touches, this might be a shooting tap
    if (
      this.inputHandler &&
      this.inputHandler.isJoystickActive() &&
      this.inputHandler.getActiveTouchCount() > 1
    ) {
      this.shoot(e)
      return
    }

    const now = performance.now()
    if (now - this.lastTapTime < InputConsts.DOUBLE_TAP_THRESHOLD) {
      this.shoot(e)
      this.lastTapTime = 0
    } else {
      this.lastTapTime = now
    }
  }

  update(): void {
    this.checkIfBulletOutOfBounds()
  }

  public addListener(): void {
    this.scene.sys.canvas.addEventListener('pointerdown', this.tapListener)
  }

  public removeListener(): void {
    this.scene.sys.canvas.removeEventListener('pointerdown', this.tapListener)
  }

  private handleGamePause(): void {
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, false)
  }

  private handleGameResume(): void {
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, true)
  }

  public static readonly SHOOT_CONE_HALF_ANGLE = Phaser.Math.DegToRad(40)

  public shoot(e?: PointerEvent | TouchEvent): void {
    if (
      !this.player.active ||
      !getPlayerAttribute(Attribute.CanShoot, this.playerEntity) ||
      getPlayerAttribute(Attribute.IsDead, this.playerEntity)
    )
      return

    let pointerX: number, pointerY: number
    const canvas = this.scene.sys.canvas
    const cam = this.scene.cameras.main

    if (e instanceof TouchEvent && e.touches.length > 0) {
      e.preventDefault()
      const touch = e.touches[0]
      const rect = canvas.getBoundingClientRect()
      const wp = cam.getWorldPoint(touch.clientX - rect.left, touch.clientY - rect.top)
      pointerX = wp.x
      pointerY = wp.y
    } else if (e instanceof PointerEvent) {
      e.preventDefault()
      const rect = canvas.getBoundingClientRect()
      const wp = cam.getWorldPoint(e.clientX - rect.left, e.clientY - rect.top)
      pointerX = wp.x
      pointerY = wp.y
    } else {
      pointerX = this.scene.input.activePointer.worldX
      pointerY = this.scene.input.activePointer.worldY
    }

    const px = this.player.x
    const py = this.player.y
    const rawX = pointerX - px
    let rawY = pointerY - py
    if (rawY > 0) rawY = -rawY
    const mag = Math.hypot(rawX, rawY)
    if (mag === 0) return

    let nx = rawX / mag
    let ny = rawY / mag

    const half = PlayerShootingHandler.SHOOT_CONE_HALF_ANGLE
    const cosHalf = Math.cos(half)
    const upAngle = -Math.PI / 2

    if (-ny < cosHalf) {
      if (nx < 0) {
        nx = Math.cos(upAngle - half)
        ny = Math.sin(upAngle - half)
      } else {
        nx = Math.cos(upAngle + half)
        ny = Math.sin(upAngle + half)
      }
    }

    const vx = nx * BulletConsts.SPEED
    const vy = ny * BulletConsts.SPEED

    let bulletEntity: number
    let bulletSpine: SpineGameObject

    if (this.bulletPool.length > 0) {
      bulletEntity = this.bulletPool.pop()!
      bulletSpine = this.bullets.get(bulletEntity)!
      ;(bulletSpine.body as Phaser.Physics.Arcade.Body).enable = true
      bulletSpine.setActive(true).setVisible(true).setPosition(px, py)
      bulletSpine.animationState.clearTracks()
      bulletSpine.animationState.setAnimation(0, 'appear', false)
    } else {
      bulletEntity = addEntity(getGameWorld())
      addComponent(getGameWorld(), Position, bulletEntity)
      addComponent(getGameWorld(), Velocity, bulletEntity)
      addComponent(getGameWorld(), Active, bulletEntity)
      addComponent(getGameWorld(), Rotation, bulletEntity)
      bulletSpine = this.scene.add.spine(
        px,
        py,
        MobDataSetKeys.STAR_DATA,
        MobDataSetKeys.STAR_ATLAS
      ) as SpineGameObject
      bulletSpine.setScale(BulletConsts.SCALE).setDepth(DepthOrder.Player - 1)
      bulletSpine.on('ready', () => bulletSpine.animationState.setAnimation(0, 'appear', false))
      this.scene.physics.add.existing(bulletSpine)
      const body = bulletSpine.body as Phaser.Physics.Arcade.Body
      body.setAllowGravity(false)
      body.setSize(PlayerShootingHandler.BULLET_SIZE, PlayerShootingHandler.BULLET_SIZE)
      this.bullets.set(bulletEntity, bulletSpine)
      this.bulletEntityMap.set(bulletSpine, bulletEntity)
      this.bulletsGroup.add(bulletSpine)
    }

    Position.x[bulletEntity] = px
    Position.y[bulletEntity] = py
    Velocity.x[bulletEntity] = vx
    Velocity.y[bulletEntity] = vy
    Active.active[bulletEntity] = 1
    const angle = Math.atan2(vy, vx)
    Rotation.angle[bulletEntity] = angle
    bulletSpine.rotation = angle + Math.PI / 2
  }

  lookAt(bulletEntity: number, bulletVelocityX: number, bulletVelocityY: number) {
    const angle = Math.atan2(bulletVelocityY, bulletVelocityX)
    Rotation.angle[bulletEntity] = angle
    const bulletSprite = this.bullets.get(bulletEntity)
    if (bulletSprite) {
      bulletSprite.rotation = angle + Math.PI / 2
    }
  }

  private checkIfBulletOutOfBounds(): void {
    const bounds = this.scene.cameras.main.worldView
    const entities = bulletQuery(getGameWorld())
    for (let i = 0; i < entities.length; i++) {
      const bulletEntity = entities[i]
      if (
        Active.active[bulletEntity] &&
        (Position.y[bulletEntity] < bounds.top ||
          Position.y[bulletEntity] > bounds.bottom ||
          Position.x[bulletEntity] < bounds.left ||
          Position.x[bulletEntity] > bounds.right)
      ) {
        this.deactivateBullet(bulletEntity)
      } else if (Active.active[bulletEntity]) {
        const bullet = this.bullets.get(bulletEntity)
        if (bullet) {
          bullet.setPosition(Position.x[bulletEntity], Position.y[bulletEntity])
          bullet.rotation = Rotation.angle[bulletEntity] + Math.PI / 2
        }
      }
    }
  }

  private deactivateBullet(bulletEntity: number): void {
    Active.active[bulletEntity] = 0
    Velocity.x[bulletEntity] = 0
    Velocity.y[bulletEntity] = 0
    Position.x[bulletEntity] = PlayerShootingHandler.BULLET_POSITION_OFFSET
    Position.y[bulletEntity] = PlayerShootingHandler.BULLET_POSITION_OFFSET
    Rotation.angle[bulletEntity] = 0
    const bullet = this.bullets.get(bulletEntity)
    if (bullet) {
      const bulletBody = bullet.body as Phaser.Physics.Arcade.Body
      bulletBody.enable = false
      bullet.setVisible(false)
      bullet.setActive(false)
      if (this.bulletPool.length < PlayerShootingHandler.MAX_BULLET_POOL_SIZE) {
        this.bulletPool.push(bulletEntity)
      }
    }
  }

  public shootAt(targetX: number, targetY: number, overrideYConstraint: boolean = false): void {
    if (
      !this.player.active ||
      !getPlayerAttribute(Attribute.CanShoot, this.playerEntity) ||
      getPlayerAttribute(Attribute.IsDead, this.playerEntity)
    ) {
      return
    }
    const playerX = this.player.x
    const playerY = this.player.y
    const screenHeight = this.scene.cameras.main.height
    const finalTargetY = overrideYConstraint
      ? targetY
      : Math.min(targetY, playerY - screenHeight * 0.8)
    const directionX = targetX - playerX
    const directionY = finalTargetY - playerY
    const magnitude = Math.sqrt(directionX * directionX + directionY * directionY)
    const bulletVelocityX = (directionX / magnitude) * BulletConsts.SPEED
    const bulletVelocityY = (directionY / magnitude) * BulletConsts.SPEED
    let bulletEntity: number
    let bulletSpine: SpineGameObject
    if (this.bulletPool.length > 0) {
      bulletEntity = this.bulletPool.pop()!
      bulletSpine = this.bullets.get(bulletEntity)!
      const bulletBody = bulletSpine.body as Phaser.Physics.Arcade.Body
      bulletBody.enable = true
      bulletSpine.setActive(true)
      bulletSpine.setVisible(true)
      bulletSpine.setPosition(playerX, playerY)
      bulletSpine.animationState.clearTracks()
      bulletSpine.animationState.setAnimation(0, 'appear', false)
    } else {
      bulletEntity = addEntity(getGameWorld())
      addComponent(getGameWorld(), Position, bulletEntity)
      addComponent(getGameWorld(), Velocity, bulletEntity)
      addComponent(getGameWorld(), Active, bulletEntity)
      addComponent(getGameWorld(), Rotation, bulletEntity)
      bulletSpine = this.scene.add.spine(
        playerX,
        playerY,
        MobDataSetKeys.STAR_DATA,
        MobDataSetKeys.STAR_ATLAS
      ) as SpineGameObject
      bulletSpine.setScale(BulletConsts.SCALE)
      bulletSpine.setDepth(DepthOrder.Player - 1)
      bulletSpine.on('ready', () => {
        bulletSpine.animationState.setAnimation(0, 'appear', false)
      })
      this.scene.physics.add.existing(bulletSpine)
      const bulletBody = bulletSpine.body as Phaser.Physics.Arcade.Body
      bulletBody.setAllowGravity(false)
      bulletBody.setSize(PlayerShootingHandler.BULLET_SIZE, PlayerShootingHandler.BULLET_SIZE)
      this.bullets.set(bulletEntity, bulletSpine)
      this.bulletEntityMap.set(bulletSpine, bulletEntity)
      this.bulletsGroup.add(bulletSpine)
    }
    Position.x[bulletEntity] = playerX
    Position.y[bulletEntity] = playerY
    Velocity.x[bulletEntity] = bulletVelocityX
    Velocity.y[bulletEntity] = bulletVelocityY
    Active.active[bulletEntity] = 1
    this.lookAt(bulletEntity, bulletVelocityX, bulletVelocityY)
  }

  public clearBulletData(): void {
    const world = getGameWorld()
    this.bullets.forEach((bulletSpine, bulletEntity) => {
      bulletSpine.destroy()
      removeEntity(world, bulletEntity)
    })
    this.bullets.clear()
    this.bulletPool = []
    this.bulletEntityMap.clear()
  }

  checkForCollision(): void {
    try {
      if (this.interactables.MobsGroup.children == undefined) return
      this.scene.physics.world.collide(
        this.bulletsGroup,
        this.interactables.MobsGroup,
        this.handleBulletEnemyCollision as Phaser.Types.Physics.Arcade.ArcadePhysicsCallback,
        undefined,
        this
      )
    } catch (error) {
      console.error(error)
    }
  }

  private handleBulletEnemyCollision(
    bullet: Phaser.GameObjects.GameObject,
    enemy: Phaser.GameObjects.GameObject
  ): void {
    const bulletEntity = this.bulletEntityMap.get(bullet as SpineGameObject) || 0
    this.deactivateBullet(bulletEntity)
    const enemySprite = enemy as Phaser.Physics.Arcade.Sprite
    const baseEnemy = this.findBaseEnemyBySprite(enemySprite)
    if (baseEnemy) {
      if (baseEnemy.entityType() == EntityType.MobBlackHole) {
        return
      }
      const bulletPos = new Phaser.Math.Vector2(Position.x[bulletEntity], Position.y[bulletEntity])
      const enemyDefeat = baseEnemy.triggerHit(bulletPos)
      if (enemyDefeat) {
        this.scene.events.emit('enemyDefeat')
      }
    }
  }

  private findBaseEnemyBySprite(sprite: Phaser.Physics.Arcade.Sprite): BaseEnemy | null {
    for (const enemy of this.interactables.Mobs) {
      if (enemy.getSprite() === sprite) {
        return enemy
      }
    }
    return null
  }
}

export let bulletHandler: PlayerShootingHandler

export function initializeBulletHandler(
  scene: GameScene,
  player: Phaser.Physics.Arcade.Sprite,
  interactables: Interactables,
  entity: number,
  uiEventBus: EventBus
) {
  bulletHandler = new PlayerShootingHandler(scene, player, interactables, entity, uiEventBus)
}
