import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { BlackHole } from '@/game/core/CoreGameplay/Enemies/BlackHole'
import { Logger, logger } from '@/shared/Logger'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import { HapticsService, hapticsService } from '@/shared/haptics/hapticsService'
import { clearTween } from '../../HelperFunctions'
import { Enemy } from '../../ecs/Components/EntityComponents'
import { GameScene } from '../../scenes/GameScene'
import { PlayerConsts } from '../Constants/PlayerConsts'
import { UFO } from '../Enemies/UFO'
import {
  Attribute,
  getPlayerAttribute,
  setPlayerAttribute,
  togglePlayerAttribute
} from './PlayerAttributeContainer'
import { PlayerController } from './PlayerController'
import { PlayerStates } from './PlayerStates/States/PlayerStates'
import type { BubbleManager } from './Starters/BubbleManager'

export class PlayerEnemyInteractions {
  protected haptics: HapticsService = hapticsService
  private logger: Logger = logger
  private scene: GameScene
  private playerController: PlayerController
  private bubbleManager: BubbleManager | null = null
  private playerEntity: number
  private playerTween: Phaser.Tweens.Tween | null = null
  private justConsumedBubble: boolean = false

  constructor(scene: GameScene, playerController: PlayerController, bubbleManager: BubbleManager) {
    this.scene = scene
    this.playerController = playerController
    this.bubbleManager = bubbleManager
    this.playerEntity = playerController.getPlayerEntity()
    this.subscribeEvents()
  }

  public unsubscribeEvents(): void {
    this.scene.events.off(GAME_EVENTS.PLAYER_TOUCHED_LIGHT, this.handlePlayerTouchedLight, this)
    this.scene.events.off(GAME_EVENTS.TRAP_TOUCHED, this.handleTrapCollision, this)
  }

  public subscribeEvents(): void {
    this.scene.events.on(GAME_EVENTS.PLAYER_TOUCHED_LIGHT, this.handlePlayerTouchedLight, this)
    this.scene.events.on(GAME_EVENTS.TRAP_TOUCHED, this.handleTrapCollision, this)
  }

  public onEnemyCollision(enemy: BaseEnemy): void {
    const player = this.playerController.getPlayer()
    const playerBody = player.body as Phaser.Physics.Arcade.Body

    const touchingSides = playerBody.touching.right || playerBody.touching.left
    const defaultHitboxY = playerBody.position.y + playerBody.height
    const enemyBounds = enemy.getSprite().getBounds()
    const verticalOffset = enemyBounds.y - defaultHitboxY > 20 ? enemyBounds.y - defaultHitboxY : 0
    const stompHitbox = new Phaser.Geom.Rectangle(
      playerBody.position.x - playerBody.width * 0.25,
      defaultHitboxY + verticalOffset,
      playerBody.width * 1.5,
      20
    )
    const canStomp = this.isStompable(playerBody, enemy, touchingSides, stompHitbox)

    if (canStomp) {
      this.justConsumedBubble = false
      this.handleEnemyStomp(enemy, playerBody)
      return
    }

    if (this.bubbleManager && this.bubbleManager.isActive()) {
      if (this.bubbleManager?.onContact(enemy)) {
        this.justConsumedBubble = true
        this.scene.time.delayedCall(1000, () => {
          this.justConsumedBubble = false
        })
        return
      }
    }

    if (this.justConsumedBubble) return

    if (this.isImmortal()) {
      enemy instanceof BlackHole ? null : this.handleImmortalState(enemy, playerBody)
      return
    }

    if (enemy instanceof BlackHole) {
      this.handleBlackHoleCollision(enemy, playerBody)
      return
    }

    if (getPlayerAttribute(Attribute.IsShieldActive, this.playerEntity)) {
      if (canStomp) {
        this.handleEnemyStomp(enemy, playerBody)
      } else {
        this.handleImmortalState(enemy, playerBody)
      }
      return
    }

    if (canStomp) {
      this.handleEnemyStomp(enemy, playerBody)
    } else if (!touchingSides) {
      this.handlePlayerDeath(enemy, playerBody)
    }
  }

  private isImmortal(): boolean {
    return getPlayerAttribute(Attribute.IsImmortal, this.playerEntity)
  }

  private handleImmortalState(enemy: BaseEnemy, playerBody: Phaser.Physics.Arcade.Body): void {
    if (enemy instanceof BlackHole) {
      if (__DEV__)
        this.logger.log('PlayerController', 'Player is immortal but cannot destroy a BlackHole.')
      return
    }
    if (__DEV__) this.logger.log('PlayerController', 'Player is immortal, enemy destroyed!')
    this.scene.events.emit('enemyDefeat')
    enemy.onCollideWithPlayer(
      playerBody.position,
      getPlayerAttribute(Attribute.IsOnPropeller, this.playerEntity)
    )
  }

  private handleBlackHoleCollision(enemy: BaseEnemy, playerBody: Phaser.Physics.Arcade.Body): void {
    if (__DEV__) this.logger.log('PlayerController', 'Player collided with a BlackHole!')
    togglePlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity)
    this.playerController.enablePlayerCollision(false)
    this.playerController.showShield(false)
    this.playerController.showBubble(false)
    this.playerController.showBootsEffect(false)
    const blackhole = enemy as BlackHole
    blackhole.disableCollider()
    blackhole.onCollideWithPlayer(playerBody.position, false)
    this.playerController?.turnOnMagnet(false)
    this.playBlackHoleSequence(blackhole)
  }

  private isStompable(
    playerBody: Phaser.Physics.Arcade.Body,
    enemy: BaseEnemy,
    touchingSides: boolean,
    stompHitbox: Phaser.Geom.Rectangle
  ): boolean {
    const isTouchingDown = playerBody.touching.down || playerBody.velocity.y > -50
    const canKill = Enemy.canBeKilled[enemy.entity] === 1
    const inStompHitbox = Phaser.Geom.Intersects.RectangleToRectangle(
      stompHitbox,
      enemy.getSprite().getBounds()
    )
    if (__DEV__) {
      console.log('[isStompable] =>', {
        isTouchingDown,
        canKill,
        notTouchingSides: !touchingSides,
        inStompHitbox
      })
    }
    return isTouchingDown && canKill && !touchingSides && inStompHitbox
  }

  private handleEnemyStomp(enemy: BaseEnemy, playerBody: Phaser.Physics.Arcade.Body): void {
    if (__DEV__) this.logger.log('PlayerController', 'Player stomped on the enemy!')
    enemy.onCollideWithPlayer(playerBody.position, true)
    enemy.disableCollider()
    this.scene.events.emit('enemyDefeat')
    this.playerController.jump(PlayerConsts.JUMP_VELOCITY * 1.5)
    this.haptics.triggerImpactHapticEvent('heavy')
    this.playerController.changeAnimationState(PlayerStates.Jump)
  }

  private handlePlayerDeath(enemy: BaseEnemy, playerBody: Phaser.Physics.Arcade.Body): void {
    if (this.isImmortal()) {
      if (__DEV__)
        this.logger.log(
          'PlayerController',
          'Player is immortal and cannot die from this collision.'
        )
      return
    }
    if (
      getPlayerAttribute(Attribute.IsShieldActive, this.playerEntity) &&
      !(enemy instanceof BlackHole) &&
      !(enemy instanceof UFO)
    ) {
      return
    }
    if (__DEV__) this.logger.log('PlayerController', 'Player collided with an enemy!')
    this.haptics.triggerNotificationHapticEvent('error')
    this.playerController.enablePlayerCollision(false)
    this.playerController.showBootsEffect(false)
    this.playerController.showShield(false)
    this.playerController.showBubble(false)
    this.playerController.turnOnMagnet(false)
    setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
    this.playerController.getPlayerStateMachine().updateStateBasedOnInput(PlayerStates.Death)
    enemy.onCollideWithPlayer(playerBody.position, false)
    this.animatePlayersDeath()
  }

  private handleTrapCollision(): void {
    this.haptics.triggerNotificationHapticEvent('error')
    this.playerController.enablePlayerCollision(false)
    setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
    this.playerController.getPlayerStateMachine().updateStateBasedOnInput(PlayerStates.Death)
    this.animatePlayersDeath()
  }

  public animatePlayersDeath(): void {
    const playerBody = this.playerController.getPlayer().body as Phaser.Physics.Arcade.Body
    const playerSpine = this.playerController.getPlayerSpine()
    const camera = this.scene.cameras.main
    playerBody.setVelocityY(-800)
    if (this.playerTween) {
      this.playerTween.stop()
      this.playerTween = null
    }
    this.playerTween = this.scene.tweens.add({
      targets: playerBody,
      y: {
        getStart: () => playerBody.y,
        getEnd: () => playerBody.y - 1500
      },
      ease: 'Bounce.easeOut',
      duration: 2000,
      onUpdate: (tween, target: any) => {
        if (target.x < 30) {
          target.x = 0
        } else if (target.x > camera.width - 30) {
          target.x = camera.width
        }
        playerSpine.angle = 360 * tween.progress
      },
      onComplete: () => {
        playerBody.setVelocityX(0)
        playerSpine.angle = 0
        this.playerTween = null
      }
    })
  }

  public stopPlayerTween(): void {
    if (this.playerTween) {
      clearTween(this.playerTween)
      this.playerTween = null
    }
  }

  private playBlackHoleSequence(blackHole: BlackHole): void {
    const playerBody = this.playerController.getPlayer()
    const playerPhysicsBody = this.playerController.getPlayer()
    const playerSpine = this.playerController.getPlayerSpine()
    const blackHoleCenter = blackHole.getSprite().getCenter()
    this.playerController.enablePlayerCollision(false)
    playerPhysicsBody.body!.enable = false
    const initialScaleX = playerBody.scaleX
    const initialScaleY = playerBody.scaleY
    const playerColliderCenter = playerPhysicsBody.body!.center
    const radius = Phaser.Math.Distance.Between(
      playerColliderCenter.x,
      playerColliderCenter.y,
      blackHoleCenter.x,
      blackHoleCenter.y
    )
    let angle = Math.atan2(
      playerColliderCenter.y - blackHoleCenter.y,
      playerColliderCenter.x - blackHoleCenter.x
    )
    this.scene.tweens.add({
      targets: playerSpine,
      duration: 1500,
      ease: 'Exponential.In',
      startDelay: 0,
      onUpdate: tween => {
        const progress = tween.progress
        const currentRadius = radius * (1 - progress * 1.2)
        const newX = blackHoleCenter.x + currentRadius * Math.cos(angle)
        const newY = blackHoleCenter.y + currentRadius * Math.sin(angle)
        playerSpine.x = newX
        playerSpine.y = newY
        const scale = 1 - progress * 0.95
        playerBody.setScale(initialScaleX * scale, initialScaleY * scale)
        playerSpine.setScale(playerBody.scale * 0.55)
        playerSpine.rotation += 0.1
        angle += 0.09
      },
      onComplete: () => {
        playerPhysicsBody.body!.enable = true
        playerSpine.rotation = 0
        playerPhysicsBody.setVelocityY(300)
        playerBody.setActive(true)
        playerBody.setVisible(true)
        playerSpine.setActive(false)
        playerSpine.setVisible(false)
        this.playerController.onPlayerBelowScreenBottom()
        this.playerController.freezePlayerInPlace()
        this.playerController.fadeInScene(true)
      }
    })
  }

  private handlePlayerTouchedLight(mob: any, topCenter: Phaser.Math.Vector2): void {
    if (this.isImmortal()) return
    const playerSpine = this.playerController.getPlayerSpine()
    const playerPhysicsBody = this.playerController.getPlayer()
    const initialScale = playerSpine.scale
    const scaleSign = Math.sign(initialScale)
    playerPhysicsBody.body!.enable = false
    this.playerController.showShield(false)
    this.playerController.showBubble(false)
    this.playerController.showBootsEffect(false)
    this.playerController.enablePlayerCollision(false)
    togglePlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity)
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, false)
    this.scene.tweens.add({
      targets: playerSpine,
      x: topCenter.x,
      y: topCenter.y + 20,
      scale: 0,
      duration: 1150,
      ease: 'Power2',
      onUpdate: tween => {
        playerSpine.rotation = Phaser.Math.DegToRad(360 * tween.progress)
      },
      onComplete: () => {
        playerPhysicsBody.body!.enable = true
        playerSpine.rotation = 0
        playerSpine.setVisible(false)
        playerSpine.scale = initialScale * scaleSign
        togglePlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity)
        this.playerController.onPlayerBelowScreenBottom()
        this.playerController.fadeInScene(true)
      }
    })
  }
}
