import { sendTutorialAnalyticsEvent } from '@/utils/analytics'

class TutorialAnalyticsDataCollector {
  private static instance: TutorialAnalyticsDataCollector

  private lastPlatform: string | null = null
  private lastBooster: string | null = null
  private firstTicketScore: number | null = null
  private firstBoosterScore: number | null = null
  private sessionScore: number | null = null
  private collectedBoosters: number = 0
  private sessionStartTime: number = 0
  private lastPreset: number | null = null
  private generatedPresets: string[] = []

  private passedTutorial!: boolean

  private constructor() {
    this.sessionStartTime = Date.now()
  }

  public static getInstance(): TutorialAnalyticsDataCollector {
    if (!TutorialAnalyticsDataCollector.instance) {
      TutorialAnalyticsDataCollector.instance = new TutorialAnalyticsDataCollector()
    }
    return TutorialAnalyticsDataCollector.instance
  }

  public setTutorailPassed(passed: boolean) {
    this.passedTutorial = passed
  }

  public getTutorailPassed(): boolean {
    return this.passedTutorial
  }

  public setLastPlatformType(platform: string): void {
    this.lastPlatform = platform
  }

  public setLastBoosterType(boosterType: string): void {
    this.lastBooster = boosterType
  }

  public setFirstTicketScore(score: number): void {
    if (this.firstTicketScore === null) {
      this.firstTicketScore = score
    }
  }

  public setFirstBoosterScore(score: number): void {
    if (this.firstBoosterScore === null) {
      this.firstBoosterScore = score
    }
  }

  public incrementCollectedBoosters(): void {
    this.collectedBoosters++
  }

  public addGeneratedPreset(presetId: string): void {
    if (
      this.generatedPresets.length === 0 ||
      this.generatedPresets[this.generatedPresets.length - 1] !== presetId
    ) {
      this.generatedPresets.push(presetId)
    }
  }

  public setLastPreset(presetId: number): void {
    this.lastPreset = presetId
  }

  public setSessionScore(score: number): void {
    this.sessionScore = score
  }

  public getSessionDuration(): number {
    return Date.now() - this.sessionStartTime
  }

  private clearData(): void {
    this.lastPlatform = null
    this.lastBooster = null
    this.firstTicketScore = 0
    this.firstBoosterScore = 0
    this.collectedBoosters = 0
    this.generatedPresets = []
    this.sessionStartTime = Date.now()
    this.lastPreset = null
  }

  public sendTutorialPassedEvent(): void {
    const sessionDuration = this.getSessionDuration()

    const eventData = {
      last_platform: this.lastPlatform || 'unknown',
      last_booster: this.lastBooster || 'none',
      first_ticket_score: this.firstTicketScore,
      first_booster: this.firstBoosterScore,
      collected_boosters: this.collectedBoosters,
      generated_presets: this.generatedPresets.join(','),
      session_count: this.sessionScore,
      session_duration: sessionDuration,
      last_preset: this.lastPreset || 'none'
    }

    console.log('Sending tutorial analytics event with data:', eventData)

    sendTutorialAnalyticsEvent('tutorial_passed', eventData)
    this.clearData()
  }
}

export default TutorialAnalyticsDataCollector
