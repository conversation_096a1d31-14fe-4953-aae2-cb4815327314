import type { <PERSON><PERSON>anager } from '@/game/core/CoreGameplay/CameraManager'
import type { Interactables } from '@/game/core/CoreGameplay/Interactables.ts'
import { PlayerController } from '@/game/core/CoreGameplay/Player/PlayerController'
import type { GameScene } from '@/game/core/scenes/GameScene'
import type { EventBus } from '@/shared/types'
import type { Vector2 } from '@esotericsoftware/spine-phaser'

export class PlayerManager {
  public playerController!: PlayerController | undefined

  constructor(
    private scene: GameScene,
    private interactables: Interactables,
    private uiEventBus: EventBus,
    private cameraManager: CameraManager
  ) {}

  createPlayer(
    startPosition: Vector2 | undefined,
    ticketsMultiplier: number
  ): Phaser.Physics.Arcade.Sprite {
    this.playerController = new PlayerController(
      this.scene,
      this.interactables,
      this.uiEventBus,
      this.cameraManager
    )
    return this.playerController.create(startPosition, ticketsMultiplier)
  }

  cheatUni() {
    this.playerController?.changeSkin()
  }

  toggleBoots() {
    this.scene.events.emit('boots-toggled')
  }

  toggleAim() {
    this.scene.events.emit('aim-toggled')
  }

  updatePlayer(time: number, delta: number): void {
    if (this.playerController) {
      this.playerController.update(time, delta)
    }
  }

  cleanup() {
    this.playerController = undefined
  }
}
