import { SpineGameObject } from '@esotericsoftware/spine-phaser'
import type { GameScene } from '../../scenes/GameScene'
import { DepthOrder } from '../Constants/DephOrdering'
import { MobDataSetKeys } from './PlayerStates/States/SpineAnimations'

export class RevivePortal {
  private portalSpine!: SpineGameObject
  private scene: GameScene

  constructor(scene: GameScene) {
    this.scene = scene
  }

  createPortal() {
    this.portalSpine = this.scene.add.spine(
      0,
      0,
      MobDataSetKeys.PORTAL_DATA,
      MobDataSetKeys.PORTAL_ATLAS
    )

    this.portalSpine.scale = 0.2
    this.portalSpine.setOrigin(0.5, 0.5)
    this.portalSpine.setDepth(DepthOrder.Player)

    this.portalSpine.animationState.setAnimation(0, PortalAnimationNames.T0_IDLE, true)
    this.portalSpine.animationState.setAnimation(1, PortalAnimationNames.T1_IDLE, true)
    this.portalSpine.animationState.setAnimation(2, PortalAnimationNames.T2_IDLE, true)
    this.portalSpine.animationState.setAnimation(3, PortalAnimationNames.T3_IDLE_STARS, true)
    this.portalSpine.animationState.setAnimation(4, PortalAnimationNames.T4_IDLE, true)

    this.portalSpine.setVisible(false)
  }

  showPortalOnDeath(xPos: number, yPos: number) {
    this.portalSpine.setPosition(xPos, yPos)

    this.scene.time.delayedCall(200, () => {
      this.portalSpine.animationState.setAnimation(5, PortalAnimationNames.APPEAR_ON_DEATH, false)
      this.portalSpine.animationState.addAnimation(5, PortalAnimationNames.T5_IDLE, false)
      this.scene.time.delayedCall(25, () => {
        this.portalSpine.setVisible(true)
      })
    })
  }

  playSplashAnimation() {
    this.portalSpine.animationState.setAnimation(5, PortalAnimationNames.SPLASH_ON_DROP, false)
    this.portalSpine.animationState.addAnimation(5, PortalAnimationNames.T5_IDLE, false)
  }

  showRainbowPortalOnRevive(xPos: number, yPos: number) {
    this.portalSpine.setPosition(xPos, yPos)
  }

  turnOffPortal() {
    this.portalSpine.animationState.setAnimation(5, PortalAnimationNames.T5_OFF)
  }

  setPosition(x: number, y: number) {
    this.portalSpine.setPosition(x, y)
  }

  playReviveAnimation() {
    this.portalSpine.animationState.clearTrack(5)
    this.portalSpine.animationState.setAnimation(
      5,
      PortalAnimationNames.RAINBOW_APPEAR_ON_REVIVE,
      false
    )

    const animationName = PortalAnimationNames.RAINBOW_APPEAR_ON_REVIVE
    const animation = this.portalSpine.skeleton.data.findAnimation(animationName)

    if (animation) {
      const duration = animation.duration
      this.scene.time.delayedCall(duration * 500, () => {
        this.scene.events.emit('portal-opened')
      })
    }
  }
}

export class PortalAnimationNames {
  static readonly T0_IDLE: string = 't0_iDLE'
  static readonly T1_IDLE: string = 't1_iDLE'
  static readonly T2_IDLE: string = 't2_iDLE'
  static readonly T3_IDLE_STARS: string = 't3_IDLE_Stars'
  static readonly T4_IDLE: string = 't4_IDLE'
  static readonly APPEAR_ON_DEATH: string = 't5_Appear'
  static readonly SPLASH_ON_DROP: string = 't5_Drop'
  static readonly T5_IDLE: string = 't5_IDLE'
  static readonly T5_OFF: string = 't5_Off'
  static readonly RAINBOW_APPEAR_ON_REVIVE: string = 't5_Rainbow_Appear'
}
