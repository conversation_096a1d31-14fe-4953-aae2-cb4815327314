import { InputConsts } from '@/game/core/CoreGameplay/Constants/PlayerConsts'
import { type GameScene } from '@/game/core/scenes/GameScene.ts'
import { $version, on, postEvent } from '@telegram-apps/sdk'
import { TouchJoystick } from './TouchJoystick'

export class PlayerInputHandler {
  private scene: GameScene
  private canvas: HTMLCanvasElement
  private cursors: Phaser.Types.Input.Keyboard.CursorKeys | null
  private joystick: TouchJoystick
  private currentVelocityX = 0
  private gyroscopeTiltX: number = 0
  private swipeStartX: number | null = null
  private swipeDeltas: number[] = []
  private swipeDeltaSum = 0
  private lastPointerMoveTime = 0
  private smoothingFactor!: number
  private isGyroscopeOn: boolean
  private activeTouches: Map<number, Touch> = new Map()
  private joystickTouchId: number | null = null
  private pendingJoystickTouch: { id: number; startX: number; startY: number } | null = null
  private readonly MOVEMENT_THRESHOLD = 10

  private readonly isIOS: boolean
  private readonly supportsByCurrentVersion: boolean = true
  private readonly maxVelocity = InputConsts.MAX_VELOCITY
  private readonly angularVelocity = InputConsts.ANGULAR_VELOCITY
  private readonly decelerationFactor = InputConsts.DECELERATION_FACTOR * this.maxVelocity
  private readonly pointerStaleThreshold = InputConsts.POINTER_STALE_THRESHOLD
  private readonly swipeSensitivity = InputConsts.SWIPE_SENSITIVITY
  private readonly maxDeltasLength = InputConsts.SWIPE_DELTAS_MAX_LENGTH

  constructor(scene: GameScene) {
    this.scene = scene
    this.isIOS = this.detectIOS()
    this.supportsByCurrentVersion = this.checkForLatestVersion()
    this.canvas = this.scene.sys.canvas
    this.cursors = this.scene.input.keyboard!.createCursorKeys()
    this.joystick = new TouchJoystick(scene)

    this.isGyroscopeOn = this.scene.IsGyroscopeControlMethod

    if (this.isGyroscopeOn) {
      this.initializeGyroscope()
    } else {
      this.smoothingFactor = InputConsts.SMOOTHING_FACTOR_SWIPE
      this.initializeSwipeControls()
      this.scene.events.on('camera-bottom-changed', this.handleCameraMove, this)
      this.scene.time.delayedCall(100, () => {
        this.joystick.initialize()
      })
    }
  }

  private initializeGyroscope(): void {
    if (this.isIOS && this.supportsByCurrentVersion) {
      this.smoothingFactor = InputConsts.SMOOTHING_FACTOR_IOS
      this.setupIOSAccelerometer()
    } else {
      this.smoothingFactor = InputConsts.SMOOTHING_FACTOR
      this.setupDefaultMotionEvent()
    }
  }

  private detectIOS(): boolean {
    return ['iPad', 'iPhone', 'iPod'].includes(navigator.platform)
  }

  private checkForLatestVersion(): boolean {
    return $version() >= '8.0'
  }

  private setupIOSAccelerometer(): void {
    // @ts-ignore
    postEvent('web_app_start_accelerometer', { refresh_rate: 25 })
    // @ts-ignore
    on('accelerometer_changed', payload => {
      this.handleIOSAccelerometerChange(payload)
    })

    window.addEventListener('accelerometerFailed', this.handleIOSAccelerometerError.bind(this))
  }

  private setupDefaultMotionEvent(): void {
    window.addEventListener('devicemotion', this.handleMotion.bind(this), true)
  }

  private handleIOSAccelerometerChange(event: any): void {
    const angularVelocity = event.x * InputConsts.ANGULAR_VELOCITY_IOS
    this.updateGyroscopeTilt(angularVelocity)
  }

  private handleIOSAccelerometerError(event: any): void {
    console.error('Accelerometer error:', event.error)
  }

  private handleMotion(event: DeviceMotionEvent): void {
    const accelerationX = event.accelerationIncludingGravity?.x || 0
    const angularVelocity = -accelerationX * InputConsts.ANGULAR_VELOCITY
    this.updateGyroscopeTilt(angularVelocity)
  }

  private updateGyroscopeTilt(angularVelocity: number): void {
    this.gyroscopeTiltX =
      angularVelocity + (this.gyroscopeTiltX - angularVelocity) * Math.exp(-this.smoothingFactor)
  }

  private initializeSwipeControls(): void {
    this.canvas.addEventListener('touchstart', this.handleTouchStart, { passive: false })
    this.canvas.addEventListener('touchmove', this.handleTouchMove, { passive: false })
    this.canvas.addEventListener('touchend', this.handleTouchEnd, { passive: false })
  }

  private removeSwipeControls(): void {
    this.canvas.removeEventListener('touchstart', this.handleTouchStart)
    this.canvas.removeEventListener('touchmove', this.handleTouchMove)
    this.canvas.removeEventListener('touchend', this.handleTouchEnd)
  }

  private getPointerWorldPoint(clientX: number, clientY: number) {
    const rect = this.canvas.getBoundingClientRect()
    return this.scene.cameras.main.getWorldPoint(clientX - rect.left, clientY - rect.top)
  }

  private handleTouchStart = (e: TouchEvent): void => {
    e.preventDefault()
    if (!e.touches.length) return

    for (let i = 0; i < e.touches.length; i++) {
      const touch = e.touches[i]
      this.activeTouches.set(touch.identifier, touch)
    }

    if (
      this.joystickTouchId === null &&
      this.pendingJoystickTouch === null &&
      e.touches.length > 0
    ) {
      const touch = e.touches[0]
      this.pendingJoystickTouch = {
        id: touch.identifier,
        startX: touch.clientX,
        startY: touch.clientY
      }

      this.swipeStartX = touch.clientX
      this.swipeDeltas = []
      this.swipeDeltaSum = 0
      this.lastPointerMoveTime = this.scene.time.now
    }
  }

  private handleTouchMove = (e: TouchEvent): void => {
    e.preventDefault()
    if (this.swipeStartX === null || !e.touches.length) return

    for (let i = 0; i < e.touches.length; i++) {
      const touch = e.touches[i]
      this.activeTouches.set(touch.identifier, touch)
    }

    if (this.pendingJoystickTouch && this.joystickTouchId === null) {
      let pendingTouch: Touch | null = null
      for (let i = 0; i < e.touches.length; i++) {
        if (e.touches[i].identifier === this.pendingJoystickTouch.id) {
          pendingTouch = e.touches[i]
          break
        }
      }

      if (pendingTouch) {
        const deltaX = Math.abs(pendingTouch.clientX - this.pendingJoystickTouch.startX)
        const deltaY = Math.abs(pendingTouch.clientY - this.pendingJoystickTouch.startY)
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        if (distance >= this.MOVEMENT_THRESHOLD) {
          const startX = this.pendingJoystickTouch.startX
          const startY = this.pendingJoystickTouch.startY

          this.joystickTouchId = this.pendingJoystickTouch.id
          this.pendingJoystickTouch = null

          const world = this.getPointerWorldPoint(startX, startY)
          this.joystick.show(world.x, world.y)
        }
      }
    }

    if (this.joystickTouchId === null) return

    let joystickTouch: Touch | null = null
    for (let i = 0; i < e.touches.length; i++) {
      if (e.touches[i].identifier === this.joystickTouchId) {
        joystickTouch = e.touches[i]
        break
      }
    }

    if (!joystickTouch) return

    const world = this.getPointerWorldPoint(joystickTouch.clientX, joystickTouch.clientY)
    this.joystick.update(world.x, world.y)

    const deltaX = joystickTouch.clientX - this.swipeStartX
    this.swipeStartX = joystickTouch.clientX

    this.swipeDeltas.push(deltaX)
    this.swipeDeltaSum += deltaX
    if (this.swipeDeltas.length > this.maxDeltasLength) {
      this.swipeDeltaSum -= this.swipeDeltas.shift()!
    }

    const avgDelta = this.swipeDeltaSum / this.swipeDeltas.length
    const desiredVelocity = Phaser.Math.Clamp(
      avgDelta * this.swipeSensitivity,
      -this.maxVelocity,
      this.maxVelocity
    )

    this.currentVelocityX = Phaser.Math.Linear(
      this.currentVelocityX,
      desiredVelocity,
      this.smoothingFactor
    )
    this.lastPointerMoveTime = this.scene.time.now
  }

  private handleTouchEnd = (e: TouchEvent): void => {
    const remainingTouchIds = new Set<number>()
    for (let i = 0; i < e.touches.length; i++) {
      remainingTouchIds.add(e.touches[i].identifier)
    }

    for (const [touchId] of this.activeTouches) {
      if (!remainingTouchIds.has(touchId)) {
        this.activeTouches.delete(touchId)

        if (touchId === this.joystickTouchId) {
          this.joystickTouchId = null
          this.swipeStartX = null
          this.joystick.hide()
        }

        if (this.pendingJoystickTouch && touchId === this.pendingJoystickTouch.id) {
          this.pendingJoystickTouch = null
          this.swipeStartX = null
        }
      }
    }
  }

  public getMovementDirection(deltaInSeconds: number): number {
    const now = this.scene.time.now
    const swipeActive =
      this.swipeStartX !== null && now - this.lastPointerMoveTime <= this.pointerStaleThreshold
    const keyboardActive = this.cursors?.left.isDown || this.cursors?.right.isDown

    if (keyboardActive) {
      this.applyKeyboardInput(deltaInSeconds)
      return this.currentVelocityX * InputConsts.ANGULAR_VELOCITY * InputConsts.KEYBOARD_MULTIPLIER
    }

    if (this.isGyroscopeOn) {
      return this.currentVelocityX + this.gyroscopeTiltX * deltaInSeconds * this.maxVelocity
    }

    if (!swipeActive) {
      this.applyDeceleration(deltaInSeconds)
    }
    return this.currentVelocityX * InputConsts.ANGULAR_VELOCITY
  }

  private applyKeyboardInput(delta: number): void {
    if (this.cursors?.left.isDown) {
      this.currentVelocityX = Math.max(
        this.currentVelocityX - this.angularVelocity * delta,
        -this.maxVelocity
      )
    } else if (this.cursors?.right.isDown) {
      this.currentVelocityX = Math.min(
        this.currentVelocityX + this.angularVelocity * delta,
        this.maxVelocity
      )
    }
  }

  private applyDeceleration(delta: number): void {
    if (this.currentVelocityX > 0) {
      this.currentVelocityX = Math.max(this.currentVelocityX - this.decelerationFactor * delta, 0)
    } else if (this.currentVelocityX < 0) {
      this.currentVelocityX = Math.min(this.currentVelocityX + this.decelerationFactor * delta, 0)
    }
  }

  private handleCameraMove = (): void => {
    this.joystick.updateCameraPosition()
  }

  public isJoystickActive(): boolean {
    return this.joystickTouchId !== null || this.pendingJoystickTouch !== null
  }

  public getActiveTouchCount(): number {
    return this.activeTouches.size
  }

  public destroy(): void {
    this.joystick.destroy()
    if (!this.isGyroscopeOn) {
      this.removeSwipeControls()
      this.scene.events.off('camera-bottom-changed', this.handleCameraMove, this)
    }
  }
}
