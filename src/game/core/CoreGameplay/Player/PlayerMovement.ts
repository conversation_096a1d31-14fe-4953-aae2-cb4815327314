import { achievements } from '@/game/core/CoreGameplay/Achievements/GameplayAchievementsHandler.ts'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import type { SpineGameObject } from '@esotericsoftware/spine-phaser'
import type { GameScene } from '../../scenes/GameScene'
import { PlayerConsts } from '../Constants/PlayerConsts'
import { Attribute, getPlayerAttribute, setPlayerAttribute } from './PlayerAttributeContainer'
import { PlayerController } from './PlayerController'
import { PlayerInputHandler } from './PlayerInputHandler'
import type { PlayerStateMachine } from './PlayerStates/PlayerStateMachine'
import { PlayerStates } from './PlayerStates/States/PlayerStates'
import { PlayerAnimations } from './PlayerStates/States/SpineAnimations'
import TutorialAnalyticsDataCollector from './TutorialAnalyticsDataCollector'

export class PlayerMovement {
  private static readonly MAX_VERTICAL_VELOCITY = 900
  private static readonly MAX_FALLING_VELOCITY = 900
  private static readonly JUMP_VELOCITY_THRESHOLD = 75
  private static readonly SCORE_THRESHOLD = 500
  private static readonly SCORE_UPDATE_INTERVAL = 10
  private static readonly GLOBAL_SCORE_UPDATE_INTERVAL = 1000

  private scene: GameScene
  private uiEventBus: EventBus
  private playerSpine: SpineGameObject
  private inputHandler!: PlayerInputHandler
  private playerController: PlayerController
  private playerStateMachine: PlayerStateMachine
  private playerBody!: Phaser.Physics.Arcade.Body
  private mainCamera: Phaser.Cameras.Scene2D.Camera
  private playerSprite: Phaser.Physics.Arcade.Sprite
  private customAcceleration: number = 0
  private customMaxSpeed: number = 0
  private timeSinceLastScoreUpdate: number = 0
  private timeSinceLastGlobalScoreUpdate: number = 0
  private facingDirection: number = 1
  private startingYPosition: number = 0
  private playerYVelocity!: number
  private playerEntity: number
  private previousScoreForAchievements: number = 0

  public currentScore: number = 0
  public highestYPosition: number = 0

  constructor(scene: GameScene, uiEventBus: EventBus, playerController: PlayerController) {
    this.scene = scene
    this.uiEventBus = uiEventBus
    this.playerController = playerController
    this.playerSprite = playerController.getPlayer()
    this.playerBody = playerController.getPlayerBody()
    this.playerSpine = playerController.getPlayerSpine()
    this.playerStateMachine = playerController.getPlayerStateMachine()
    this.playerEntity = playerController.getPlayerEntity()
    this.mainCamera = scene.cameras.main
    this.inputHandler = new PlayerInputHandler(scene)
  }

  updateSpine(newSpine: SpineGameObject) {
    this.playerSpine = newSpine
  }

  getPlayerVelocityY(): number {
    return this.playerYVelocity
  }

  getPlayerFacingDirection(): number {
    return this.facingDirection
  }

  getInputHandler(): PlayerInputHandler {
    return this.inputHandler
  }

  updateTimeSinceLastScoreUpdate(delta: number) {
    this.timeSinceLastScoreUpdate += delta
    this.timeSinceLastGlobalScoreUpdate += delta
  }

  applyCustomAcceleration(deltaTime: number): void {
    if (this.customAcceleration !== 0) {
      let newVelocityY = this.playerBody.velocity.y + this.customAcceleration * deltaTime
      if (newVelocityY < this.customMaxSpeed) {
        newVelocityY = this.customMaxSpeed
      }
      this.playerBody.setVelocityY(newVelocityY)
    }
  }

  handleJumpingAndFalling(deltaTime: number): void {
    const isJumping = getPlayerAttribute(Attribute.IsJumping, this.playerEntity)
    if (isJumping && this.playerBody.velocity.y >= 0) {
      setPlayerAttribute(Attribute.IsJumping, this.playerEntity, false)
    }
    if (!isJumping && this.playerBody.velocity.y > 0) {
      this.playerBody.setVelocityY(this.playerBody.velocity.y + PlayerConsts.GRAVITY * deltaTime)
    }
    if (isJumping && this.playerBody.velocity.y >= -PlayerMovement.JUMP_VELOCITY_THRESHOLD) {
      this.startFalling()
    }
  }

  clampVerticalVelocity(): void {
    this.scene.events.emit('player-moved', { y: this.playerBody.y })
    if (this.playerBody.velocity.y > PlayerMovement.MAX_VERTICAL_VELOCITY) {
      this.playerBody.velocity.y = PlayerMovement.MAX_VERTICAL_VELOCITY
    }
    if (this.playerBody.velocity.y > PlayerMovement.MAX_FALLING_VELOCITY) {
      this.playerBody.velocity.y = PlayerMovement.MAX_FALLING_VELOCITY
    }
  }

  checkLanding(): void {
    if (
      getPlayerAttribute(Attribute.IsFalling, this.playerEntity) &&
      this.playerBody.blocked.down
    ) {
      this.land()
    }
  }

  startFalling(): void {
    setPlayerAttribute(Attribute.IsFalling, this.playerEntity, true)
    setPlayerAttribute(Attribute.IsJumping, this.playerEntity, false)
    this.playerStateMachine.updateStateBasedOnInput(PlayerStates.Idle)
  }

  land(): void {
    setPlayerAttribute(Attribute.IsFalling, this.playerEntity, false)
  }

  handlePlayerXMovement(deltaInSeconds: number) {
    if (getPlayerAttribute(Attribute.IsDead, this.playerEntity)) {
      return
    }
    const movement = this.inputHandler.getMovementDirection(deltaInSeconds)
    this.playerSprite.x += movement
    if (getPlayerAttribute(Attribute.IsBigJumpBoosterActive, this.playerEntity)) {
      return
    }
    const threshold = PlayerConsts.FLIP_THRESHOLD
    if (movement > threshold && this.playerSpine.scaleX > 0) {
      this.playerSpine.scaleX = -Math.abs(this.playerSpine.scaleX)
      this.facingDirection = -1
    } else if (movement < -threshold && this.playerSpine.scaleX < 0) {
      this.playerSpine.scaleX = Math.abs(this.playerSpine.scaleX)
      this.facingDirection = 1
    }
  }

  wrapPlayer() {
    const playerHalfWidth = this.playerSprite.displayWidth / 2
    if (!this.mainCamera.worldView) return
    const cameraLeft = this.mainCamera.worldView.left
    const cameraRight = this.mainCamera.worldView.right
    if (this.playerSprite.x < cameraLeft - playerHalfWidth + PlayerConsts.WRAP_OFFSET) {
      this.playerSprite.x = cameraRight + playerHalfWidth - PlayerConsts.WRAP_OFFSET
    } else if (this.playerSprite.x > cameraRight + playerHalfWidth - PlayerConsts.WRAP_OFFSET) {
      this.playerSprite.x = cameraLeft - playerHalfWidth + PlayerConsts.WRAP_OFFSET
    }
  }

  syncPlayerVisualWithPhysicalBody(): void {
    if (getPlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity)) return
    this.playerSpine.setPosition(
      this.playerSprite.x,
      this.playerSprite.y + PlayerAnimations.Y_OFFSET
    )
  }

  updatePlayerYVelocity() {
    this.playerYVelocity = this.playerBody.velocity.y
  }

  updateHighestYPosition(): void {
    if (getPlayerAttribute(Attribute.IsDead, this.playerEntity)) return
    const newHighestY = this.startingYPosition - this.playerSprite.y
    if (newHighestY > this.highestYPosition) {
      this.highestYPosition = newHighestY
      this.playerController.playerMaxHeight = this.playerSprite.y
      const oldScore = this.currentScore
      this.currentScore = Math.floor(this.highestYPosition * PlayerConsts.POINTS_INDEX)
      if (
        !getPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity) &&
        this.currentScore >= PlayerMovement.SCORE_THRESHOLD
      ) {
        this.passTutorial()
      }
      if (this.timeSinceLastScoreUpdate >= PlayerMovement.SCORE_UPDATE_INTERVAL) {
        this.uiEventBus.emit(GAME_EVENTS.UPDATE_SCORE, this.currentScore)
        this.scene.events.emit('current-score-changed', this.currentScore)
        this.timeSinceLastScoreUpdate = 0
      }
      if (this.timeSinceLastGlobalScoreUpdate >= PlayerMovement.GLOBAL_SCORE_UPDATE_INTERVAL) {
        const achievementDelta = this.currentScore - this.previousScoreForAchievements
        if (achievementDelta > 0) {
          achievements.checkAchievement(3, achievementDelta)
          achievements.checkAchievement(13, achievementDelta)
          this.previousScoreForAchievements = this.currentScore
        }
        this.timeSinceLastGlobalScoreUpdate = 0
      }
      this.scene.events.emit('player-position-updated', {
        x: this.playerSprite.x,
        y: this.playerSprite.y
      })
    }
  }

  resetVelocity(): void {
    this.playerBody.setVelocityX(0)
  }

  private passTutorial() {
    setPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity, true)
    const tutorailAnalytics = TutorialAnalyticsDataCollector.getInstance()
    tutorailAnalytics.setSessionScore(this.currentScore)
    tutorailAnalytics.setTutorailPassed(true)
    tutorailAnalytics.sendTutorialPassedEvent()
  }
}
