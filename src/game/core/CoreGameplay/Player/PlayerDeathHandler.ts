import { achievements } from '@/game/core/CoreGameplay/Achievements/GameplayAchievementsHandler.ts'
import { EntityType } from '@/game/core/MapGen/MapGenerator.ts'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'
import type { SpineGameObject } from '@esotericsoftware/spine-phaser'
import type { GameScene } from '../../scenes/GameScene'
import type { CameraManager } from '../CameraManager'
import { PlayerConsts } from '../Constants/PlayerConsts'
import { Attribute, setPlayerAttribute } from './PlayerAttributeContainer'
import { PlayerController } from './PlayerController'
import type { PlayerEnemyInteractions } from './PlayerEnemyInteractions'
import type { PlayerStateMachine } from './PlayerStates/PlayerStateMachine'
import { PlayerStates } from './PlayerStates/States/PlayerStates'
import { RevivePortal } from './RevivePortal'

export class PlayerDeathHandler {
  private static readonly DEATH_TWEEN_DURATION = 900
  private static readonly OFFSET_Y_TOP = 100
  private static readonly OFFSET_Y_BOTTOM = 100
  private static readonly DEATH_ANIMATION_DELAY = 1000
  private static readonly SCENE_FADE_DELAY = 400
  private static readonly SCENE_FADE_DURATION = 500
  private static readonly FADE_COLOR = 0x000000
  private static readonly REVIVING_OFFSET = 50
  private static readonly REVIVE_TWEEN_DURATION = 1000
  private static readonly JUMP_FORCE = 100

  private scene: GameScene
  private playerSpine: SpineGameObject
  private playerStateMachine: PlayerStateMachine
  private revivePortal: RevivePortal
  private portalY!: number
  private uiEventBus: EventBus
  private playerController: PlayerController
  private cameraManager: CameraManager
  private playerSprite: Phaser.Physics.Arcade.Sprite
  private playerEnemyInteractions: PlayerEnemyInteractions
  private cachedTweens: { [key: string]: Phaser.Tweens.Tween } = {}
  private playerEntity: number
  private revivePointY!: number
  private fadeOverlay!: Phaser.GameObjects.Rectangle

  constructor(
    scene: GameScene,
    uiEventBus: EventBus,
    playerController: PlayerController,
    playerEnemyInteractions: PlayerEnemyInteractions
  ) {
    this.scene = scene
    this.uiEventBus = uiEventBus
    this.playerController = playerController
    this.playerEnemyInteractions = playerEnemyInteractions

    this.playerSprite = playerController.getPlayer()
    this.playerSpine = playerController.getPlayerSpine()
    this.cachedTweens = playerController.getCachedTweens()
    this.cameraManager = playerController.getCameraManager()
    this.playerStateMachine = playerController.getPlayerStateMachine()
    this.playerEntity = playerController.getPlayerEntity()

    this.revivePortal = new RevivePortal(scene)
    this.revivePortal.createPortal()
  }

  updateSpine(newSpine: SpineGameObject) {
    this.playerSpine = newSpine
  }

  playDeathAnimation(hadDeathAnimation = false) {
    const startY = this.scene.cameras.main.worldView.top - PlayerDeathHandler.OFFSET_Y_TOP
    const finalY = this.scene.cameras.main.worldView.bottom - PlayerDeathHandler.OFFSET_Y_BOTTOM
    const centreX = this.scene.cameras.main.worldView.centerX
    this.portalY = finalY

    this.playerController.showBubble(false)
    this.playerController.showShield(false)
    this.playerController.showBootsEffect(false)
    this.playerController.showBootsEffect(false)
    this.playerSpine.setPosition(centreX, startY)
    this.revivePortal.showPortalOnDeath(centreX, this.portalY)
    this.playerSpine.setVisible(true)
    this.playerStateMachine.updateStateBasedOnInput(PlayerStates.Death)
    this.playerController?.turnOnMagnet(false)

    if (!hadDeathAnimation) {
      this.scene.time.delayedCall(PlayerDeathHandler.DEATH_ANIMATION_DELAY, () =>
        this.revivePortal.playSplashAnimation()
      )

      this.scene.tweens.add({
        targets: this.playerSpine,
        x: centreX,
        y: finalY,
        duration: PlayerDeathHandler.DEATH_TWEEN_DURATION,
        ease: 'Linear',
        onUpdate: () => {
          if (this.playerSpine.y >= this.portalY) {
            this.playerSpine.setVisible(false)
          }
        },
        onComplete: () => {
          this.scene.time.delayedCall(PlayerDeathHandler.SCENE_FADE_DELAY, () =>
            this.fadeInScene(false)
          )
        }
      })
    }
  }

  fadeInScene(
    animatedDeath: boolean,
    duration = PlayerDeathHandler.SCENE_FADE_DURATION,
    color = PlayerDeathHandler.FADE_COLOR,
    targetAlpha = 0.5
  ) {
    if (!this.fadeOverlay) {
      this.fadeOverlay = this.scene.add
        .rectangle(0, 0, this.scene.scale.width, this.scene.scale.height, color, 0)
        .setOrigin(0)
        .setScrollFactor(0)
    } else {
      this.fadeOverlay.fillColor = color
    }
    this.scene.tweens.add({
      targets: this.fadeOverlay,
      alpha: targetAlpha,
      duration: duration,
      onComplete: () => {
        try {
          const tonInfo = this.playerController.getNextTokenInfo(EntityType.CollectibleTon)
          const customTokenInfo = this.playerController.getNextTokenInfo(
            EntityType.CollectibleCustomCoin
          )
          const shouldShowLimitEnded = this.playerController.checkForTonLimitReached()
          const killedMobsCount = this.scene.EnemyStats?.getKilledMobsCount()
          //todo: refactor this event params
          this.uiEventBus.emit(
            GAME_EVENTS.GAME_ENDED,
            this.scene.getActiveSessionInfo(),
            tonInfo,
            customTokenInfo,
            shouldShowLimitEnded,
            killedMobsCount
          )
          if (animatedDeath) {
            this.cameraManager.cameraController.instantMoveToEndGame()
            this.playDeathAnimation(true)
          }
        } catch (e) {
          console.error('Error in fadeInScene', e)
        }
      }
    })
  }

  async fadeOutScene(
    duration = PlayerDeathHandler.SCENE_FADE_DURATION,
    color = PlayerDeathHandler.FADE_COLOR,
    initialAlpha = 0.5
  ): Promise<void> {
    this.revivePortal.turnOffPortal()
    const revivingPoint = await this.scene.setupAfterReviving(this.playerController.playerMaxHeight)
    this.revivePointY = revivingPoint
    this.cameraManager.cameraController.instantMoveToStartGame(revivingPoint)
    if (!this.fadeOverlay) {
      this.fadeOverlay = this.scene.add
        .rectangle(0, 0, this.scene.scale.width, this.scene.scale.height, color, initialAlpha)
        .setOrigin(0)
        .setScrollFactor(0)
    } else {
      this.fadeOverlay.fillColor = color
      this.fadeOverlay.alpha = initialAlpha
    }
    await new Promise<void>(resolve => {
      this.scene.tweens.add({
        targets: this.fadeOverlay,
        alpha: 0,
        duration: duration,
        onComplete: () => {
          const centreX = this.scene.cameras.main.worldView.centerX
          const finalY =
            this.scene.cameras.main.worldView.bottom - PlayerDeathHandler.REVIVING_OFFSET
          this.revivePortal.setPosition(centreX, finalY)
          this.revivePortal.playReviveAnimation()
          resolve()
        }
      })
    })
  }

  revive() {
    const centreX = this.scene.cameras.main.worldView.centerX
    const finalY = this.scene.cameras.main.worldView.bottom - PlayerDeathHandler.REVIVING_OFFSET

    this.playerController.showShield(true)
    this.playerController.showBootsEffect(true)
    this.playerController.reEnableBubble()
    this.playerSpine.setActive(true)
    this.playerSpine.setVisible(true)
    this.playerSprite.setPosition(centreX, finalY)
    this.playerController?.checkForMagnetActivation()

    this.playerSpine.setOrigin(0.5, 0.5)

    this.cameraManager.cameraController.startGameLoop(true)

    this.playerStateMachine.updateStateBasedOnInput(PlayerStates.Idle)
    this.playerEnemyInteractions.stopPlayerTween()
    setPlayerAttribute(Attribute.IsOnPropeller, this.playerEntity, false)

    this.resetPlayerState()
    this.removeCachedTween('respawn')

    this.playerController.enablePlayer()

    this.cachedTweens['respawn'] = this.scene.tweens.add({
      targets: this.playerSprite,
      y: this.revivePointY - PlayerDeathHandler.REVIVING_OFFSET * 3,
      ease: 'Cubic.easeInOut',
      duration: PlayerDeathHandler.REVIVE_TWEEN_DURATION,
      onUpdate: () => {
        this.playerSpine.scale = this.playerController.getInitialSpineScale()
      },
      onComplete: () => this.onReviveComplete()
    })
  }

  private resetPlayerState() {
    setPlayerAttribute(Attribute.IsImmortal, this.playerEntity, false)
  }

  private onReviveComplete() {
    setPlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity, false)
    this.revivePortal.turnOffPortal()
    this.playerController.jump(PlayerDeathHandler.JUMP_FORCE)
    this.playerController.enablePlayerCollision(true)
    this.playerSprite!.setGravityY(PlayerConsts.GRAVITY)
    this.playerEnemyInteractions.subscribeEvents()
    this.cameraManager.cameraController.trackEndGameInvoked(false)
    this.playerSpine.scale = this.playerController.getInitialSpineScale()
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, true)
    setPlayerAttribute(Attribute.IsDead, this.playerEntity, false)
    this.playerController.triggerSkinAnimation()
    achievements.checkAchievement(2)
  }

  private removeCachedTween(key: string) {
    if (this.cachedTweens[key]) {
      this.cachedTweens[key].remove()
      delete this.cachedTweens[key]
    }
  }
}
