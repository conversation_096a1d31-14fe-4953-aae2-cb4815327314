import { <PERSON>Names } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { GameScene } from '../../scenes/GameScene'
import { DepthOrder } from '../Constants/DephOrdering'

export class PlayerVFX {
  private static readonly RAINBOW_TRAIL_Y_OFFSET: number = 120
  private static readonly LIGHT_TRAIL_Y_OFFSET: number = 120
  private static readonly RAINBOW_TRAIL_LIFESPAN: number = 1200
  private static readonly LIGHT_TRAIL_LIFESPAN: number = 800
  private static readonly RAINBOW_TRAIL_QUANTITY: number = 1
  private static readonly LIGHT_TRAIL_QUANTITY: number = 1
  private static readonly RAINBOW_TRAIL_SCALE: number = 0.5
  private static readonly LIGHT_TRAIL_SCALE: number = 0.4
  private static readonly LIGHT_TRAIL_ALPHA_START: number = 0.15
  private static readonly LIGHT_TRAIL_ALPHA_END: number = 0
  private static readonly PARTICLE_DEPTH_OFFSET: number = 1
  private static readonly LIGHT_TRAIL_EXPLODE_COUNT: number = 5
  private static readonly RAINBOW_TRAIL_EXPLODE_COUNT: number = 1

  private scene: GameScene
  private readonly player: Phaser.Physics.Arcade.Sprite
  private rainbowEmitter!: Phaser.GameObjects.Particles.ParticleEmitter
  private lightEmitter!: Phaser.GameObjects.Particles.ParticleEmitter

  constructor(scene: GameScene, player: Phaser.Physics.Arcade.Sprite) {
    this.scene = scene
    this.player = player

    this.createRainbowTrail()
    this.createLightTrail()
  }

  public emitRainbowTrail() {
    if (this.rainbowEmitter) {
      this.rainbowEmitter.explode(PlayerVFX.RAINBOW_TRAIL_EXPLODE_COUNT)
    }
  }

  public emitLightTrail() {
    if (this.lightEmitter) {
      this.lightEmitter.explode(PlayerVFX.LIGHT_TRAIL_EXPLODE_COUNT)
    }
  }

  private createRainbowTrail(): void {
    if (!this.player || !this.player.body) return

    const playerCenter = this.player.getCenter()

    this.rainbowEmitter = this.scene.add.particles(
      playerCenter.x,
      playerCenter.y + PlayerVFX.RAINBOW_TRAIL_Y_OFFSET,
      AtlasNames.ENV,
      {
        frame: 'rainbow.png',
        lifespan: PlayerVFX.RAINBOW_TRAIL_LIFESPAN,
        quantity: PlayerVFX.RAINBOW_TRAIL_QUANTITY,
        speed: 0,
        scale: { start: PlayerVFX.RAINBOW_TRAIL_SCALE, end: PlayerVFX.RAINBOW_TRAIL_SCALE },
        frequency: -1,
        alpha: { start: 1, end: 0 },
        emitting: false
      }
    )

    this.rainbowEmitter.setDepth(DepthOrder.Player - PlayerVFX.PARTICLE_DEPTH_OFFSET)

    this.scene.events.on('update', () => {
      if (this.player && this.rainbowEmitter) {
        const playerCenter = this.player.getCenter()
        this.rainbowEmitter.setPosition(
          playerCenter.x,
          playerCenter.y + PlayerVFX.RAINBOW_TRAIL_Y_OFFSET
        )
      }
    })
  }

  private createLightTrail(): void {
    if (!this.player || !this.player.body) return

    const playerCenter = this.player.getCenter()

    this.lightEmitter = this.scene.add.particles(
      playerCenter.x,
      playerCenter.y + PlayerVFX.LIGHT_TRAIL_Y_OFFSET,
      AtlasNames.ENV,
      {
        frame: 'light.png',
        lifespan: PlayerVFX.LIGHT_TRAIL_LIFESPAN,
        quantity: PlayerVFX.LIGHT_TRAIL_QUANTITY,
        speed: 0,
        scale: { start: PlayerVFX.LIGHT_TRAIL_SCALE, end: PlayerVFX.LIGHT_TRAIL_SCALE },
        frequency: -1,
        alpha: { start: PlayerVFX.LIGHT_TRAIL_ALPHA_START, end: PlayerVFX.LIGHT_TRAIL_ALPHA_END },
        emitting: false
      }
    )

    this.lightEmitter.setDepth(DepthOrder.Player - PlayerVFX.PARTICLE_DEPTH_OFFSET)

    this.scene.events.on('update', () => {
      if (this.player && this.lightEmitter) {
        const playerCenter = this.player.getCenter()
        this.lightEmitter.setPosition(
          playerCenter.x,
          playerCenter.y + PlayerVFX.LIGHT_TRAIL_Y_OFFSET
        )
      }
    })
  }
}
