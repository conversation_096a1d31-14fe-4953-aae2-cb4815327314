import { GAME_EVENTS, UI_EVENTS } from '@/shared/constants/uiEvents'
import { HapticsService, hapticsService } from '@/shared/haptics/hapticsService.ts'
import type { EventBus } from '@/shared/types'
import type { Vector2 } from '@esotericsoftware/spine-phaser'
import { EntityType } from '../../MapGen/MapGenerator'
import type { GameScene } from '../../scenes/GameScene'
import type { BaseBooster } from '../Boosters/BaseBooster'
import type { CollectableItemBase } from '../Collectables/CollectableItemBase'
import { BoostersConsts } from '../Constants/BoostersConsts'
import {
  CustomCoinAnimationConfig,
  TicketPickupAnimationConsts,
  TonPickupAnimationConsts,
  type AnimationConfig
} from '../Constants/CollectableConsts'
import { DepthOrder } from '../Constants/DephOrdering'
import { AtlasNames } from '../Constants/GameViewConsts'
import { PlayerConsts } from '../Constants/PlayerConsts'
import { ShieldBoosterConsts } from '../Constants/ShieldConsts'
import type { BasePlatform } from '../Platforms/BasePlatform'
import { BoosterPlatform } from '../Platforms/BoosterPlatform'
import { IcePlatform } from '../Platforms/IcePlatform'
import { SpikedPlatform } from '../Platforms/SpikedPlatform'
import { TrapPlatform } from '../Platforms/TrapPlatform'
import { WreckablePlatform } from '../Platforms/WreckablePlatform'
import { Attribute, getPlayerAttribute, setPlayerAttribute } from './PlayerAttributeContainer'
import { PlayerController } from './PlayerController'
import type { PlayerStateMachine } from './PlayerStates/PlayerStateMachine'
import { PlayerStates } from './PlayerStates/States/PlayerStates'
import { PlayerVFX } from './PlayerVFX'
import TutorialAnalyticsDataCollector from './TutorialAnalyticsDataCollector'

export class PlayerInteractions {
  private static readonly INITIAL_JUMP_VELOCITY = 1806
  private static readonly ASCENT_DELAY_MULTIPLIER = (5 / 60) * 1000
  private static readonly HAPTIC_DURATION_MULTIPLIER = 1200
  private static readonly ASCENT_TIMER_DELAY_MULTIPLIER = 1000

  private scene: GameScene
  private playerStateMachine: PlayerStateMachine
  private playerController: PlayerController
  private playerSprite: Phaser.Physics.Arcade.Sprite
  private cachedTweens: { [key: string]: Phaser.Tweens.Tween } = {}
  private playerVFX!: PlayerVFX
  private playerBody!: Phaser.Physics.Arcade.Body
  private ascentTimer: Phaser.Time.TimerEvent | null = null
  private uiEventBus: EventBus
  protected haptics: HapticsService = hapticsService
  private playerEntity: number
  private shieldTimerEvent?: Phaser.Time.TimerEvent
  private shieldExpireTime?: number
  private shieldGlow: Phaser.GameObjects.Image | null = null
  private shieldSpikes: Phaser.GameObjects.Image | null = null
  private positionTweenEvent: Phaser.Tweens.Tween | null = null
  private shieldContainer: Phaser.GameObjects.Container | null = null
  private glowTween: Phaser.Tweens.Tween | null = null
  private spikesTween: Phaser.Tweens.Tween | null = null

  constructor(scene: GameScene, playerController: PlayerController, uiEventBus: EventBus) {
    this.scene = scene
    this.playerController = playerController
    this.uiEventBus = uiEventBus

    this.playerSprite = playerController.getPlayer()
    this.playerBody = playerController.getPlayerBody()
    this.cachedTweens = playerController.getCachedTweens()
    this.playerStateMachine = playerController.getPlayerStateMachine()
    this.playerEntity = playerController.getPlayerEntity()

    this.scene.events.on(GAME_EVENTS.FLOATING_AREA_STAY, this.onFloatingAreaStay, this)

    this.uiEventBus.on(UI_EVENTS.PAUSE, this.handleGamePause, this)
    this.uiEventBus.on(UI_EVENTS.UNPAUSE, this.handleGameUnpause, this)

    this.playerVFX = new PlayerVFX(this.scene, this.playerSprite)
  }

  onBoosterInteraction(booster: BaseBooster, collisionPos: Vector2 | undefined): void {
    if (this.shouldIgnoreBoosterInteraction(booster, collisionPos)) return
    const passedTutor = getPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity)
    const boosterType = booster.getType()

    switch (boosterType) {
      case EntityType.BoosterSmallJump:
      case EntityType.BoosterBigJump:
        if (this.playerSprite!.body!.blocked.down) {
          this.handleJumpBooster(booster)
          booster.applyEffect(collisionPos!)
          if (passedTutor) return
          const tutorStatTracker = TutorialAnalyticsDataCollector.getInstance()
          const currentScore = this.playerController.getCurrentScore()
          tutorStatTracker.setLastBoosterType(boosterType.toString())
          tutorStatTracker.setFirstBoosterScore(currentScore)
          tutorStatTracker.incrementCollectedBoosters()
        }
        break
      case EntityType.BoosterSmallFly:
      case EntityType.BoosterBigFly:
        this.handleFlyBooster(booster)
        booster.applyEffect(collisionPos!)
        break
      case EntityType.BoosterShield:
        booster.applyEffect(collisionPos!)
        this.applyShieldEffect()
        break
    }
  }

  private applyShieldEffect() {
    if (getPlayerAttribute(Attribute.isBubbleActive, this.playerEntity)) return
    setPlayerAttribute(Attribute.IsShieldActive, this.playerEntity, true)
    const currentTime = this.scene.time.now

    if (this.shieldExpireTime) {
      this.shieldExpireTime += ShieldBoosterConsts.IMMORTALITY_DURATION_SHIELD
      if (this.shieldTimerEvent) {
        this.shieldTimerEvent.remove(false)
      }
      const remaining = this.shieldExpireTime - currentTime
      this.shieldTimerEvent = this.scene.time.addEvent({
        delay: remaining,
        callback: () => {
          setPlayerAttribute(Attribute.IsShieldActive, this.playerEntity, false)
          this.destroyShieldEffects()
        }
      })
      return
    }

    this.shieldExpireTime = currentTime + ShieldBoosterConsts.IMMORTALITY_DURATION_SHIELD

    this.shieldContainer = this.scene.add.container(this.playerSprite.x, this.playerSprite.y)
    this.shieldContainer.setDepth(DepthOrder.Player)

    this.shieldGlow = this.scene.add.image(0, 0, AtlasNames.EFFECTS, 'shield-glow.png')
    this.shieldSpikes = this.scene.add.image(0, 0, AtlasNames.EFFECTS, 'shield-spikes.png')

    this.shieldGlow.setOrigin(
      ShieldBoosterConsts.SHIELD_EFFECT_ORIGIN,
      ShieldBoosterConsts.SHIELD_EFFECT_ORIGIN
    )
    this.shieldSpikes.setOrigin(
      ShieldBoosterConsts.SHIELD_EFFECT_ORIGIN,
      ShieldBoosterConsts.SHIELD_EFFECT_ORIGIN
    )

    this.shieldGlow.setDisplaySize(
      ShieldBoosterConsts.SHIELD_GLOW_DISPLAY_SIZE.x,
      ShieldBoosterConsts.SHIELD_GLOW_DISPLAY_SIZE.y
    )
    this.shieldSpikes.setDisplaySize(
      ShieldBoosterConsts.SHIELD_SPIKE_DISPLAY_SIZE.x,
      ShieldBoosterConsts.SHIELD_SPIKE_DISPLAY_SIZE.y
    )

    this.shieldContainer.add([this.shieldGlow, this.shieldSpikes])

    this.glowTween = this.scene.tweens.add({
      targets: this.shieldGlow,
      alpha: {
        from: ShieldBoosterConsts.SHIELD_GLOW_ALPHA_FROM,
        to: ShieldBoosterConsts.SHIELD_GLOW_ALPHA_TO
      },
      duration: ShieldBoosterConsts.SHIELD_GLOW_TWEEN_DURATION,
      yoyo: true,
      repeat: -1
    })

    this.spikesTween = this.scene.tweens.add({
      targets: this.shieldSpikes,
      angle: ShieldBoosterConsts.SHIELD_SPIKES_ROTATION_ANGLE,
      duration: ShieldBoosterConsts.SHIELD_SPIKES_TWEEN_DURATION,
      repeat: -1
    })

    this.positionTweenEvent = this.scene.tweens.add({
      targets: this.shieldContainer,
      x: this.playerSprite.x,
      y: this.playerSprite.y,
      duration: ShieldBoosterConsts.SHIELD_POSITION_TWEEN_DURATION,
      repeat: ShieldBoosterConsts.SHIELD_POSITION_TWEEN_REPEAT,
      onUpdate: tween => {
        if (!this.shieldContainer || !this.shieldContainer.active) {
          tween.stop()
          return
        }
        this.shieldContainer.setPosition(this.playerSprite.x, this.playerSprite.y)
      }
    })

    this.shieldTimerEvent = this.scene.time.addEvent({
      delay: ShieldBoosterConsts.IMMORTALITY_DURATION_SHIELD,
      callback: () => {
        setPlayerAttribute(Attribute.IsShieldActive, this.playerEntity, false)
        this.destroyShieldEffects()
      }
    })
  }

  private destroyShieldEffects() {
    if (this.glowTween) {
      this.glowTween.stop()
      this.glowTween = null
    }
    if (this.spikesTween) {
      this.spikesTween.stop()
      this.spikesTween = null
    }
    if (this.positionTweenEvent) {
      this.positionTweenEvent.stop()
      this.positionTweenEvent = null
    }
    if (this.shieldTimerEvent) {
      this.shieldTimerEvent.remove(false)
      this.shieldTimerEvent = undefined
    }
    if (this.shieldContainer) {
      this.shieldContainer.destroy(true)
      this.shieldContainer = null
      this.shieldGlow = null
      this.shieldSpikes = null
    }
    this.shieldExpireTime = undefined
  }

  public showShield(shouldBeVisible: boolean) {
    if (this.shieldContainer) {
      this.shieldContainer.setVisible(shouldBeVisible)
    }
  }

  private shouldIgnoreBoosterInteraction(
    booster: BaseBooster,
    collisionPos: Vector2 | undefined
  ): boolean {
    return (
      collisionPos == undefined ||
      booster.isUsed ||
      getPlayerAttribute(Attribute.IsBoosterActive, this.playerEntity)
    )
  }

  private handleJumpBooster(booster: BaseBooster): void {
    this.playerController.showBootsEffect(false)
    const isSmallJump = booster.getType() === EntityType.BoosterSmallJump

    this.playerStateMachine.updateStateBasedOnInput(
      isSmallJump ? PlayerStates.Spring : PlayerStates.JumpPad
    )

    const jumpVelocity = isSmallJump
      ? BoostersConsts.SMALL_JUMP_VELOCITY
      : BoostersConsts.BIG_JUMP_VELOCITY
    this.playerController.jump(jumpVelocity)
    isSmallJump ? this.handleSmallJumpBooster() : this.handleBigJumpBooster()
  }

  private handleSmallJumpBooster(): void {
    this.playerVFX.emitLightTrail()
    this.scene.time.delayedCall(750, () => {
      this.playerController.showBootsEffect(true)
    })
  }

  private handleBigJumpBooster(): void {
    this.playerVFX.emitRainbowTrail()
    setPlayerAttribute(Attribute.IsBigJumpBoosterActive, this.playerEntity, true)
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, false)
    this.startSomersault()
    this.scene.time.delayedCall(750, () => {
      this.playerController.showBootsEffect(true)
    })
  }

  private handleFlyBooster(booster: BaseBooster): void {
    this.playerController.showBootsEffect(false)
    setPlayerAttribute(Attribute.IsImmortal, this.playerEntity, true)
    setPlayerAttribute(Attribute.IsBoosterActive, this.playerEntity, true)
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, false)
    this.applyFlyBoosterEffect(booster)
  }

  private applyFlyBoosterEffect(booster: BaseBooster): void {
    switch (booster.getType()) {
      case EntityType.BoosterSmallFly:
        this.configureFlyBooster(
          BoostersConsts.SMALL_FLY_SCORE,
          BoostersConsts.SMALL_FLY_MAX_SPEED,
          PlayerStates.Propeller,
          true
        )
        break
      case EntityType.BoosterBigFly:
        this.configureFlyBooster(
          BoostersConsts.BIG_FLY_SCORE,
          BoostersConsts.BIG_FLY_MAX_SPEED,
          PlayerStates.Jetpack,
          false
        )
        break
    }
  }

  private configureFlyBooster(
    score: number,
    maxSpeed: number,
    state: PlayerStates,
    isOnPropeller: boolean
  ): void {
    const H_desired = score / PlayerConsts.POINTS_INDEX
    const g = BoostersConsts.FLY_GRAVITY

    const H_descent = (maxSpeed * maxSpeed) / (2 * g)
    const H_ascent = H_desired - H_descent

    if (H_ascent <= 0) {
      console.error('Invalid parameters: Ascent height must be positive.')
      return
    }

    const t_up = H_ascent / maxSpeed
    this.haptics.triggerHapticEventForDuration(
      () => this.haptics.triggerNotificationHapticEvent('success'),
      t_up * PlayerInteractions.HAPTIC_DURATION_MULTIPLIER,
      100
    )
    this.startCustomFly(t_up, maxSpeed, state)
    this.playerStateMachine.updateStateBasedOnInput(state)
    setPlayerAttribute(Attribute.IsOnPropeller, this.playerEntity, isOnPropeller)
  }

  private handleGamePause(): void {
    hapticsService.pauseHapticEvent()
  }

  private handleGameUnpause(): void {
    hapticsService.resumeHapticEvent()
  }

  private startCustomFly(t_up: number, maxSpeed: number, state: PlayerStates): void {
    this.playerBody.setVelocityY(-maxSpeed).setAccelerationY(0).setGravityY(0)
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, false)

    if (this.ascentTimer) {
      this.ascentTimer.remove(false)
    }

    const delay =
      state === PlayerStates.Jetpack
        ? PlayerConsts.IMMORTALITY_DURATION_JETPACK
        : PlayerConsts.IMMORTALITY_DURATION_PROPELLER

    this.ascentTimer = this.scene.time.addEvent({
      delay: t_up * PlayerInteractions.ASCENT_TIMER_DELAY_MULTIPLIER,
      callback: () => {
        this.playerBody.setGravityY(BoostersConsts.FLY_GRAVITY)
        this.playerController.enablePlayerCollision(true)
        this.playerStateMachine.updateStateBasedOnInput(PlayerStates.Idle)
        setPlayerAttribute(Attribute.IsOnPropeller, this.playerEntity, false)

        this.scene.time.delayedCall(delay, () => {
          setPlayerAttribute(Attribute.IsImmortal, this.playerEntity, false)
          this.playerController.showBootsEffect(true)
        })
      }
    })
  }

  private startSomersault(): void {
    const direction = this.playerSprite.flipX ? 1 : -1
    const ascentTime = this.calculateJumpAscentTime()
    const tweenDuration = Math.max(
      ascentTime - PlayerInteractions.ASCENT_DELAY_MULTIPLIER,
      ascentTime
    )

    if (!this.cachedTweens['somersault']) {
      this.cachedTweens['somersault'] = this.scene.tweens.add({
        targets: this.playerSprite,
        angle: 360 * direction,
        duration: tweenDuration,
        ease: 'Power2',
        paused: true,
        onComplete: () => {
          this.playerSprite.angle = 0
          setPlayerAttribute(Attribute.IsBigJumpBoosterActive, this.playerEntity, false)
        }
      })
    }

    this.scene.time.delayedCall(PlayerInteractions.ASCENT_DELAY_MULTIPLIER, () => {
      this.cachedTweens['somersault'].restart()
    })
  }

  private calculateJumpAscentTime(): number {
    const gravity = PlayerConsts.GRAVITY
    const ascentTime = -PlayerInteractions.INITIAL_JUMP_VELOCITY / gravity
    return ascentTime * PlayerInteractions.ASCENT_TIMER_DELAY_MULTIPLIER
  }

  onPlatformInteraction(platform: BasePlatform): void {
    switch (true) {
      case platform instanceof WreckablePlatform:
        this.handleWreckablePlatformInteraction(platform as WreckablePlatform)
        break
      case platform instanceof SpikedPlatform:
        this.handleSpikePlatformInteraction(platform as SpikedPlatform)
        break
      case platform instanceof BoosterPlatform:
        this.handleBoosterPlatformInteraction(platform as BoosterPlatform)
        break
      case platform instanceof IcePlatform:
        this.handleIcePlatformInteraction(platform as IcePlatform)
        break
      case platform instanceof TrapPlatform:
        this.handleTrapPlatformInteraction(platform as TrapPlatform)
        break
      default:
        this.handleDefaultPlatformInteraction(platform)
        break
    }

    const passedTutor = getPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity)
    if (passedTutor) return

    const tutorStatTracker = TutorialAnalyticsDataCollector.getInstance()
    const platformType = platform.getType()
    tutorStatTracker.setLastPlatformType(platformType.toString())
  }

  private stabilizationForce: number = 30
  private maxEntrySpeed: number = 200
  private oscillationSpeed: number = 0.15
  private oscillationRange: number = 150
  private oscillationPhase: number = 0
  private colliderTop: number = 0
  private colliderBottom: number = 0

  private onFloatingAreaEnter(): void {
    if (!this.playerBody) return

    const velocityY = this.playerBody.velocity.y

    if (velocityY > this.maxEntrySpeed) {
      this.playerBody.setVelocityY(velocityY - this.stabilizationForce)
    } else if (velocityY < -this.maxEntrySpeed) {
      this.playerBody.setVelocityY(velocityY + this.stabilizationForce)
    }
  }

  private onFloatingAreaStay(): void {
    if (!this.playerBody) return

    this.oscillationPhase += this.oscillationSpeed
    const oscillationOffset = Math.sin(this.oscillationPhase) * this.oscillationRange

    const newY = Phaser.Math.Clamp(
      this.playerBody.y + oscillationOffset,
      this.colliderTop,
      this.colliderBottom
    )

    this.playerBody.y = newY
    this.playerBody.setVelocityY(Phaser.Math.Clamp(oscillationOffset, -this.oscillationRange, 0))
  }

  private onFloatingAreaExit(): void {
    if (!this.playerBody) return

    this.playerBody.setVelocityY(0)
  }

  private handleDefaultPlatformInteraction(platform: BasePlatform): void {
    this.scene.physics.world.separate(
      this.playerBody,
      platform.image!.body as Phaser.Physics.Arcade.Body
    )

    const playerBottom = this.playerBody.bottom
    const platformTop = platform.image!.getBounds().top

    if (this.playerSprite!.body!.blocked.down) {
      if (Math.abs(playerBottom - platformTop) <= 5) {
        this.playerStateMachine.updateStateBasedOnInput(PlayerStates.Jump)
        this.playerController.jump(
          this.playerController.getSelectedJumpVelocity(PlayerConsts.JUMP_VELOCITY)
        )
        platform.interact(this.playerBody.position)
      }
    }
  }

  private handleWreckablePlatformInteraction(platform: WreckablePlatform): void {
    const playerBottom = this.playerSprite.getBottomCenter().y
    const platformTop = platform.image!.y - platform.image!.displayHeight * platform.image!.originY
    const threshold = PlayerConsts.WRECKABLE_DESTRUCTION_THRESHHOLD

    if (this.playerBody.velocity?.y > 0 && playerBottom <= platformTop + threshold) {
      this.playerSprite.setVelocityY(this.playerController.getPlayerVelocityY())
      platform.interact(this.playerBody.position)
    }
  }

  private handleSpikePlatformInteraction(platform: SpikedPlatform): void {
    this.scene.physics.world.separate(
      this.playerBody,
      platform.image!.body as Phaser.Physics.Arcade.Body
    )

    if (!this.playerSprite!.body!.blocked.down) return

    if (platform.IsSpiked()) {
      this.playerController.enablePlayerCollision(false)
      setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
      this.playerController.getPlayerStateMachine().updateStateBasedOnInput(PlayerStates.Death)
      this.playerController.animatePlayerDeath()
    } else {
      this.handleDefaultPlatformInteraction(platform)
    }
  }

  private handleBoosterPlatformInteraction(platform: BoosterPlatform): void {
    this.scene.physics.world.separate(
      this.playerBody,
      platform.image!.body as Phaser.Physics.Arcade.Body
    )
    this.playerController.jump(BoostersConsts.SMALL_JUMP_VELOCITY)
    platform.interact(this.playerBody.position)
  }

  private handleIcePlatformInteraction(platform: IcePlatform): void {
    this.scene.physics.world.separate(
      this.playerBody,
      platform.image!.body as Phaser.Physics.Arcade.Body
    )
    if (!this.playerSprite!.body!.blocked.up) {
      this.handleDefaultPlatformInteraction(platform)
      return
    }

    this.playerController.enablePlayerCollision(false)
    setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
    this.playerController.getPlayerStateMachine().updateStateBasedOnInput(PlayerStates.Death)
    this.playerController.animatePlayerDeath()
  }

  private handleTrapPlatformInteraction(platform: IcePlatform): void {
    this.scene.physics.world.separate(
      this.playerBody,
      platform.image!.body as Phaser.Physics.Arcade.Body
    )
    if ((platform as any).isTrap == true) {
      this.playerController.enablePlayerCollision(false)
      setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
      this.playerController.getPlayerStateMachine().updateStateBasedOnInput(PlayerStates.Death)
      this.playerController.animatePlayerDeath()
      return
    } else {
      this.handleDefaultPlatformInteraction(platform)
    }
  }

  addWithoutRoundUp(a: number, b: number, decimals: number): number {
    const factor = Math.pow(10, decimals)
    const sum = a + b
    const epsilon = 1e-7
    return Math.floor((sum + epsilon) * factor) / factor
  }

  strictTruncate(num: number, decimals: number): number {
    const factor = Math.pow(10, decimals)
    const epsilon = 1e-7
    return Math.floor((num + epsilon) * factor) / factor
  }

  onCollisionWithCollectable(collectable: CollectableItemBase, pos: Vector2 | undefined): void {
    if (pos == undefined) return
    if (collectable.isCollected) return
    if (this.playerSprite!.body!.blocked.down) {
      this.playerSprite.setVelocityY(this.playerController.getPlayerVelocityY())
    }

    collectable.collect(pos)
    let animationValue = collectable.getAmount().toString()

    if (collectable.getType() == EntityType.CollectibleTicket) {
      const ticketValue = collectable.getAmount() * this.playerController.ticketsMultiplier
      this.playerController.ticketsScore += ticketValue
      animationValue = ticketValue.toString()
      this.uiEventBus.emit(GAME_EVENTS.UPDATE_TICKETS_SCORE, this.playerController.ticketsScore)
      const passedTutor = getPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity)
      if (passedTutor) return
      const tutorStatTracker = TutorialAnalyticsDataCollector.getInstance()
      tutorStatTracker.setFirstTicketScore(this.playerController.getCurrentScore())
    } else if (collectable.getType() === EntityType.CollectibleTon) {
      const tonValue = collectable.getAmount()
      this.playerController.tonScore = this.addWithoutRoundUp(
        this.playerController.tonScore,
        tonValue,
        4
      )
      animationValue = this.strictTruncate(tonValue, 4).toString()
      this.uiEventBus.emit(GAME_EVENTS.UPDATE_TON, this.playerController.tonScore)
      const collectableSprite = collectable.getSprite()
      this.playPickupAnimation(collectableSprite, {
        ...this.getTonAnimationConfig(),
        value: '+ ' + animationValue
      })
    } else if (collectable.getType() == EntityType.CollectibleCustomCoin) {
      const coinValue = collectable.getAmount()
      this.playerController.customCoinScore = parseFloat(
        (this.playerController.customCoinScore + coinValue).toFixed(4)
      )
      animationValue = this.strictTruncate(coinValue, 4).toString()
      if (collectable.getCustomCoinType() == 2) {
        this.uiEventBus.emit(
          GAME_EVENTS.UPDATE_CUSTOM_COIN_SCORE,
          this.playerController.customCoinScore
        )
      }

      if (collectable.getCustomCoinType() == 1) {
        this.uiEventBus.emit(
          GAME_EVENTS.UPDATE_BATTLE_COIN_SCORE,
          this.playerController.customCoinScore
        )
      }

      const collectableSprite = collectable.getSprite()
      this.playPickupAnimation(collectableSprite, {
        ...this.getCustomCoinAnimationConfig(),
        value: '+ ' + animationValue
      })
    } else if (collectable.getType() == EntityType.CollectibleDynamicCoin) {
      const coinValue = collectable.getAmount()
      this.playerController.dynamicCoinScore = parseFloat(
        (this.playerController.dynamicCoinScore + coinValue).toFixed(4)
      )
      animationValue = this.strictTruncate(coinValue, 4).toString()
      this.uiEventBus.emit(
        GAME_EVENTS.UPDATE_DYNAMIC_COIN_SCORE,
        this.playerController.dynamicCoinScore
      )
      const collectableSprite = collectable.getSprite()
      this.playPickupAnimation(collectableSprite, {
        ...this.getCustomCoinAnimationConfig(),
        value: '+ ' + animationValue
      })
    } else if (collectable.getType() == EntityType.CollectiblePuzzleCoin) {
      const coinValue = collectable.getAmount()
      this.playerController.fragmentCoinScore = parseFloat(
        (this.playerController.fragmentCoinScore + coinValue).toFixed(4)
      )
      animationValue = this.strictTruncate(coinValue, 4).toString()
      this.uiEventBus.emit(
        GAME_EVENTS.UPDATE_FRAGMENT_SCORE,
        this.playerController.fragmentCoinScore
      )
      const collectableSprite = collectable.getSprite()
      this.playPickupAnimation(collectableSprite, {
        ...this.getCustomCoinAnimationConfig(),
        value: '+ ' + animationValue
      })
    }
  }

  private getTicketAnimationConfig(): AnimationConfig {
    return TicketPickupAnimationConsts
  }
  private getTonAnimationConfig(): AnimationConfig {
    return TonPickupAnimationConsts
  }

  private getCustomCoinAnimationConfig(): AnimationConfig {
    return CustomCoinAnimationConfig
  }

  protected playPickupAnimation(
    collectableSprite: Phaser.GameObjects.Sprite,
    config: AnimationConfig
  ): void {
    const spriteCenterX = collectableSprite.x + config.spawnOffsetX
    const spriteCenterY = collectableSprite.y + config.spawnOffsetY

    const text = this.scene.add.text(spriteCenterX, spriteCenterY, config.value, {
      font: `900 ${config.font}`,
      color: config.color,
      stroke: config.stroke,
      strokeThickness: config.strokeThickness
    })

    text.setOrigin(config.origin, config.origin)
    text.setDepth(config.depth)

    text.setScale(1)

    this.scene.tweens.add({
      targets: text,
      y: spriteCenterY - config.floatUpDistanceY,
      alpha: config.alpha,
      scale: 0.5,
      duration: config.duration,
      ease: config.ease,
      onUpdate: (tween, target) => {},
      onComplete: () => {
        text.destroy()
      }
    })
  }

  clearEventSubscriptions() {
    this.scene.events.off(GAME_EVENTS.FLOATING_AREA_STAY)
    this.scene.events.off('pause', this.handleGamePause, this)
  }
}
