import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'

export class JumpPadState implements IPlayerState {
  getState(): PlayerStates {
    return PlayerStates.JumpPad
  }
  enter(machine: PlayerStateMachine): void {
    machine.spineObject.animationState.timeScale = 0.9

    const animationName = 'Jump_spring_start'

    machine.spineObject?.animationState.setAnimation(2, animationName, false)

    machine.getScene().time.delayedCall(700, () => {
      machine.spineObject?.scene.events.emit('jumppad-animation-complete')
    })
  }

  execute(machine: PlayerStateMachine): void {}

  exit(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.clearTrack(1)
    machine.spineObject.animationState.timeScale = 1
  }
}
