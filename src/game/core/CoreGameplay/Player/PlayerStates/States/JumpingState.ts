import type { SpineGameObject } from '@esotericsoftware/spine-phaser'
import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'
import { PlayerAnimations } from './SpineAnimations'

export class JumpingState implements IPlayerState {
  getState(): PlayerStates {
    return PlayerStates.Jump
  }

  enter(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.setAnimation(0, PlayerAnimations.IDLE, true)
    machine.spineObject?.animationState.addAnimation(1, PlayerAnimations.JUMP_BASIC_START, false)
  }

  execute(machine: PlayerStateMachine): void {}

  exit(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.clearTrack(1)
    machine.spineObject.animationState.timeScale = 1
  }

  private changeAnimationSpeed(speed: number, spine: SpineGameObject) {
    spine.animationState.timeScale = speed
  }
}
