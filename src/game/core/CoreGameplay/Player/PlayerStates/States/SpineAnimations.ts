export class PlayerAnimations {
  public static readonly DEATH: string = 'death'
  public static readonly IDLE: string = 'IDLE'
  public static readonly JUMP_BASIC_START: string = 'Jump_basic_start'
  public static readonly JUMP_END: string = 'Jump_end'
  public static readonly JUMP_SPRING_START: string = 'Jump_spring_start'
  public static readonly JUMP_TRAMPOLINE_START: string = 'Jump_trampoline_start'
  public static readonly CHAINSAW_IDLE: string = 't0_idle'

  public static readonly JUMP_JET_END: string = 'Jump_jet_end'
  public static readonly JUMP_JET_IDLE: string = 'Jump_jet_idle'
  public static readonly JUMP_JET_START: string = 'Jump_jet_start'

  public static readonly JUMP_PROPELLER_END: string = 'Jump_Propeller_end'
  public static readonly JUMP_PROPELLER_IDLE: string = 'Jump_propeller_idle'
  public static readonly JUMP_PROPELLER_START: string = 'Jump_propeller_start'

  public static readonly X_OFFSET: number = 0
  public static readonly Y_OFFSET: number = 42
  public static readonly SCALE: number = 0.45
}

export class RostyAnimations {
  // pink mob horizontal middle
  public static readonly IDLE_TEST: string = '.wip_Test'
  public static readonly IDLE: string = 't1_IDLE'
  public static readonly IDLE_LOOKING_DOWN: string = 't1_IDLE2'
  public static readonly KICKING_AIR: string = 't1_Reaction'
  public static readonly KICKING_AIR2: string = 't1_Reaction2'
  public static readonly DIE: string = 't1_Death'
  public static readonly DIE_2: string = 't1_Death2'

  public static readonly EYE_DOWN: string = 't2_eye_down'
  public static readonly EYE_LEFT: string = 't2_eye_left'
  public static readonly EYE_LEFT_DOWN: string = 't2_eye_left-down'
  public static readonly EYE_RIGHT: string = 't2_eye_right'
  public static readonly EYE_RIGHT_DOWN: string = 't2_eye_right-down'

  public static readonly OUTLINE_COLOR_1: string = 't0_000000'
  public static readonly OUTLINE_COLOR_2: string = 't0_405c80'
  public static readonly Y_OFFSET: number = 50
  public static readonly SCALE: number = 0.5
}

export class SigmaAnimations {
  // purple mob dynamic horizontal verical
  public static readonly BLINKING: string = 'animation'
  public static readonly X_OFFSET: number = 45
  public static readonly Y_OFFSET: number = 140
  public static readonly SCALE: number = 0.5
}

export class SlippyAnimations {
  //mob static orange
  public static readonly BLUE_OUTLINE: string = 't0_405c80'
  public static readonly T1_DEATH: string = 't1_Death'
  public static readonly T1_DEATH2: string = 't1_Death2'
  public static readonly T1_HIT: string = 't1_Hit'
  public static readonly T1_HIT2: string = 't1_Hit2'
  public static readonly T1_IDLE: string = 't1_IDLE'
  public static readonly T1_IDLE2: string = 't1_IDLE2'
  public static readonly T1_IDLE3: string = 't1_IDLE3'

  public static readonly X_OFFSET: number = 78
  public static readonly Y_OFFSET: number = 50
}

export class SimpyAnimations {
  //green mob horizontal small
  public static readonly DEATH: string = 't1_Death'
  public static readonly DEATH_2: string = 't1_Death2'
  public static readonly IDLE: string = 't1_IDLE'
  public static readonly IDLE_2: string = 't1_IDLE2'
  public static readonly REACTION: string = 't1_reaction'
  public static readonly OUTLINE_COLOR_1: string = 't0_000000'
  public static readonly OUTLINE_COLOR_2: string = 't0_405c80'
  public static readonly X_OFFSET: number = 60
  public static readonly Y_OFFSET: number = 52
  public static readonly SCALE: number = 0.5
}

export class SunnyAnimations {
  public static readonly OUTLINE_COLOR = 't0_405c80'

  public static readonly DEATH = 't1_Death'
  public static readonly IDLE = 't1_IDLE'
  public static readonly IDLE_2 = 't1_IDLE2'
  public static readonly REACTION = 't1_Reaction'

  public static readonly X_OFFSET = 38
  public static readonly Y_OFFSET = 34
  public static readonly SCALE = 0.5
}

export class BlackHoleAnimations {
  public static readonly T0_IDLE: string = 't0_IDLE'
  public static readonly T1_IDLE: string = 't1_IDLE'
  public static readonly T2_IDLE: string = 't2_IDLE'

  public static readonly X_OFFSET: number = 62
  public static readonly Y_OFFSET: number = 62
}

export class UFOAnimations {
  public static readonly BLUE_OUTLINE: string = 't0_405c80'
  public static readonly T1_Death: string = 't1_Death'
  public static readonly T1_IDLE: string = 't1_IDLE'
  public static readonly T1_IDLE2: string = 't1_IDLE2'
  public static readonly T1_IDLE3: string = 't1_IDLE3'
  public static readonly T1_IDLE_OLD: string = 't1_IDLE_old'
  public static readonly T1_IDLE_OLD2: string = 't1_IDLE_old2'
  public static readonly T1_Reaction: string = 't1_Reaction'
  public static readonly T1_Reaction_OLD: string = 't1_Reaction_old'

  public static readonly X_OFFSET: number = 79
  public static readonly Y_OFFSET: number = 35
}

export class FlegmaFlexAnimations {
  //red static vertical
  public static readonly BLUE_OUTLINE: string = 't0_405c80'
  public static readonly T1_DEATH: string = 't1_Death'
  public static readonly T1_HIT: string = 't1_Hit'
  public static readonly T1_IDLE: string = 't1_IDLE'
  public static readonly T1_IDLE2: string = 't1_IDLE2'
  public static readonly T1_IDLE3: string = 't1_IDLE3'

  public static readonly X_OFFSET: number = 45
  public static readonly Y_OFFSET: number = 155
}

export class TicketAnimations {
  public static readonly T1_RAYS: string = 't1_rays'
  public static readonly T2_IDLE: string = 't2_idle'
  public static readonly T2_PICK_UP: string = 't2_pick_up'

  public static readonly X_OFFSET: number = 25
  public static readonly Y_OFFSET: number = 10
  public static readonly SCALE: number = 0.7
}

export class CoinAnimations {
  public static readonly T0_DISAPPEAR: string = 't0_Disappear'
  public static readonly T0_IDLE: string = 't0_IDLE'
  public static readonly T1_IDLE_R: string = 't1_IDLE_R'

  public static readonly SKIN_DEFAULT: string = 'default'
  public static readonly SKIN_TON: string = 'ton'
  public static readonly SKIN_UNI: string = 'uni'
  public static readonly SKIN_USDT: string = 'usdt'

  public static readonly X_OFFSET: number = 25
  public static readonly Y_OFFSET: number = 25
  public static readonly SCALE: number = 0.5
}

export class MobDataSetKeys {
  public static readonly BLACKHOLE_DATA: string = 'blackhole-data'
  public static readonly BLACKHOLE_ATLAS: string = 'blackhole-atlas'
  public static readonly UNICORN_NEW_DATA: string = 'unicorn-new-data'
  public static readonly UNICORN_NEW_ATLAS: string = 'unicorn-new-atlas'
  public static readonly STAR_DATA: string = 'star-data'
  public static readonly STAR_ATLAS: string = 'star-atlas'
  public static readonly TICKET_DATA: string = 'ticket-data'
  public static readonly TICKET_ATLAS: string = 'ticket-atlas'
  public static readonly PORTAL_DATA: string = 'portal-data'
  public static readonly PORTAL_ATLAS: string = 'portal-atlas'
  public static readonly TON_COIN_DATA: string = 'coin-data'
  public static readonly TON_COIN_ATLAS: string = 'coin-atlas'
  public static readonly SIMPY_DATA: string = 'simpy-data'
  public static readonly SIMPY_ATLAS: string = 'simpy-atlas'
  public static readonly SIGMA_DATA: string = 'sigma-data'
  public static readonly SIGMA_ATLAS: string = 'sigma-atlas'
  public static readonly ROSTY_DATA: string = 'rosty-data'
  public static readonly ROSTY_ATLAS: string = 'rosty-atlas'
  public static readonly FLEGMA_FLEX_DATA: string = 'flegma-data'
  public static readonly FLEGMA_FLEX_ATLAS: string = 'flegma-atlas'
  public static readonly SLIPPY_DATA: string = 'slippy-data'
  public static readonly SLIPPY_ATLAS: string = 'slippy-atlas'
  public static readonly UFO_DATA: string = 'ufo-data'
  public static readonly UFO_ATLAS: string = 'ufo-atlas'
  public static readonly SUNNY_DATA: string = 'sunny-data'
  public static readonly SUNNY_ATLAS: string = 'sunny-atlas'
}
