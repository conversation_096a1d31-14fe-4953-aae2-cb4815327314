import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'
import { PlayerAnimations } from './SpineAnimations'

export class IdleState implements IPlayerState {
  getState(): PlayerStates {
    return PlayerStates.Idle
  }

  enter(machine: PlayerStateMachine): void {
    machine.spineObject.animationState.setAnimation(0, PlayerAnimations.IDLE, true)
  }

  execute(machine: PlayerStateMachine): void {}

  exit(machine: PlayerStateMachine): void {
    machine.spineObject.animationState.setAnimation(0, PlayerAnimations.IDLE, true)
  }
}
