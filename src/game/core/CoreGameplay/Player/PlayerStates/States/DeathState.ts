import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'
import { PlayerAnimations } from './SpineAnimations'

export class DeathState implements IPlayerState {
  getState(): PlayerStates {
    return PlayerStates.Death
  }

  enter(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.setAnimation(0, PlayerAnimations.DEATH, true)
  }

  execute(machine: PlayerStateMachine): void {}

  exit(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.clearTracks()
  }
}
