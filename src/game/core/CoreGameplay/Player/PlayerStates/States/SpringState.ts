import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'

export class SpringState implements IPlayerState {
  getState(): PlayerStates {
    return PlayerStates.Spring
  }
  enter(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.setAnimation(1, 'Jump_trampoline_start', false)

    // machine.spineObject.animationState.addListener({
    //   complete: entry => {
    //   }
    // })
  }

  execute(machine: PlayerStateMachine): void {}

  exit(machine: PlayerStateMachine): void {
    machine.spineObject?.animationState.clearTrack(1)
    machine.spineObject.animationState.timeScale = 1
  }
}
