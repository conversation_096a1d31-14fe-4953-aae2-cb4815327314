import { Physics, SpineGameObject } from '@esotericsoftware/spine-phaser'
import { PlayerStateMachine } from '../PlayerStateMachine'
import type { IPlayerState } from './IPlayerState'
import { PlayerStates } from './PlayerStates'
import { PlayerAnimations } from './SpineAnimations'

export class PropellerState implements IPlayerState {
  private previousPosition: { x: number; y: number } = { x: 0, y: 0 }

  getState(): PlayerStates {
    return PlayerStates.Propeller
  }

  enter(machine: PlayerStateMachine): void {
    const { spineObject } = machine
    spineObject.animationState.timeScale = 1

    spineObject?.animationState.clearTracks()
    spineObject?.skeleton.updateWorldTransform(Physics.none)

    spineObject?.animationState.setAnimation(1, PlayerAnimations.JUMP_PROPELLER_START, false)
    spineObject?.animationState.addAnimation(1, PlayerAnimations.JUMP_PROPELLER_IDLE, true)
    spineObject?.animationState.addAnimation(2, PlayerAnimations.CHAINSAW_IDLE, true)

    this.restoreFacingDirection(machine, spineObject)
  }

  private restoreFacingDirection(machine: PlayerStateMachine, spineObject: SpineGameObject) {
    if (machine.playerFacingDirection === -1 && spineObject.scaleX > 0) {
      spineObject.scaleX = -spineObject.scaleX
    } else if (machine.playerFacingDirection === 1 && spineObject.scaleX < 0) {
      spineObject.scaleX = -spineObject.scaleX
    }
  }

  execute(machine: PlayerStateMachine): void {
    const { spineObject } = machine

    const currentPosition = {
      x: spineObject.x,
      y: spineObject.y
    }

    const deltaX = currentPosition.x - this.previousPosition.x
    const deltaY = currentPosition.y - this.previousPosition.y

    if (deltaX !== 0 || deltaY !== 0) {
      const pointBefore = { x: this.previousPosition.x, y: this.previousPosition.y }
      const pointAfter = { x: currentPosition.x, y: currentPosition.y }

      spineObject?.phaserWorldCoordinatesToSkeleton(pointBefore)
      spineObject?.phaserWorldCoordinatesToSkeleton(pointAfter)

      spineObject?.skeleton.physicsTranslate(
        pointAfter.x - pointBefore.x,
        pointAfter.y - pointBefore.y
      )

      this.previousPosition = { ...currentPosition }
    }
  }

  exit(machine: PlayerStateMachine): void {
    const { spineObject } = machine
    spineObject.animationState.timeScale = 1
    const animationName = PlayerAnimations.JUMP_PROPELLER_END

    spineObject?.animationState.setAnimation(1, animationName, false)

    this.restoreFacingDirection(machine, spineObject)
    spineObject?.skeleton.updateWorldTransform(Physics.none)
    spineObject?.scene.events.emit('booster-animation-finished')
  }
}
