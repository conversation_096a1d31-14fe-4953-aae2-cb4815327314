import { SpineGameObject } from '@esotericsoftware/spine-phaser'
import { DeathState } from './States/DeathState'
import type { IPlayerState } from './States/IPlayerState'
import { IdleState } from './States/IdleState'
import { JetpackState } from './States/JetpackState'
import { JumpPadState } from './States/JumpPadState'
import { JumpingState } from './States/JumpingState'
import { PlayerStates } from './States/PlayerStates'
import { PropellerState } from './States/PropellerState'
import { SpringState } from './States/SpringState'

export class PlayerStateMachine {
  private currentState: IPlayerState
  private readonly scene!: Phaser.Scene
  public spineObject: SpineGameObject
  public playerFacingDirection!: number
  public playerXVelocity!: number
  public defaultSpineScale!: number

  constructor(spineObject: SpineGameObject, scene: Phaser.Scene) {
    this.spineObject = spineObject
    this.scene = scene
    this.currentState = new IdleState()
    this.defaultSpineScale = spineObject.scale
    this.currentState.enter(this)
  }

  updateSpine(newSpine: SpineGameObject) {
    this.spineObject = newSpine
  }

  changeState(newState: IPlayerState): void {
    if (!this.spineObject || !this.spineObject.scene) {
      console.warn('Cannot change state because spineObject or its scene is undefined.')
      return
    }

    this.currentState.exit(this)
    this.currentState = newState
    this.currentState.enter(this)
  }

  updateStateBasedOnInput(currentState: PlayerStates): void {
    switch (currentState) {
      case PlayerStates.Jump:
        this.changeState(new JumpingState())
        break

      case PlayerStates.JumpPad:
        this.changeState(new JumpPadState())
        break

      case PlayerStates.Death:
        this.changeState(new DeathState())
        break

      case PlayerStates.Idle:
        this.changeState(new IdleState())
        break

      case PlayerStates.Jetpack:
        this.changeState(new JetpackState())
        break

      case PlayerStates.Propeller:
        this.changeState(new PropellerState())
        break

      case PlayerStates.Spring:
        this.changeState(new SpringState())
        break
    }
  }

  getCurrentStateType(): PlayerStates {
    switch (this.currentState.constructor) {
      case JumpingState:
        return PlayerStates.Jump
      case JumpPadState:
        return PlayerStates.JumpPad
      case DeathState:
        return PlayerStates.Death
      case IdleState:
        return PlayerStates.Idle
      case JetpackState:
        return PlayerStates.Jetpack
      case PropellerState:
        return PlayerStates.Propeller
      case SpringState:
        return PlayerStates.Spring
      default:
        throw new Error('Unknown Player State')
    }
  }

  getScene(): Phaser.Scene {
    return this.scene
  }

  update(facingDirection: number): void {
    this.playerFacingDirection = facingDirection
    this.currentState.execute(this)
  }
}
