import { BlackHole } from '@/game/core/CoreGameplay/Enemies/BlackHole'
import { UFO } from '@/game/core/CoreGameplay/Enemies/UFO'
import Phaser from 'phaser'
import { DepthOrder } from '../../Constants/DephOrdering'
import { Attribute, getPlayerAttribute, setPlayerAttribute } from '../PlayerAttributeContainer'

export class BubbleManager {
  private scene: Phaser.Scene
  private player: Phaser.GameObjects.Sprite
  private bubble: Phaser.GameObjects.Image
  private maxContacts: number = 1
  private remainingContacts: number = 1
  private playerEntity: number

  constructor(scene: Phaser.Scene, player: Phaser.GameObjects.Sprite, playerEntity: number) {
    this.scene = scene
    this.player = player
    this.playerEntity = playerEntity
    this.bubble = this.scene.add.image(player.x, player.y, 'bubble')
    this.bubble.setDepth(DepthOrder.Player + 3)
    this.bubble.setDisplaySize(150, 150)
    this.bubble.setVisible(false)
  }

  public activate(): void {
    setPlayerAttribute(Attribute.isBubbleActive, this.playerEntity, true)
    this.remainingContacts = this.maxContacts
    this.bubble.setVisible(true)
  }

  public deactivate(): void {
    setPlayerAttribute(Attribute.isBubbleActive, this.playerEntity, false)
    this.bubble.setVisible(false)
    this.scene.events.emit('bubbleDestroyed', this.player)
  }

  public showBubbleVisuals(show: boolean): void {
    if (!this.bubble) return
    this.bubble.setVisible(show)
  }

  public reEnableBubbleVisuals(): void {
    if (!this.bubble) return
    if (this.remainingContacts >= 1) {
      this.bubble.setVisible(true)
    }
  }

  public onContact(mob: any): boolean {
    if (!getPlayerAttribute(Attribute.isBubbleActive, this.playerEntity)) return false
    if (mob instanceof BlackHole || mob instanceof UFO) return false
    this.remainingContacts--
    if (this.remainingContacts <= 0) {
      this.deactivate()
    }
    return true
  }

  public update(): void {
    if (getPlayerAttribute(Attribute.isBubbleActive, this.playerEntity)) {
      this.bubble.x = this.player.x
      this.bubble.y = this.player.y
    }
  }

  public isActive(): boolean {
    return getPlayerAttribute(Attribute.isBubbleActive, this.playerEntity)
  }
}
