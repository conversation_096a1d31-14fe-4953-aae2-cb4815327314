import { InputConsts } from '@/game/core/CoreGameplay/Constants/PlayerConsts'
import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import { Enemy } from '@/game/core/ecs/Components/EntityComponents'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import { type GameScene } from '@/game/core/scenes/GameScene'
import { GAME_EVENTS } from '@/shared/constants/uiEvents.ts'
import { Vector2 } from '@esotericsoftware/spine-core'
import type { PlayerShootingHandler } from '../PlayerShootingHandler'
import type { StarterParams } from './StartersManager'

export class AimBotManager {
  private enabled = false
  private targets: Map<BaseEnemy, number> = new Map()
  private lastTapTime = 0
  private isGyroOn: boolean

  private readonly aimBotTapListener: (e: PointerEvent) => void
  private readonly canvas: HTMLCanvasElement

  constructor(
    private scene: GameScene,
    private playerShootingHandler: PlayerShootingHandler,
    private params: StarterParams
  ) {
    this.canvas = this.scene.sys.canvas
    this.aimBotTapListener = this.handleAimBotTap.bind(this)
    this.isGyroOn = this.scene.IsGyroscopeControlMethod
    this.enable()
  }

  private handleAimBotTap(e: PointerEvent): void {
    if (!this.enabled) return

    if (this.isGyroOn) {
      if (this.targets.size === 0) {
        this.playerShootingHandler.shoot(e)
      } else {
        this.processTargets()
      }
      return
    }

    const now = performance.now()
    if (now - this.lastTapTime > InputConsts.DOUBLE_TAP_THRESHOLD) {
      this.lastTapTime = now
      return
    }
    this.lastTapTime = 0

    if (this.targets.size === 0) {
      this.playerShootingHandler.shoot(e)
    } else {
      this.processTargets()
    }
  }

  public toggleAimBot(): void {
    this.enabled ? this.disable() : this.enable()
  }

  public enable(): void {
    if (!this.enabled) {
      this.enabled = true

      this.playerShootingHandler.removeListener()

      this.canvas.addEventListener('pointerdown', this.aimBotTapListener)
      this.scene.events.on('aimbot:shoot', this.handleAimBotEvent, this)
    }
  }

  public disable(): void {
    if (this.enabled) {
      this.enabled = false

      this.canvas.removeEventListener('pointerdown', this.aimBotTapListener)
      this.scene.events.off('aimbot:shoot', this.handleAimBotEvent, this)

      this.playerShootingHandler.addListener()
    }
  }

  private handleAimBotEvent(mob: BaseEnemy): void {
    if (mob.entityType() === EntityType.MobBlackHole) return
    if (this.targets.has(mob)) return

    const shots = mob.getHealth() + 1
    this.targets.set(mob, shots)
  }

  private processTargets(): void {
    const validEnemies = Array.from(this.targets.entries())
      .filter(([mob, shots]) => this.isValidTarget(mob, shots))
      .map(([mob]) => mob)

    if (validEnemies.length === 0) {
      this.playerShootingHandler.shoot()
      return
    }

    const playerPos = this.getPlayerColliderCenter()

    validEnemies.sort((a, b) => {
      const aPos = a.getSpriteColliderCenter()
      const bPos = b.getSpriteColliderCenter()
      const distA = Phaser.Math.Distance.Between(playerPos.x, playerPos.y, aPos.x, aPos.y)
      const distB = Phaser.Math.Distance.Between(playerPos.x, playerPos.y, bPos.x, bPos.y)
      return distA - distB
    })

    const closest = validEnemies[0]
    const { x, y } = closest.getSpriteColliderCenter()
    this.playerShootingHandler.shootAt(x, y, true)

    const remaining = this.targets.get(closest)! - 1
    if (remaining <= 0) {
      this.targets.delete(closest)
    } else {
      this.targets.set(closest, remaining)
    }
  }

  private getPlayerColliderCenter(): Vector2 {
    const playerSprite = this.scene.getPlayer()
    const bounds = playerSprite?.getBounds()
    const centerX = bounds?.centerX
    const centerY = bounds?.centerY
    return new Vector2(centerX, centerY)
  }

  private isValidTarget(mob: BaseEnemy, shots: number): boolean {
    if (mob.entityType() === EntityType.MobBlackHole) return false
    if (mob.getHealth() <= 0 || Enemy.isDefeated[mob.entity] !== 0) return false
    if (shots <= 0) return false
    return true
  }

  public checkTimerExpiry(): void {
    if (this.params.timerBased && Date.now() / 1000 >= this.params.timeToFinish && this.enabled) {
      this.scene.events.emit(GAME_EVENTS.BOOSTER_ENDED, {
        boosterType: 'timeBoundAimbot'
      })
      console.info('AimBot timer expired. Disabling aim bot.')
      this.disable()
    }
  }
}
