import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering.ts'
import { GAME_EVENTS } from '@/shared/constants/uiEvents.ts'
import type { EventBus } from '@/shared/types'
import { AtlasNames } from '../../Constants/GameViewConsts'
import type { StarterParams } from './StartersManager'

export class MagnetFieldManager {
  private scene: Phaser.Scene
  private playerSprite: Phaser.Physics.Arcade.Sprite
  private magnetCollider!: Phaser.Physics.Arcade.Body
  private magnetSprite!: Phaser.GameObjects.Image
  private params: StarterParams
  private uiEventBus: EventBus
  private magnetActive = false

  constructor(
    scene: Phaser.Scene,
    playerSprite: Phaser.Physics.Arcade.Sprite,
    uiEventBus: EventBus,
    params: StarterParams
  ) {
    this.scene = scene
    this.playerSprite = playerSprite
    this.uiEventBus = uiEventBus
    this.params = params
    this.setUpMagnet()
    this.updateActivation()
  }

  private getUTCSeconds(): number {
    return Date.now() / 1000
  }

  private shouldBeActive(): boolean {
    return this.params.timerBased
      ? this.getUTCSeconds() < this.params.timeToFinish
      : this.params.chargeUsed
  }

  private updateActivation(): void {
    const active = this.shouldBeActive()
    if (!active && this.params.timerBased) {
      console.info('Magnet activation time has already expired')
    } else if (!active) {
      console.info('No magnet charges available, magnet will not be activated')
    }
    this.turnOnMagnet(active)
  }

  public turnOnMagnet(on: boolean): void {
    this.magnetActive = on
    console.info(`Magnet is now ${on ? 'on' : 'off'}`)
    if (this.magnetCollider) {
      this.magnetCollider.enable = on
    }
    if (this.magnetSprite) {
      this.magnetSprite.setVisible(on)
    }
  }

  public checkForMagnetActivation(): void {
    if (this.params.timerBased) {
      if (this.getUTCSeconds() < this.params.timeToFinish) {
        this.turnOnMagnet(true)
      } else {
        console.log('Magnet timer expired, disabling magnet')
        this.uiEventBus.emit(GAME_EVENTS.BOOSTER_ENDED, { boosterType: 'timeBoundMagneticField' })
        this.turnOnMagnet(false)
      }
    } else {
      this.turnOnMagnet(this.params.chargeUsed)
      if (!this.params.chargeUsed) {
        console.log('No magnet charges left, disabling magnet')
      }
    }
  }

  public update(): void {
    if (!this.magnetActive) return
    this.magnetCollider.x = this.playerSprite.x - this.magnetCollider.width / 2
    this.magnetCollider.y = this.playerSprite.y - this.magnetCollider.height / 2
    this.magnetSprite.x = this.playerSprite.x
    this.magnetSprite.y = this.playerSprite.y
    this.checkTimerExpiry()
  }

  private checkTimerExpiry(): void {
    if (this.params.timerBased && this.getUTCSeconds() >= this.params.timeToFinish) {
      console.info('Magnet activation time has expired')
      this.uiEventBus.emit(GAME_EVENTS.BOOSTER_ENDED, { boosterType: 'timeBoundMagneticField' })
      this.turnOnMagnet(false)
    }
  }

  public getMagnetCollider(): Phaser.Physics.Arcade.Body {
    return this.magnetCollider
  }

  private setUpMagnet(): void {
    const dummyObject = this.scene.add.circle(
      this.playerSprite.x,
      this.playerSprite.y,
      this.params.radius,
      0x000000,
      0
    )
    this.scene.physics.add.existing(dummyObject, false)
    const body = dummyObject.body as Phaser.Physics.Arcade.Body
    body.setCircle(this.params.radius)
    body.x = this.playerSprite.x - this.params.radius
    body.y = this.playerSprite.y - this.params.radius
    const image = this.scene.add.image(
      this.playerSprite.x,
      this.playerSprite.y,
      AtlasNames.EFFECTS,
      'magnet-big.png'
    )
    image
      .setDisplaySize(this.params.radius * 2, this.params.radius * 2)
      .setDepth(DepthOrder.Player + 1)
    this.magnetSprite = image
    this.magnetCollider = body
  }
}
