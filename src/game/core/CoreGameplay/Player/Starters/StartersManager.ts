import { boosterTypeToEnumValue, type GameScene } from '@/game/core/scenes/GameScene'
import type { StackableBoosterType } from '@/services/openapi'
import { cachedPlayerState } from '@/shared/storage/cachedPlayerState'
import { MagnetFieldConsts } from '../../Constants/MagnetConsts'
import type { PlayerShootingHandler } from '../PlayerShootingHandler'
import { AimBotManager } from './AimBotManager'
import { BootsManager } from './BootsManager'
import { BubbleManager } from './BubbleManager'
import { MagnetFieldManager } from './MagnetFieldManager'

function reverseMapBoosterNumbersToTypes(boosterNumbers: number[]): StackableBoosterType[] {
  return boosterNumbers.map(num => {
    const booster = (Object.keys(boosterTypeToEnumValue) as StackableBoosterType[]).find(
      key => boosterTypeToEnumValue[key] === num
    )
    if (!booster) {
      throw new Error(`No booster type found for number ${num}`)
    }
    return booster
  })
}

export class StartersManager {
  public magnetField?: MagnetFieldManager
  public bubbleManager?: BubbleManager
  public bootsManager?: BootsManager
  public aimBotManager?: AimBotManager

  private readonly playerEntity: number
  private readonly playerSprite: Phaser.Physics.Arcade.Sprite
  private readonly scene: GameScene
  private readonly bulletHandler?: PlayerShootingHandler

  constructor(
    scene: GameScene,
    playerSprite: Phaser.Physics.Arcade.Sprite,
    playerEntity: number,
    bulletHandler?: PlayerShootingHandler
  ) {
    this.scene = scene
    this.playerSprite = playerSprite
    this.playerEntity = playerEntity
    this.bulletHandler = bulletHandler
  }

  private safeExecute(action: () => void, errorMessage: string): void {
    try {
      action()
    } catch (error) {
      console.error(errorMessage, error)
    }
  }

  private safeExecuteWithReturn<T>(action: () => T, errorMessage: string, defaultValue?: T): T {
    try {
      const result = action()
      return result === undefined && defaultValue !== undefined ? defaultValue : result
    } catch (error) {
      console.error(errorMessage, error)
      return defaultValue!
    }
  }

  public setupStarters(): void {
    const usedBoosterNumbers = this.scene.getActiveSessionInfo()?.usedBoosters
    const usedBoosterTypes = usedBoosterNumbers
      ? reverseMapBoosterNumbersToTypes(usedBoosterNumbers)
      : []

    this.safeExecute(() => {
      const timeParams =
        cachedPlayerState?.playerState?.boostersView?.timeBoundMagneticFieldActiveTill
      const boosterKey: StackableBoosterType = 'stackableMagneticField'
      if (timeParams) {
        console.info(`Timer found – using timer-based MagneticField logic.`)
        this.magnetField = new MagnetFieldManager(
          this.scene,
          this.playerSprite,
          this.scene.UiEventBus,
          {
            radius: MagnetFieldConsts.MAGNETIC_FIELD_SIZE,
            timeToFinish: timeParams,
            timerBased: true,
            chargeUsed: false
          }
        )
      } else if (usedBoosterTypes.includes(boosterKey)) {
        console.info(`No timer found – MagneticField booster is active, using charge-based logic.`)
        this.magnetField = new MagnetFieldManager(
          this.scene,
          this.playerSprite,
          this.scene.UiEventBus,
          {
            radius: MagnetFieldConsts.MAGNETIC_FIELD_SIZE,
            timeToFinish: 0,
            timerBased: false,
            chargeUsed: true
          }
        )
      } else {
        console.info(`No MagneticField booster available.`)
      }
    }, 'Error during MagnetField setup:')

    this.safeExecute(() => {
      const timeParams = cachedPlayerState?.playerState?.boostersView?.timeBoundJumpersActiveTill
      const boosterKey: StackableBoosterType = 'stackableJumper'
      if (timeParams) {
        console.info(`Timer found – using timer-based Jumper logic.`)
        this.bootsManager = new BootsManager(this.scene, this.playerSprite, this.scene.UiEventBus, {
          radius: 0,
          timeToFinish: timeParams,
          timerBased: true,
          chargeUsed: false
        })
      } else if (usedBoosterTypes.includes(boosterKey)) {
        console.info(`No timer found – Jumper booster is active, using charge-based logic.`)
        this.bootsManager = new BootsManager(this.scene, this.playerSprite, this.scene.UiEventBus, {
          radius: 0,
          timeToFinish: 0,
          timerBased: false,
          chargeUsed: true
        })
      } else {
        console.info(`No Jumper booster available.`)
      }
    }, 'Error during Boots setup:')

    this.safeExecute(() => {
      if (!this.bulletHandler) {
        console.warn('bulletHandler is not available; cannot set up AimBotManager.')
        return
      }
      const timeParams = cachedPlayerState?.playerState?.boostersView?.timeBoundAimbotsActiveTill
      const boosterKey: StackableBoosterType = 'stackableAimbot'
      if (timeParams) {
        console.info(`Timer found – using timer-based Aimbot logic.`)
        this.aimBotManager = new AimBotManager(this.scene, this.bulletHandler, {
          radius: 0,
          timeToFinish: timeParams,
          timerBased: true,
          chargeUsed: false
        })
      } else if (usedBoosterTypes.includes(boosterKey)) {
        console.info(`No timer found – Aimbot booster is active, using charge-based logic.`)
        this.aimBotManager = new AimBotManager(this.scene, this.bulletHandler, {
          radius: 0,
          timeToFinish: 0,
          timerBased: false,
          chargeUsed: true
        })
      } else {
        console.info(`No Aimbot booster available.`)
      }
    }, 'Error during AimBot setup:')
  }

  public update(): void {
    this.safeExecute(() => this.magnetField?.update(), 'Error updating magnet field:')
    this.safeExecute(() => this.bubbleManager?.update(), 'Error updating bubble manager:')
    this.safeExecute(() => this.bootsManager?.update(), 'Error updating boots manager:')
    this.safeExecute(
      () => this.aimBotManager?.checkTimerExpiry(),
      'Error updating aim bot manager:'
    )
  }

  public getMagnetField(): Phaser.Physics.Arcade.Body {
    if (!this.magnetField) {
      throw new Error('MagnetFieldManager is not initialized.')
    }
    return this.safeExecuteWithReturn(
      () => this.magnetField!.getMagnetCollider(),
      'Error retrieving magnet collider:'
    )
  }

  public getSelectedJumpVelocity(defaultVelocity: number): number {
    if (!this.bootsManager) {
      return defaultVelocity
    }
    return this.safeExecuteWithReturn(
      () => this.bootsManager!.getJumpVelocity(defaultVelocity),
      'Error getting selected jump velocity:',
      defaultVelocity
    )
  }

  public toggleBoots(): void {
    if (!this.bootsManager) {
      console.error('BootsManager is not initialized. Cannot toggle boots.')
      return
    }
    this.safeExecute(() => this.bootsManager!.toggle(), 'Error toggling boots:')
  }

  public subscribeEvents(): void {
    this.safeExecute(() => {
      this.scene.events.on('boots-toggled', this.onBootsToggled)
      this.scene.events.on('aim-toggled', this.onAimToggled)
    }, 'Error subscribing to events:')
  }

  public unsubscribeEvents(): void {
    this.safeExecute(() => {
      this.scene.events.off('boots-toggled', this.onBootsToggled)
      this.scene.events.off('aim-toggled', this.onAimToggled)
    }, 'Error unsubscribing from events:')
  }

  public showBubble(show: boolean): void {
    if (!this.bubbleManager) {
      console.warn('BubbleManager is not initialized. Cannot change bubble visuals.')
      return
    }
    this.safeExecute(
      () => this.bubbleManager!.showBubbleVisuals(show),
      'Error showing/hiding bubble visuals:'
    )
  }

  public reEnableBubble(): void {
    if (!this.bubbleManager) {
      console.warn('BubbleManager is not initialized. Cannot re-enable bubble visuals.')
      return
    }
    this.safeExecute(
      () => this.bubbleManager!.reEnableBubbleVisuals(),
      'Error re-enabling bubble visuals:'
    )
  }

  public playBootsAnimation(): void {
    if (!this.bootsManager) return
    this.safeExecute(
      () => this.bootsManager!.playAnimationIfActive(),
      'Error playing boots animation:'
    )
  }

  public showBootsEffect(show: boolean): void {
    if (!this.bootsManager) return
    this.safeExecute(() => this.bootsManager!.showEffect(show), 'Error showing boots effect:')
  }

  private onAimToggled = (): void => {
    if (this.aimBotManager) {
      this.safeExecute(() => this.aimBotManager!.toggleAimBot(), 'Error toggling AimBotManager:')
    } else {
      console.warn('AimBotManager is not available to toggle.')
    }
  }

  private onBootsToggled = (): void => {
    this.toggleBoots()
  }
}

export interface StarterParams {
  radius: number
  timeToFinish: number
  timerBased: boolean
  chargeUsed: boolean
}
