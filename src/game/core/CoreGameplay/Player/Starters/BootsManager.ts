import { GAME_EVENTS } from '@/shared/constants/uiEvents.ts'
import type { EventBus } from '@/shared/types'
import { BoostersConsts } from '../../Constants/BoostersConsts'
import { DepthOrder } from '../../Constants/DephOrdering'
import { AtlasNames } from '../../Constants/GameViewConsts'
import type { StarterParams } from './StartersManager'

export class BootsManager {
  private active = false
  private effectSprite?: Phaser.GameObjects.Sprite
  private params: StarterParams
  private uiEventBus: EventBus

  constructor(
    private scene: Phaser.Scene,
    private playerSprite: Phaser.Physics.Arcade.Sprite,
    uiEventBus: EventBus,
    params: StarterParams
  ) {
    this.params = params
    this.uiEventBus = uiEventBus
    this.updateActivation()
  }

  private getCurrentTime(): number {
    return Date.now() / 1000
  }

  private shouldBeActive(): boolean {
    return this.params.timerBased
      ? this.getCurrentTime() < this.params.timeToFinish
      : this.params.chargeUsed
  }

  private updateActivation(): void {
    const desiredState = this.shouldBeActive()
    if (desiredState && !this.active) {
      this.activate()
    } else if (!desiredState && this.active) {
      if (this.params.timerBased) {
        this.uiEventBus.emit(GAME_EVENTS.BOOSTER_ENDED, { boosterType: 'timeBoundJumpers' })
      }
      this.deactivate()
    } else if (!this.active) {
      this.deactivate()
    }
  }

  public activate(): void {
    this.active = true
    this.createEffect()
  }

  public deactivate(): void {
    this.active = false
    this.removeEffect()
  }

  public toggle(): void {
    this.active = !this.active
    if (this.active) {
      this.createEffect()
    } else {
      this.removeEffect()
    }
  }

  public isActive(): boolean {
    return this.active
  }

  public getJumpVelocity(defaultVelocity: number): number {
    return this.active ? BoostersConsts.SMALL_JUMP_VELOCITY : defaultVelocity
  }

  public playAnimationIfActive(): void {
    if (this.active && this.effectSprite) {
      this.effectSprite.play('bootsAnim')
    }
  }

  public showEffect(show: boolean): void {
    if (show) {
      if (!this.active) return
      this.effectSprite?.setVisible(true)
    } else {
      this.effectSprite?.setVisible(false)
    }
  }

  public update(): void {
    if (!this.effectSprite) return
    this.effectSprite.x = this.playerSprite.x
    this.effectSprite.y = this.playerSprite.y + 35
    this.checkTimerExpiry()
  }

  public removeEffect(): void {
    if (this.effectSprite) {
      this.effectSprite.destroy()
      this.effectSprite = undefined
    }
  }

  private createEffect(): void {
    this.effectSprite = this.scene.add.sprite(
      this.playerSprite.x,
      this.playerSprite.y + 35,
      AtlasNames.EFFECTS,
      'Jumpers_effect-01.png'
    )
    this.effectSprite.setScale(0.5)
    this.effectSprite.setDepth(DepthOrder.Player - 1)
    this.effectSprite.setVisible(true)
  }

  private checkTimerExpiry(): void {
    if (this.params.timerBased) {
      this.updateActivation()
    }
  }
}
