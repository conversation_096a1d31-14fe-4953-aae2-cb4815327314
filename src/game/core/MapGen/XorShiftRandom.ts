export type Seed = [bigint, bigint]

export class XorShiftRandom {
  private state: Seed

  constructor(seed: Seed) {
    this.state = seed
  }

  private rotl(x: bigint, k: number): bigint {
    return (x << BigInt(k)) | (x >> (64n - BigInt(k)))
  }

  public nextU32(): number {
    return Number(this.nextU64() & BigInt(0xffffffff))
  }

  public nextU64(): bigint {
    const s0 = this.state[0]
    let s1 = this.state[1]
    const result = (s0 + s1) & BigInt('0xFFFFFFFFFFFFFFFF')

    s1 ^= s0
    this.state[0] = (this.rotl(s0, 55) ^ s1 ^ (s1 << 14n)) & BigInt('0xFFFFFFFFFFFFFFFF')
    this.state[1] = this.rotl(s1, 36) & BigInt('0xFFFFFFFFFFFFFFFF')

    return result
  }

  public nextInRange([min, max]: [number, number]): number {
    const range = max - min
    const result = min + Math.floor(this.nextFloat() * range)

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(`[XOR] Next in range (${min}, ${max}) => ${result}`)
    }

    return result
  }

  public nextFloat(): number {
    const maxU64 = BigInt('0xFFFFFFFFFFFFFFFF')
    const result = Number(this.nextU64()) / Number(maxU64)

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(`[XOR] Next float => ${result}`)
    }

    return result
  }

  public nextBool(): boolean {
    const value = this.nextU32()
    const combined = (value >> 0) ^ (value >> 1) ^ (value >> 2) ^ (value >> 3)
    const result = (combined & 1) === 1

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(`[XOR] Next in bool => ${result}`)
    }

    return result
  }

  public getState(): Seed {
    return this.state
  }
}
