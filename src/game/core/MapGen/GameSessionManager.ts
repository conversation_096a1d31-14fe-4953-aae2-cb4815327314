import { readSession, writeCreateSession, writeSessionUpdate } from '@/protos/messages'
import { apiClient } from '@/services/client'
import { eventBus } from '@/services/eventBus/uiEventBus'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import { useToast } from '@/stores/toastStore.ts'
import type { Options } from '@hey-api/client-fetch'
import { Mutation, MutationCache } from '@tanstack/query-core'
import EventEmitter from 'eventemitter3'
import Pbf from 'pbf'

export type ChunkUpdate = {
  entities: Array<EntityUpdate>
  index: number
}

export type EntityUpdate = {
  entity_index: number
  update_type: number
  position_x: number
  position_y: number
  time: number
}

export interface SessionDecoded {
  id: number
  inner_seed: [string, string]
  outer_seed: [string, string]
  multiplier: number
  highest_score: number
  total_score_event?: number
  highest_score_event?: number
  excluded_presets?: number[]
  ton_coin_values?: number[]
  ton_spawn_positions?: number[]
  custom_coin_type?: number
  custom_coin_values?: number[]
  custom_coin_spawn_positions?: number[]
  remaining_beginner_ton_allocation?: number
  used_boosters?: number[]
  dynamic_coin_type?: number
  dynamic_coin_values?: number[]
  dynamic_coin_spawn_positions?: number[]
  puzzle_coins_type?: number
  puzzle_coins_values?: number[]
  puzzle_coins_spawn_positions?: number[]
}

export interface SessionProperties {
  id: number
  innerSeed: [bigint, bigint]
  outerSeed: [bigint, bigint]
  multiplier: number
  highestScore: number
  totalScoreEvent?: number
  highestScoreEvent?: number
  excludedPresets: number[]
  tonCoinValues: number[]
  tonSpawnPositions: number[]
  customCoinType?: number
  customCoinValues: number[]
  customSpawnPositions: number[]
  remainingBeginnerTonAllocation?: number
  usedBoosters?: number[]
  dynamicCoinType?: number
  dynamicCoinValues: number[]
  dynamicCoinSpawnPositions: number[]
  puzzleCoinsType?: number
  puzzleCoinsValues: number[]
  puzzleCoinsSpawnPositions: number[]
}

export enum InteractionType {
  GameEnd = 0,
  PlatformCollide = 1,
  BoosterCollide = 2,
  MobCollideAlive = 3,
  MobCollideDeath = 4,
  MobCollideBullet = 5,
  CollectableCollide = 6
}

export interface EntityInteraction {
  chunkIndex: number
  entityIndex: number
  interactionType: InteractionType
  x: number
  y: number
  time?: number
}

export class GameSessionManager extends EventEmitter {
  private session?: SessionProperties
  private interactionMap: Map<string, EntityInteraction> = new Map()
  private maxRetries = 60
  private retryDelay = 1000
  private lastChunkIndex = 0
  private sessionCreatedAt: number | undefined
  private gameEndScore: number | undefined
  private isGameEnded = false
  private mutationCache: MutationCache
  private toastStore: any

  constructor() {
    super()
    this.mutationCache = new MutationCache()
    this.toastStore = useToast()
  }

  async createSession(
    hasTutorial: boolean,
    hasMobsTutorial: boolean,
    playerLeague: number | undefined,
    boosters: number[],
    controlMode: number = 0
  ): Promise<SessionProperties> {
    if (this.session) {
      throw new Error('A session is already active.')
    }

    let pbf = new Pbf()

    let configVersion = import.meta.env.VITE_MAPGEN_CONFIG_VER
    if (hasTutorial) {
      configVersion = import.meta.env.VITE_MAPGEN_CONFIG_TUTORIAL_VER
      console.log('configVersion: tutorial')
    } else if (hasMobsTutorial || playerLeague! <= 2) {
      configVersion = import.meta.env.VITE_MAPGEN_CONFIG_MOBS_TUTORIAL_VER
      console.log('configVersion: mobs tutorial')
    } else if (playerLeague === 3) {
      configVersion = import.meta.env.VITE_MAPGEN_CONFIG_THIRD_LEAGUES_VER
      console.log('configVersion: league 3')
    }

    console.log('controlMode: ', controlMode)
    writeCreateSession(
      {
        mapgen_ver: import.meta.env.VITE_MAPGEN_VER,
        config_ver: configVersion,
        used_boosters: boosters ?? [],
        control_mode: controlMode
      },
      pbf
    )

    const mutation = new Mutation({
      mutationId: Date.now(),
      mutationCache: this.mutationCache,
      options: {
        retry: this.maxRetries,
        retryDelay: this.retryDelay,
        mutationFn: async (options: Options<{ body: Uint8Array }>) => {
          try {
            const response = await fetch('/api/v1/gameplay/session/octet/create', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/octet-stream'
              },
              body: options.body
            })

            if (!response.ok) {
              let errorMessage = `HTTP error! Status: ${response.status}`
              try {
                const errorData = await response.json()
                errorMessage = errorData?.error ?? errorMessage
                console.error('Error response:', errorMessage)
                this.toastStore.showToast(errorMessage, 'warning', 5000)
              } catch {
                console.error('Error response:', errorMessage)
                this.toastStore.showToast(errorMessage, 'warning', 5000)
              }
              throw new Error(errorMessage)
            }

            const arrayBuffer = await response.arrayBuffer()
            return new Uint8Array(arrayBuffer)
          } catch (error) {
            console.error('Failed to create session:', error)
            throw error
          }
        }
      }
    })

    try {
      const sessionResponse = await mutation.execute({
        client: apiClient,
        body: pbf.finish()
      })

      pbf = new Pbf(sessionResponse)

      const session = readSession(pbf) as SessionDecoded

      if (
        !Array.isArray(session.outer_seed) ||
        session.outer_seed.length !== 2 ||
        !Array.isArray(session.inner_seed) ||
        session.inner_seed.length !== 2
      ) {
        throw new Error('Invalid session seeds.')
      }

      this.session = {
        id: session.id,
        outerSeed: [BigInt(session.outer_seed[0]), BigInt(session.outer_seed[1])],
        innerSeed: [BigInt(session.inner_seed[0]), BigInt(session.inner_seed[1])],
        multiplier: session.multiplier === 0 ? 1 : session.multiplier,
        highestScore: session.highest_score,
        totalScoreEvent: session.total_score_event,
        highestScoreEvent: session.highest_score_event,
        excludedPresets: session.excluded_presets ?? [],
        tonCoinValues: session.ton_coin_values ?? [],
        tonSpawnPositions: session.ton_spawn_positions ?? [],
        customCoinType: session.custom_coin_type,
        customCoinValues: session.custom_coin_values ?? [],
        customSpawnPositions: session.custom_coin_spawn_positions ?? [],
        remainingBeginnerTonAllocation: session.remaining_beginner_ton_allocation,
        usedBoosters: session.used_boosters ?? [],
        dynamicCoinType: session.dynamic_coin_type,
        dynamicCoinValues: session.dynamic_coin_values ?? [],
        dynamicCoinSpawnPositions: session.dynamic_coin_spawn_positions ?? [],
        puzzleCoinsType: session.puzzle_coins_type,
        puzzleCoinsValues: session.puzzle_coins_values ?? [],
        puzzleCoinsSpawnPositions: session.puzzle_coins_spawn_positions ?? []
      }

      if (this.session.remainingBeginnerTonAllocation != undefined) {
        this.session.remainingBeginnerTonAllocation =
          this.session.remainingBeginnerTonAllocation / 1000000000
      }
      console.log('session: ', this.session)
      this.sessionCreatedAt = Date.now()
      this.isGameEnded = false

      return this.session
    } catch (error) {
      console.error('Session creation failed:', error)
      throw error
    }
  }

  getActiveSession() {
    return this.session
  }

  registerInteraction(i: EntityInteraction, score: number = 0) {
    if (this.session && this.sessionCreatedAt) {
      const key = this.generateInteractionKey(i)
      this.interactionMap.set(key, { ...i, time: Date.now() - this.sessionCreatedAt })

      if (
        i.interactionType === InteractionType.GameEnd ||
        i.interactionType === InteractionType.MobCollideDeath
      ) {
        this.isGameEnded = true
        this.gameEndScore = score
        this.processAllInteractions({ gameEnd: true })
      }

      this.lastChunkIndex = i.chunkIndex
    }
  }

  registerGameEnd(score: number) {
    if (this.session && this.sessionCreatedAt && !this.isGameEnded) {
      this.registerInteraction(
        {
          interactionType: InteractionType.GameEnd,
          chunkIndex: this.lastChunkIndex,
          entityIndex: 0,
          x: 0,
          time: Date.now() - this.sessionCreatedAt,
          y: 0
        },
        score
      )
    }
  }

  private generateInteractionKey(interaction: EntityInteraction): string {
    return `${interaction.chunkIndex}_${interaction.entityIndex}_${interaction.interactionType}`
  }

  private async processAllInteractions(params: { gameEnd?: boolean } = {}) {
    if (this.interactionMap.size === 0) {
      if (__DEV__) console.log('processAllInteractions: no interactions found, returning.')
      return
    }
    const interactions: EntityInteraction[] = []
    for (const key of this.interactionMap.keys()) {
      const interaction = this.interactionMap.get(key)
      if (
        interaction?.chunkIndex === undefined ||
        (interaction.chunkIndex === 0 &&
          interaction.interactionType !== InteractionType.GameEnd &&
          interaction.interactionType !== InteractionType.MobCollideDeath)
      ) {
        if (__DEV__)
          console.log(
            'Skipping interaction with key',
            key,
            'due to invalid chunkIndex',
            interaction?.chunkIndex
          )
        continue
      }
      interactions.push(interaction)
      this.interactionMap.delete(key)
    }
    if (__DEV__)
      console.log(
        'processAllInteractions: interactions length after filtering:',
        interactions.length
      )
    if (interactions.length > 0 && this.session) {
      const chunks: ChunkUpdate[] = []
      let initialChunkIndex = interactions[0].chunkIndex
      if (initialChunkIndex === 0) {
        if (__DEV__)
          console.warn(
            'Game end interaction found with chunk index 0, adjusting initialChunkIndex to 1'
          )
        initialChunkIndex = 1
      }
      let currentChunkIndex = initialChunkIndex
      let chunkUpdate: ChunkUpdate = { index: currentChunkIndex, entities: [] }
      for (const interaction of interactions) {
        if (interaction.time === undefined) {
          if (__DEV__) console.log('Skipping interaction with missing time:', interaction)
          continue
        }
        const interactionTime = interaction.time
        let chunkIndex = interaction.chunkIndex
        if (
          chunkIndex === 0 &&
          (interaction.interactionType === InteractionType.GameEnd ||
            interaction.interactionType === InteractionType.MobCollideDeath)
        ) {
          chunkIndex = initialChunkIndex
        }
        if (chunkIndex === currentChunkIndex) {
          chunkUpdate.entities.push({
            update_type: interaction.interactionType,
            entity_index: interaction.entityIndex,
            position_x: interaction.x,
            position_y: interaction.y,
            time: interactionTime
          })
        } else {
          chunks.push(chunkUpdate)
          currentChunkIndex = chunkIndex
          chunkUpdate = {
            index: currentChunkIndex,
            entities: [
              {
                update_type: interaction.interactionType,
                entity_index: interaction.entityIndex,
                position_x: interaction.x,
                position_y: interaction.y,
                time: interactionTime
              }
            ]
          }
        }
      }
      if (chunkUpdate.index >= 0) {
        chunks.push(chunkUpdate)
      }
      if (__DEV__) console.log('processAllInteractions: built chunks:', chunks)
      const pbf = new Pbf()
      const sessionObj = { id: this.session.id, chunks, score: 0 }
      if (params.gameEnd && this.gameEndScore) {
        sessionObj.score = parseInt(this.gameEndScore.toFixed())
      }
      writeSessionUpdate(sessionObj, pbf)
      const mutation = new Mutation({
        mutationId: Date.now(),
        mutationCache: this.mutationCache,
        options: {
          retry: (failureCount, error) => {
            if (__DEV__)
              console.warn('mutation retry, failureCount:', failureCount, 'error:', error)
            if (failureCount > this.maxRetries || error.message === 'SESSION_NOT_FOUND') {
              return false
            }
            return true
          },
          retryDelay: this.retryDelay,
          mutationFn: async (options: Options<{ body: Uint8Array }>) => {
            try {
              if (__DEV__)
                console.log('mutationFn: calling fetch with body length', options.body.length)
              const response = await fetch('/api/v1/gameplay/session/octet/update', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/octet-stream'
                },
                body: options.body
              })
              if (__DEV__) console.log('mutationFn: fetch response status:', response.status)
              if (!response.ok) {
                let errorMessage = `Error: ${response.status}`
                try {
                  const errorData = await response.json()
                  errorMessage = errorData?.error ?? errorMessage
                } catch (e) {
                  if (__DEV__) console.warn('mutationFn: error parsing JSON error response', e)
                }
                if (__DEV__) console.error('mutationFn: throwing error', errorMessage)
                throw new Error(errorMessage)
              }
              const jsonData = await response.json()
              if (__DEV__) console.log('mutationFn: success, response JSON:', jsonData)
              return jsonData
            } catch (error) {
              if (__DEV__) console.error('mutationFn: caught error', error)
              throw error
            }
          }
        }
      })
      try {
        if (__DEV__) console.log('processAllInteractions: executing mutation...')
        await mutation.execute({ client: apiClient, body: pbf.finish() })
        if (__DEV__) console.log('processAllInteractions: mutation executed successfully')
      } catch (error) {
        if (__DEV__) console.error('processAllInteractions: mutation execution error', error)
      } finally {
        eventBus.emit(GAME_EVENTS.SESSION_UPDATE_LOCK_RELEASED)
        if (__DEV__)
          console.warn('GAME SESSION MANAGER: SESSION_UPDATE_LOCK_RELEASED emitted in finally')
      }
    } else {
      if (this.session) {
        if (__DEV__)
          console.log('No valid interactions to process; emitting SESSION_UPDATE_LOCK_RELEASED.')
        eventBus.emit(GAME_EVENTS.SESSION_UPDATE_LOCK_RELEASED)
      }
    }
  }

  async blobToUint8Array(blob: Blob): Promise<Uint8Array> {
    const arrayBuffer = await blob.arrayBuffer()
    return new Uint8Array(arrayBuffer)
  }
}
