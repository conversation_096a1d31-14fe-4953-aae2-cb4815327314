import fs from 'fs'
import path from 'path'
import { describe, expect, it } from 'vitest'
import { getRandomBigInt } from './Utils'
import { XorShiftRandom } from './XorShiftRandom'

interface Snapshot {
  seed: [string, string]
  values64: string[]
  values32: number[]
  valuesFloat: number[]
  valuesInRange: number[]
  valuesBool: boolean[]
}

describe('XorShiftRandom', () => {
  let snapshot: Snapshot = {} as any

  try {
    snapshot = JSON.parse(
      fs.readFileSync(path.resolve(__dirname, `./test/xor-snapshot.json`), 'utf-8')
    )
  } catch {
    snapshot.seed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
  }

  const seed = snapshot.seed.map((v: any) => BigInt(v)) as [bigint, bigint]

  it('should generate values correct values', () => {
    const values64: string[] = []
    const values32: number[] = []
    const valuesFloat: number[] = []
    const valuesInRange: number[] = []
    const valuesBool: boolean[] = []

    const xor = new XorShiftRandom([...seed])

    for (let i = 0; i < 100; i++) {
      values64.push(xor.nextU64().toString())
    }

    for (let i = 0; i < 100; i++) {
      values32.push(xor.nextU32())
    }

    for (let i = 0; i < 100; i++) {
      valuesFloat.push(xor.nextFloat())
    }

    for (let i = 0; i < 100; i++) {
      valuesInRange.push(xor.nextInRange([0, 100_000]))
    }

    for (let i = 0; i < 100; i++) {
      valuesBool.push(xor.nextBool())
    }

    const newSnapshot = {
      seed: seed.map((v: any) => v.toString()),
      values64,
      values32,
      valuesFloat,
      valuesInRange,
      valuesBool
    }

    expect(JSON.stringify(newSnapshot, null, 2)).toMatchFileSnapshot('./test/xor-snapshot.json')
  })
})
