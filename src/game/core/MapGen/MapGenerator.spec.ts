import fs from 'fs'
import path from 'path'
import { describe, expect, it } from 'vitest'
import type { SessionProperties } from './GameSessionManager'
import { MapGenerator, type Chunk } from './MapGenerator'
import { getRandomBigInt } from './Utils'

describe('MapGenerator', () => {
  const chunksToGenerate = 100
  const numberOfSnapshots = 10

  const configVer = Number(process.env.VITE_MAPGEN_CONFIG_VER)
  const configVerThirdLeague = Number(process.env.VITE_MAPGEN_CONFIG_THIRD_LEAGUES_VER)
  const configVerTutorial = Number(process.env.VITE_MAPGEN_CONFIG_TUTORIAL_VER)
  const configVerTutorialMobs = Number(process.env.VITE_MAPGEN_CONFIG_MOBS_TUTORIAL_VER)

  it(`should generate ${chunksToGenerate} chunks`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-main.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(__dirname, `./test/snapshots/config-v${configVer}-snapshot_${i}.json`),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: undefined,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVer}-snapshot_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVer}-snapshot_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks for ton event`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-main.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(__dirname, `./test/snapshots/config-v${configVer}-snapshot-ton_${i}.json`),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [
          104, 208, 303, 317, 404, 511, 602, 707, 908, 1008, 1006, 1009, 1108, 1106
        ],
        tonCoinValues: [
          1323488, 1323488, 1389663, 1459146, 1532103, 1608708, 1689144, 1773601, 1862281, 1955395
        ],
        tonSpawnPositions: [
          1000, 21000, 41000, 61000, 81000, 101000, 121000, 141000, 161000, 181000
        ],
        customCoinType: 2003,
        customCoinValues: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        customSpawnPositions: [500, 10500, 20500, 30500, 40500, 50500, 60500, 70500, 80500, 90500],
        dynamicCoinType: 2006,
        dynamicCoinValues: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        dynamicCoinSpawnPositions: [
          550, 10550, 20550, 30550, 40550, 50550, 60550, 70550, 80550, 90550
        ],
        puzzleCoinsType: 2007,
        puzzleCoinsValues: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        puzzleCoinsSpawnPositions: [
          600, 11000, 21000, 31000, 41000, 51000, 61000, 71000, 81000, 91000
        ]
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVer}-snapshot-ton_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVer}-snapshot-ton_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks with ton in presets`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-main.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            // path.resolve(__dirname, `./test/snapshots/config-v${configVer}-snapshot-ton_${i}.json`),
            path.resolve(
              __dirname,
              `./test/snapshots/config-v${configVer}-snapshot-ton_on_preset_${i}.json`
            ),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: 2001,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVer}-snapshot-ton_on_preset_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVer}-snapshot-ton_on_preset_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks with excluded presets`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-main.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(
              __dirname,
              `./test/snapshots/config-v${configVer}-snapshot-excluded_presets_${i}.json`
            ),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [
          104, 208, 303, 317, 404, 511, 602, 707, 908, 1008, 1006, 1009, 1108, 1106
        ],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: undefined,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVer}-snapshot-excluded_presets_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVer}-snapshot-excluded_presets_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks for tutorial`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-tutorial.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(
              __dirname,
              `./test/snapshots/config-v${configVerTutorial}-snapshot_${i}.json`
            ),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: undefined,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVerTutorial}-snapshot_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVerTutorial}-snapshot_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks for tutorial with mobs`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(
          path.resolve(__dirname, `../../../../mapgen/config-tutorial-mobs.json`),
          'utf-8'
        )
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(
              __dirname,
              `./test/snapshots/config-v${configVerTutorialMobs}-snapshot_${i}.json`
            ),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: undefined,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVerTutorialMobs}-snapshot_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVerTutorialMobs}-snapshot_${i}_frontend.log`
        )
      }
    }
  })

  it(`should generate ${chunksToGenerate} chunks for the third league`, async () => {
    for (let i = 0; i < numberOfSnapshots; i++) {
      ;(window as any).mapgenLog = []

      const mapGenConfig: {
        weights: any[]
        variables: any[]
        procedural: any[]
        composition: any[]
      } = JSON.parse(
        fs.readFileSync(path.resolve(__dirname, `../../../../mapgen/config-league-3.json`), 'utf-8')
      )

      let snapshot: any = {}
      try {
        snapshot = JSON.parse(
          fs.readFileSync(
            path.resolve(
              __dirname,
              `./test/snapshots/config-v${configVerThirdLeague}-snapshot_${i}.json`
            ),
            'utf-8'
          )
        )
      } catch {
        snapshot.outerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
        snapshot.innerSeed = [getRandomBigInt().toString(), getRandomBigInt().toString()]
      }

      const outerSeed = snapshot.outerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]
      const innerSeed = snapshot.innerSeed.map((v: any) => BigInt(v)) as [bigint, bigint]

      const sessionProps: SessionProperties = {
        id: 1,
        outerSeed: [...outerSeed],
        innerSeed: [...innerSeed],
        multiplier: 1,
        highestScore: 0,
        excludedPresets: [],
        tonCoinValues: [],
        tonSpawnPositions: [],
        customCoinType: undefined,
        customCoinValues: [],
        customSpawnPositions: [],
        dynamicCoinType: undefined,
        dynamicCoinValues: [],
        dynamicCoinSpawnPositions: [],
        puzzleCoinsType: undefined,
        puzzleCoinsValues: [],
        puzzleCoinsSpawnPositions: []
      }

      const mapgen = new MapGenerator(sessionProps, mapGenConfig)

      let currentChunksLength = 0

      const chunks: Chunk[] = []
      for (let i = 0; i < chunksToGenerate; i++) {
        const chunk = mapgen.generateChunk(currentChunksLength)
        currentChunksLength += chunk.length

        // @ts-ignore
        delete chunk.prevChunkCumulativeLenght

        chunks.push(chunk)
      }

      const newSnapshot = JSON.stringify(
        {
          outerSeed: outerSeed.map(v => v.toString()),
          innerSeed: innerSeed.map(v => v.toString()),
          chunks
        },
        null,
        2
      )

      await expect(newSnapshot).toMatchFileSnapshot(
        `./test/snapshots/config-v${configVerThirdLeague}-snapshot_${i}.json`
      )

      if ((window as any).mapgenLog) {
        const logs = []
        for (const logLine of (window as any).mapgenLog) {
          logs.push(`[INFO] ${logLine}`)
        }
        await expect(logs.join('\n')).toMatchFileSnapshot(
          `./test/logs/config-v${configVerThirdLeague}-snapshot_${i}_frontend.log`
        )
      }
    }
  })
})
