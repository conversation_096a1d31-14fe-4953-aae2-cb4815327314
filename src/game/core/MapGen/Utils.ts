import { ENTITIES_SIZES, type BaseEntity, type PlatformComponent } from './MapGenerator'

export function getRandomBigInt(): bigint {
  const high = BigInt(Math.floor(Math.random() * 0xffffffff)) // Higher 32 bits
  const low = BigInt(Math.floor(Math.random() * 0xffffffff)) // Lower 32 bits

  return (high << 32n) | low
}

export class Rect {
  x: number
  y: number
  width: number
  height: number

  constructor(x: number, y: number, width: number, height: number) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
  }

  static fromEntity(entity: BaseEntity): Rect {
    const [width, height] = ENTITIES_SIZES[entity.type]
    return new Rect(entity.x, entity.y, width, height)
  }

  static fromPlatformComponent(component: PlatformComponent): Rect {
    const [width, height] = ENTITIES_SIZES[component.type]
    return new Rect(component.x, component.y, width, height)
  }

  intersects(other: Rect, error: number): boolean {
    const selfRight = this.x + this.width
    const selfBottom = this.y + this.height
    const otherRight = other.x + other.width
    const otherBottom = other.y + other.height

    return (
      this.x - error < otherRight &&
      selfRight + error > other.x &&
      this.y - error < otherBottom &&
      selfBottom + error > other.y
    )
  }

  toSvg(): string {
    return `<rect x="${this.x}" y="${this.y}" width="${this.width}" height="${this.height}" fill="#F97316" />`
  }
}
