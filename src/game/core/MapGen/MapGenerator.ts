import type { SessionProperties } from './GameSessionManager'
import { XorShiftRandom } from './XorShiftRandom'

let DEFAULT_CONFIG: Configuration = __GAME_CONFIG_DATA__

const POINTS_FACTOR = 0.5
const BOOSTER_Y_OFFSET = 7

export interface GeneralStagedVariables {
  ticketsFraction: number
  ticketsPerPlatform: number
  tonPerPlatform: number | undefined
  movementDuration: [number, number]
}

export interface ProceduralStagesVariables {
  score: [number, number]
  distance: [number, number]
  distanceWreckable: [number, number]
  dynamicHProb?: number | undefined
  wreckableProb?: number | undefined
  disposableProb?: number | undefined
  dynamicHMax?: number | undefined

  boostSmallJumpCount?: [number, number]
  boostSmallJumpProb?: number | undefined

  boostBigJumpCount?: [number, number]
  boostBigJumpProb?: number | undefined
}

export interface BoosterStagedVariables {
  boostBigFlyWeight?: number | undefined
  boostSmallFlyWeight?: number | undefined
}

export type StagedVariablesTypes = 'general' | 'procedural' | 'booster'

export interface StagedVariables {
  score: number
  general?: GeneralStagedVariables
  procedural?: ProceduralStagesVariables
  booster?: BoosterStagedVariables
}

export interface GlobalWeights {
  score: [number, number]
  procedural: number
  composition: number
  maxProceduralSeries: number
  maxCompositionSeries: number
  tags: { option: number; weight: number }[]
}

export enum GenerationType {
  Procedural = 0,
  Composition = 1
}

export interface Composable {
  platform: {
    type: EntityPlatformType
    x?: number
    y?: number
    distance?: [number, number]
    probability?: number
    position?: [number, number] | undefined
    movable?: boolean
    movementRange?: [number, number]
    movementDuration?: [number, number]
    visible?: boolean | undefined
    explosiveDistance?: number | undefined
    explosiveDelay?: number | undefined
    explosiveAsDecoration?: boolean | undefined
    clone?: number | undefined
    tickets?: boolean | undefined
    tonToken?: boolean | undefined
  }
  booster?: {
    type: EntityBoosterType
    probability?: number
  }
  mob?: {
    type: EntityMobType
    probability?: number
    position?: [number, number]
    moveHalfScreen?: boolean
  }
  decoration?: {
    type: EntityDecorationsType
    key: string
  }
  token?: {
    type: CollectibleTokenType
  }
}

export interface CompositionConfig {
  composables: Composable[]
}

export type Configuration = {
  weights: GlobalWeights[]
  variables: StagedVariables[]
  composition: {
    id: number
    stringName?: string
    tags: number[]
    config: CompositionConfig
    weight: number
  }[]
}

export type CompositionEntry = Configuration['composition'][number]

// All possible entity types
export enum EntityType {
  // None
  None = -1,

  // Platforms
  PlatformEmpty = 0,
  PlatformStatic = 1,
  PlatformDynamicH = 10,
  PlatformDynamicV = 11,
  PlatformDisposable = 20,
  PlatformWreckable = 21,
  PlatformShifting = 30,
  PlatformInvisible = 40,
  PlatformExplosive = 50,
  PlatformSpiked = 60,
  PlatformIce = 70,
  PlatformBooster = 80,
  PlatformLevitating = 90,
  PlatformTrap = 100,
  PlatformPropeller = 110,

  // Boosters
  BoosterBigFly = 500,
  BoosterSmallFly = 501,
  BoosterBigJump = 502,
  BoosterSmallJump = 503,
  BoosterShield = 504,

  // Enemies
  MobDynamicHorizontalTiny = 1000, //червоний прибулець
  //****Version 1 launch****
  MobDynamicHorizontalSmall = 1001, //шестилапий монстр
  MobStatic = 1002, //плоский монстр
  MobBlackHole = 1003, //чорна діра
  //****Version 1 launch****
  MobDynamicHorizontalMiddle = 1004, //синій прибулець
  MobUFO = 1005, //нло
  MobStaticVertical = 1006, //довгий стрибаючийприбулець
  MobDynamicHorizontalBig = 1007, //літаючий синій прибулець
  MobDynamicVertical = 1008, //великий синій прибулець
  MobDynamicHorizontalHuge = 1009, // зелений прибулець
  MobDynamicHorizontalVertical = 1010, //літаючий прибулець

  // Collectibles
  CollectibleTicket = 2000,
  CollectibleTon = 2001,

  CollectibleCustomCoin = 2005,
  CollectibleDynamicCoin = 2006,
  CollectiblePuzzleCoin = 2007,

  //Decorations
  FTUEImage = 3000
}

// Sizes of entities in pixels (width, height)
export const ENTITIES_SIZES: Record<EntityType, [number, number]> = {
  // None
  [EntityType.None]: [0, 0],

  // Platforms
  [EntityType.PlatformEmpty]: [0, 0],
  [EntityType.PlatformStatic]: [114, 30],
  [EntityType.PlatformDynamicH]: [114, 30],
  [EntityType.PlatformDynamicV]: [114, 30],
  [EntityType.PlatformDisposable]: [114, 30],
  [EntityType.PlatformWreckable]: [114, 30],
  [EntityType.PlatformShifting]: [114, 30],
  [EntityType.PlatformInvisible]: [114, 30],
  [EntityType.PlatformExplosive]: [114, 30],
  [EntityType.PlatformSpiked]: [114, 30],
  [EntityType.PlatformIce]: [114, 30],
  [EntityType.PlatformBooster]: [114, 30],
  [EntityType.PlatformLevitating]: [114, 30],
  [EntityType.PlatformTrap]: [114, 30],
  [EntityType.PlatformPropeller]: [114, 30],

  // Boosters
  [EntityType.BoosterBigFly]: [48, 73],
  [EntityType.BoosterBigJump]: [72, 42],
  [EntityType.BoosterSmallFly]: [64, 64],
  [EntityType.BoosterSmallJump]: [40, 30],
  [EntityType.BoosterShield]: [64, 64],

  // Mobs
  [EntityType.MobDynamicHorizontalTiny]: [73, 73],
  [EntityType.MobDynamicHorizontalSmall]: [99, 89],
  [EntityType.MobDynamicHorizontalMiddle]: [135, 78],
  [EntityType.MobDynamicHorizontalBig]: [175, 125],
  [EntityType.MobStatic]: [160, 54],
  [EntityType.MobBlackHole]: [145, 131],
  [EntityType.MobUFO]: [155, 256],
  [EntityType.MobDynamicHorizontalHuge]: [200, 98],
  [EntityType.MobDynamicHorizontalVertical]: [90, 200],
  [EntityType.MobDynamicVertical]: [130, 80],
  [EntityType.MobStaticVertical]: [90, 156],

  // Collectibles
  [EntityType.CollectibleTicket]: [50, 50],
  [EntityType.CollectibleTon]: [50, 50],

  [EntityType.CollectibleCustomCoin]: [50, 50],
  [EntityType.CollectibleDynamicCoin]: [50, 50],
  [EntityType.CollectiblePuzzleCoin]: [50, 50],

  // Images
  [EntityType.FTUEImage]: [256, 256]
}

export const REVERSE_HEIGHT_FOR_BOOST = {
  [EntityType.BoosterBigFly]: 7440,
  [EntityType.BoosterBigJump]: 1300,
  [EntityType.BoosterSmallFly]: 4464,
  [EntityType.BoosterSmallJump]: 1040
}

export interface EntityMovement {
  duration: number
  range?: [number, number]
}

export interface EntityVisibility {
  visible: boolean
}

export interface EntityExplosive {
  distance: number
  delay: number
  decoration: boolean
}

export interface EntityCollectible {
  amount: number
  coinType?: number
}

export interface EntityMobMovement {
  moveHalfScreen: boolean
}

export interface BaseEntity {
  type: EntityType
  index: number
  x: number
  y: number
  movement?: EntityMovement
  visibility?: EntityVisibility
  explosive?: EntityExplosive
  collectible?: EntityCollectible
  mobMovement?: EntityMobMovement
}

export type EntityProperties = Omit<BaseEntity, 'x' | 'y'>

export type CollectibleTokenType =
  | EntityType.CollectibleTon
  | EntityType.CollectibleCustomCoin
  | EntityType.CollectibleDynamicCoin
  | EntityType.CollectiblePuzzleCoin

export type EntityPlatformType =
  | EntityType.None
  | EntityType.PlatformEmpty
  | EntityType.PlatformStatic
  | EntityType.PlatformShifting
  | EntityType.PlatformInvisible
  | EntityType.PlatformExplosive
  | EntityType.PlatformDynamicH
  | EntityType.PlatformDynamicV
  | EntityType.PlatformDisposable
  | EntityType.PlatformWreckable
  | EntityType.PlatformSpiked
  | EntityType.PlatformIce
  | EntityType.PlatformBooster
  | EntityType.PlatformLevitating
  | EntityType.PlatformTrap
  | EntityType.PlatformPropeller

export type EntityBoosterType =
  | EntityType.None
  | EntityType.BoosterBigFly
  | EntityType.BoosterBigJump
  | EntityType.BoosterSmallFly
  | EntityType.BoosterSmallJump
  | EntityType.BoosterShield

export type EntityMobType =
  | EntityType.None
  | EntityType.MobDynamicHorizontalTiny
  | EntityType.MobDynamicHorizontalSmall
  | EntityType.MobStatic
  | EntityType.MobBlackHole
  | EntityType.MobDynamicHorizontalMiddle
  | EntityType.MobUFO
  | EntityType.MobDynamicHorizontalBig
  | EntityType.MobDynamicHorizontalHuge
  | EntityType.MobDynamicHorizontalVertical
  | EntityType.MobDynamicVertical
  | EntityType.MobStaticVertical

export type EntityCollectibleType = EntityType.None | EntityType.CollectibleTicket

export type EntityDecorationsType = EntityType.FTUEImage

export interface Platform extends BaseEntity {
  type: EntityPlatformType
}

export interface Booster extends BaseEntity {
  type: EntityBoosterType
}

export interface Ticket extends BaseEntity {
  type: EntityCollectibleType
}

export interface Token extends BaseEntity {
  type: CollectibleTokenType
}

export interface Mob extends BaseEntity {
  type: EntityMobType
}

export interface Decoration extends BaseEntity {
  type: EntityDecorationsType
  key: string
}

export type Entity = Platform | Booster | Mob | Decoration

export interface PlatformComponent
  extends Omit<Platform, 'boost' | 'mob' | 'collectible' | 'decoration' | 'token'> {
  booster?: Booster
  mob?: Mob
  clone?: boolean
  ticket?: Ticket
  decoration?: Decoration
  token?: Token
}

export interface Chunk {
  id: number
  index: number
  generation: GenerationType
  components: PlatformComponent[]
  length: number
  prevChunkCumulativeLenght: number
}

export type ChunkContent = Pick<Chunk, 'components'>

const PLAYER_JUMP_HEIGHT = 300
for (let i = 0; i <= 100; i++) {
  ;(window as any)[`PJ${i}`] = `PJ${i}`
  ;(window as any)[`PJI${i}`] = `PJI${i}`
}
const SCREEN_WIDTH = 640

if (__DEV__) {
  let tagsIndex = 0
  const tags: Record<string, number> = {}

  const replaceTag = (tag: string) => {
    if (tags[tag] === undefined) {
      tags[tag] = tagsIndex++
    }

    return tags[tag]
  }

  let newConfig: Configuration = {
    weights: [],
    variables: [],
    composition: []
  }

  let isNewConfig = false
  window.addEventListener('updateMapConfig', (event: any) => {
    DEFAULT_CONFIG = event.detail.config
  })
  ;(window as any).EntityType = EntityType
  ;(window as any).ENTITIES_SIZES = ENTITIES_SIZES
  ;(window as any).center = 'center'
  ;(window as any).start = 'start'
  ;(window as any).end = 'end'
  ;(window as any).RANDOM = undefined

  const parseSinglePreset = (presetBlock: string): Composable[] => {
    const composables: Composable[] = []
    const lines = presetBlock
      .split('}')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('//'))

    for (const line of lines) {
      const match = line.match(/(\w+): \{([^}]+)/)

      if (match) {
        const [_, type, config] = match
        const properties = config.split(';').map(prop => prop.trim())

        const platform: Composable['platform'] = { type: mapPlatformType(type) }
        let booster: Composable['booster'] | undefined = undefined
        let mob: Composable['mob'] | undefined = undefined
        let decoration: Composable['decoration'] | undefined = undefined
        let token: Composable['token'] | undefined = undefined

        properties.forEach(prop => {
          const [key, value] = prop.split(':').map(p => p.trim())
          const parsedValue = value

          switch (key) {
            case 'distance':
              platform.distance = transformDistance(parsedValue)
              break
            case 'probability':
              platform.probability = eval(parsedValue)
              break
            case 'movable':
              platform.movable = eval(parsedValue)
              break
            case 'x':
              platform.x = eval(parsedValue)
              break
            case 'y':
              platform.y = eval(parsedValue)
              break
            case 'tickets':
              platform.tickets = eval(parsedValue)
              break
            case 'tonToken':
              platform.tonToken = eval(parsedValue)
              break
            case 'position':
              if (parsedValue == 'RANDOM') break

              platform.position =
                parsedValue != undefined ? transformPosition(parsedValue) : undefined
              break
            case 'clone':
              platform.clone = eval(parsedValue)
              break
            case 'movementRange':
              platform.movementRange = transformDistance(parsedValue)
              break
            case 'movementDuration':
              platform.movementDuration = transformDistance(parsedValue)
              break
            case 'boosterType':
              booster = { type: mapBoosterType(parsedValue) }
              break
            case 'boosterProbability':
              if (booster) booster.probability = eval(parsedValue)
              break
            case 'mobType':
              mob = { type: mapMobType(parsedValue) }
              break
            case 'mobProbability':
              if (mob) mob.probability = eval(parsedValue)
              break
            case 'mobPosition':
              if (mob)
                mob.position = parsedValue != undefined ? transformPosition(parsedValue) : undefined
              break
            case 'visible':
              platform.visible = eval(parsedValue)
              break
            case 'explosiveDisatance':
              platform.explosiveDistance = eval(parsedValue)
              break
            case 'explosiveDelay':
              platform.explosiveDelay = eval(parsedValue)
              break
            case 'explosiveAsDecoration':
              platform.explosiveAsDecoration = eval(parsedValue)
              break
            case 'moveHalfScreen':
              if (mob) mob.moveHalfScreen = eval(parsedValue)
              break
            case 'decoration':
              decoration = { type: EntityType.FTUEImage, key: parsedValue }
              break
            case 'token':
              token = { type: mapTokenType(parsedValue) }
          }
        })

        composables.push({ platform, booster, mob, decoration, token })
      }
    }

    return composables
  }

  const transformPosition = (value: string): [number, number] => {
    if (value.indexOf('[') !== -1) {
      const arr = value.replace(/(\[|\])/gi, '').split(',')
      return [transformPositionValue(arr[0]), transformPositionValue(arr[1])]
    }
    return [transformPositionValue(value), transformPositionValue(value)]
  }

  const transformPositionValue = (value: string | number) => {
    if (typeof value === 'string') {
      value = value.replace(/ /gi, '')

      if (value.startsWith('W')) {
        return -parseInt(value.replace('W', ''))
      } else if (value.startsWith('center+')) {
        return -parseInt(value.replace('center+', '')) - 101
      } else if (value.startsWith('center-')) {
        return -parseInt(value.replace('center-', '')) - 151
      } else if (value.startsWith('start+')) {
        return -parseInt(value.replace('start+', ''))
      } else if (value.startsWith('end-')) {
        return -100 + parseInt(value.replace('end-', ''))
      } else if (value.startsWith('end')) {
        return -100
      } else if (value.startsWith('start')) {
        return 0
      } else if (value.startsWith('center')) {
        return -101
      }

      return parseInt(value)
    }

    return value
  }

  const transformDistance = (value: string): [number, number] => {
    if (value.indexOf('[') !== -1) {
      const arr = value.replace(/(\[|\])/gi, '').split(',')
      return [transformDistanceValue(arr[0]), transformDistanceValue(arr[1])]
    }

    return [transformDistanceValue(value), transformDistanceValue(value)]
  }

  const transformDistanceValue = (value: string | number) => {
    if (typeof value === 'string') {
      value = value.replace(/ /gi, '')

      if (value.startsWith('PJI')) {
        return -parseInt(value.replace('PJI', '')) - 100
      } else if (value.startsWith('PJ')) {
        return -parseInt(value.replace('PJ', ''))
      }

      return parseInt(value)
    }

    return value
  }

  const parsePreset = (props: Partial<CompositionEntry>, presetText: string): CompositionEntry => {
    const presetBlocks = presetText.split(/preset (\(.+\))? ?(\[\d+,\d+\])? ?\{/).slice(1)
    for (const block of presetBlocks) {
      if (!block) {
        continue
      }

      const presetBlock = block.split('\n}')[0]
      const composables = parseSinglePreset(presetBlock)

      const config: CompositionEntry = {
        id: 0,
        weight: 0,
        tags: [0],
        stringName: 'preset',
        config: { composables },
        ...props
      }

      config.tags = (config.tags as any).map((v: string) => replaceTag(v))

      return config
    }

    throw new Error('Preset not found')
  }

  const applyPreset = (props: Partial<CompositionEntry>, text: string) => {
    const config = parsePreset(props, text)

    __GAME_CHUNK_NAMES__[config.id] = config.stringName

    console.log(JSON.stringify(config))
    window.dispatchEvent(
      new CustomEvent('updateMapConfig', {
        detail: {
          config: {
            composition: [config]
          }
        }
      })
    )

    if (isNewConfig) {
      newConfig.composition.push(config)
    }

    return config
  }

  const copyToCB = (value: string | object) => {
    const text = typeof value === 'string' ? value : JSON.stringify(value, null, 2)
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed' // Avoid scrolling to bottom
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
    } catch (err) {
      console.error('Could not copy text', err)
    }
    document.body.removeChild(textArea)
  }

  const mapPlatformType = (platformType: string): EntityPlatformType => {
    switch (platformType) {
      case 'empty':
        return EntityType.PlatformEmpty
      case 'static':
        return EntityType.PlatformStatic
      case 'dynamic_h':
        return EntityType.PlatformDynamicH
      case 'dynamic_v':
        return EntityType.PlatformDynamicV
      case 'disposable':
        return EntityType.PlatformDisposable
      case 'wreckable':
        return EntityType.PlatformWreckable
      case 'shifting':
        return EntityType.PlatformShifting
      case 'invisible':
        return EntityType.PlatformInvisible
      case 'explosive':
        return EntityType.PlatformExplosive
      case 'spiked':
        return EntityType.PlatformSpiked
      case 'ice':
        return EntityType.PlatformIce
      case 'booster':
        return EntityType.PlatformBooster
      case 'levitating':
        return EntityType.PlatformLevitating
      case 'trap':
        return EntityType.PlatformTrap
      case 'propeller':
        return EntityType.PlatformPropeller
      default:
        throw new Error(`Unknown platform type: ${platformType}`)
    }
  }

  const mapBoosterType = (boosterType: string): EntityBoosterType => {
    boosterType = boosterType.replace(/'/gi, '').replace(/"/gi, '')
    switch (boosterType) {
      case 'small-jump':
        return EntityType.BoosterSmallJump
      case 'small-fly':
        return EntityType.BoosterSmallFly
      case 'big-jump':
        return EntityType.BoosterBigJump
      case 'big-fly':
        return EntityType.BoosterBigFly
      case 'shield':
        return EntityType.BoosterShield
      default:
        throw new Error(`Unknown booster type: ${boosterType}`)
    }
  }

  const mapMobType = (mobType: string): EntityMobType => {
    mobType = mobType.replace(/'/gi, '').replace(/"/gi, '')
    switch (mobType) {
      case 'static':
        return EntityType.MobStatic
      case 'dynamic-horizontal-tiny':
        return EntityType.MobDynamicHorizontalTiny
      case 'dynamic-horizontal-small':
        return EntityType.MobDynamicHorizontalSmall
      case 'black-hole':
        return EntityType.MobBlackHole
      case 'dynamic-horizontal-middle':
        return EntityType.MobDynamicHorizontalMiddle
      case 'ufo':
        return EntityType.MobUFO
      case 'static-vertical':
        return EntityType.MobStaticVertical
      case 'dynamic-horizontal-big':
        return EntityType.MobDynamicHorizontalBig
      case 'dynamic-vertical':
        return EntityType.MobDynamicVertical
      case 'dynamic-horizontal-huge':
        return EntityType.MobDynamicHorizontalHuge
      case 'dynamic-horizontal-vertical':
        return EntityType.MobDynamicHorizontalVertical
      default:
        throw new Error(`Unknown mob type: ${mobType}`)
    }
  }

  const mapTokenType = (tokenType: string): CollectibleTokenType => {
    tokenType = tokenType.replace(/'/gi, '').replace(/"/gi, '')
    switch (tokenType) {
      case 'ton':
        return EntityType.CollectibleTon
      default:
        throw new Error(`Unknown token type: ${tokenType}`)
    }
  }

  const useNewConfig = () => {
    isNewConfig = true
    newConfig = {
      weights: [],
      variables: [],
      composition: []
    }
    return 'ok'
  }

  const copyNewConfig = () => {
    newConfig.variables = newConfig.variables.sort((a, b) => a.score - b.score)
    newConfig.composition.forEach(v => {
      v.config.composables.sort((v1, v2) => {
        return (v1.platform.y ?? 0) - (v2.platform.y ?? 0)
      })
    })
    copyToCB(newConfig)
    return 'ok'
  }

  const applyNewConfig = () => {
    newConfig.variables = newConfig.variables.sort((a, b) => a.score - b.score)
    newConfig.composition.forEach(v => {
      v.config.composables.sort((v1, v2) => {
        return (v1.platform.y ?? 0) - (v2.platform.y ?? 0)
      })
    })
    DEFAULT_CONFIG = newConfig
    return 'ok'
  }

  const applyGlobalWeights = (configs: any) => {
    newConfig.weights.push({
      ...configs,
      score: configs.score.map((v: number) => Math.floor(v / POINTS_FACTOR)) as [number, number],
      tags: configs.tags.map((v: any) => ({
        option: replaceTag(v.option),
        weight: v.weight
      }))
    })
    return configs
  }

  const applyStagedVariables = (configs: StagedVariables) => {
    const c = {
      ...configs,
      score: Math.floor(configs.score / POINTS_FACTOR)
    }

    if (c.procedural !== undefined) {
      c.procedural = {
        ...c.procedural,
        score: c.procedural.score.map((v: number) => Math.floor(v / POINTS_FACTOR)) as [
          number,
          number
        ],
        distance: c.procedural.distance.map((v: any) => transformDistanceValue(v)) as [
          number,
          number
        ],
        distanceWreckable: c.procedural.distanceWreckable.map((v: any) =>
          transformDistanceValue(v)
        ) as [number, number]
      }
    }

    newConfig.variables.push(c)
    return c
  }

  ;(window as any).applyGlobalWeights = applyGlobalWeights
  ;(window as any).applyStagedVariables = applyStagedVariables
  ;(window as any).applyPreset = applyPreset
  ;(window as any).copyToCB = copyToCB
  ;(window as any).useNewConfig = useNewConfig
  ;(window as any).useNewConfig = useNewConfig
  ;(window as any).copyNewConfig = copyNewConfig
  ;(window as any).applyNewConfig = applyNewConfig
}

export interface InitialStages {
  stages: StagedVariables[]
  activeStage: number
  nextStageScore: number | undefined
}

export interface DeterminePlatformTypeConfig {
  platformDynH?: number | undefined
  platformWreckable?: number | undefined
  platformDisposable?: number | undefined
}

export interface DetermineBoosterTypeConfig {
  boostSmallJump?: number | undefined
  boostBigJump?: number | undefined
}

export class MapGenerator {
  private configs: Configuration
  private sessionProps: SessionProperties
  private outerXor: XorShiftRandom
  private innerXor: XorShiftRandom
  private сhunkIndex = 1
  private proceduralChunksCount = 0
  private compositionChunksCount = 0
  private prevChunkCumulativeLength = 0
  private ticketsSpawn = 0
  private nextBoosterSpawn: number | undefined = undefined
  private nextBoosterType: EntityType.BoosterBigFly | EntityType.BoosterSmallFly | undefined

  constructor(sessionProps: SessionProperties, configs: Configuration = DEFAULT_CONFIG) {
    this.configs = configs
    this.sessionProps = sessionProps
    this.outerXor = new XorShiftRandom(this.sessionProps.outerSeed)
    this.innerXor = new XorShiftRandom(this.sessionProps.innerSeed)
  }

  generateChunk(currentChunksLength: number): Chunk {
    const index = this.сhunkIndex++

    if (this.nextBoosterSpawn === undefined || this.nextBoosterSpawn < currentChunksLength) {
      const initialStagesBooster = this.getInitialStages('booster', currentChunksLength)
      const activeBoosterStage = initialStagesBooster.activeStage

      const boosterStage = initialStagesBooster.stages[activeBoosterStage].booster

      if (boosterStage === undefined) {
        throw new Error('No general stage found')
      }

      const { boostBigFlyWeight, boostSmallFlyWeight } = boosterStage

      if (boostBigFlyWeight !== undefined && boostSmallFlyWeight !== undefined) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[GENERATION] [chunkIndex: ${index}] Roll booster type spawn`
          )
        }
        const roll = this.outerXor.nextFloat() * 100
        this.nextBoosterType =
          this.selectWeighted(roll, [
            { option: EntityType.BoosterBigFly, weight: boostBigFlyWeight },
            { option: EntityType.BoosterSmallFly, weight: boostSmallFlyWeight }
          ]) ?? EntityType.BoosterBigFly
      } else if (boostBigFlyWeight !== undefined && boostSmallFlyWeight === undefined) {
        this.nextBoosterType = EntityType.BoosterBigFly
      } else if (boostBigFlyWeight === undefined && boostSmallFlyWeight !== undefined) {
        this.nextBoosterType = EntityType.BoosterSmallFly
      } else if (boostBigFlyWeight === undefined && boostSmallFlyWeight === undefined) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(`[GENERATION] [chunkIndex: ${index}] Skip booster spawn`)
        }
        this.nextBoosterType = undefined
      }

      if (this.nextBoosterType) {
        const activeStageScore = initialStagesBooster.stages[activeBoosterStage].score
        let nextBoosterStageScore = 0
        if (initialStagesBooster.stages[activeBoosterStage + 1] !== undefined) {
          nextBoosterStageScore = initialStagesBooster.stages[activeBoosterStage + 1].score

          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[GENERATION] [chunkIndex: ${index}] Roll booster spawn height`
            )
          }

          this.nextBoosterSpawn = this.outerXor.nextInRange([
            activeStageScore,
            nextBoosterStageScore
          ])

          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[GENERATION] [chunkIndex: ${index}] Booster spawned: ${this.nextBoosterType} At: ${this.nextBoosterSpawn}`
            )
          }
        }
      }
    }

    const globalWeightsFiltered = this.configs.weights.filter(({ score }) => {
      const minScore = score[0] === 0 ? -Infinity : score[0]
      const maxScore = score[1] === 0 ? Infinity : score[1]
      return currentChunksLength >= minScore && currentChunksLength <= maxScore
    })

    if (globalWeightsFiltered.length === 0) {
      throw new Error('No weights found')
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(`[GENERATION] [chunkIndex: ${index}] Pick global weights`)
    }
    const globalWeights =
      globalWeightsFiltered[
        globalWeightsFiltered.length == 1
          ? 0
          : this.outerXor.nextInRange([0, globalWeightsFiltered.length - 1])
      ]

    let generation: GenerationType = GenerationType.Procedural

    const isProceduralMustSkip = this.proceduralChunksCount >= globalWeights.maxProceduralSeries
    const isCompositionMustSkip = this.compositionChunksCount >= globalWeights.maxCompositionSeries

    if (!isProceduralMustSkip && !isCompositionMustSkip) {
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(`[GENERATION] [chunkIndex: ${index}] Pick generation type`)
      }
      const roll = this.outerXor.nextFloat() * 100
      generation =
        this.selectWeighted(roll, [
          { option: GenerationType.Procedural, weight: globalWeights.procedural },
          { option: GenerationType.Composition, weight: globalWeights.composition }
        ]) ?? generation
    } else {
      if (isProceduralMustSkip) {
        generation = GenerationType.Composition
      } else if (isCompositionMustSkip) {
        generation = GenerationType.Procedural
      }

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Generation forced to ${generation}`
        )
      }
    }

    let id: number = 0
    let components: PlatformComponent[] | undefined = undefined
    let length: number | undefined = undefined

    if (generation === GenerationType.Procedural) {
      if (__IS_TEST__) {
        const outerSeed = this.outerXor.getState()
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] outerSeed: ${outerSeed[0]}, ${outerSeed[1]}`
        )
      }
      const [result, len] = this.generateEntitiesFromProcedural(currentChunksLength)
      components = result.components
      length = len
    } else {
      if (__IS_TEST__) {
        const outerSeed = this.outerXor.getState()
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] outerSeed: ${outerSeed[0]}, ${outerSeed[1]}`
        )
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Pick composition config`
        )
      }
      const rand = this.outerXor.nextFloat() * 100
      const tag = this.selectWeighted(rand, globalWeights.tags) ?? globalWeights.tags[0].option

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Picked composition tag ${tag}`
        )
      }

      const entries = this.configs.composition.filter(({ tags }) => tags.includes(tag))

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Filtered composition config, found ${entries.length}`
        )
      }

      let entriesFresh = entries.filter(({ id }) => !this.sessionProps.excludedPresets.includes(id))

      if (entriesFresh.length === 0 && entries.length !== 0) {
        const from = Math.floor(entries[0].id / 100) * 100
        const to = from + 99
        this.sessionProps.excludedPresets = this.sessionProps.excludedPresets.filter(
          id => id < from || id > to
        )
        entriesFresh = entries.filter(({ id }) => !this.sessionProps.excludedPresets.includes(id))
      }

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Filtered fresh composition config, found ${entriesFresh.length}`
        )
      }

      if (entriesFresh.length == 0) {
        throw new Error('No composition configs found')
      }

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Pick composition entry`
        )
      }

      let i = 0
      if (entriesFresh.length > 1) {
        const rand = this.outerXor.nextFloat() * 100

        i =
          this.selectWeighted(
            rand,
            entriesFresh.map((v, index) => ({ option: index, weight: v.weight }))
          ) ?? 0
      }

      const entry = entriesFresh[i]
      this.sessionProps.excludedPresets.push(entry.id)

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Excluded presets: ` +
            this.sessionProps.excludedPresets.join(',')
        )
        ;(window as any).mapgenLog?.push(
          `[GENERATION] [chunkIndex: ${index}] Picked composition entry with id ${entry.id}`
        )
      }

      const [result, len] = this.generateEntitiesFromComposition(entry.config, currentChunksLength)
      id = entry.id
      components = result.components
      length = len
    }

    if (generation === GenerationType.Procedural) {
      this.proceduralChunksCount++
      this.compositionChunksCount = 0
    } else if (generation === GenerationType.Composition) {
      this.compositionChunksCount++
      this.proceduralChunksCount = 0
    }

    const prevChunkCumulativeLenght = this.prevChunkCumulativeLength
    this.prevChunkCumulativeLength += length

    return {
      id,
      index,
      generation,
      components,
      length,
      prevChunkCumulativeLenght
    }
  }

  private generateEntitiesFromProcedural(currentChunksLength: number): [ChunkContent, number] {
    const components: PlatformComponent[] = []
    let entityIndex = 0

    const initialStagesGeneral = this.getInitialStages('general', currentChunksLength)
    let activeGeneralStage = initialStagesGeneral.activeStage
    let nextGeneralStageScore = initialStagesGeneral.nextStageScore

    let generalStage = initialStagesGeneral.stages[activeGeneralStage].general

    if (generalStage === undefined) {
      throw new Error('No general stage found')
    }

    const initialStagesProcedural = this.getInitialStages('procedural', currentChunksLength)
    let activeProceduralStage = initialStagesProcedural.activeStage
    let nextProceduralStageScore = initialStagesProcedural.nextStageScore

    let proceduralStage = initialStagesProcedural.stages[activeProceduralStage].procedural

    if (proceduralStage === undefined) {
      throw new Error('No procedural stage found')
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[PROCEDURAL] Active general stage: ${activeGeneralStage} Next stage score: ${nextGeneralStageScore}`
      )
    }

    let score = 0
    const targetScore = proceduralStage.score
    if (targetScore[0] === targetScore[1]) {
      score = targetScore[0]
    } else {
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick rand score`
        )
      }
      score = this.innerXor.nextInRange(targetScore)
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Picked rand score: ${score}`
        )
      }
    }

    let boostBigJumpMaxCount = 0
    let boostSmallJumpMaxCount = 0
    let boostBigJumpCurrentCount = 0
    let boostSmallJumpCurrentCount = 0

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[PROCEDURAL] [entityIndex: ${entityIndex}] Must pick max jump counts: ${proceduralStage.boostBigJumpCount !== undefined} ${proceduralStage.boostSmallJumpCount !== undefined}`
      )
    }

    if (proceduralStage.boostBigJumpCount !== undefined) {
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick max big boost count`
        )
      }
      boostBigJumpMaxCount = this.innerXor.nextInRange(proceduralStage.boostBigJumpCount)
    }

    if (proceduralStage.boostSmallJumpCount !== undefined) {
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick max small boost count`
        )
      }
      boostSmallJumpMaxCount = this.innerXor.nextInRange(proceduralStage.boostSmallJumpCount)
    }

    let prevY = 0
    let isPrevPlatformWreckable = false
    let prevPlatformType: EntityPlatformType | undefined = undefined
    let prevPlatformX: number | undefined = undefined
    let prevPlatformSeriesCount = 0
    const flyBoosterShouldSpawn =
      this.nextBoosterSpawn !== undefined && currentChunksLength + score > this.nextBoosterSpawn
    let flyBoosterAlreadySpawned = false

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[PROCEDURAL] [entityIndex: ${entityIndex}] Fly booster should spawn: ${flyBoosterShouldSpawn}`
      )
    }

    let boosterHeightLock: number | undefined = undefined

    while (prevY < score || (boosterHeightLock !== undefined && prevY < boosterHeightLock)) {
      entityIndex = components.length
      const isFirst = entityIndex === 0

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick roll for platform type isFirst: ${isFirst} isPrevPlatformWreckable: ${isPrevPlatformWreckable}`
        )
      }

      const typeRoll = this.innerXor.nextFloat() * 100
      const platformTypeConfig: DeterminePlatformTypeConfig = {
        platformDynH: !isFirst ? proceduralStage.dynamicHProb : undefined,
        platformWreckable: !(isFirst || isPrevPlatformWreckable)
          ? proceduralStage.wreckableProb
          : undefined,
        platformDisposable: proceduralStage.disposableProb
      }

      let platformType = this.determinePlatformType(typeRoll, platformTypeConfig)

      if (
        proceduralStage.dynamicHMax != undefined &&
        proceduralStage.dynamicHMax > 0 &&
        prevPlatformType === EntityType.PlatformDynamicH &&
        prevPlatformSeriesCount + 1 >= proceduralStage.dynamicHMax
      ) {
        platformType = EntityType.PlatformStatic
        prevPlatformSeriesCount = 0
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Platform type forced to: ${platformType}`
          )
        }
      }

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Picked platform type: ${platformType}`
        )
      }

      isPrevPlatformWreckable = platformType === EntityType.PlatformWreckable

      const distanceRange = this.transformDistanceRange(
        platformType != EntityType.PlatformWreckable
          ? proceduralStage.distance
          : proceduralStage.distanceWreckable,
        platformType
      )

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick rand distance`
        )
      }
      const distance = this.innerXor.nextInRange(distanceRange)

      const positionRange = this.transformPositionRange([0, -100], platformType)
      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(`[PROCEDURAL] [entityIndex: ${entityIndex}] Pick rand X`)
      }
      const x = this.innerXor.nextInRange(positionRange)
      const y = prevY + distance

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] typeRoll: ${typeRoll} platformType: ${platformType} x: ${x} y: ${y} distance: ${distance} distanceRange: ${distanceRange}`
        )
      }

      prevY = y

      const lastActiveProceduralStage = activeProceduralStage
      let updated = this.updateStagedVariables(
        currentChunksLength + y,
        initialStagesProcedural.stages,
        activeProceduralStage,
        nextProceduralStageScore
      )
      activeProceduralStage = updated.activeStage
      nextProceduralStageScore = updated.nextStageScore
      proceduralStage = initialStagesProcedural.stages[activeProceduralStage].procedural

      if (proceduralStage === undefined) {
        throw new Error('Procedural stage not found')
      }

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Procedural stage updated to ${activeProceduralStage}`
        )
      }

      if (activeProceduralStage !== lastActiveProceduralStage) {
        if (proceduralStage.boostBigJumpCount) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick max big boost count for next stage`
            )
          }
          boostBigJumpMaxCount = this.innerXor.nextInRange(proceduralStage.boostBigJumpCount)
        } else {
          boostBigJumpMaxCount = 0
        }

        if (proceduralStage.boostSmallJumpCount) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick max small boost count for next stage`
            )
          }
          boostSmallJumpMaxCount = this.innerXor.nextInRange(proceduralStage.boostSmallJumpCount)
        } else {
          boostSmallJumpMaxCount = 0
        }
      }

      updated = this.updateStagedVariables(
        currentChunksLength + y,
        initialStagesGeneral.stages,
        activeGeneralStage,
        nextGeneralStageScore
      )
      activeGeneralStage = updated.activeStage
      nextGeneralStageScore = updated.nextStageScore
      generalStage = initialStagesGeneral.stages[activeGeneralStage].general

      if (generalStage === undefined) {
        throw new Error('General stage not found')
      }

      this.ticketsSpawn += Math.floor(distance * POINTS_FACTOR * generalStage.ticketsFraction)

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[PROCEDURAL] [entityIndex: ${entityIndex}] Spawn tickets balance: ${this.ticketsSpawn}`
        )
      }

      let component: PlatformComponent | undefined = undefined

      if (platformType === EntityType.PlatformStatic) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y
        }
      } else if (
        platformType === EntityType.PlatformDisposable ||
        platformType === EntityType.PlatformWreckable ||
        platformType === EntityType.PlatformSpiked
      ) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y
        }
      } else if (platformType === EntityType.PlatformDynamicH) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y,
          movement: {
            duration: 3000
          }
        }

        if (component.movement) {
          let value = generalStage.movementDuration[0]
          if (value != generalStage.movementDuration[1]) {
            if (__IS_TEST__) {
              ;(window as any).mapgenLog?.push(
                `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick rand duration for movement`
              )
            }
            value = this.innerXor.nextInRange(generalStage.movementDuration)
          }
          component.movement.duration = value
        }
      }

      if (__IS_TEST__) {
        if (prevPlatformX !== undefined) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Check is new X required, less: ${prevPlatformX - 40 < x} more: ${prevPlatformX + 40 > x} x: ${x} prevPlatform: ${prevPlatformType} prevPlatformX: ${prevPlatformX}`
          )
        }
      }

      if (
        component &&
        (prevPlatformType === EntityType.PlatformStatic ||
          prevPlatformType === EntityType.PlatformWreckable) &&
        prevPlatformX !== undefined &&
        prevPlatformX - 40 < x &&
        prevPlatformX + 40 > x
      ) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick new rand X`
          )
        }
        component.x = this.innerXor.nextInRange(positionRange)
        if (Math.abs(prevPlatformX - component.x) < 40) {
          if (component.x - 150 > 0) {
            component.x -= 150
          } else {
            component.x += 150
          }
        }
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Force change position platformType: ${component.type} x: ${component.x} y: ${component.y}`
          )
        }
      }

      const isPlatformCanHaveTicketOrBooster =
        component &&
        (component.type === EntityType.PlatformStatic ||
          component.type === EntityType.PlatformDynamicH)

      if (
        !flyBoosterAlreadySpawned &&
        component &&
        isPlatformCanHaveTicketOrBooster &&
        flyBoosterShouldSpawn &&
        this.nextBoosterSpawn !== undefined &&
        this.nextBoosterSpawn <= currentChunksLength + y
      ) {
        if (this.nextBoosterType !== undefined) {
          const type = this.nextBoosterType
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Spawn fly booster (${type})`
            )
          }
          const positionX =
            component.x +
            this.innerXor.nextInRange([
              10,
              ENTITIES_SIZES[component.type][0] - ENTITIES_SIZES[type][0] - 10
            ])
          const booster: Booster = {
            type: type,
            index: component.index,
            x: positionX,
            y: component.y + ENTITIES_SIZES[type][1] - BOOSTER_Y_OFFSET
          }
          prevY += ENTITIES_SIZES[type][1]
          component.booster = booster
          flyBoosterAlreadySpawned = true
          boosterHeightLock = prevY + REVERSE_HEIGHT_FOR_BOOST[this.nextBoosterType]
          this.nextBoosterSpawn = undefined

          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Booster height lock: ${boosterHeightLock}`
            )
          }
        }
      }

      const targetTickets = generalStage.ticketsPerPlatform * 1000
      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        !flyBoosterAlreadySpawned &&
        component.booster === undefined &&
        this.ticketsSpawn >= targetTickets
      ) {
        this.ticketsSpawn -= targetTickets
        component.ticket = this.spawnTickets(component, generalStage.ticketsPerPlatform)

        if (component && component.ticket) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Spawn tickets: ${generalStage.ticketsPerPlatform} x: ${component.ticket.x} y: ${component.ticket.y}`
            )
          }
        }
      }

      const boosterTypeConfig: DetermineBoosterTypeConfig = {
        boostBigJump: proceduralStage.boostBigJumpProb,
        boostSmallJump: proceduralStage.boostSmallJumpProb
      }

      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        component.booster === undefined &&
        component.ticket === undefined &&
        (boosterTypeConfig.boostBigJump !== undefined ||
          boosterTypeConfig.boostSmallJump !== undefined)
      ) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick roll for booster type`
          )
        }
        const roll = this.innerXor.nextFloat() * 100
        const type = this.determineBoosterType(roll, boosterTypeConfig)

        if (
          (type !== undefined &&
            type === EntityType.BoosterBigJump &&
            boostBigJumpCurrentCount < boostBigJumpMaxCount) ||
          (type === EntityType.BoosterSmallJump &&
            boostSmallJumpCurrentCount < boostSmallJumpMaxCount)
        ) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[PROCEDURAL] [entityIndex: ${entityIndex}] Pick rand X for booster (${type}) position`
            )
          }
          const positionX =
            component.x +
            this.innerXor.nextInRange([
              10,
              ENTITIES_SIZES[component.type][0] - ENTITIES_SIZES[type][0] - 10
            ])
          const booster: Booster = {
            type,
            index: component.index,
            x: positionX,
            y: component.y + ENTITIES_SIZES[type][1] - BOOSTER_Y_OFFSET
          }
          prevY += ENTITIES_SIZES[type][1]
          component.booster = booster

          if (type == EntityType.BoosterBigJump) {
            boostBigJumpCurrentCount += 1
          } else if (type == EntityType.BoosterSmallJump) {
            boostSmallJumpCurrentCount += 1
          }
        }
      }

      const totalLength = prevY + this.prevChunkCumulativeLength
      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        this.sessionProps.tonSpawnPositions.length > 0 &&
        this.sessionProps.tonCoinValues.length > 0 &&
        this.sessionProps.tonSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.tonSpawnPositions.shift()

        const amount = this.sessionProps.tonCoinValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleTon][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleTon][1] + 15,
          type: EntityType.CollectibleTon,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Ton coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.customCoinType !== undefined &&
        this.sessionProps.customSpawnPositions.length > 0 &&
        this.sessionProps.customCoinValues.length > 0 &&
        this.sessionProps.customSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.customSpawnPositions.shift()

        const amount = this.sessionProps.customCoinValues.shift() ?? 0
        const coinType = this.sessionProps.customCoinType
        console.log('Map generator coin type', coinType)

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleCustomCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleCustomCoin][1] + 15,
          type: EntityType.CollectibleCustomCoin,
          collectible: { amount, coinType }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Custom coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.dynamicCoinType !== undefined &&
        this.sessionProps.dynamicCoinSpawnPositions.length > 0 &&
        this.sessionProps.dynamicCoinValues.length > 0 &&
        this.sessionProps.dynamicCoinSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.dynamicCoinSpawnPositions.shift()

        const amount = this.sessionProps.dynamicCoinValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleDynamicCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleDynamicCoin][1] + 15,
          type: EntityType.CollectibleDynamicCoin,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Custom coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformCanHaveTicketOrBooster &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.puzzleCoinsType !== undefined &&
        this.sessionProps.puzzleCoinsSpawnPositions.length > 0 &&
        this.sessionProps.puzzleCoinsValues.length > 0 &&
        this.sessionProps.puzzleCoinsSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.puzzleCoinsSpawnPositions.shift()

        const amount = this.sessionProps.puzzleCoinsValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectiblePuzzleCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectiblePuzzleCoin][1] + 15,
          type: EntityType.CollectiblePuzzleCoin,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[PROCEDURAL] [entityIndex: ${entityIndex}] Custom coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (component) {
        components.push(component)

        if (component.type === prevPlatformType) {
          prevPlatformSeriesCount++
        } else {
          prevPlatformSeriesCount = 0
        }

        prevPlatformType = component.type
        prevPlatformX = component.x
      }
    }

    return [{ components }, prevY]
  }

  private determinePlatformType(
    typeRoll: number,
    config: DeterminePlatformTypeConfig
  ): EntityPlatformType {
    const options: { option: EntityPlatformType; weight: number }[] = []
    let total = 0

    if (config.platformDynH !== undefined) {
      options.push({ option: EntityType.PlatformDynamicH, weight: config.platformDynH })
      total += config.platformDynH
    }

    if (config.platformWreckable !== undefined) {
      options.push({
        option: EntityType.PlatformWreckable,
        weight: config.platformWreckable
      })
      total += config.platformWreckable
    }

    if (config.platformDisposable !== undefined) {
      options.push({
        option: EntityType.PlatformDisposable,
        weight: config.platformDisposable
      })
      total += config.platformDisposable
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[PLATFORM_TYPE] Roll: ${typeRoll} options: ${options.length}`
      )
    }

    if (options.length == 0) {
      return EntityType.PlatformStatic
    }

    options.push({ option: EntityType.PlatformStatic, weight: 100 - total })

    return this.selectWeighted(typeRoll, options) ?? EntityType.PlatformStatic
  }

  private determineBoosterType(
    typeRoll: number,
    config: DetermineBoosterTypeConfig
  ): EntityBoosterType | undefined {
    const weights: { option: EntityBoosterType; weight: number }[] = []
    let total = 0

    if (config.boostBigJump !== undefined) {
      weights.push({ option: EntityType.BoosterBigJump, weight: config.boostBigJump })
      total += config.boostBigJump
    }

    if (config.boostSmallJump !== undefined) {
      weights.push({ option: EntityType.BoosterSmallJump, weight: config.boostSmallJump })
      total += config.boostSmallJump
    }

    if (weights.length == 0) {
      return undefined
    }

    weights.push({ option: EntityType.None, weight: 100 - total })

    const result = this.selectWeighted(typeRoll, weights)

    if (result === undefined || result == EntityType.None) {
      return undefined
    }

    return result
  }

  private generateEntitiesFromComposition(
    config: CompositionConfig,
    currentChunksLength: number
  ): [ChunkContent, number] {
    const components: PlatformComponent[] = []
    let prevY = 0

    const initialStagesGeneral = this.getInitialStages('general', currentChunksLength)
    let activeGeneralStage = initialStagesGeneral.activeStage
    let nextGeneralStageScore = initialStagesGeneral.nextStageScore

    let generalStage = initialStagesGeneral.stages[activeGeneralStage].general

    if (generalStage === undefined) {
      throw new Error('No general stage found')
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[COMPOSITION] Active general stage: ${activeGeneralStage} Next stage score: ${nextGeneralStageScore}`
      )
    }

    for (let index = 0; index < config.composables.length; index++) {
      const entityIndex = components.length
      const composable = config.composables[index]

      if (composable.platform.probability) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Roll platform probability`
          )
        }
        const roll = this.innerXor.nextFloat() * 100
        if (roll > composable.platform.probability) {
          continue
        }
      }

      const platformType = composable.platform.type

      let x = composable.platform.x
      let y = composable.platform.y ?? 0

      if (composable.platform.distance !== undefined) {
        let distance = 0
        if (composable.platform.distance[0] === composable.platform.distance[1]) {
          distance = this.transformDistance(composable.platform.distance[0], platformType)
        } else {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[COMPOSITION] [entityIndex: ${entityIndex}] Pick platform distance`
            )
          }
          distance = this.innerXor.nextInRange(
            this.transformDistanceRange(composable.platform.distance, platformType)
          )
        }
        y = prevY + distance
      }

      if (composable.platform.position !== undefined) {
        if (composable.platform.position[0] === composable.platform.position[1]) {
          x = this.transformPosition(composable.platform.position[0], platformType)
        } else {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[COMPOSITION] [entityIndex: ${entityIndex}] Pick platform position`
            )
          }
          x = this.innerXor.nextInRange(
            this.transformPositionRange(composable.platform.position, platformType)
          )
        }
      } else if (x === undefined) {
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Pick platform position random`
          )
        }
        x = this.innerXor.nextInRange(this.transformPositionRange([0, -100], platformType))
      }

      const updated = this.updateStagedVariables(
        currentChunksLength + y,
        initialStagesGeneral.stages,
        activeGeneralStage,
        nextGeneralStageScore
      )
      activeGeneralStage = updated.activeStage
      nextGeneralStageScore = updated.nextStageScore
      generalStage = initialStagesGeneral.stages[activeGeneralStage].general

      if (generalStage === undefined) {
        throw new Error('General stage not found')
      }

      this.ticketsSpawn += Math.floor((y - prevY) * POINTS_FACTOR * generalStage.ticketsFraction)

      prevY = y

      if (__IS_TEST__) {
        ;(window as any).mapgenLog?.push(
          `[COMPOSITION] [entityIndex: ${entityIndex}] Spawn tickets balance: ${this.ticketsSpawn}`
        )
      }

      let component: PlatformComponent | undefined = undefined

      if (
        platformType == EntityType.PlatformEmpty ||
        platformType === EntityType.PlatformStatic ||
        platformType === EntityType.PlatformShifting ||
        platformType === EntityType.PlatformIce ||
        platformType === EntityType.PlatformBooster ||
        platformType === EntityType.PlatformTrap ||
        platformType === EntityType.PlatformPropeller
      ) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y
        }
      } else if (
        platformType === EntityType.PlatformDisposable ||
        platformType === EntityType.PlatformWreckable ||
        platformType === EntityType.PlatformDynamicH ||
        platformType === EntityType.PlatformDynamicV ||
        platformType === EntityType.PlatformLevitating
      ) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y
        }

        if (
          composable.platform.movable === true ||
          platformType === EntityType.PlatformDynamicH ||
          platformType === EntityType.PlatformDynamicV ||
          platformType === EntityType.PlatformLevitating
        ) {
          let value = 1000
          let duration: [number, number]

          if (composable.platform.movementDuration === undefined) {
            duration = generalStage.movementDuration
          } else {
            duration = composable.platform.movementDuration
          }

          if (duration[0] !== duration[1]) {
            if (__IS_TEST__) {
              ;(window as any).mapgenLog?.push(
                `[COMPOSITION] [entityIndex: ${entityIndex}] Pick platform movement duration`
              )
            }
            value = this.innerXor.nextInRange(duration)
          } else {
            value = duration[0]
          }

          component.movement = {
            duration: value
          }
        }

        if (component.movement) {
          if (composable.platform.movementRange !== undefined) {
            component.movement!.range = composable.platform.movementRange
          } else if (platformType === EntityType.PlatformDynamicV) {
            component.movement!.range = [100, 100]
          }
        }
      } else if (
        platformType === EntityType.PlatformInvisible ||
        platformType === EntityType.PlatformSpiked
      ) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y,
          visibility: {
            visible: false
          }
        }

        if (composable.platform.visible !== undefined) {
          component.visibility = {
            visible: composable.platform.visible
          }
        }
      } else if (platformType === EntityType.PlatformExplosive) {
        component = {
          type: platformType,
          index: entityIndex,
          x,
          y,
          explosive: {
            distance: 100,
            delay: 1000,
            decoration: false
          }
        }

        if (composable.platform.explosiveDistance !== undefined) {
          component.explosive!.distance = composable.platform.explosiveDistance
        }

        if (composable.platform.explosiveDelay !== undefined) {
          component.explosive!.delay = composable.platform.explosiveDelay
        }

        if (composable.platform.explosiveAsDecoration !== undefined) {
          component.explosive!.decoration = composable.platform.explosiveAsDecoration
        }
      }

      const isPlatformAbleToHaveCollectible =
        component &&
        (component.type === EntityType.PlatformStatic ||
          component.type === EntityType.PlatformDynamicH ||
          component.type === EntityType.PlatformEmpty)

      if (
        isPlatformAbleToHaveCollectible &&
        composable.booster === undefined &&
        composable.mob === undefined &&
        composable.decoration === undefined &&
        composable.token === undefined &&
        component
      ) {
        const targetTickets = generalStage.ticketsPerPlatform * 1000
        if (composable.platform.tickets !== false && this.ticketsSpawn >= targetTickets) {
          this.ticketsSpawn -= targetTickets
          component.ticket = this.spawnTickets(component, generalStage.ticketsPerPlatform)

          if (component && component.ticket) {
            if (__IS_TEST__) {
              ;(window as any).mapgenLog?.push(
                `[COMPOSITION] [entityIndex: ${entityIndex}] Spawn tickets: ${generalStage.ticketsPerPlatform} x: ${component.ticket.x} y: ${component.ticket.y}`
              )
            }
          }
        } else if (
          generalStage.tonPerPlatform &&
          composable.platform.tonToken &&
          generalStage.tonPerPlatform > 0
        ) {
          component.token = this.spawnCoin(
            component,
            EntityType.CollectibleTon,
            generalStage.tonPerPlatform
          )
          if (component && component.token) {
            if (__IS_TEST__) {
              ;(window as any).mapgenLog?.push(
                `[COMPOSITION] [entityIndex: ${entityIndex}] Spawn ${generalStage.tonPerPlatform} nanoton on x: ${component.token.x} y: ${component.token.y}`
              )
            }
          }
        }
      }

      if (component && component.ticket === undefined && composable.booster) {
        if (composable.booster.probability) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[COMPOSITION] [entityIndex: ${entityIndex}] Roll booster probability`
            )
          }
          const roll = this.innerXor.nextFloat() * 100
          if (roll > composable.booster.probability) {
            continue
          }
        }
        const type = composable.booster.type
        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Pick booster position`
          )
        }
        const positionX =
          component.x +
          this.innerXor.nextInRange([
            10,
            ENTITIES_SIZES[component.type][0] - ENTITIES_SIZES[type][0] - 10
          ])

        const positionY = component.y + ENTITIES_SIZES[type][1] - BOOSTER_Y_OFFSET

        const booster: Booster = {
          type,
          index: component.index,
          x: positionX,
          y: positionY
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Calculated booster y: ${positionY}`
          )
        }

        component.booster = booster
      }

      if (component && component.ticket === undefined && composable.mob) {
        if (composable.mob.probability) {
          if (__IS_TEST__) {
            ;(window as any).mapgenLog?.push(
              `[COMPOSITION] [entityIndex: ${entityIndex}] Roll mob probability`
            )
          }
          const roll = this.innerXor.nextFloat() * 100
          if (roll > composable.mob.probability) {
            continue
          }
        }

        const type = composable.mob.type

        component.mob = {
          type,
          index: component.index,
          x: component.x + (ENTITIES_SIZES[platformType][0] / 2 - ENTITIES_SIZES[type][0] / 2),
          y: component.y + ENTITIES_SIZES[type][1]
        }

        component.mob.mobMovement = {
          moveHalfScreen: composable.mob.moveHalfScreen || false
        }
      }

      if (component && composable.decoration !== undefined) {
        const type: EntityDecorationsType = composable.decoration.type
        try {
          component.decoration = {
            type,
            index: component.index,
            x: component.x + (ENTITIES_SIZES[platformType][0] / 2 - ENTITIES_SIZES[type][0] / 2),
            y: component.y + ENTITIES_SIZES[type][1],
            key: composable.decoration.key
          }
        } catch (e) {
          console.error('error creating decoration: ', e)
        }
      }

      const totalLength = prevY + this.prevChunkCumulativeLength
      if (
        component &&
        isPlatformAbleToHaveCollectible &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        this.sessionProps.tonSpawnPositions.length > 0 &&
        this.sessionProps.tonCoinValues.length > 0 &&
        this.sessionProps.tonSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.tonSpawnPositions.shift()

        const amount = this.sessionProps.tonCoinValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleTon][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleTon][1] + 15,
          type: EntityType.CollectibleTon,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Ton coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformAbleToHaveCollectible &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.customCoinType !== undefined &&
        this.sessionProps.customSpawnPositions.length > 0 &&
        this.sessionProps.customCoinValues.length > 0 &&
        this.sessionProps.customSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.customSpawnPositions.shift()

        const amount = this.sessionProps.customCoinValues.shift() ?? 0
        const coinType = this.sessionProps.customCoinType
        console.log('Map generator coin type', coinType)

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleCustomCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleCustomCoin][1] + 15,
          type: EntityType.CollectibleCustomCoin,
          collectible: { amount, coinType }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Custom coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformAbleToHaveCollectible &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.dynamicCoinType !== undefined &&
        this.sessionProps.dynamicCoinSpawnPositions.length > 0 &&
        this.sessionProps.dynamicCoinValues.length > 0 &&
        this.sessionProps.dynamicCoinSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.dynamicCoinSpawnPositions.shift()

        const amount = this.sessionProps.dynamicCoinValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectibleDynamicCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectibleDynamicCoin][1] + 15,
          type: EntityType.CollectibleDynamicCoin,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Dynamic coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (
        component &&
        isPlatformAbleToHaveCollectible &&
        component.booster === undefined &&
        component.ticket === undefined &&
        component.mob === undefined &&
        component.token === undefined &&
        this.sessionProps.puzzleCoinsType !== undefined &&
        this.sessionProps.puzzleCoinsSpawnPositions.length > 0 &&
        this.sessionProps.puzzleCoinsValues.length > 0 &&
        this.sessionProps.puzzleCoinsSpawnPositions[0] < totalLength
      ) {
        this.sessionProps.puzzleCoinsSpawnPositions.shift()

        const amount = this.sessionProps.puzzleCoinsValues.shift() ?? 0

        component.token = {
          index: component.index,
          x:
            component.x +
            (ENTITIES_SIZES[component.type][0] / 2 -
              ENTITIES_SIZES[EntityType.CollectiblePuzzleCoin][0] / 2),
          y: component.y + ENTITIES_SIZES[EntityType.CollectiblePuzzleCoin][1] + 15,
          type: EntityType.CollectiblePuzzleCoin,
          collectible: { amount }
        }

        if (__IS_TEST__) {
          ;(window as any).mapgenLog?.push(
            `[COMPOSITION] [entityIndex: ${entityIndex}] Puzzle coin spawned at ${component.token.x},${component.token.y} (Amount: ${amount})`
          )
        }
      }

      if (component) {
        components.push(component)
        if (composable.platform.clone !== undefined) {
          for (let i = 1; i < composable.platform.clone + 1; i++) {
            const component = components[components.length - 1]
            components.push({
              ...component,
              index: components.length,
              x: component.x + ENTITIES_SIZES[component.type][0] + 5,
              clone: true
            })
          }
        }
      }
    }

    return [{ components }, prevY]
  }

  private getInitialStages(
    type: StagedVariablesTypes,
    currentChunksLength: number
  ): {
    stages: StagedVariables[]
    activeStage: number
    nextStageScore: number | undefined
  } {
    const stages: StagedVariables[] = []
    let nextStageScore: number | undefined = undefined
    const variables = this.configs.variables.filter(v => v[type] !== undefined)
    const len = variables.length

    for (let i = len - 1; i >= 0; i--) {
      const variable = variables[i]

      if (variable.score >= currentChunksLength) {
        stages.unshift(variable)
      } else {
        stages.unshift(variable)
        break
      }
    }

    if (stages.length > 1) {
      nextStageScore = stages[1].score
    }

    const activeStage = 0
    return { stages, activeStage, nextStageScore }
  }

  private updateStagedVariables(
    newChunksLength: number,
    stages: StagedVariables[],
    activeStage: number,
    nextStageScore: number | undefined
  ) {
    if (nextStageScore && nextStageScore <= newChunksLength) {
      activeStage += 1
      if (activeStage >= stages.length - 1) {
        activeStage = stages.length - 1
        nextStageScore = undefined
      } else {
        nextStageScore = stages[activeStage + 1]?.score
      }
    }

    return { activeStage, nextStageScore }
  }

  private spawnTickets(component: PlatformComponent, amount: number): Ticket {
    const type = EntityType.CollectibleTicket
    const positionX =
      component.x +
      this.innerXor.nextInRange([
        10,
        ENTITIES_SIZES[component.type][0] - ENTITIES_SIZES[type][0] - 10
      ])

    return {
      type: EntityType.CollectibleTicket,
      index: component.index,
      x: positionX,
      y: component.y + ENTITIES_SIZES[type][1],
      collectible: {
        amount
      }
    }
  }

  private spawnCoin(
    component: PlatformComponent,
    coinType: CollectibleTokenType,
    amount: number
  ): Token {
    const positionX =
      component.x +
      this.innerXor.nextInRange([
        10,
        ENTITIES_SIZES[component.type][0] - ENTITIES_SIZES[coinType][0] - 10
      ])

    return {
      type: coinType,
      index: component.index,
      x: positionX,
      y: component.y + ENTITIES_SIZES[coinType][1],
      collectible: {
        amount
      }
    }
  }

  private transformDistanceRange(value: [number, number], entity: EntityType): [number, number] {
    return [this.transformDistance(value[0], entity), this.transformDistance(value[1], entity)]
  }

  private transformDistance(value: number, entity: EntityType) {
    const entityHeight = ENTITIES_SIZES[entity][1]
    const jumpHeight = PLAYER_JUMP_HEIGHT

    let result = 0
    if (value >= 0) {
      result = value
    } else if (value <= -1 && value >= -100) {
      result = Math.floor(jumpHeight * (-value / 100)) + entityHeight
    } else if (value <= -101 && value >= -201) {
      result = Math.floor(jumpHeight * (-(value + 101) / 100))
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[TRANSFORM_DISTANCE] [entity: ${entity}] ${value} => ${result}`
      )
    }

    return result
  }

  private transformPositionRange(value: [number, number], entity: EntityType): [number, number] {
    return [this.transformPosition(value[0], entity), this.transformPosition(value[1], entity)]
  }

  private transformPosition(value: number, entity: EntityType) {
    const entityWidth = ENTITIES_SIZES[entity][0]
    const screenWidth = SCREEN_WIDTH

    let result = 0
    if (value >= 0) {
      result = value
    } else if (value <= -1 && value >= -100) {
      result = Math.floor(screenWidth * (-value / 100))
    } else if (value <= -101 && value >= -151) {
      result = screenWidth / 2 - entityWidth / 2 + Math.floor(screenWidth * (-(value + 101) / 100))
    } else if (value <= -152 && value >= -202) {
      result = screenWidth / 2 - entityWidth / 2 - Math.floor(screenWidth * (-(value + 152) / 100))
    }

    if (result >= screenWidth - entityWidth) {
      result = result - entityWidth
    } else if (result <= 0) {
      result = 0
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[TRANSFORM_POSITION] [entity: ${entity}] ${value} => ${result}`
      )
    }

    return result
  }

  private selectWeighted<T>(
    typeRoll: number,
    options: Array<{ option: T; weight: number }>
  ): T | undefined {
    let totalProbability = 0
    const validOptions: Array<[T, number]> = []

    for (const element of options) {
      if (element.weight !== undefined) {
        totalProbability += element.weight
        validOptions.push([element.option, element.weight])
      }
    }

    const normalizedOptions: Array<[T, number]> = validOptions.map(([option, probability]) => [
      option,
      (probability / totalProbability) * 100
    ])

    let cumulativeSum = 0
    const cumulativeProbabilities: Array<[T, number]> = []

    for (const [option, normalizedProb] of normalizedOptions) {
      cumulativeSum += normalizedProb
      cumulativeProbabilities.push([option, cumulativeSum])
    }

    for (const [option, cumulative] of cumulativeProbabilities) {
      if (typeRoll < cumulative) {
        if (__IS_TEST__) {
          const optionsStr = options.map(option => option.weight.toFixed(1)).join(' | ')
          ;(window as any).mapgenLog?.push(
            `[SELECT_WEIGHTED] typeRoll: ${typeRoll} cumulative: ${cumulative} options length: ${options.length} options: ${optionsStr}`
          )
        }
        return option
      }
    }

    if (__IS_TEST__) {
      ;(window as any).mapgenLog?.push(
        `[SELECT_WEIGHTED] typeRoll: ${typeRoll} cumulativeSum: ${cumulativeSum} undefined`
      )
    }

    return undefined
  }
}
