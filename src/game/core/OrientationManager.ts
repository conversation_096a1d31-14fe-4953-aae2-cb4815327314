import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import type { EventBus } from '@/shared/types'

// OrientationManager.ts
export class OrientationManager {
  private isPortrait: boolean = true
  private orientationWarning: Phaser.GameObjects.Text | null = null
  private orientationWarningOverlay: Phaser.GameObjects.Rectangle | null = null
  private readonly uiEventBus: EventBus
  private readonly scene: Phaser.Scene
  private fullscreenCheckInterval: number | null = null

  constructor(uiEventBus: EventBus, scene: Phaser.Scene) {
    this.uiEventBus = uiEventBus
    this.scene = scene
    this.setupEventListeners()
    this.trackInitialOrientation()
    this.startFullscreenCheck()
  }

  private setupEventListeners(): void {
    if (typeof screen?.orientation?.addEventListener === 'function') {
      screen.orientation.addEventListener('change', this.handleOrientationChange.bind(this))
    }
    window.addEventListener('resize', this.handleResize.bind(this))
    document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
  }

  private trackInitialOrientation(): void {
    this.isPortrait = this.checkIsPortrait()
    this.uiEventBus.emit(GAME_EVENTS.ORIENTATION_STATE_CHANGED, this.isPortrait)
    this.updateOrientationWarning()
  }

  public isDeviceInPortrait(): boolean {
    return this.isPortrait
  }

  public isDeviceInLandscape(): boolean {
    return !this.isPortrait
  }

  private handleOrientationChange(): void {
    this.updateOrientationState()
  }

  private handleResize(): void {
    this.updateOrientationState()
  }

  private handleFullscreenChange(): void {
    setTimeout(() => this.updateOrientationState(), 100)
  }

  private startFullscreenCheck(): void {
    this.fullscreenCheckInterval = window.setInterval(() => {
      if (document.fullscreenElement) {
        this.updateOrientationState()
      }
    }, 500)
  }

  private stopFullscreenCheck(): void {
    if (this.fullscreenCheckInterval !== null) {
      clearInterval(this.fullscreenCheckInterval)
      this.fullscreenCheckInterval = null
      console.log('Fullscreen check interval cleared')
    }
  }

  private checkIsPortrait(): boolean {
    const ratio = window.innerHeight / window.innerWidth
    const isPortrait = ratio > 1 || window.matchMedia('(orientation: portrait)').matches
    return isPortrait
  }

  private updateOrientationState(): void {
    this.isPortrait = this.checkIsPortrait()
    this.uiEventBus.emit(GAME_EVENTS.ORIENTATION_STATE_CHANGED, this.isPortrait)
    this.updateOrientationWarning()
  }

  private updateOrientationWarning(): void {
    if (this.isPortrait) {
      this.hideOrientationWarning()
    } else {
      this.showOrientationWarning()
    }
  }

  private showOrientationWarning(): void {
    if (!this.orientationWarningOverlay) {
      this.orientationWarningOverlay = this.addOverlay()
    }
    if (!this.orientationWarning) {
      this.orientationWarning = this.addWarningText()
    }
  }

  private hideOrientationWarning(): void {
    if (this.orientationWarningOverlay) {
      this.orientationWarningOverlay.destroy()
      this.orientationWarningOverlay = null
    }
    if (this.orientationWarning) {
      this.orientationWarning.destroy()
      this.orientationWarning = null
    }
  }

  private addOverlay(): Phaser.GameObjects.Rectangle {
    return this.scene.add
      .rectangle(
        this.scene.cameras.main.centerX,
        this.scene.cameras.main.centerY,
        this.scene.cameras.main.width,
        this.scene.cameras.main.height,
        0x000000,
        1
      )
      .setOrigin(0.5)
  }

  private addWarningText(): Phaser.GameObjects.Text {
    return this.scene.add
      .text(
        this.scene.cameras.main.centerX,
        this.scene.cameras.main.centerY,
        'Use portrait orientation for better experience',
        { fontSize: '24px', color: '#ffffff' }
      )
      .setOrigin(0.5)
  }

  public destroy(): void {
    this.stopFullscreenCheck()
    window.removeEventListener('resize', this.handleResize.bind(this))
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
    if (typeof screen?.orientation?.removeEventListener === 'function') {
      screen.orientation.removeEventListener('change', this.handleOrientationChange.bind(this))
    }
  }
}
