import { defineQuery, type IWorld } from 'bitecs'
import type { BaseBooster } from '../../CoreGameplay/Boosters/BaseBooster'
import type { CollectableItemBase } from '../../CoreGameplay/Collectables/CollectableItemBase'
import { GameViewConsts } from '../../CoreGameplay/Constants/GameViewConsts'
import type { BaseEnemy } from '../../CoreGameplay/Enemies/BaseEnemy'
import { Active, Platform, Position, Velocity } from '../../ecs/Components/EntityComponents'

const Paused = new Set<number>() // To keep track of paused entities

// Define a query specifically for horizontal platforms
const horizontalPlatformQuery = defineQuery([Position, Velocity, Platform, Active])

export function createHorizontalMovementSystem(
  duration: number,
  booster: BaseBooster | null,
  mob: BaseEnemy | null,
  collectable: CollectableItemBase | null
) {
  return (world: IWorld, delta: number) => {
    const entities = horizontalPlatformQuery(world)
    for (let i = 0; i < entities.length; i++) {
      const eid = entities[i]
      if (Paused.has(eid) || !Active.active[eid]) continue // Skip paused and inactive entities

      if (Velocity.x[eid] !== undefined && Position.x[eid] !== undefined) {
        const screenRight = GameViewConsts.REF_WIDTH
        const screenLeft = 0
        const spriteWidth = 50 // Assuming constant sprite width; replace as needed
        let targetX

        // Determine the initial direction of movement
        if (Velocity.x[eid] === 0) {
          if (Position.x[eid] < screenRight / 2) {
            targetX = screenRight - spriteWidth
            Velocity.x[eid] = (targetX - Position.x[eid]) / (duration / 1000)
          } else {
            targetX = screenLeft
            Velocity.x[eid] = (targetX - Position.x[eid]) / (duration / 1000)
          }
        }

        // Update the position based on velocity
        Position.x[eid] += Velocity.x[eid] * (delta / 1000) // Convert delta to seconds for consistency

        // Handle oscillation when reaching boundaries
        if (
          (Velocity.x[eid] > 0 && Position.x[eid] >= screenRight - spriteWidth) ||
          (Velocity.x[eid] < 0 && Position.x[eid] <= screenLeft)
        ) {
          Velocity.x[eid] = -Velocity.x[eid]
        }

        // Sync the position of the booster and mob if they exist
        if (booster) {
          booster.syncPosition(Position.x[eid])
        }
        if (mob) {
          mob.syncPosition(Position.x[eid])
        }
        if (collectable) {
          collectable.syncPosition(Position.x[eid])
        }
      }
    }
  }
}

export function updatePositionSystem(world: IWorld, delta: number) {
  const entities = horizontalPlatformQuery(world)
  for (let i = 0; i < entities.length; i++) {
    const eid = entities[i]
    if (Paused.has(eid)) continue // Skip paused entities

    if (Position.x[eid] !== undefined && Velocity.x[eid] !== undefined) {
      Position.x[eid] += Velocity.x[eid] * (delta / 1000) // Convert delta to seconds for consistency
    }
    if (Position.y[eid] !== undefined && Velocity.y[eid] !== undefined) {
      Position.y[eid] += Velocity.y[eid] * (delta / 1000) // Convert delta to seconds for consistency
    }
  }
}

// Function to pause movement of an entity
export function pauseMovement(entityId: number) {
  Paused.add(entityId)
}

// Function to resume movement of an entity
export function resumeMovement(entityId: number) {
  Paused.delete(entityId)
}
