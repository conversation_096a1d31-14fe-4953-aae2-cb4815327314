import { defineQuery, type IWorld } from 'bitecs'
import { bulletHandler } from '../../CoreGameplay/Player/PlayerShootingHandler'
import { Active, Position, Rotation, Velocity } from '../Components/EntityComponents'
import { getGameWorld } from '../GameWorld'

export const bulletQuery = defineQuery([Position, Velocity, Active, Rotation])

export const bulletSystem = (world: IWorld) => {
  const entities = bulletQuery(getGameWorld())
  for (let i = 0; i < entities.length; i++) {
    const bulletEntity = entities[i]
    if (Active.active[bulletEntity]) {
      Position.x[bulletEntity] += Velocity.x[bulletEntity] * (1 / 60)
      Position.y[bulletEntity] += Velocity.y[bulletEntity] * (1 / 60)

      const bulletSprite = bulletHandler.bullets.get(bulletEntity)
      if (bulletSprite) {
        bulletSprite.setPosition(Position.x[bulletEntity], Position.y[bulletEntity])
      }
    }
  }
  return world
}
