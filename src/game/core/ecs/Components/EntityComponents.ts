import { defineComponent, Types } from 'bitecs'

export const Active = defineComponent({
  active: Types.ui8
})

export const HorizontalVelocity = defineComponent({
  x: Types.f32
})

export const VerticalVelocity = defineComponent({
  y: Types.f32
})

export const Enemy = defineComponent({
  isDefeated: Types.ui8,
  canBeKilled: Types.ui8,
  mobType: Types.ui32,
  tintTimer: Types.ui32,
  proximityToTriggerBehaviour: Types.ui32
})

export const Health = defineComponent({
  value: Types.i32
})

export const PhysicsComponent = defineComponent({
  gravity: Types.f32,
  mass: Types.f32
})

export const Position = defineComponent({
  x: Types.f32,
  y: Types.f32
})

export const Rotation = defineComponent({
  angle: Types.f32
})

export const SpriteComponent = defineComponent({
  id: Types.ui32
})

export const Velocity = defineComponent({
  x: Types.f32,
  y: Types.f32
})

export const Original = defineComponent({
  X: Types.f32,
  Y: Types.f32,
  ScaleX: Types.f32,
  ScaleY: Types.f32
})

export const Platform = defineComponent({
  shouldAnimateTouch: Types.ui8,
  isActive: Types.ui8,
  shouldTrackInteraction: Types.ui8,
  platformType: Types.ui32,
  shakeTimer: Types.ui32
})
