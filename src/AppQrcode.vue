<template>
  <div class="qr-code">
    <div class="qr-code__wrapper">
      <img class="qr-code__image" src="@/assets/images/temp/qr-code.png" alt="QR code" />
      <img class="qr-code__image-uni" src="@/assets/images/temp/uni.png" alt="QR code" />
    </div>
    <p class="qr-code__text">Use Telegram Mobile version</p>
  </div>
</template>

<style lang="scss">
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

body {
  width: 100dvw;
  height: 100dvh;
  overflow: inherit;
}

#app {
  width: inherit;
  height: inherit;
  max-width: 100dvw;
  max-height: 100dvh;
}

.qr-code {
  position: relative;
  padding: 0 16px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  background: linear-gradient(180deg, #28599C 0%, #73b7ff 100%);

  &__wrapper {
    position: relative;
    width: 100%;
  }

  &__image {
    width: 100%;
  }

  &__image-uni {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    width: 70px;
    background-color: #73b7ff;
    padding: 9px 11px 11px 9px;
    border-radius: 7px;
    border: 3px solid #28599C;
  }

  &__text {
    font-size: 25px;
    font-weight: 700;
    color: white;
  }
}
</style>
