// UIUtils.ts
export class UIUtils {
  public static displayMessage(methodName: string, message: string): void {
    const messageElement = document.createElement('div')
    messageElement.style.position = 'fixed'
    messageElement.style.bottom = '10px'
    messageElement.style.left = '10px'
    messageElement.style.backgroundColor = 'black'
    messageElement.style.color = 'red'
    messageElement.style.padding = '10px'
    messageElement.style.borderRadius = '5px'
    messageElement.style.fontFamily = 'Arial, sans-serif'
    messageElement.style.fontSize = '14px'
    messageElement.style.zIndex = '1000'

    messageElement.innerHTML = `<strong>${methodName}:</strong> ${message}`

    document.body.appendChild(messageElement)

    // Remove the message after 5 seconds
    setTimeout(() => {
      document.body.removeChild(messageElement)
    }, 5000)
  }
}
