export class Logger {
  private isLoggingEnabled: boolean = false

  toggleLogging() {
    this.isLoggingEnabled = !this.isLoggingEnabled
    console.log('Logging Enabled: ', this.isLoggingEnabled)
  }

  private getTimeStamp(): string {
    const now = new Date()
    const timePart = now.toTimeString().split(' ')[0]
    const milliseconds = ('000' + now.getMilliseconds()).slice(-3)

    return `${timePart}.${milliseconds}`
  }

  public info(message: string, ...optionalParams: any[]): void {
    // if (this.isLoggingEnabled) {
    //   console.info(`[INFO] ${this.getTimeStamp()} - ${message}`, ...optionalParams)
    // }
  }

  public warn(message: string, ...optionalParams: any[]): void {
    // if (this.isLoggingEnabled) {
    //   console.warn(`[WARN] ${this.getTimeStamp()} - ${message}`, ...optionalParams)
    // }
  }

  public error(message: string, ...optionalParams: any[]): void {
    if (this.isLoggingEnabled) {
      console.error(`[ERROR] ${this.getTimeStamp()} - ${message}`, ...optionalParams)
    }
  }

  public debug(message: string, ...optionalParams: any[]): void {
    // if (this.isLoggingEnabled) {
    //   console.debug(`[DEBUG] ${this.getTimeStamp()} - ${message}`, ...optionalParams)
    // }
  }

  public log(level: string, message: string, ...optionalParams: any[]): void {
    // if (this.isLoggingEnabled) {
    //   console.log(`[${level.toUpperCase()}] ${this.getTimeStamp()} - ${message}`, ...optionalParams)
    // }
  }
}

export const logger = new Logger()
