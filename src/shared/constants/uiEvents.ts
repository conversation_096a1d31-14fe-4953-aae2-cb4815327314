export const UI_EVENTS = {
  PAUSE: 'pauseGame',
  UNPAUSE: 'unpauseGame',
  START: 'startGame',
  DESTROY: 'destroyGame',
  GAME_OVER: 'gameOver',
  GAME_CONTINUED: 'gameContinued',
  RESTART: 'restartGame'
} as const

export const GAME_EVENTS = {
  UPDATE_SCORE: 'updateScore',
  UPDATE_TICKETS_SCORE: 'updateTicketsScore',
  UPDATE_TON: 'updateTon',
  UPDATE_CUSTOM_COIN_SCORE: 'updateCustomCoinScore',
  UPDATE_BATTLE_COIN_SCORE: 'updateBattleCoinScore',
  UPDATE_DYNAMIC_COIN_SCORE: 'updateDynamicCoinScore',
  UPDATE_FRAGMENT_SCORE: 'updateFragmentCoinScore',
  GAME_ENDED: 'gameEnded',
  GAME_STATE_CLEARED: 'gameStateCleared',
  GAME_PRELOAD_ENDED: 'game_preload_ended',
  ASSETS_PRELOAD_ENDED: 'assets_preload_ended',
  ASSETS_PRELOAD_ERROR: 'assets_preload_error',
  ORIENTATION_STATE_CHANGED: 'orientationStateChanged',
  PLAYER_RESSURECTED: 'playerRessurected',
  PLAYER_DIED: 'playerDied',
  PLAYER_TOUCHED_LIGHT: 'playerTouchedLight',
  PLAYER_BELOW_SCREEN_BOTTOM: 'player_below_screen_bottom',
  TRAP_TOUCHED: 'player_touched_trap',
  FLOATING_AREA_STAY: 'player_floating',
  REPLACE_SPINE: 'replace_spine',
  SESSION_UPDATE_LOCK_RELEASED: 'session_update_lock_released',
  ACHIEVEMENT_UNLOCKED: 'achievement_unlocked',
  BOOSTER_ENDED: 'booster_ended'
} as const
