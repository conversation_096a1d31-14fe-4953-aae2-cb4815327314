import { IS_HAPTICS_ENABLED } from '@/shared/constants/keys'
import type { ImpactHapticFeedbackStyle, NotificationHapticFeedbackType } from '@telegram-apps/sdk'
import {
  hapticFeedbackImpactOccurred,
  hapticFeedbackNotificationOccurred,
  hapticFeedbackSelectionChanged,
  isHapticFeedbackSupported
} from '@telegram-apps/sdk'
import { localStorageService } from '../storage/localStorageService'

export class HapticsService {
  private isEnabled: boolean
  private activeIntervalId: number | null = null

  private isPaused: boolean = false
  private remainingDuration: number = 0
  private pausedCallback: Function | null = null
  private pausedFrequency: number = 0

  private startTime: number = 0
  private pauseTime: number = 0

  constructor(enabled: boolean = true) {
    this.isEnabled = enabled
  }

  isSupported(): boolean {
    return isHapticFeedbackSupported()
  }

  isEnabledHaptics(): boolean {
    return this.isEnabled
  }

  toggleHapticsEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  triggerHapticEventForDuration(event: Function, duration: number, frequency: number): void {
    if (!this.isSupported() || !this.isEnabled) return

    this.stopHapticEvent()

    const iterations = Math.floor(duration / frequency)
    this.remainingDuration = iterations * frequency
    this.pausedCallback = event
    this.pausedFrequency = frequency
    this.startTime = Date.now()

    let count = 0
    this.activeIntervalId = setInterval(() => {
      if (this.isPaused) return

      const elapsed = Date.now() - this.startTime
      if (elapsed >= this.remainingDuration) {
        this.stopHapticEvent()
        return
      }

      if (count >= iterations) {
        this.stopHapticEvent()
        return
      }

      event()
      count++
    }, frequency) as unknown as number
  }

  stopHapticEvent(): void {
    if (this.activeIntervalId !== null) {
      clearInterval(this.activeIntervalId)
      this.activeIntervalId = null
    }

    this.isPaused = false
    this.remainingDuration = 0
    this.pausedCallback = null
    this.pausedFrequency = 0
    this.startTime = 0
    this.pauseTime = 0
  }

  pauseHapticEvent(): void {
    if (this.activeIntervalId !== null) {
      clearInterval(this.activeIntervalId)
      this.activeIntervalId = null
    }

    if (!this.isPaused) {
      this.isPaused = true
      this.pauseTime = Date.now()
      this.remainingDuration -= this.pauseTime - this.startTime
    }
  }

  resumeHapticEvent(): void {
    if (!this.isPaused || this.pausedCallback === null || this.pausedFrequency === 0) return

    if (this.remainingDuration <= 0) {
      this.stopHapticEvent()
      return
    }

    const iterations = Math.ceil(this.remainingDuration / this.pausedFrequency)
    let count = 0

    this.startTime = Date.now()
    this.isPaused = false

    this.activeIntervalId = setInterval(() => {
      if (this.isPaused) return

      const elapsed = Date.now() - this.startTime
      if (elapsed >= this.remainingDuration) {
        this.stopHapticEvent()
        return
      }

      if (count >= iterations) {
        this.stopHapticEvent()
        return
      }

      this.pausedCallback?.()
      count++
    }, this.pausedFrequency) as unknown as number
  }

  triggerImpactHapticEvent(impactStyle: ImpactHapticFeedbackStyle) {
    if (!this.isSupported() || !this.isEnabled) return
    hapticFeedbackImpactOccurred(impactStyle)
  }

  triggerNotificationHapticEvent(notificationType: NotificationHapticFeedbackType) {
    if (!this.isSupported() || !this.isEnabled) return
    hapticFeedbackNotificationOccurred(notificationType)
  }

  triggerSelectionChangedHapticEvent() {
    if (!this.isSupported() || !this.isEnabled) return
    hapticFeedbackSelectionChanged()
  }
}

const wasEnabledHaptics = localStorageService.load<boolean>(IS_HAPTICS_ENABLED)
export const hapticsService = new HapticsService(wasEnabledHaptics ?? true)
