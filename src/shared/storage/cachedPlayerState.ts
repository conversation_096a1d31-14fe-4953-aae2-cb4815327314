import type { PlayerStateResponse } from '@/services/openapi'

export class CachedPlayerState {
  public playerState: PlayerStateResponse | undefined

  get skinId(): number {
    if (__DEV__) {
      console.info('getting skinId from playerState', this.playerState)
    }

    return this.playerState?.skin ?? -1
  }

  get currentProgressiveOffer(): number {
    return this.playerState?.progressiveOffers.find(offer => offer.usageDynamicCoins)?.id ?? 10000
  }
}

export const cachedPlayerState = new CachedPlayerState()
