import type { UnattainedAchievement } from '@/services/openapi'

export class CachedAchievementsData {
  private achievements: Array<UnattainedAchievement> | undefined

  public set playerAchievements(achievements: Array<UnattainedAchievement> | undefined) {
    console.info('setting playerAchievements', achievements)
    this.achievements = achievements
  }
  get playerAchievements(): Array<UnattainedAchievement> | undefined {
    if (this.achievements !== undefined) {
      console.info('getting playerAchievements from achievements', this.achievements)
      return this.achievements
    } else {
      console.info('there are no achievements to attain')
      return []
    }
  }
}

export const cachedAchievementsData = new CachedAchievementsData()
