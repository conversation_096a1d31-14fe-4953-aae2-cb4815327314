import { FALL<PERSON>C<PERSON>_LOCALE, LOCALE_STORAGE_KEY, en, ru, uk } from '@/i18n'
import { localStorageService } from '@/shared/storage/localStorageService'
import { createI18n } from 'vue-i18n'

const userSelectedLocale = localStorageService.load<string>(LOCALE_STORAGE_KEY) ?? FALLBACK_LOCALE

export const i18n = createI18n({
  legacy: false,
  locale: userSelectedLocale,
  fallbackLocale: FALLBACK_LOCALE,
  messages: {
    en,
    uk,
    ru
  }
})
