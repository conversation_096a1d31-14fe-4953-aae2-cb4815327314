import './assets/main.scss'

import { initSDK } from '@/services/telegram-sdk'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { isTMA } from '@telegram-apps/sdk'
import { createApp } from 'vue'
import App from './AppContest.vue'
import { i18n } from './plugins/i18n'

export async function initApp() {
  const isTma = await isTMA()
  if (isTma) {
    initSDK()
  }

  const app = createApp(App)
  app.use(VueQueryPlugin)
  app.use(i18n)

  return app
}
