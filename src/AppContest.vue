<script setup lang="ts">
import VButton from '@/components/UI/VButton.vue'
import { useContestInfo } from '@/services/client/useContestInto.ts'
import { type ContestRequirementStatus } from '@/services/openapi'
import type { App } from 'vue'
import { computed, inject } from 'vue'
import { useI18n } from 'vue-i18n'
import LoaderText from './components/LoaderText.vue'
import { APP_INSTANCE_KEY, CONTEST_ID, GET_DEFAULT_APP } from './utils/symbols.ts'

const appInstance = inject<App>(APP_INSTANCE_KEY)
const getDefaultApp = inject<() => App>(GET_DEFAULT_APP)
const contestId = inject<number>(CONTEST_ID)

const { t } = useI18n()

const { contestInfo, isLoading, isError } = useContestInfo(contestId ?? 0)

const requirementsCompleted = computed(() => contestInfo.value?.requirementsCompleted ?? false)

const imageClass = computed(() => {
  return {
    image: true,
    'image-check': requirementsCompleted.value,
    'image-stop': !requirementsCompleted.value
  }
})

const caption = computed(() => {
  if (isError.value) {
    return t('contest.errorCaption')
  }

  return requirementsCompleted.value ? t('contest.successCaption') : t('contest.failCaption')
})

const requirements = computed(() => {
  return contestInfo.value?.requirements ? [...contestInfo.value.requirements].reverse() : []
})

const formatTaskTitle = (req: ContestRequirementStatus) => {
  if (req.requirement === 'skin') {
    return t('contest.task.skin', { value: t(`skins.list.${req.value}.title`) })
  } else if (req.requirement === 'task') {
    return t(`earn.missions.${req.value}.name`)
  }

  return t('contest.task.' + req.requirement, { value: req.value })
}

const continueToGame = () => {
  if (appInstance && getDefaultApp) {
    appInstance.unmount()
    getDefaultApp().mount('#app')
  }
}
</script>

<template>
  <div id="contest-view">
    <div class="contest-container flex-row" v-if="!isLoading">
      <div :class="imageClass"></div>
      <div class="mb-4">{{ caption }}</div>

      <div class="text-left mt-4 ml-1 leading-8" v-if="!requirementsCompleted">
        <div v-for="req in requirements" :key="req.requirement">
          <span
            :class="['mr-1', 'list-image', req.completed ? 'list-image-done' : 'list-image-fail']"
          ></span>
          {{ formatTaskTitle(req) }}
        </div>
      </div>
    </div>
    <VButton
      v-if="!isLoading"
      type="success"
      :text="t('actions.continue')"
      class="!w-[80%] flex-row mt-5"
      @click="continueToGame"
    />
    <LoaderText class="text-shadow text-shadow_black" :isLoading="isLoading" />
  </div>
</template>

<style lang="scss">
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

body {
  width: 100dvw;
  height: 100dvh;
  overflow: inherit;
}

#app {
  width: inherit;
  height: inherit;
  max-width: 100dvw;
  max-height: 100dvh;
}

#contest-view {
  font-family: 'Nunito', sans-serif;
  background: url('@/assets/images/temp/background.png') no-repeat;
  background-size: 100% 100%;
  color: var(--white);
  line-height: 20px;
  font-weight: 900;
  font-size: 16px;
  font-synthesis: none;
  paint-order: stroke fill;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100dvw;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.contest-container {
  width: 80%;
  min-height: 100px;
  background: linear-gradient(360deg, #b0e7ff 0%, #ccf0ff 92.65%);
  border-radius: 15px;
  border: 3px solid #3b7fe5;
  padding: 10px;
  color: #1e4073;

  .image {
    width: 100%;
    height: 40px;
    margin: 20px 0px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    &-check {
      background-image: url('@/assets/images/temp/check.png');
    }

    &-stop {
      background-image: url('@/assets/images/temp/stop.png');
    }
  }

  .list-image {
    height: 20px;
    width: 20px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: inline-block;
    position: relative;
    top: 5px;

    &-done {
      background-image: url('@/assets/images/temp/check.png');
    }

    &-fail {
      background-image: url('@/assets/images/temp/cross.png');
    }
  }
}
</style>
