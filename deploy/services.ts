import { Service, ServiceSpec } from 'kubernetes-models/v1'
import { API_PORT, SERVICE_NAME } from './config'

const createService = (
  serviceName: string,
  servicePort = API_PORT,
  containerPort = API_PORT,
  selector?: string
) =>
  new Service({
    metadata: {
      name: serviceName,
      labels: {
        app: serviceName
      }
    },
    spec: new ServiceSpec({
      type: 'ClusterIP',
      ports: [
        {
          port: servicePort,
          targetPort: containerPort,
          name: serviceName,
          protocol: 'TCP'
        }
      ],
      selector: {
        app: selector ?? serviceName
      }
    })
  })

export const service = createService(SERVICE_NAME, API_PORT, API_PORT)

service.validate()
