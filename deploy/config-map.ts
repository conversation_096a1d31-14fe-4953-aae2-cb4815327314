import { IIoK8sApiCoreV1EnvVar } from 'kubernetes-models/v1'
import { logger } from '../src/shared/Logger'

let requiredKeys: any[] = []

const envDataArr: string[] = []
export const envVar: IIoK8sApiCoreV1EnvVar[] = []
for (const [key, value] of Object.entries(process.env)) {
  if (!key.startsWith('APP_ENV_')) {
    continue
  }
  if (value) {
    const envKey = key.replace('APP_ENV_', '')
    envDataArr.push(`${envKey}=${value}`)
    envVar.push({ name: envKey, value })
    requiredKeys = requiredKeys.filter(k => k !== envKey)
  }
}
if (requiredKeys.length) {
  logger.error('Empty env vars: ', requiredKeys)
  throw new Error('Bed env config')
}
