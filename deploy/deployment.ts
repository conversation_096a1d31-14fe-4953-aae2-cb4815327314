import { Deployment } from 'kubernetes-models/apps/v1'
import { Container, IIoK8sApiCoreV1Probe } from 'kubernetes-models/v1'
import {
  API_PORT,
  APP_IMAGE,
  DEPLOYMENT_NAME,
  IMAGE_PULL_POLICY,
  PROD_RELEASE,
  REPLICAS,
  SERVICE_NAME
} from './config'
import { envVar } from './config-map'

const labels = {
  app: SERVICE_NAME,
  'app.kubernetes.io/name': SERVICE_NAME,
  'app.kubernetes.io/version': process.env.IMAGE_TAG!,
  'app.kubernetes.io/commit': process.env.CI_COMMIT_SHORT_SHA!,
  'app.kubernetes.io/part-of': 'oriongames.lol'
}

const imagePullSecrets = [{ name: 'gitlab-registry-uni-front' }]

const livenessProbe: IIoK8sApiCoreV1Probe = {
  httpGet: {
    path: '/',
    port: API_PORT,
    scheme: 'HTTP'
  },
  initialDelaySeconds: 60,
  timeoutSeconds: 60,
  failureThreshold: 3
}

const apiContainer = new Container({
  name: SERVICE_NAME,
  image: APP_IMAGE,
  imagePullPolicy: IMAGE_PULL_POLICY,
  ports: [{ containerPort: API_PORT }],
  livenessProbe,
  env: [
    {
      name: 'K8S_NODE_NAME',
      valueFrom: { fieldRef: { fieldPath: 'spec.nodeName' } }
    },
    {
      name: 'K8S_NAMESPACE',
      valueFrom: { fieldRef: { fieldPath: 'metadata.namespace' } }
    },
    ...envVar
  ]
})

export const deployment = new Deployment({
  metadata: {
    name: SERVICE_NAME,
    labels
  },
  spec: {
    strategy: {
      type: 'RollingUpdate',
      rollingUpdate: {
        maxSurge: 1,
        maxUnavailable: 1
      }
    },
    replicas: REPLICAS,
    selector: {
      matchLabels: {
        app: SERVICE_NAME
      }
    },
    revisionHistoryLimit: 10,
    template: {
      metadata: {
        labels
      },
      spec: {
        nodeSelector: !PROD_RELEASE
          ? {
              [`unijump-${DEPLOYMENT_NAME}`]: 'true'
            }
          : undefined,
        imagePullSecrets,
        containers: [apiContainer]
      }
    }
  }
})

deployment.validate()
