import { Ingress, IngressSpec } from 'kubernetes-models/networking.k8s.io/v1'
import { API_PORT, SERVICE_NAME } from './config'

const tls = [
  {
    hosts: [process.env.APP_ENV_DOMAIN!],
    secretName: process.env.APP_ENV_DOMAIN!.split('.').join('-') + '-tls'
  }
]

const annotations = {
  'kubernetes.io/ingress.class': 'nginx',
  'cert-manager.io/cluster-issuer': 'letsencrypt',
  'nginx.ingress.kubernetes.io/proxy-body-size': '5m'
  // 'nginx.ingress.kubernetes.io/upstream-hash-by': '$request_uri'
}

export const ingress = new Ingress({
  metadata: {
    name: 'nginx' + (process.env.SERVICE_NAME_SUFFIX || ''),
    annotations
  },
  spec: new IngressSpec({
    tls,
    rules: [
      {
        host: process.env.APP_ENV_DOMAIN,
        http: {
          paths: [
            {
              path: '/',
              pathType: 'ImplementationSpecific',
              backend: {
                service: {
                  name: SERVICE_NAME,
                  port: { number: API_PORT }
                }
              }
            }
          ]
        }
      }
    ]
  })
})

ingress.validate()
