import { Container } from 'kubernetes-models/v1'

export const APP_IMAGE = `${process.env.IMAGE}:${process.env.IMAGE_TAG}`
export const API_PORT = parseInt(process.env.APP_ENV_PORT || '80')
export const REPLICAS = parseInt(process.env.REPLICAS || '1')
export const SERVICE_NAME = 'uni-frontend' + (process.env.SERVICE_NAME_SUFFIX || '')
export const PROD_RELEASE = (process.env.PROD_RELEASE || 'false') === 'true'
export const DEV_NODE_NAME = process.env.DEV_NODE_NAME || 'k8s-worker-0'
export const DEPLOYMENT_NAME = process.env.DEPLOYMENT_NAME ?? 'dev'

export const IMAGE_PULL_POLICY = (process.env.IMAGE_PULL_POLICY ||
  'IfNotPresent') as Container['imagePullPolicy']

if (!process.env.IMAGE) {
  throw new Error('Provide IMAGE env var')
}

if (!process.env.IMAGE_TAG) {
  throw new Error('Provide IMAGE_TAG env var')
}
