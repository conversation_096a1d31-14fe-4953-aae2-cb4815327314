import * as fs from 'fs'
import * as path from 'path'
import { deployment } from './deployment'
import { ingress } from './ingress'
import { service } from './services'

const configsDir = path.join(__dirname, './configs')

fs.mkdirSync(configsDir)

const stringify = (data: any) => JSON.stringify(data, null, 2)
const file = (fileName: string) => path.join(configsDir, fileName)

fs.writeFileSync(file('deployment.json'), stringify(deployment))
fs.writeFileSync(file('service.json'), stringify(service))
fs.writeFileSync(file('ingress.json'), stringify(ingress))
