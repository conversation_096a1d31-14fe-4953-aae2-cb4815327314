#!/usr/bin/env node

/* eslint-env node */
/* eslint-disable no-console */

const fs = require('fs');
const path = require('path');

/**
 * Converts a JSON localization file to TypeScript format
 * Usage: node scripts/json-to-ts.js <input.json> [output.ts]
 * 
 * Examples:
 * node scripts/json-to-ts.js src/i18n/en/en.json
 * node scripts/json-to-ts.js src/i18n/uk/uk.json src/i18n/uk/uk.ts
 */

function escapeString(str, useDoubleQuotes = false) {
  // Escape special characters for TypeScript string literals
  let escaped = str
    .replace(/\\/g, '\\\\')  // Escape backslashes first
    .replace(/\n/g, '\\n')   // Escape newlines
    .replace(/\r/g, '\\r')   // Escape carriage returns
    .replace(/\t/g, '\\t');  // Escape tabs

  if (useDoubleQuotes) {
    // Escape double quotes for double-quoted strings
    escaped = escaped.replace(/"/g, '\\"');
  } else {
    // Escape single quotes for single-quoted strings
    escaped = escaped.replace(/'/g, "\\'");
  }

  return escaped;
}

function shouldUseDoubleQuotes(str) {
  // Use double quotes if string contains single quotes/apostrophes
  // and doesn't contain double quotes, or if it has more single quotes than double quotes
  const singleQuoteCount = (str.match(/'/g) || []).length;
  const doubleQuoteCount = (str.match(/"/g) || []).length;

  // If string has single quotes but no double quotes, use double quotes
  if (singleQuoteCount > 0 && doubleQuoteCount === 0) {
    return true;
  }

  // If string has more single quotes than double quotes, use double quotes
  if (singleQuoteCount > doubleQuoteCount) {
    return true;
  }

  // Default to single quotes
  return false;
}

function isNumericKey(key) {
  // Check if key is numeric (including negative numbers)
  return /^-?\d+$/.test(key);
}

function sortObjectKeys(obj) {
  // Sort object keys to match the original TypeScript format
  const entries = Object.entries(obj);

  // Separate numeric and non-numeric keys
  const numericEntries = [];
  const stringEntries = [];

  entries.forEach(([key, value]) => {
    if (isNumericKey(key)) {
      numericEntries.push([parseInt(key), value, key]); // [numValue, value, originalKey]
    } else {
      stringEntries.push([key, value]);
    }
  });

  // Sort numeric entries by their numeric value
  numericEntries.sort((a, b) => a[0] - b[0]);

  // Convert back to [key, value] format
  const sortedNumericEntries = numericEntries.map(([, value, originalKey]) => [originalKey, value]);

  // Return sorted entries: numeric keys first (sorted), then string keys
  return [...sortedNumericEntries, ...stringEntries];
}

function formatValue(value, indent = 0) {
  const spaces = '  '.repeat(indent);

  if (typeof value === 'string') {
    // Choose quote type based on string content
    const useDoubleQuotes = shouldUseDoubleQuotes(value);
    const escapedValue = escapeString(value, useDoubleQuotes);

    if (useDoubleQuotes) {
      return `"${escapedValue}"`;
    } else {
      return `'${escapedValue}'`;
    }
  }

  if (typeof value === 'number' || typeof value === 'boolean') {
    return value.toString();
  }

  if (Array.isArray(value)) {
    if (value.length === 0) return '[]';
    const items = value.map(item => formatValue(item, indent + 1));
    return `[\n${spaces}  ${items.join(`,\n${spaces}  `)}\n${spaces}]`;
  }

  if (typeof value === 'object' && value !== null) {
    const sortedEntries = sortObjectKeys(value);
    if (sortedEntries.length === 0) return '{}';

    const formattedEntries = sortedEntries.map(([key, val]) => {
      // Special handling for key formatting:
      // - Negative numbers like '-1' should be quoted
      // - Positive numbers like '0', '1', '10' should not be quoted
      // - String keys should not be quoted (they're already valid identifiers)
      let formattedKey;
      if (isNumericKey(key)) {
        if (key.startsWith('-')) {
          formattedKey = `'${key}'`; // Quote negative numbers
        } else {
          formattedKey = key; // Don't quote positive numbers
        }
      } else {
        formattedKey = key; // Don't quote string keys
      }

      const formattedValue = formatValue(val, indent + 1);

      // Handle special formatting for long descriptions (>80 chars, single line)
      if (typeof val === 'string' && val.length > 80 && !val.includes('\n')) {
        return `${spaces}  ${formattedKey}:\n${spaces}    ${formattedValue}`;
      }

      return `${spaces}  ${formattedKey}: ${formattedValue}`;
    });

    return `{\n${formattedEntries.join(',\n')}\n${spaces}}`;
  }

  return 'null';
}

function convertJsonToTs(jsonContent, variableName) {
  try {
    const data = JSON.parse(jsonContent);
    const formattedObject = formatValue(data);
    return `export const ${variableName} = ${formattedObject};\n`;
  } catch (error) {
    throw new Error(`Failed to parse JSON: ${error.message}`);
  }
}

function getVariableNameFromPath(filePath) {
  // Extract variable name from file path
  // e.g., "src/i18n/en/en.json" -> "en"
  // e.g., "src/i18n/uk/uk.json" -> "uk"
  // e.g., "test-locale.json" -> "testLocale"
  const basename = path.basename(filePath, path.extname(filePath));

  // Convert kebab-case to camelCase for valid JavaScript variable names
  return basename.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.error('Usage: node scripts/json-to-ts.js <input.json> [output.ts]');
    console.error('');
    console.error('Examples:');
    console.error('  node scripts/json-to-ts.js src/i18n/en/en.json');
    console.error('  node scripts/json-to-ts.js src/i18n/uk/uk.json src/i18n/uk/uk.ts');
    console.error('  node scripts/json-to-ts.js src/i18n/en/en.json src/i18n/en/en.ts');
    process.exit(1);
  }
  
  const inputPath = args[0];
  let outputPath = args[1];
  
  // Validate input file
  if (!fs.existsSync(inputPath)) {
    console.error(`Error: Input file "${inputPath}" does not exist.`);
    process.exit(1);
  }
  
  if (!inputPath.endsWith('.json')) {
    console.error('Error: Input file must be a .json file.');
    process.exit(1);
  }
  
  // Generate output path if not provided
  if (!outputPath) {
    outputPath = inputPath.replace(/\.json$/, '.ts');
  }
  
  // Ensure output path ends with .ts
  if (!outputPath.endsWith('.ts')) {
    outputPath += '.ts';
  }
  
  try {
    // Read input JSON file
    console.log(`Reading JSON file: ${inputPath}`);
    const jsonContent = fs.readFileSync(inputPath, 'utf8');
    
    // Get variable name from file path
    const variableName = getVariableNameFromPath(inputPath);
    
    // Convert JSON to TypeScript
    console.log(`Converting to TypeScript format...`);
    const tsContent = convertJsonToTs(jsonContent, variableName);
    
    // Create output directory if it doesn't exist
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write TypeScript file
    fs.writeFileSync(outputPath, tsContent, 'utf8');
    
    console.log(`✅ Successfully converted JSON to TypeScript!`);
    console.log(`📁 Input:  ${inputPath}`);
    console.log(`📁 Output: ${outputPath}`);
    console.log(`🔤 Variable: ${variableName}`);
    
    // Show file size info
    const inputStats = fs.statSync(inputPath);
    const outputStats = fs.statSync(outputPath);
    console.log(`📊 Input size:  ${inputStats.size} bytes`);
    console.log(`📊 Output size: ${outputStats.size} bytes`);
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { convertJsonToTs, formatValue, escapeString };
