# JSON to TypeScript Converter

Автоматичний скрипт для конвертації JSON файлів локалізації в TypeScript формат.

## Використання

### Через npm script (рекомендовано)
```bash
npm run json-to-ts <input.json> [output.ts]
```

### Прямо через Node.js
```bash
node scripts/json-to-ts.cjs <input.json> [output.ts]
```

## Приклади використання

### 1. Автоматичне визначення вихідного файлу
```bash
# Конвертує src/i18n/en/en.json → src/i18n/en/en.ts
npm run json-to-ts src/i18n/en/en.json

# Конвертує src/i18n/uk/uk.json → src/i18n/uk/uk.ts
npm run json-to-ts src/i18n/uk/uk.json
```

### 2. Вказання власного вихідного файлу
```bash
# Конвертує в конкретний файл
npm run json-to-ts src/i18n/en/en.json src/i18n/en/en.ts
npm run json-to-ts src/i18n/uk/uk.json src/i18n/uk/uk.ts
```

### 3. Конвертація будь-яких JSON файлів
```bash
# Працює з будь-якими JSON файлами локалізації
npm run json-to-ts locales/fr.json locales/fr.ts
npm run json-to-ts translations/de.json translations/de.ts

# Автоматично конвертує kebab-case в camelCase для імен змінних
npm run json-to-ts my-locale.json  # → export const myLocale = {...}
```

### 4. Реальні приклади з проекту
```bash
# Конвертація основних файлів локалізації
npm run json-to-ts src/i18n/en/en.json
npm run json-to-ts src/i18n/uk/uk.json

# Результат:
# ✅ Successfully converted JSON to TypeScript!
# 📁 Input:  src/i18n/en/en.json
# 📁 Output: src/i18n/en/en.ts
# 🔤 Variable: en
# 📊 Input size:  41379 bytes
# 📊 Output size: 39297 bytes
```

## Особливості скрипта

### ✅ Автоматичне форматування
- **Одинарні лапки**: Використовує одинарні лапки замість подвійних для TypeScript
- **Правильне екранування**: Автоматично екранує спеціальні символи
- **Багаторядкові рядки**: Правильно обробляє рядки з `\n`
- **Числові ключі**: Коректно обробляє числові ключі об'єктів

### ✅ Розумне визначення імен змінних
Автоматично визначає ім'я змінної з шляху до файлу:
- `src/i18n/en/en.json` → `export const en = {...}`
- `src/i18n/uk/uk.json` → `export const uk = {...}`
- `locales/fr.json` → `export const fr = {...}`

### ✅ Збереження структури
- Повністю зберігає вкладену структуру об'єктів
- Правильно обробляє масиви та примітивні типи
- Зберігає всі ключі та значення без втрат

### ✅ Інформативний вивід
```
✅ Successfully converted JSON to TypeScript!
📁 Input:  src/i18n/uk/uk.json
📁 Output: src/i18n/uk/uk.ts
🔤 Variable: uk
📊 Input size:  53711 bytes
📊 Output size: 51630 bytes
```

## Структура вихідного файлу

Скрипт генерує TypeScript файли у форматі:

```typescript
export const en = {
  actions: {
    play: 'Play',
    continue: 'Continue',
    // ...
  },
  skins: {
    title: 'Skins',
    list: {
      0: {
        title: 'Dizzy Uni',
        description: 'Dizzy Uni is spinning into chaos!'
      },
      // ...
    }
  }
  // ...
};
```

## Обробка помилок

Скрипт перевіряє:
- ✅ Існування вхідного файлу
- ✅ Правильність розширення (.json)
- ✅ Валідність JSON синтаксису
- ✅ Права на запис у вихідну директорію

## Технічні деталі

- **Мова**: Node.js (CommonJS)
- **Залежності**: Тільки вбудовані модулі Node.js
- **Підтримка**: Node.js 14+
- **Розмір**: ~8KB
- **Швидкість**: Обробляє великі файли (50KB+) за секунди

## Інтеграція з проектом

Скрипт автоматично:
1. Створює вихідні директорії якщо їх немає
2. Перезаписує існуючі TypeScript файли
3. Зберігає правильне форматування коду
4. Підтримує всі особливості TypeScript синтаксису

Ідеально підходить для автоматизації процесу конвертації файлів локалізації в CI/CD пайплайнах або для ручного використання під час розробки.
