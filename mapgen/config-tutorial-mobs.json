{"weights": [{"score": [0, 1560], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 0, "weight": 100}]}, {"score": [1560, 3080], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 1, "weight": 100}]}, {"score": [3080, 4600], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 2, "weight": 100}]}, {"score": [4600, 5300], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 3, "weight": 100}]}, {"score": [5300, 6000], "procedural": 100, "composition": 0, "maxProceduralSeries": 2, "maxCompositionSeries": 0, "tags": [{"option": 4, "weight": 100}]}, {"score": [6000, 7500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 4, "weight": 100}]}, {"score": [7500, 9000], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 5, "weight": 100}]}, {"score": [9000, 10500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 6, "weight": 100}]}, {"score": [10500, 13500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 4, "weight": 40}, {"option": 5, "weight": 40}, {"option": 7, "weight": 20}]}, {"score": [13500, 19500], "procedural": 10, "composition": 90, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 7, "weight": 10}, {"option": 8, "weight": 10}, {"option": 9, "weight": 80}]}, {"score": [19500, 21000], "procedural": 10, "composition": 90, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 6, "weight": 50}, {"option": 10, "weight": 50}]}, {"score": [21000, 27500], "procedural": 10, "composition": 90, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 11, "weight": 30}, {"option": 8, "weight": 20}, {"option": 12, "weight": 50}]}, {"score": [27500, 29000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [29000, 30500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 6, "weight": 20}, {"option": 10, "weight": 80}]}, {"score": [30500, 33500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 12, "weight": 50}, {"option": 13, "weight": 50}]}, {"score": [33500, 39500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [39500, 46500], "procedural": 20, "composition": 80, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 12, "weight": 30}, {"option": 13, "weight": 30}, {"option": 14, "weight": 40}]}, {"score": [46500, 48000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [48000, 49500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 6, "weight": 60}, {"option": 15, "weight": 20}, {"option": 10, "weight": 20}]}, {"score": [49500, 53500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [53500, 59500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [59500, 63000], "procedural": 50, "composition": 50, "maxProceduralSeries": 1, "maxCompositionSeries": 3, "tags": [{"option": 17, "weight": 40}, {"option": 18, "weight": 25}, {"option": 14, "weight": 10}, {"option": 13, "weight": 10}, {"option": 12, "weight": 15}]}, {"score": [63000, 64500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [64500, 66000], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 2, "tags": [{"option": 6, "weight": 20}, {"option": 15, "weight": 20}, {"option": 19, "weight": 30}, {"option": 10, "weight": 30}]}, {"score": [66000, 67500], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [67500, 69000], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 17, "weight": 40}, {"option": 18, "weight": 20}, {"option": 14, "weight": 15}, {"option": 13, "weight": 15}, {"option": 12, "weight": 10}]}, {"score": [69000, 75000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [75000, 80000], "procedural": 70, "composition": 30, "maxProceduralSeries": 2, "maxCompositionSeries": 2, "tags": [{"option": 17, "weight": 20}, {"option": 18, "weight": 15}, {"option": 14, "weight": 10}, {"option": 13, "weight": 10}, {"option": 12, "weight": 5}]}, {"score": [78500, 80000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [80000, 81500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 3, "tags": [{"option": 6, "weight": 5}, {"option": 15, "weight": 20}, {"option": 19, "weight": 35}, {"option": 10, "weight": 40}]}, {"score": [81500, 83000], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [83000, 84500], "procedural": 70, "composition": 30, "maxProceduralSeries": 2, "maxCompositionSeries": 2, "tags": [{"option": 14, "weight": 40}, {"option": 17, "weight": 30}, {"option": 18, "weight": 30}]}, {"score": [84500, 90500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [90500, 99500], "procedural": 80, "composition": 20, "maxProceduralSeries": 2, "maxCompositionSeries": 2, "tags": [{"option": 17, "weight": 20}, {"option": 18, "weight": 20}, {"option": 14, "weight": 20}, {"option": 13, "weight": 20}, {"option": 12, "weight": 20}]}, {"score": [99500, 101000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [101000, 102500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 3, "tags": [{"option": 6, "weight": 5}, {"option": 15, "weight": 20}, {"option": 19, "weight": 30}, {"option": 10, "weight": 45}]}, {"score": [102500, 104000], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [104000, 105500], "procedural": 80, "composition": 20, "maxProceduralSeries": 2, "maxCompositionSeries": 2, "tags": [{"option": 14, "weight": 40}, {"option": 17, "weight": 30}, {"option": 18, "weight": 30}]}, {"score": [105500, 113000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [113000, 120500], "procedural": 85, "composition": 15, "maxProceduralSeries": 3, "maxCompositionSeries": 1, "tags": [{"option": 17, "weight": 30}, {"option": 18, "weight": 30}, {"option": 14, "weight": 20}, {"option": 13, "weight": 10}, {"option": 12, "weight": 10}]}, {"score": [120500, 122000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [122000, 123500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 3, "tags": [{"option": 6, "weight": 5}, {"option": 15, "weight": 30}, {"option": 19, "weight": 30}, {"option": 10, "weight": 35}]}, {"score": [123500, 125000], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 25}]}, {"score": [125000, 126500], "procedural": 85, "composition": 15, "maxProceduralSeries": 3, "maxCompositionSeries": 1, "tags": [{"option": 14, "weight": 20}, {"option": 17, "weight": 40}, {"option": 18, "weight": 40}]}, {"score": [126500, 132500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [132500, 147500], "procedural": 85, "composition": 15, "maxProceduralSeries": 3, "maxCompositionSeries": 1, "tags": [{"option": 17, "weight": 30}, {"option": 18, "weight": 25}, {"option": 14, "weight": 15}, {"option": 13, "weight": 15}, {"option": 12, "weight": 15}]}, {"score": [147500, 149000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [149000, 150500], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 3, "tags": [{"option": 15, "weight": 25}, {"option": 19, "weight": 35}, {"option": 10, "weight": 40}]}, {"score": [150500, 152000], "procedural": 60, "composition": 40, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [152000, 153500], "procedural": 85, "composition": 15, "maxProceduralSeries": 3, "maxCompositionSeries": 1, "tags": [{"option": 14, "weight": 20}, {"option": 17, "weight": 40}, {"option": 18, "weight": 40}]}, {"score": [153500, 159500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [159500, 167000], "procedural": 85, "composition": 15, "maxProceduralSeries": 4, "maxCompositionSeries": 1, "tags": [{"option": 17, "weight": 30}, {"option": 18, "weight": 25}, {"option": 14, "weight": 15}, {"option": 13, "weight": 15}, {"option": 12, "weight": 15}]}, {"score": [167000, 168500], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [168500, 170000], "procedural": 0, "composition": 100, "maxProceduralSeries": 0, "maxCompositionSeries": 3, "tags": [{"option": 15, "weight": 25}, {"option": 19, "weight": 35}, {"option": 10, "weight": 40}]}, {"score": [170000, 171500], "procedural": 40, "composition": 60, "maxProceduralSeries": 1, "maxCompositionSeries": 2, "tags": [{"option": 16, "weight": 100}]}, {"score": [171500, 173000], "procedural": 90, "composition": 10, "maxProceduralSeries": 4, "maxCompositionSeries": 1, "tags": [{"option": 14, "weight": 10}, {"option": 17, "weight": 45}, {"option": 18, "weight": 45}]}, {"score": [173000, 180000], "procedural": 100, "composition": 0, "maxProceduralSeries": 10, "maxCompositionSeries": 0, "tags": [{"option": 5, "weight": 100}]}, {"score": [180000, 240000], "procedural": 93, "composition": 7, "maxProceduralSeries": 10, "maxCompositionSeries": 1, "tags": [{"option": 17, "weight": 30}, {"option": 18, "weight": 25}, {"option": 14, "weight": 15}, {"option": 13, "weight": 15}, {"option": 12, "weight": 15}]}, {"score": [240000, 0], "procedural": 95, "composition": 5, "maxProceduralSeries": 10, "maxCompositionSeries": 1, "tags": [{"option": 16, "weight": 10}, {"option": 17, "weight": 10}, {"option": 18, "weight": 10}, {"option": 14, "weight": 10}, {"option": 13, "weight": 10}, {"option": 12, "weight": 10}, {"option": 15, "weight": 10}, {"option": 19, "weight": 15}, {"option": 10, "weight": 15}]}], "variables": [{"score": 0, "general": {"ticketsFraction": 0, "ticketsPerPlatform": 1, "movementDuration": [12000, 12000]}}, {"score": 0, "booster": {}}, {"score": 200, "procedural": {"score": [400, 400], "distance": [50, 50], "distanceWreckable": [50, 50], "wreckableProb": 7, "dynamicHMax": 1, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 2000, "general": {"ticketsFraction": 0, "ticketsPerPlatform": 1, "movementDuration": [11000, 11000]}}, {"score": 3000, "procedural": {"score": [800, 800], "distance": [60, 60], "distanceWreckable": [60, 60], "dynamicHProb": 10, "wreckableProb": 7, "dynamicHMax": 1, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 3000, "booster": {}}, {"score": 5000, "general": {"ticketsFraction": 3, "ticketsPerPlatform": 1, "movementDuration": [10000, 10000]}}, {"score": 6000, "procedural": {"score": [1500, 1500], "distance": [60, 60], "distanceWreckable": [60, 60], "dynamicHProb": 10, "wreckableProb": 7, "dynamicHMax": 1, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 9000, "booster": {}}, {"score": 10000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [10000, 10000]}}, {"score": 10000, "procedural": {"score": [1500, 1500], "distance": [70, 70], "distanceWreckable": [70, 70], "dynamicHProb": 10, "wreckableProb": 25, "dynamicHMax": 1, "boostSmallJumpCount": [1, 3], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 15}}, {"score": 12000, "booster": {}}, {"score": 15000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [9000, 9000]}}, {"score": 20000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [8000, 8000]}}, {"score": 20000, "procedural": {"score": [1500, 1500], "distance": [70, 80], "distanceWreckable": [70, 80], "dynamicHProb": 15, "wreckableProb": 25, "dynamicHMax": 1, "boostSmallJumpCount": [1, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 1], "boostBigJumpProb": 15}}, {"score": 22000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [8000, 8000]}}, {"score": 22000, "booster": {}}, {"score": 26000, "procedural": {"score": [1500, 1500], "distance": [80, 90], "distanceWreckable": [80, 90], "dynamicHProb": 15, "dynamicHMax": 2, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 30000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [6500, 6500]}}, {"score": 30000, "procedural": {"score": [1500, 1500], "distance": [90, 90], "distanceWreckable": [90, 90], "dynamicHProb": 15, "dynamicHMax": 2, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 30000, "booster": {"boostSmallFlyWeight": 20}}, {"score": 40000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [6000, 6000]}}, {"score": 40000, "booster": {"boostSmallFlyWeight": 40}}, {"score": 42000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [6000, 6000]}}, {"score": 46000, "procedural": {"score": [1500, 1500], "distance": [100, 120], "distanceWreckable": [100, 120], "dynamicHProb": 30, "dynamicHMax": 2, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 50000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [5000, 5000]}}, {"score": 50000, "procedural": {"score": [1500, 1500], "distance": [120, 150], "distanceWreckable": [120, 150], "dynamicHProb": 30, "dynamicHMax": 2, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 50000, "booster": {"boostBigFlyWeight": 70, "boostSmallFlyWeight": 30}}, {"score": 54000, "booster": {"boostBigFlyWeight": 70, "boostSmallFlyWeight": 30}}, {"score": 60000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [4500, 4500]}}, {"score": 60000, "procedural": {"score": [1500, 1500], "distance": [160, 180], "distanceWreckable": [160, 180], "dynamicHProb": 60, "dynamicHMax": 9, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 62000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [4400, 4400]}}, {"score": 64000, "booster": {"boostBigFlyWeight": 20, "boostSmallFlyWeight": 80}}, {"score": 66000, "procedural": {"score": [1500, 1500], "distance": [180, 200], "distanceWreckable": [180, 200], "dynamicHProb": 60, "dynamicHMax": 9, "boostSmallJumpCount": [0, 1], "boostSmallJumpProb": 10, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 10}}, {"score": 78000, "procedural": {"score": [1500, 1500], "distance": [210, 220], "distanceWreckable": [210, 220], "dynamicHProb": 50, "dynamicHMax": 4, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 80000, "general": {"ticketsFraction": 3, "ticketsPerPlatform": 1, "movementDuration": [4000, 4000]}}, {"score": 82000, "general": {"ticketsFraction": 2, "ticketsPerPlatform": 1, "movementDuration": [3900, 3900]}}, {"score": 82000, "procedural": {"score": [1500, 1500], "distance": [220, 230], "distanceWreckable": [220, 230], "dynamicHProb": 50, "dynamicHMax": 4, "boostSmallJumpCount": [0, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 15}}, {"score": 98000, "procedural": {"score": [1500, 1500], "distance": [230, 250], "distanceWreckable": [230, 250], "dynamicHProb": 60, "dynamicHMax": 6, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 100000, "general": {"ticketsFraction": 3, "ticketsPerPlatform": 1, "movementDuration": [3500, 3500]}}, {"score": 102000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [3250, 3250]}}, {"score": 102000, "procedural": {"score": [1500, 1500], "distance": [250, 260], "distanceWreckable": [250, 260], "dynamicHProb": 60, "dynamicHMax": 6, "boostSmallJumpCount": [1, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 1], "boostBigJumpProb": 15}}, {"score": 116000, "booster": {"boostBigFlyWeight": 40, "boostSmallFlyWeight": 60}}, {"score": 120000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [3000, 3000]}}, {"score": 120000, "procedural": {"score": [1500, 1500], "distance": [260, 270], "distanceWreckable": [260, 270], "dynamicHProb": 80, "dynamicHMax": 8, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 122000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [2750, 2750]}}, {"score": 124000, "procedural": {"score": [1500, 1500], "distance": [270, 280], "distanceWreckable": [270, 280], "dynamicHProb": 80, "dynamicHMax": 8, "boostSmallJumpCount": [1, 2], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 1], "boostBigJumpProb": 15}}, {"score": 130000, "booster": {"boostBigFlyWeight": 50, "boostSmallFlyWeight": 50}}, {"score": 146000, "procedural": {"score": [1500, 1500], "distance": [280, 290], "distanceWreckable": [280, 290], "dynamicHProb": 90, "dynamicHMax": 10, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 150000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [2500, 2500]}}, {"score": 150000, "procedural": {"score": [1500, 1500], "distance": [290, 290], "distanceWreckable": [290, 290], "dynamicHProb": 90, "dynamicHMax": 10, "boostSmallJumpCount": [1, 2], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 1], "boostBigJumpProb": 15}}, {"score": 164000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [2250, 2250]}}, {"score": 166000, "procedural": {"score": [1500, 1500], "distance": [290, 300], "distanceWreckable": [290, 300], "dynamicHProb": 95, "dynamicHMax": 10, "boostSmallJumpCount": [0, 0], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 170000, "procedural": {"score": [1500, 1500], "distance": [310, 310], "distanceWreckable": [310, 310], "dynamicHProb": 95, "dynamicHMax": 15, "boostSmallJumpCount": [1, 2], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 1], "boostBigJumpProb": 15}}, {"score": 180000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [2000, 2000]}}, {"score": 180000, "procedural": {"score": [3000, 4000], "distance": [310, 320], "distanceWreckable": [310, 320], "dynamicHProb": 95, "dynamicHMax": 20, "boostSmallJumpCount": [1, 2], "boostSmallJumpProb": 20, "boostBigJumpCount": [1, 2], "boostBigJumpProb": 15}}, {"score": 192000, "booster": {"boostBigFlyWeight": 50, "boostSmallFlyWeight": 50}}, {"score": 200000, "general": {"ticketsFraction": 4, "ticketsPerPlatform": 1, "movementDuration": [1750, 1750]}}, {"score": 200000, "procedural": {"score": [3000, 4000], "distance": [320, 320], "distanceWreckable": [320, 320], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [1, 1], "boostSmallJumpProb": 30, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 12}}, {"score": 202000, "general": {"ticketsFraction": 6, "ticketsPerPlatform": 1, "movementDuration": [1500, 1500]}}, {"score": 210000, "booster": {"boostBigFlyWeight": 50, "boostSmallFlyWeight": 50}}, {"score": 220000, "procedural": {"score": [3000, 4000], "distance": [320, 330], "distanceWreckable": [320, 330], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [1, 1], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 230000, "general": {"ticketsFraction": 6, "ticketsPerPlatform": 1, "movementDuration": [1450, 1450]}}, {"score": 230000, "procedural": {"score": [3000, 4000], "distance": [330, 340], "distanceWreckable": [330, 340], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [1, 1], "boostSmallJumpProb": 0, "boostBigJumpCount": [0, 0], "boostBigJumpProb": 0}}, {"score": 240000, "general": {"ticketsFraction": 6, "ticketsPerPlatform": 1, "movementDuration": [1400, 1400]}}, {"score": 240000, "procedural": {"score": [3000, 4000], "distance": [340, 340], "distanceWreckable": [340, 340], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [0, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 15}}, {"score": 250000, "general": {"ticketsFraction": 6, "ticketsPerPlatform": 1, "movementDuration": [1350, 1350]}}, {"score": 250000, "procedural": {"score": [3000, 4000], "distance": [340, 340], "distanceWreckable": [340, 340], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [0, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 15}}, {"score": 260000, "general": {"ticketsFraction": 6, "ticketsPerPlatform": 1, "movementDuration": [1300, 1300]}}, {"score": 260000, "procedural": {"score": [3000, 4000], "distance": [340, 340], "distanceWreckable": [340, 340], "dynamicHProb": 100, "dynamicHMax": 30, "boostSmallJumpCount": [0, 1], "boostSmallJumpProb": 20, "boostBigJumpCount": [0, 1], "boostBigJumpProb": 15}}], "composition": [{"id": 14, "weight": 100, "tags": [0], "stringName": "14", "config": {"composables": [{"platform": {"type": 1, "x": 208, "y": 91, "tickets": false}}, {"platform": {"type": 1, "x": 441, "y": 97, "tickets": false}}, {"platform": {"type": 1, "x": 330, "y": 112}}, {"platform": {"type": 1, "x": 118, "y": 127, "tickets": false}}, {"platform": {"type": 1, "x": 526, "y": 132, "tickets": false}}, {"platform": {"type": 1, "x": 12, "y": 157}}, {"platform": {"type": 21, "x": 169, "y": 325, "tickets": false}}, {"platform": {"type": 0, "x": 480, "y": 333, "tickets": false}, "decoration": {"type": 3000, "key": "0002"}}, {"platform": {"type": 1, "x": 79, "y": 486, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 55, "y": 707}}, {"platform": {"type": 0, "x": 300, "y": 826, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 44, "y": 898, "tickets": false}}, {"platform": {"type": 1, "x": 498, "y": 928, "tickets": false}}, {"platform": {"type": 1, "x": 263, "y": 966, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 0, "x": 160, "y": 1028, "tickets": false}, "decoration": {"type": 3000, "key": "0003_text"}}, {"platform": {"type": 21, "x": 474, "y": 1067, "tickets": false}}, {"platform": {"type": 0, "x": 205, "y": 1145, "tickets": false}, "decoration": {"type": 3000, "key": "0203"}}, {"platform": {"type": 21, "x": 383, "y": 1277, "tickets": false}}, {"platform": {"type": 0, "x": 100, "y": 1426, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 263, "y": 1570, "tickets": false}}]}}, {"id": 15, "weight": 100, "tags": [1], "stringName": "15Decor", "config": {"composables": [{"platform": {"type": 21, "x": 231, "y": 91, "tickets": false}}, {"platform": {"type": 1, "x": 241, "y": 254, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 469, "y": 482, "tickets": false}}, {"platform": {"type": 1, "x": 83, "y": 512, "tickets": false}}, {"platform": {"type": 21, "x": 184, "y": 646, "tickets": false}}, {"platform": {"type": 1, "x": 355, "y": 735, "tickets": false}}, {"platform": {"type": 0, "x": 59, "y": 798, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 500, "y": 872, "tickets": false}}, {"platform": {"type": 1, "x": 469, "y": 966, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 70, "y": 1033, "tickets": false}}, {"platform": {"type": 1, "x": 412, "y": 1176, "tickets": false}}, {"platform": {"type": 1, "x": 301, "y": 1324, "tickets": false}}, {"platform": {"type": 0, "x": 483, "y": 1324, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 68, "y": 1424, "tickets": false}}, {"platform": {"type": 1, "x": 282, "y": 1455, "tickets": false}}, {"platform": {"type": 1, "x": 497, "y": 1515, "tickets": false}}]}}, {"id": 16, "weight": 100, "tags": [2], "stringName": "16Decor", "config": {"composables": [{"platform": {"type": 1, "x": 21, "y": 83, "tickets": false}}, {"platform": {"type": 1, "x": 483, "y": 105, "tickets": false}}, {"platform": {"type": 21, "x": 280, "y": 137, "tickets": false}}, {"platform": {"type": 1, "x": 426, "y": 339, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 280, "y": 476, "tickets": false}}, {"platform": {"type": 21, "x": 337, "y": 538, "tickets": false}}, {"platform": {"type": 1, "x": 21, "y": 609, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 369, "y": 712, "tickets": false}}, {"platform": {"type": 21, "x": 41, "y": 805, "tickets": false}}, {"platform": {"type": 0, "x": 98, "y": 917, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 312, "y": 967, "tickets": false}}, {"platform": {"type": 0, "x": 284, "y": 1109, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 446, "y": 1176, "tickets": false}}, {"platform": {"type": 1, "x": 41, "y": 1225, "tickets": false}}, {"platform": {"type": 21, "x": 398, "y": 1273, "tickets": false}}, {"platform": {"type": 1, "x": 263, "y": 1381, "tickets": false}}, {"platform": {"type": 1, "x": 41, "y": 1506, "tickets": false}}, {"platform": {"type": 1, "x": 483, "y": 1521, "tickets": false}}]}}, {"id": 17, "weight": 100, "tags": [3], "stringName": "17", "config": {"composables": [{"platform": {"type": 21, "x": 452, "y": 129, "tickets": false}}, {"platform": {"type": 21, "x": 190, "y": 244, "tickets": false}}, {"platform": {"type": 21, "x": 483, "y": 435, "tickets": false}}, {"platform": {"type": 21, "x": 92, "y": 502, "tickets": false}}, {"platform": {"type": 21, "x": 294, "y": 630, "tickets": false}}, {"platform": {"type": 1, "x": 3, "y": 730, "tickets": false}}, {"platform": {"type": 1, "x": 206, "y": 730, "tickets": false}}, {"platform": {"type": 1, "x": 408, "y": 730, "tickets": false}}, {"platform": {"type": 1, "x": 107, "y": 760, "tickets": false}}, {"platform": {"type": 1, "x": 304, "y": 760, "tickets": false}}, {"platform": {"type": 1, "x": 522, "y": 760, "tickets": false}}]}}, {"id": 201, "weight": 10, "tags": [4], "stringName": "201", "config": {"composables": [{"platform": {"type": 1, "x": 141, "y": 127}}, {"platform": {"type": 1, "x": 406, "y": 201, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 500, "y": 309}}, {"platform": {"type": 1, "x": 294, "y": 384, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 155, "y": 527, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 26, "y": 629}}, {"platform": {"type": 1, "x": 506, "y": 669}}, {"platform": {"type": 1, "x": 294, "y": 745}}, {"platform": {"type": 1, "x": 62, "y": 942}}, {"platform": {"type": 1, "x": 486, "y": 975}}, {"platform": {"type": 21, "x": 119, "y": 1080}}, {"platform": {"type": 1, "x": 119, "y": 1174}}, {"platform": {"type": 21, "x": 483, "y": 1204}}, {"platform": {"type": 1, "x": 386, "y": 1241}}, {"platform": {"type": 1, "x": 41, "y": 1403}}, {"platform": {"type": 1, "x": 500, "y": 1423}}, {"platform": {"type": 1, "x": 269, "y": 1524}}]}}, {"id": 202, "weight": 10, "tags": [4], "stringName": "202", "config": {"composables": [{"platform": {"type": 1, "x": 141, "y": 127}}, {"platform": {"type": 1, "x": 269, "y": 170}}, {"platform": {"type": 1, "x": 406, "y": 210, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 500, "y": 309}}, {"platform": {"type": 1, "x": 50, "y": 344}}, {"platform": {"type": 1, "x": 443, "y": 407}}, {"platform": {"type": 1, "x": 365, "y": 517}}, {"platform": {"type": 1, "x": 155, "y": 527}}, {"platform": {"type": 1, "x": 26, "y": 629}}, {"platform": {"type": 1, "x": 506, "y": 686}}, {"platform": {"type": 1, "x": 326, "y": 805, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 107, "y": 917}}, {"platform": {"type": 21, "x": 377, "y": 965}}, {"platform": {"type": 1, "x": 500, "y": 965}}, {"platform": {"type": 1, "x": 27, "y": 1044, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 107, "y": 1128}}, {"platform": {"type": 1, "x": 180, "y": 1192, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 320, "y": 1198}}, {"platform": {"type": 1, "x": 479, "y": 1324}}, {"platform": {"type": 1, "x": 41, "y": 1339}}, {"platform": {"type": 1, "x": 250, "y": 1398}}, {"platform": {"type": 1, "x": 479, "y": 1489}}, {"platform": {"type": 1, "x": 83, "y": 1525}}]}}, {"id": 203, "weight": 10, "tags": [4], "stringName": "203", "config": {"composables": [{"platform": {"type": 1, "x": 159, "y": 127}}, {"platform": {"type": 1, "x": 429, "y": 207}}, {"platform": {"type": 1, "x": 59, "y": 210, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 486, "y": 274}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 406, "y": 422, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 155, "y": 527}}, {"platform": {"type": 1, "x": 62, "y": 624}}, {"platform": {"type": 1, "x": 66, "y": 745}}, {"platform": {"type": 1, "x": 466, "y": 815}}, {"platform": {"type": 21, "x": 159, "y": 823}}, {"platform": {"type": 1, "x": 294, "y": 912}}, {"platform": {"type": 21, "x": 377, "y": 965}}, {"platform": {"type": 1, "x": 155, "y": 1022}}, {"platform": {"type": 1, "x": 465, "y": 1077, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 25, "y": 1198, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 250, "y": 1243}}, {"platform": {"type": 1, "x": 390, "y": 1349}}, {"platform": {"type": 1, "x": 118, "y": 1439}}, {"platform": {"type": 1, "x": 408, "y": 1469}}, {"platform": {"type": 1, "x": 45, "y": 1515}}]}}, {"id": 204, "weight": 10, "tags": [4], "stringName": "204", "config": {"composables": [{"platform": {"type": 1, "x": 420, "y": 108}}, {"platform": {"type": 1, "x": 114, "y": 188}}, {"platform": {"type": 1, "x": 44, "y": 323}}, {"platform": {"type": 1, "x": 60, "y": 402, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 245, "y": 451}}, {"platform": {"type": 1, "x": 364, "y": 490}}, {"platform": {"type": 1, "x": 294, "y": 558, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 506, "y": 574}}, {"platform": {"type": 1, "x": 473, "y": 642}}, {"platform": {"type": 1, "x": 91, "y": 669, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 304, "y": 833}}, {"platform": {"type": 1, "x": 38, "y": 973}}, {"platform": {"type": 21, "x": 199, "y": 988}}, {"platform": {"type": 1, "x": 330, "y": 997}}, {"platform": {"type": 1, "x": 161, "y": 1042}}, {"platform": {"type": 1, "x": 366, "y": 1075}}, {"platform": {"type": 1, "x": 465, "y": 1153}}, {"platform": {"type": 1, "x": 305, "y": 1244}}, {"platform": {"type": 1, "x": 462, "y": 1248}}, {"platform": {"type": 1, "x": 284, "y": 1426}}, {"platform": {"type": 1, "x": 169, "y": 1520}}]}}, {"id": 205, "weight": 10, "tags": [4], "stringName": "205", "config": {"composables": [{"platform": {"type": 1, "x": 36, "y": 75, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 194, "y": 88}}, {"platform": {"type": 1, "x": 300, "y": 132}}, {"platform": {"type": 1, "x": 228, "y": 176}}, {"platform": {"type": 1, "x": 439, "y": 319, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 258, "y": 326}}, {"platform": {"type": 1, "x": 240, "y": 411}}, {"platform": {"type": 21, "x": 124, "y": 425}}, {"platform": {"type": 21, "x": 204, "y": 495}}, {"platform": {"type": 1, "x": 363, "y": 529}}, {"platform": {"type": 21, "x": 349, "y": 614}}, {"platform": {"type": 1, "x": 303, "y": 693, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 347, "y": 862}}, {"platform": {"type": 1, "x": 468, "y": 940}}, {"platform": {"type": 1, "x": 271, "y": 947}}, {"platform": {"type": 1, "x": 149, "y": 1008}}, {"platform": {"type": 1, "x": 56, "y": 1136}}, {"platform": {"type": 1, "x": 209, "y": 1143}}, {"platform": {"type": 1, "x": 33, "y": 1260}}, {"platform": {"type": 1, "x": 338, "y": 1263}}, {"platform": {"type": 1, "x": 217, "y": 1394}}]}}, {"id": 206, "weight": 10, "tags": [4], "stringName": "206", "config": {"composables": [{"platform": {"type": 1, "x": 356, "y": 125}}, {"platform": {"type": 1, "x": 473, "y": 149}}, {"platform": {"type": 1, "x": 270, "y": 198}}, {"platform": {"type": 1, "x": 194, "y": 279}}, {"platform": {"type": 1, "x": 469, "y": 286}}, {"platform": {"type": 1, "x": 39, "y": 295, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 269, "y": 340}}, {"platform": {"type": 1, "x": 493, "y": 341}}, {"platform": {"type": 1, "x": 206, "y": 433, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 87, "y": 445}}, {"platform": {"type": 1, "x": 448, "y": 627}}, {"platform": {"type": 1, "x": 211, "y": 703, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 510, "y": 842}}, {"platform": {"type": 1, "x": 365, "y": 914}}, {"platform": {"type": 21, "x": 15, "y": 915}}, {"platform": {"type": 1, "x": 422, "y": 960}}, {"platform": {"type": 1, "x": 408, "y": 1069}}, {"platform": {"type": 1, "x": 227, "y": 1250}}, {"platform": {"type": 1, "x": 82, "y": 1259}}, {"platform": {"type": 21, "x": 456, "y": 1339}}, {"platform": {"type": 1, "x": 5, "y": 1340}}, {"platform": {"type": 1, "x": 377, "y": 1524}}]}}, {"id": 207, "weight": 10, "tags": [4], "stringName": "207", "config": {"composables": [{"platform": {"type": 1, "x": 460, "y": 121}}, {"platform": {"type": 1, "x": 150, "y": 199}}, {"platform": {"type": 1, "x": 349, "y": 291, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 39, "y": 346, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 376, "y": 533}}, {"platform": {"type": 1, "x": 207, "y": 580}}, {"platform": {"type": 21, "x": 517, "y": 590}}, {"platform": {"type": 1, "x": 349, "y": 636}}, {"platform": {"type": 1, "x": 521, "y": 775, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 341, "y": 783}}, {"platform": {"type": 1, "x": 106, "y": 845}}, {"platform": {"type": 1, "x": 261, "y": 865}}, {"platform": {"type": 1, "x": 255, "y": 921}}, {"platform": {"type": 1, "x": 227, "y": 1017}}, {"platform": {"type": 1, "x": 47, "y": 1057}}, {"platform": {"type": 1, "x": 525, "y": 1099}}, {"platform": {"type": 1, "x": 65, "y": 1195}}, {"platform": {"type": 1, "x": 329, "y": 1199}}, {"platform": {"type": 1, "x": 204, "y": 1353}}, {"platform": {"type": 1, "x": 21, "y": 1390}}, {"platform": {"type": 21, "x": 46, "y": 1438}}, {"platform": {"type": 1, "x": 330, "y": 1441}}]}}, {"id": 208, "weight": 10, "tags": [4], "stringName": "208", "config": {"composables": [{"platform": {"type": 1, "x": 132, "y": 135}}, {"platform": {"type": 1, "x": 210, "y": 243, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 325, "y": 437}}, {"platform": {"type": 21, "x": 52, "y": 450}}, {"platform": {"type": 1, "x": 272, "y": 485, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 117, "y": 531}}, {"platform": {"type": 1, "x": 249, "y": 726}}, {"platform": {"type": 21, "x": 84, "y": 734}}, {"platform": {"type": 1, "x": 378, "y": 734}}, {"platform": {"type": 1, "x": 115, "y": 834}}, {"platform": {"type": 1, "x": 389, "y": 838}}, {"platform": {"type": 1, "x": 282, "y": 990}}, {"platform": {"type": 1, "x": 305, "y": 1048}}, {"platform": {"type": 1, "x": 77, "y": 1050}}, {"platform": {"type": 1, "x": 245, "y": 1118}}, {"platform": {"type": 1, "x": 201, "y": 1252}}, {"platform": {"type": 1, "x": 73, "y": 1280}}, {"platform": {"type": 21, "x": 164, "y": 1355}}, {"platform": {"type": 1, "x": 322, "y": 1363}}, {"platform": {"type": 1, "x": 231, "y": 1395}}, {"platform": {"type": 1, "x": 17, "y": 1449}}]}}, {"id": 209, "weight": 10, "tags": [4], "stringName": "209", "config": {"composables": [{"platform": {"type": 1, "x": 7, "y": 131}}, {"platform": {"type": 1, "x": 492, "y": 135}}, {"platform": {"type": 1, "x": 372, "y": 168}}, {"platform": {"type": 1, "x": 53, "y": 243}}, {"platform": {"type": 1, "x": 404, "y": 252}}, {"platform": {"type": 1, "x": 194, "y": 282}}, {"platform": {"type": 1, "x": 171, "y": 339}}, {"platform": {"type": 1, "x": 450, "y": 349}}, {"platform": {"type": 21, "x": 482, "y": 385}}, {"platform": {"type": 21, "x": 186, "y": 457}}, {"platform": {"type": 1, "x": 461, "y": 467}}, {"platform": {"type": 1, "x": 273, "y": 556}}, {"platform": {"type": 1, "x": 89, "y": 654, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 222, "y": 664, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 518, "y": 754}}, {"platform": {"type": 1, "x": 476, "y": 979}}, {"platform": {"type": 1, "x": 273, "y": 986}}, {"platform": {"type": 1, "x": 379, "y": 1050, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 186, "y": 1239}}, {"platform": {"type": 21, "x": 469, "y": 1273}}, {"platform": {"type": 1, "x": 330, "y": 1303}}, {"platform": {"type": 1, "x": 170, "y": 1328}}, {"platform": {"type": 1, "x": 493, "y": 1378}}, {"platform": {"type": 1, "x": 193, "y": 1474}}]}}, {"id": 210, "weight": 10, "tags": [4], "stringName": "210", "config": {"composables": [{"platform": {"type": 1, "x": 375, "y": 153}}, {"platform": {"type": 1, "x": 170, "y": 213}}, {"platform": {"type": 1, "x": 55, "y": 346, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 255, "y": 378}}, {"platform": {"type": 1, "x": 292, "y": 446}}, {"platform": {"type": 21, "x": 195, "y": 512}}, {"platform": {"type": 1, "x": 344, "y": 537}}, {"platform": {"type": 1, "x": 123, "y": 705, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 274, "y": 750, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 463, "y": 828}}, {"platform": {"type": 1, "x": 448, "y": 909}}, {"platform": {"type": 21, "x": 21, "y": 921}}, {"platform": {"type": 1, "x": 424, "y": 1149}}, {"platform": {"type": 1, "x": 167, "y": 1176}}, {"platform": {"type": 21, "x": 45, "y": 1230}}, {"platform": {"type": 1, "x": 496, "y": 1330}}, {"platform": {"type": 1, "x": 212, "y": 1356}}, {"platform": {"type": 1, "x": 436, "y": 1400}}]}}, {"id": 301, "weight": 6, "tags": [5], "stringName": "301", "config": {"composables": [{"platform": {"type": 1, "x": 25, "y": 125}}, {"platform": {"type": 1, "x": 356, "y": 140}}, {"platform": {"type": 1, "x": 470, "y": 188}}, {"platform": {"type": 1, "x": 164, "y": 309}}, {"platform": {"type": 21, "x": 25, "y": 392}}, {"platform": {"type": 1, "x": 334, "y": 425}}, {"platform": {"type": 1, "x": 97, "y": 437, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 440, "y": 544}}, {"platform": {"type": 1, "x": 497, "y": 675}}, {"platform": {"type": 1, "x": 61, "y": 710, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 293, "y": 725}}, {"platform": {"type": 1, "x": 448, "y": 805}}, {"platform": {"type": 1, "x": 249, "y": 945}}, {"platform": {"type": 1, "x": 440, "y": 1041}}, {"platform": {"type": 1, "x": 50, "y": 1071}}, {"platform": {"type": 1, "x": 119, "y": 1174, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 505, "y": 1234}}, {"platform": {"type": 21, "x": 278, "y": 1292}}, {"platform": {"type": 1, "x": 456, "y": 1414}}, {"platform": {"type": 1, "x": 236, "y": 1471}}, {"platform": {"type": 1, "x": 26, "y": 1491}}]}}, {"id": 302, "weight": 6, "tags": [5], "stringName": "302", "config": {"composables": [{"platform": {"type": 1, "x": 455, "y": 100}}, {"platform": {"type": 1, "x": 356, "y": 140, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 168, "y": 309}}, {"platform": {"type": 1, "x": 311, "y": 339}}, {"platform": {"type": 21, "x": 25, "y": 392}}, {"platform": {"type": 1, "x": 470, "y": 397}}, {"platform": {"type": 1, "x": 97, "y": 437}}, {"platform": {"type": 1, "x": 282, "y": 460}}, {"platform": {"type": 1, "x": 440, "y": 544, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 61, "y": 710}}, {"platform": {"type": 1, "x": 254, "y": 742}}, {"platform": {"type": 1, "x": 132, "y": 795}}, {"platform": {"type": 1, "x": 383, "y": 837}}, {"platform": {"type": 21, "x": 475, "y": 909}}, {"platform": {"type": 1, "x": 418, "y": 950}}, {"platform": {"type": 1, "x": 403, "y": 1061}}, {"platform": {"type": 1, "x": 26, "y": 1091}}, {"platform": {"type": 1, "x": 289, "y": 1144}}, {"platform": {"type": 1, "x": 119, "y": 1174, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 383, "y": 1275}}, {"platform": {"type": 1, "x": 456, "y": 1414}}, {"platform": {"type": 1, "x": 242, "y": 1449}}, {"platform": {"type": 1, "x": 26, "y": 1491}}]}}, {"id": 303, "weight": 6, "tags": [5], "stringName": "303", "config": {"composables": [{"platform": {"type": 1, "x": 514, "y": 113}}, {"platform": {"type": 1, "x": 234, "y": 152}}, {"platform": {"type": 1, "x": 444, "y": 357, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 98, "y": 363}}, {"platform": {"type": 1, "x": 34, "y": 450, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 190, "y": 591}}, {"platform": {"type": 1, "x": 498, "y": 598}}, {"platform": {"type": 1, "x": 276, "y": 672, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 140, "y": 698}}, {"platform": {"type": 1, "x": 87, "y": 852}}, {"platform": {"type": 21, "x": 251, "y": 927}}, {"platform": {"type": 1, "x": 493, "y": 936}}, {"platform": {"type": 21, "x": 280, "y": 986}}, {"platform": {"type": 1, "x": 7, "y": 1106}}, {"platform": {"type": 1, "x": 277, "y": 1122}}, {"platform": {"type": 1, "x": 206, "y": 1270}}, {"platform": {"type": 1, "x": 439, "y": 1327}}, {"platform": {"type": 21, "x": 522, "y": 1395}}, {"platform": {"type": 1, "x": 33, "y": 1403}}, {"platform": {"type": 1, "x": 163, "y": 1422}}, {"platform": {"type": 1, "x": 388, "y": 1484}}, {"platform": {"type": 1, "x": 251, "y": 1566}}]}}, {"id": 304, "weight": 6, "tags": [5], "stringName": "304", "config": {"composables": [{"platform": {"type": 1, "x": 223, "y": 121}}, {"platform": {"type": 1, "x": 444, "y": 137}}, {"platform": {"type": 1, "x": 519, "y": 191}}, {"platform": {"type": 1, "x": 501, "y": 329}}, {"platform": {"type": 1, "x": 58, "y": 332, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 296, "y": 443}}, {"platform": {"type": 1, "x": 465, "y": 621}}, {"platform": {"type": 1, "x": 282, "y": 621, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 100, "y": 756, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 246, "y": 866}}, {"platform": {"type": 1, "x": 480, "y": 880}}, {"platform": {"type": 21, "x": 395, "y": 938}}, {"platform": {"type": 1, "x": 497, "y": 986}}, {"platform": {"type": 1, "x": 82, "y": 1026}}, {"platform": {"type": 21, "x": 113, "y": 1124}}, {"platform": {"type": 21, "x": 450, "y": 1128}}, {"platform": {"type": 1, "x": 370, "y": 1165}}, {"platform": {"type": 1, "x": 153, "y": 1175}}, {"platform": {"type": 1, "x": 10, "y": 1220}}, {"platform": {"type": 1, "x": 332, "y": 1372}}, {"platform": {"type": 1, "x": 317, "y": 1429}}, {"platform": {"type": 1, "x": 177, "y": 1459}}]}}, {"id": 305, "weight": 6, "tags": [5], "stringName": "305", "config": {"composables": [{"platform": {"type": 1, "x": 54, "y": 122}}, {"platform": {"type": 1, "x": 457, "y": 130}}, {"platform": {"type": 1, "x": 280, "y": 171}}, {"platform": {"type": 21, "x": 488, "y": 277}}, {"platform": {"type": 1, "x": 436, "y": 368}}, {"platform": {"type": 1, "x": 316, "y": 426}}, {"platform": {"type": 1, "x": 495, "y": 428}}, {"platform": {"type": 1, "x": 354, "y": 507}}, {"platform": {"type": 1, "x": 344, "y": 627, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 124, "y": 691, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 47, "y": 741, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 482, "y": 947}}, {"platform": {"type": 1, "x": 300, "y": 1027}}, {"platform": {"type": 1, "x": 415, "y": 1147}}, {"platform": {"type": 1, "x": 217, "y": 1216}}, {"platform": {"type": 21, "x": 47, "y": 1219}}, {"platform": {"type": 1, "x": 489, "y": 1296}}, {"platform": {"type": 21, "x": 311, "y": 1303}}, {"platform": {"type": 1, "x": 267, "y": 1388}}, {"platform": {"type": 21, "x": 427, "y": 1476}}, {"platform": {"type": 1, "x": 26, "y": 1477}}]}}, {"id": 306, "weight": 6, "tags": [5], "stringName": "306", "config": {"composables": [{"platform": {"type": 1, "x": 505, "y": 146}}, {"platform": {"type": 1, "x": 99, "y": 155}}, {"platform": {"type": 1, "x": 281, "y": 196}}, {"platform": {"type": 1, "x": 110, "y": 343}}, {"platform": {"type": 1, "x": 511, "y": 459}}, {"platform": {"type": 21, "x": 353, "y": 467}}, {"platform": {"type": 1, "x": 151, "y": 525}}, {"platform": {"type": 1, "x": 375, "y": 568}}, {"platform": {"type": 1, "x": 58, "y": 588, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 211, "y": 612}}, {"platform": {"type": 21, "x": 468, "y": 680}}, {"platform": {"type": 1, "x": 386, "y": 728}}, {"platform": {"type": 1, "x": 28, "y": 815, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 410, "y": 825}}, {"platform": {"type": 1, "x": 398, "y": 918, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 165, "y": 1088}}, {"platform": {"type": 1, "x": 239, "y": 1145}}, {"platform": {"type": 1, "x": 113, "y": 1298}}, {"platform": {"type": 21, "x": 473, "y": 1318}}, {"platform": {"type": 1, "x": 255, "y": 1457}}]}}, {"id": 307, "weight": 6, "tags": [5], "stringName": "307", "config": {"composables": [{"platform": {"type": 1, "x": 57, "y": 120}}, {"platform": {"type": 1, "x": 305, "y": 158}}, {"platform": {"type": 21, "x": 269, "y": 363}}, {"platform": {"type": 1, "x": 151, "y": 388}}, {"platform": {"type": 1, "x": 20, "y": 451}}, {"platform": {"type": 21, "x": 500, "y": 459}}, {"platform": {"type": 1, "x": 384, "y": 489}}, {"platform": {"type": 1, "x": 68, "y": 535}}, {"platform": {"type": 1, "x": 304, "y": 568, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 30, "y": 781}}, {"platform": {"type": 1, "x": 320, "y": 831, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 26, "y": 870}}, {"platform": {"type": 1, "x": 56, "y": 1083}}, {"platform": {"type": 1, "x": 474, "y": 1101}}, {"platform": {"type": 1, "x": 108, "y": 1170}}, {"platform": {"type": 1, "x": 500, "y": 1182}}, {"platform": {"type": 1, "x": 59, "y": 1270}}, {"platform": {"type": 1, "x": 197, "y": 1379}}, {"platform": {"type": 1, "x": 463, "y": 1425}}, {"platform": {"type": 1, "x": 156, "y": 1489}}]}}, {"id": 308, "weight": 6, "tags": [5], "stringName": "308", "config": {"composables": [{"platform": {"type": 1, "x": 433, "y": 112}}, {"platform": {"type": 1, "x": 108, "y": 143}}, {"platform": {"type": 1, "x": 230, "y": 200}}, {"platform": {"type": 21, "x": 439, "y": 225}}, {"platform": {"type": 1, "x": 326, "y": 277}}, {"platform": {"type": 1, "x": 410, "y": 326, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 256, "y": 351}}, {"platform": {"type": 1, "x": 181, "y": 418}}, {"platform": {"type": 1, "x": 89, "y": 531}}, {"platform": {"type": 21, "x": 245, "y": 666}}, {"platform": {"type": 1, "x": 47, "y": 685, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 345, "y": 890}}, {"platform": {"type": 1, "x": 507, "y": 936}}, {"platform": {"type": 1, "x": 136, "y": 1140}}, {"platform": {"type": 1, "x": 457, "y": 1154}}, {"platform": {"type": 1, "x": 74, "y": 1271}}, {"platform": {"type": 1, "x": 312, "y": 1314}}, {"platform": {"type": 1, "x": 466, "y": 1381}}, {"platform": {"type": 21, "x": 511, "y": 1473}}, {"platform": {"type": 1, "x": 57, "y": 1506}}]}}, {"id": 309, "weight": 6, "tags": [5], "stringName": "309", "config": {"composables": [{"platform": {"type": 1, "x": 212, "y": 94}}, {"platform": {"type": 1, "x": 66, "y": 109}}, {"platform": {"type": 1, "x": 486, "y": 166, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 372, "y": 184, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 45, "y": 358}}, {"platform": {"type": 1, "x": 159, "y": 377, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 452, "y": 499}}, {"platform": {"type": 1, "x": 509, "y": 550}}, {"platform": {"type": 1, "x": 196, "y": 772}}, {"platform": {"type": 1, "x": 315, "y": 772}}, {"platform": {"type": 21, "x": 159, "y": 823}}, {"platform": {"type": 21, "x": 377, "y": 965}}, {"platform": {"type": 1, "x": 155, "y": 1022}}, {"platform": {"type": 1, "x": 239, "y": 1060, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 41, "y": 1290}}, {"platform": {"type": 1, "x": 123, "y": 1339}}, {"platform": {"type": 1, "x": 282, "y": 1354}}, {"platform": {"type": 1, "x": 408, "y": 1469}}, {"platform": {"type": 1, "x": 45, "y": 1515}}, {"platform": {"type": 1, "x": 507, "y": 1515}}]}}, {"id": 310, "weight": 6, "tags": [5], "stringName": "310", "config": {"composables": [{"platform": {"type": 1, "x": 31, "y": 75}}, {"platform": {"type": 1, "x": 159, "y": 90, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 495, "y": 180}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 282, "y": 368}}, {"platform": {"type": 1, "x": 159, "y": 377, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 452, "y": 499}}, {"platform": {"type": 1, "x": 349, "y": 529}}, {"platform": {"type": 1, "x": 25, "y": 603, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 520, "y": 633}}, {"platform": {"type": 1, "x": 315, "y": 772}}, {"platform": {"type": 21, "x": 196, "y": 778}}, {"platform": {"type": 1, "x": 406, "y": 808}}, {"platform": {"type": 21, "x": 182, "y": 1000}}, {"platform": {"type": 1, "x": 45, "y": 1030, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 155, "y": 1060}}, {"platform": {"type": 1, "x": 491, "y": 1060}}, {"platform": {"type": 1, "x": 476, "y": 1290}}, {"platform": {"type": 1, "x": 381, "y": 1320}}, {"platform": {"type": 1, "x": 282, "y": 1354}}, {"platform": {"type": 1, "x": 5, "y": 1485}}, {"platform": {"type": 1, "x": 491, "y": 1485}}, {"platform": {"type": 1, "x": 374, "y": 1515}}]}}, {"id": 311, "weight": 6, "tags": [5], "stringName": "311", "config": {"composables": [{"platform": {"type": 1, "x": 489, "y": 75}}, {"platform": {"type": 1, "x": 361, "y": 90, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 25, "y": 180}}, {"platform": {"type": 21, "x": 381, "y": 309}}, {"platform": {"type": 1, "x": 238, "y": 368}}, {"platform": {"type": 1, "x": 361, "y": 377, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 68, "y": 499}}, {"platform": {"type": 1, "x": 171, "y": 529, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 495, "y": 603}}, {"platform": {"type": 1, "x": 11, "y": 633}}, {"platform": {"type": 1, "x": 205, "y": 772}}, {"platform": {"type": 21, "x": 324, "y": 778}}, {"platform": {"type": 1, "x": 114, "y": 808}}, {"platform": {"type": 21, "x": 338, "y": 1000}}, {"platform": {"type": 1, "x": 475, "y": 1030, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 29, "y": 1060}}, {"platform": {"type": 1, "x": 365, "y": 1060}}, {"platform": {"type": 1, "x": 44, "y": 1290}}, {"platform": {"type": 1, "x": 139, "y": 1320}}, {"platform": {"type": 1, "x": 238, "y": 1354}}, {"platform": {"type": 1, "x": 29, "y": 1485}}, {"platform": {"type": 1, "x": 515, "y": 1485}}, {"platform": {"type": 1, "x": 146, "y": 1515}}]}}, {"id": 312, "weight": 6, "tags": [5], "stringName": "312", "config": {"composables": [{"platform": {"type": 1, "x": 212, "y": 94}}, {"platform": {"type": 1, "x": 66, "y": 109}}, {"platform": {"type": 1, "x": 509, "y": 139}}, {"platform": {"type": 1, "x": 334, "y": 173, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 159, "y": 377, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 351, "y": 422, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 25, "y": 514}}, {"platform": {"type": 1, "x": 509, "y": 550}}, {"platform": {"type": 1, "x": 82, "y": 727}}, {"platform": {"type": 21, "x": 159, "y": 823}}, {"platform": {"type": 1, "x": 401, "y": 838}}, {"platform": {"type": 21, "x": 377, "y": 965}}, {"platform": {"type": 1, "x": 139, "y": 980}}, {"platform": {"type": 1, "x": 282, "y": 1126, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 491, "y": 1207}}, {"platform": {"type": 1, "x": 32, "y": 1237}}, {"platform": {"type": 1, "x": 339, "y": 1309}}, {"platform": {"type": 1, "x": 123, "y": 1339}}, {"platform": {"type": 1, "x": 344, "y": 1455}}, {"platform": {"type": 1, "x": 66, "y": 1522}}, {"platform": {"type": 1, "x": 508, "y": 1535}}]}}, {"id": 313, "weight": 6, "tags": [5], "stringName": "313", "config": {"composables": [{"platform": {"type": 1, "x": 194, "y": 94}}, {"platform": {"type": 1, "x": 18, "y": 109}}, {"platform": {"type": 1, "x": 509, "y": 139}}, {"platform": {"type": 1, "x": 344, "y": 154}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 159, "y": 377, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 287, "y": 377, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 32, "y": 558}}, {"platform": {"type": 1, "x": 469, "y": 573}}, {"platform": {"type": 1, "x": 145, "y": 757, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 216, "y": 813}}, {"platform": {"type": 1, "x": 434, "y": 880}}, {"platform": {"type": 1, "x": 139, "y": 980}}, {"platform": {"type": 21, "x": 66, "y": 1020}}, {"platform": {"type": 1, "x": 282, "y": 1126, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 501, "y": 1246}}, {"platform": {"type": 1, "x": 25, "y": 1261}}, {"platform": {"type": 1, "x": 248, "y": 1345}}, {"platform": {"type": 1, "x": 444, "y": 1375}}, {"platform": {"type": 1, "x": 3, "y": 1485}}, {"platform": {"type": 1, "x": 248, "y": 1512}}, {"platform": {"type": 1, "x": 508, "y": 1535}}]}}, {"id": 314, "weight": 6, "tags": [5], "stringName": "314", "config": {"composables": [{"platform": {"type": 1, "x": 353, "y": 94}}, {"platform": {"type": 1, "x": 513, "y": 94}}, {"platform": {"type": 1, "x": 18, "y": 109}}, {"platform": {"type": 1, "x": 180, "y": 154}}, {"platform": {"type": 1, "x": 273, "y": 247, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 44, "y": 407, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 273, "y": 507, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 399, "y": 592}}, {"platform": {"type": 1, "x": 491, "y": 655}}, {"platform": {"type": 1, "x": 56, "y": 805}}, {"platform": {"type": 1, "x": 456, "y": 862}}, {"platform": {"type": 1, "x": 273, "y": 910, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 82, "y": 1074}}, {"platform": {"type": 21, "x": 320, "y": 1159}}, {"platform": {"type": 1, "x": 501, "y": 1189}}, {"platform": {"type": 1, "x": 239, "y": 1273}}, {"platform": {"type": 1, "x": 434, "y": 1360}}, {"platform": {"type": 1, "x": 25, "y": 1375}}, {"platform": {"type": 1, "x": 113, "y": 1450}}, {"platform": {"type": 1, "x": 513, "y": 1482}}, {"platform": {"type": 1, "x": 239, "y": 1535}}]}}, {"id": 315, "weight": 6, "tags": [5], "stringName": "315", "config": {"composables": [{"platform": {"type": 1, "x": 168, "y": 94}}, {"platform": {"type": 1, "x": 513, "y": 94}}, {"platform": {"type": 1, "x": 18, "y": 109}}, {"platform": {"type": 1, "x": 353, "y": 167}}, {"platform": {"type": 1, "x": 479, "y": 294, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 44, "y": 407}}, {"platform": {"type": 21, "x": 273, "y": 437}}, {"platform": {"type": 1, "x": 273, "y": 507, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 75, "y": 607, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 266, "y": 610}}, {"platform": {"type": 1, "x": 456, "y": 616, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 373, "y": 805}}, {"platform": {"type": 1, "x": 139, "y": 892}}, {"platform": {"type": 1, "x": 66, "y": 1107}}, {"platform": {"type": 1, "x": 469, "y": 1107}}, {"platform": {"type": 21, "x": 168, "y": 1174}}, {"platform": {"type": 1, "x": 273, "y": 1241}}, {"platform": {"type": 1, "x": 456, "y": 1345}}, {"platform": {"type": 1, "x": 123, "y": 1375}}, {"platform": {"type": 1, "x": 513, "y": 1482}}, {"platform": {"type": 1, "x": 18, "y": 1505}}, {"platform": {"type": 1, "x": 239, "y": 1535}}]}}, {"id": 316, "weight": 6, "tags": [5], "stringName": "316", "config": {"composables": [{"platform": {"type": 1, "x": 342, "y": 94}}, {"platform": {"type": 1, "x": 18, "y": 109}}, {"platform": {"type": 1, "x": 487, "y": 167}}, {"platform": {"type": 1, "x": 196, "y": 197, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 139, "y": 309}}, {"platform": {"type": 1, "x": 430, "y": 382, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 25, "y": 412, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 273, "y": 437}}, {"platform": {"type": 1, "x": 253, "y": 537, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 487, "y": 688}}, {"platform": {"type": 1, "x": 285, "y": 790}}, {"platform": {"type": 1, "x": 48, "y": 904}}, {"platform": {"type": 1, "x": 416, "y": 1016}}, {"platform": {"type": 1, "x": 111, "y": 1092}}, {"platform": {"type": 21, "x": 168, "y": 1174}}, {"platform": {"type": 1, "x": 346, "y": 1204}}, {"platform": {"type": 1, "x": 48, "y": 1298}}, {"platform": {"type": 1, "x": 510, "y": 1315}}, {"platform": {"type": 1, "x": 285, "y": 1369}}, {"platform": {"type": 1, "x": 473, "y": 1497}}, {"platform": {"type": 1, "x": 85, "y": 1513}}]}}, {"id": 317, "weight": 4, "tags": [5], "stringName": "317", "config": {"composables": [{"platform": {"type": 1, "x": 342, "y": 94}}, {"platform": {"type": 1, "x": 18, "y": 109}}, {"platform": {"type": 1, "x": 487, "y": 167}}, {"platform": {"type": 21, "x": 97, "y": 258}}, {"platform": {"type": 1, "x": 416, "y": 258, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 217, "y": 452, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 373, "y": 552}}, {"platform": {"type": 1, "x": 54, "y": 652, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 440, "y": 667}}, {"platform": {"type": 1, "x": 232, "y": 815}}, {"platform": {"type": 1, "x": 476, "y": 933}}, {"platform": {"type": 1, "x": 75, "y": 945}}, {"platform": {"type": 1, "x": 253, "y": 1077, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 40, "y": 1174}}, {"platform": {"type": 1, "x": 430, "y": 1174}}, {"platform": {"type": 21, "x": 206, "y": 1204}}, {"platform": {"type": 1, "x": 48, "y": 1298}}, {"platform": {"type": 1, "x": 320, "y": 1328}}, {"platform": {"type": 1, "x": 476, "y": 1467}}, {"platform": {"type": 1, "x": 132, "y": 1527}}]}}, {"id": 401, "weight": 8, "tags": [11], "stringName": "401", "config": {"composables": [{"platform": {"type": 1, "x": 476, "y": 127}}, {"platform": {"type": 1, "x": 83, "y": 157}}, {"platform": {"type": 21, "x": 98, "y": 216}}, {"platform": {"type": 1, "x": 313, "y": 216}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 41, "y": 505}}, {"platform": {"type": 1, "x": 62, "y": 719}}, {"platform": {"type": 1, "x": 419, "y": 749}}, {"platform": {"type": 1, "x": 41, "y": 980}}, {"platform": {"type": 1, "x": 416, "y": 1031}}, {"platform": {"type": 21, "x": 44, "y": 1060}}, {"platform": {"type": 21, "x": 483, "y": 1204}}, {"platform": {"type": 1, "x": 263, "y": 1234}}, {"platform": {"type": 1, "x": 206, "y": 1428}}, {"platform": {"type": 1, "x": 27, "y": 1452}}, {"platform": {"type": 1, "x": 483, "y": 1513}}]}}, {"id": 402, "weight": 8, "tags": [11], "stringName": "402", "config": {"composables": [{"platform": {"type": 1, "x": 476, "y": 127}}, {"platform": {"type": 1, "x": 27, "y": 156}}, {"platform": {"type": 1, "x": 176, "y": 186}}, {"platform": {"type": 21, "x": 382, "y": 186}}, {"platform": {"type": 1, "x": 302, "y": 231}}, {"platform": {"type": 1, "x": 141, "y": 434}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 44, "y": 634}}, {"platform": {"type": 1, "x": 245, "y": 749}}, {"platform": {"type": 1, "x": 416, "y": 867}}, {"platform": {"type": 1, "x": 158, "y": 995}}, {"platform": {"type": 1, "x": 496, "y": 1020}}, {"platform": {"type": 21, "x": 44, "y": 1060}}, {"platform": {"type": 21, "x": 483, "y": 1204}}, {"platform": {"type": 1, "x": 44, "y": 1219}}, {"platform": {"type": 1, "x": 215, "y": 1347}}, {"platform": {"type": 1, "x": 382, "y": 1428}}, {"platform": {"type": 1, "x": 27, "y": 1452}}, {"platform": {"type": 1, "x": 483, "y": 1513}}]}}, {"id": 403, "weight": 8, "tags": [11], "stringName": "403", "config": {"composables": [{"platform": {"type": 1, "x": 315, "y": 135}}, {"platform": {"type": 1, "x": 28, "y": 142}}, {"platform": {"type": 1, "x": 486, "y": 210}}, {"platform": {"type": 1, "x": 177, "y": 225}}, {"platform": {"type": 1, "x": 429, "y": 388}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 142, "y": 536}}, {"platform": {"type": 21, "x": 263, "y": 566}}, {"platform": {"type": 1, "x": 44, "y": 772}}, {"platform": {"type": 1, "x": 158, "y": 995}}, {"platform": {"type": 1, "x": 496, "y": 1020}}, {"platform": {"type": 1, "x": 44, "y": 1060}}, {"platform": {"type": 21, "x": 483, "y": 1204}}, {"platform": {"type": 1, "x": 44, "y": 1219}}, {"platform": {"type": 1, "x": 407, "y": 1271}}, {"platform": {"type": 1, "x": 238, "y": 1373}}, {"platform": {"type": 1, "x": 62, "y": 1498}}, {"platform": {"type": 1, "x": 483, "y": 1513}}]}}, {"id": 404, "weight": 8, "tags": [11], "stringName": "404", "config": {"composables": [{"platform": {"type": 1, "x": 437, "y": 136}}, {"platform": {"type": 21, "x": 392, "y": 208}}, {"platform": {"type": 1, "x": 131, "y": 245, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 21, "x": 264, "y": 258}}, {"platform": {"type": 1, "x": 428, "y": 303}}, {"platform": {"type": 1, "x": 429, "y": 379}}, {"platform": {"type": 1, "x": 210, "y": 405, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 94, "y": 471}}, {"platform": {"type": 1, "x": 252, "y": 496}}, {"platform": {"type": 1, "x": 206, "y": 606}}, {"platform": {"type": 1, "x": 58, "y": 608}}, {"platform": {"type": 1, "x": 57, "y": 717}}, {"platform": {"type": 1, "x": 333, "y": 742}}, {"platform": {"type": 1, "x": 416, "y": 810}}, {"platform": {"type": 1, "x": 524, "y": 916}}, {"platform": {"type": 1, "x": 114, "y": 1128}}, {"platform": {"type": 1, "x": 307, "y": 1140}}, {"platform": {"type": 1, "x": 422, "y": 1301}}, {"platform": {"type": 1, "x": 298, "y": 1358}}, {"platform": {"type": 1, "x": 507, "y": 1382}}, {"platform": {"type": 1, "x": 41, "y": 1397}}, {"platform": {"type": 1, "x": 193, "y": 1463}}]}}, {"id": 405, "weight": 8, "tags": [11], "stringName": "405", "config": {"composables": [{"platform": {"type": 1, "x": 258, "y": 126}}, {"platform": {"type": 1, "x": 495, "y": 198}}, {"platform": {"type": 1, "x": 180, "y": 228}}, {"platform": {"type": 21, "x": 198, "y": 267}}, {"platform": {"type": 1, "x": 334, "y": 322, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 523, "y": 548, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 322, "y": 686}}, {"platform": {"type": 1, "x": 142, "y": 751}}, {"platform": {"type": 1, "x": 13, "y": 755}}, {"platform": {"type": 1, "x": 330, "y": 763}}, {"platform": {"type": 1, "x": 185, "y": 800}}, {"platform": {"type": 1, "x": 136, "y": 910}}, {"platform": {"type": 1, "x": 322, "y": 925}}, {"platform": {"type": 1, "x": 140, "y": 965}}, {"platform": {"type": 1, "x": 268, "y": 1077}}, {"platform": {"type": 1, "x": 308, "y": 1156}}, {"platform": {"type": 1, "x": 100, "y": 1159}}, {"platform": {"type": 1, "x": 438, "y": 1319}}, {"platform": {"type": 1, "x": 312, "y": 1322}}, {"platform": {"type": 1, "x": 192, "y": 1374}}, {"platform": {"type": 1, "x": 432, "y": 1460}}]}}, {"id": 406, "weight": 8, "tags": [11], "stringName": "406", "config": {"composables": [{"platform": {"type": 1, "x": 304, "y": 161}}, {"platform": {"type": 1, "x": 59, "y": 221, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 21, "x": 481, "y": 236}}, {"platform": {"type": 1, "x": 219, "y": 342}}, {"platform": {"type": 1, "x": 16, "y": 456}}, {"platform": {"type": 1, "x": 158, "y": 471}}, {"platform": {"type": 1, "x": 314, "y": 553}}, {"platform": {"type": 1, "x": 454, "y": 561}}, {"platform": {"type": 1, "x": 18, "y": 767}}, {"platform": {"type": 1, "x": 522, "y": 846}}, {"platform": {"type": 1, "x": 115, "y": 889}}, {"platform": {"type": 21, "x": 206, "y": 934}}, {"platform": {"type": 1, "x": 507, "y": 968}}, {"platform": {"type": 1, "x": 177, "y": 1049}}, {"platform": {"type": 1, "x": 469, "y": 1121}}, {"platform": {"type": 1, "x": 83, "y": 1140}}, {"platform": {"type": 21, "x": 15, "y": 1177}}, {"platform": {"type": 1, "x": 425, "y": 1209}}, {"platform": {"type": 1, "x": 513, "y": 1272}}, {"platform": {"type": 1, "x": 334, "y": 1273}}, {"platform": {"type": 1, "x": 202, "y": 1334}}]}}, {"id": 407, "weight": 8, "tags": [11], "stringName": "407", "config": {"composables": [{"platform": {"type": 1, "x": 428, "y": 121}}, {"platform": {"type": 1, "x": 31, "y": 176, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 262, "y": 242}}, {"platform": {"type": 1, "x": 93, "y": 358, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 252, "y": 449}}, {"platform": {"type": 1, "x": 170, "y": 498}}, {"platform": {"type": 1, "x": 72, "y": 622}}, {"platform": {"type": 21, "x": 397, "y": 642}}, {"platform": {"type": 1, "x": 278, "y": 669}}, {"platform": {"type": 21, "x": 11, "y": 694}}, {"platform": {"type": 1, "x": 432, "y": 870}}, {"platform": {"type": 1, "x": 513, "y": 919}}, {"platform": {"type": 21, "x": 315, "y": 947}}, {"platform": {"type": 1, "x": 10, "y": 995}}, {"platform": {"type": 1, "x": 485, "y": 1047}}, {"platform": {"type": 1, "x": 41, "y": 1048}}, {"platform": {"type": 1, "x": 319, "y": 1165}}, {"platform": {"type": 1, "x": 186, "y": 1292}}, {"platform": {"type": 1, "x": 352, "y": 1313}}, {"platform": {"type": 1, "x": 496, "y": 1318}}, {"platform": {"type": 1, "x": 174, "y": 1343}}, {"platform": {"type": 1, "x": 15, "y": 1402}}]}}, {"id": 408, "weight": 8, "tags": [11], "stringName": "408", "config": {"composables": [{"platform": {"type": 1, "x": 269, "y": 94}}, {"platform": {"type": 21, "x": 31, "y": 132}}, {"platform": {"type": 1, "x": 489, "y": 190}}, {"platform": {"type": 1, "x": 126, "y": 205, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 515, "y": 422}}, {"platform": {"type": 1, "x": 401, "y": 598}}, {"platform": {"type": 21, "x": 248, "y": 720}}, {"platform": {"type": 1, "x": 71, "y": 735}}, {"platform": {"type": 21, "x": 71, "y": 901}}, {"platform": {"type": 1, "x": 85, "y": 1020}}, {"platform": {"type": 1, "x": 341, "y": 1086}}, {"platform": {"type": 1, "x": 511, "y": 1189}}, {"platform": {"type": 21, "x": 526, "y": 1362}}, {"platform": {"type": 1, "x": 28, "y": 1434}}, {"platform": {"type": 1, "x": 320, "y": 1434}}, {"platform": {"type": 1, "x": 105, "y": 1517}}]}}, {"id": 409, "weight": 8, "tags": [11], "stringName": "409", "config": {"composables": [{"platform": {"type": 1, "x": 382, "y": 85}}, {"platform": {"type": 1, "x": 503, "y": 105, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 332, "y": 245}}, {"platform": {"type": 1, "x": 166, "y": 275}}, {"platform": {"type": 1, "x": 44, "y": 407}}, {"platform": {"type": 1, "x": 446, "y": 536}}, {"platform": {"type": 21, "x": 263, "y": 566}}, {"platform": {"type": 1, "x": 101, "y": 646}}, {"platform": {"type": 1, "x": 434, "y": 740}}, {"platform": {"type": 1, "x": 118, "y": 841}}, {"platform": {"type": 1, "x": 484, "y": 933}}, {"platform": {"type": 21, "x": 44, "y": 1060}}, {"platform": {"type": 1, "x": 227, "y": 1124}}, {"platform": {"type": 21, "x": 496, "y": 1145}}, {"platform": {"type": 1, "x": 44, "y": 1191}}, {"platform": {"type": 1, "x": 526, "y": 1348}}, {"platform": {"type": 1, "x": 372, "y": 1483}}, {"platform": {"type": 1, "x": 62, "y": 1498}}]}}, {"id": 410, "weight": 8, "tags": [11], "stringName": "410", "config": {"composables": [{"platform": {"type": 1, "x": 382, "y": 85}}, {"platform": {"type": 1, "x": 41, "y": 124, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 382, "y": 163}}, {"platform": {"type": 1, "x": 486, "y": 210}}, {"platform": {"type": 1, "x": 206, "y": 407}}, {"platform": {"type": 1, "x": 412, "y": 444}}, {"platform": {"type": 21, "x": 263, "y": 566}}, {"platform": {"type": 1, "x": 101, "y": 646}}, {"platform": {"type": 1, "x": 491, "y": 661}}, {"platform": {"type": 1, "x": 412, "y": 716}}, {"platform": {"type": 1, "x": 118, "y": 841}}, {"platform": {"type": 1, "x": 484, "y": 933}}, {"platform": {"type": 1, "x": 263, "y": 1024}}, {"platform": {"type": 21, "x": 176, "y": 1076}}, {"platform": {"type": 1, "x": 44, "y": 1191}}, {"platform": {"type": 1, "x": 101, "y": 1251}}, {"platform": {"type": 21, "x": 469, "y": 1281}}, {"platform": {"type": 1, "x": 496, "y": 1336}}, {"platform": {"type": 1, "x": 62, "y": 1498}}, {"platform": {"type": 1, "x": 439, "y": 1513}}]}}, {"id": 411, "weight": 10, "tags": [11], "stringName": "411", "config": {"composables": [{"platform": {"type": 1, "x": 304, "y": 91}}, {"platform": {"type": 1, "x": 247, "y": 139, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 26, "y": 316}}, {"platform": {"type": 1, "x": 526, "y": 331}}, {"platform": {"type": 1, "x": 175, "y": 414}}, {"platform": {"type": 1, "x": 412, "y": 444}}, {"platform": {"type": 21, "x": 263, "y": 566}}, {"platform": {"type": 1, "x": 44, "y": 612}}, {"platform": {"type": 1, "x": 499, "y": 676}}, {"platform": {"type": 1, "x": 298, "y": 753}}, {"platform": {"type": 1, "x": 44, "y": 901}}, {"platform": {"type": 1, "x": 263, "y": 974}}, {"platform": {"type": 21, "x": 184, "y": 1009}}, {"platform": {"type": 1, "x": 487, "y": 1024}}, {"platform": {"type": 1, "x": 44, "y": 1191}}, {"platform": {"type": 1, "x": 241, "y": 1251}}, {"platform": {"type": 21, "x": 469, "y": 1281}}, {"platform": {"type": 1, "x": 439, "y": 1321}}, {"platform": {"type": 1, "x": 127, "y": 1444}}, {"platform": {"type": 1, "x": 320, "y": 1528}}]}}, {"id": 412, "weight": 10, "tags": [11], "stringName": "412", "config": {"composables": [{"platform": {"type": 1, "x": 11, "y": 77}}, {"platform": {"type": 1, "x": 487, "y": 92}}, {"platform": {"type": 1, "x": 289, "y": 190, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 20, "y": 392}}, {"platform": {"type": 1, "x": 470, "y": 480}}, {"platform": {"type": 1, "x": 68, "y": 487}}, {"platform": {"type": 1, "x": 267, "y": 517}}, {"platform": {"type": 21, "x": 263, "y": 566}}, {"platform": {"type": 1, "x": 101, "y": 729}}, {"platform": {"type": 1, "x": 508, "y": 790}}, {"platform": {"type": 1, "x": 267, "y": 798}}, {"platform": {"type": 21, "x": 263, "y": 934}}, {"platform": {"type": 1, "x": 60, "y": 987}}, {"platform": {"type": 1, "x": 477, "y": 1035}}, {"platform": {"type": 1, "x": 287, "y": 1176}}, {"platform": {"type": 1, "x": 44, "y": 1232}}, {"platform": {"type": 21, "x": 394, "y": 1262}}, {"platform": {"type": 1, "x": 469, "y": 1322}}, {"platform": {"type": 1, "x": 158, "y": 1408}}, {"platform": {"type": 1, "x": 432, "y": 1513}}]}}, {"id": 501, "weight": 8, "tags": [7], "stringName": "501", "config": {"composables": [{"platform": {"type": 21, "x": 41, "y": 158}}, {"platform": {"type": 1, "x": 443, "y": 244}}, {"platform": {"type": 1, "x": 65, "y": 293}}, {"platform": {"type": 1, "x": 497, "y": 293}}, {"platform": {"type": 1, "x": 280, "y": 476, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 47, "y": 547}}, {"platform": {"type": 1, "x": 440, "y": 661}}, {"platform": {"type": 21, "x": 263, "y": 805}}, {"platform": {"type": 1, "x": 98, "y": 917}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 426, "y": 917}}, {"platform": {"type": 21, "x": 483, "y": 956}}, {"platform": {"type": 1, "x": 406, "y": 1122}}, {"platform": {"type": 1, "x": 236, "y": 1246}}, {"platform": {"type": 1, "x": 122, "y": 1354}}, {"platform": {"type": 1, "x": 500, "y": 1475}}, {"platform": {"type": 1, "x": 369, "y": 1505}}]}}, {"id": 502, "weight": 8, "tags": [7], "stringName": "502", "config": {"composables": [{"platform": {"type": 21, "x": 241, "y": 111}}, {"platform": {"type": 1, "x": 47, "y": 143, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 440, "y": 143}}, {"platform": {"type": 1, "x": 241, "y": 254, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 469, "y": 482}}, {"platform": {"type": 1, "x": 83, "y": 512}}, {"platform": {"type": 21, "x": 184, "y": 646}}, {"platform": {"type": 1, "x": 355, "y": 735, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 500, "y": 872}}, {"platform": {"type": 1, "x": 469, "y": 966}}, {"platform": {"type": 1, "x": 70, "y": 1033}}, {"platform": {"type": 1, "x": 412, "y": 1176}}, {"platform": {"type": 1, "x": 301, "y": 1324}}, {"platform": {"type": 1, "x": 68, "y": 1424}}, {"platform": {"type": 1, "x": 282, "y": 1455}}, {"platform": {"type": 1, "x": 497, "y": 1515}}]}}, {"id": 503, "weight": 8, "tags": [7], "stringName": "503", "config": {"composables": [{"platform": {"type": 1, "x": 320, "y": 157}}, {"platform": {"type": 1, "x": 166, "y": 178}}, {"platform": {"type": 1, "x": 41, "y": 242}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 473, "y": 257}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 263, "y": 381}}, {"platform": {"type": 1, "x": 305, "y": 427, "tonToken": true}}, {"platform": {"type": 1, "x": 263, "y": 603}}, {"platform": {"type": 1, "x": 84, "y": 731}}, {"platform": {"type": 21, "x": 443, "y": 751}}, {"platform": {"type": 1, "x": 443, "y": 861}}, {"platform": {"type": 21, "x": 263, "y": 1038}}, {"platform": {"type": 1, "x": 41, "y": 1053}}, {"platform": {"type": 1, "x": 483, "y": 1166}}, {"platform": {"type": 21, "x": 386, "y": 1291}}, {"platform": {"type": 1, "x": 121, "y": 1389}}, {"platform": {"type": 1, "x": 362, "y": 1465}}, {"platform": {"type": 1, "x": 27, "y": 1514}}]}}, {"id": 506, "weight": 8, "tags": [7], "stringName": "506", "config": {"composables": [{"platform": {"type": 1, "x": 516, "y": 95}}, {"platform": {"type": 1, "x": 426, "y": 132}}, {"platform": {"type": 1, "x": 347, "y": 178}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 27, "y": 370}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 263, "y": 381}}, {"platform": {"type": 1, "x": 359, "y": 550}}, {"platform": {"type": 1, "x": 263, "y": 603, "tonToken": true}}, {"platform": {"type": 1, "x": 84, "y": 731}}, {"platform": {"type": 21, "x": 443, "y": 751}}, {"platform": {"type": 1, "x": 27, "y": 820}}, {"platform": {"type": 21, "x": 263, "y": 1038}}, {"platform": {"type": 1, "x": 369, "y": 1108}}, {"platform": {"type": 1, "x": 483, "y": 1166}}, {"platform": {"type": 21, "x": 386, "y": 1291}}, {"platform": {"type": 1, "x": 161, "y": 1495}}, {"platform": {"type": 1, "x": 27, "y": 1514}}, {"platform": {"type": 1, "x": 290, "y": 1525}}]}}, {"id": 507, "weight": 8, "tags": [7], "stringName": "507", "config": {"composables": [{"platform": {"type": 1, "x": 22, "y": 83}}, {"platform": {"type": 1, "x": 491, "y": 91}}, {"platform": {"type": 1, "x": 320, "y": 106}}, {"platform": {"type": 1, "x": 136, "y": 116}}, {"platform": {"type": 1, "x": 295, "y": 355, "tonToken": true}}, {"platform": {"type": 1, "x": 434, "y": 408}}, {"platform": {"type": 1, "x": 136, "y": 415}}, {"platform": {"type": 1, "x": 49, "y": 549}}, {"platform": {"type": 1, "x": 500, "y": 589}}, {"platform": {"type": 21, "x": 49, "y": 685}}, {"platform": {"type": 21, "x": 434, "y": 750}}, {"platform": {"type": 1, "x": 136, "y": 805}}, {"platform": {"type": 1, "x": 307, "y": 856}}, {"platform": {"type": 1, "x": 220, "y": 890}}, {"platform": {"type": 1, "x": 106, "y": 924}}, {"platform": {"type": 1, "x": 22, "y": 965}}, {"platform": {"type": 21, "x": 272, "y": 1009}}, {"platform": {"type": 1, "x": 386, "y": 1145}}, {"platform": {"type": 1, "x": 491, "y": 1293}}, {"platform": {"type": 1, "x": 443, "y": 1441}}, {"platform": {"type": 1, "x": 79, "y": 1456}}, {"platform": {"type": 1, "x": 272, "y": 1510}}]}}, {"id": 508, "weight": 8, "tags": [7], "stringName": "508", "config": {"composables": [{"platform": {"type": 1, "x": 22, "y": 83}}, {"platform": {"type": 1, "x": 329, "y": 83}}, {"platform": {"type": 1, "x": 491, "y": 124}}, {"platform": {"type": 1, "x": 79, "y": 183}}, {"platform": {"type": 1, "x": 355, "y": 254}}, {"platform": {"type": 1, "x": 63, "y": 284}}, {"platform": {"type": 1, "x": 412, "y": 403}}, {"platform": {"type": 1, "x": 49, "y": 514, "tonToken": true}}, {"platform": {"type": 1, "x": 500, "y": 562}}, {"platform": {"type": 21, "x": 49, "y": 685}}, {"platform": {"type": 1, "x": 434, "y": 704}}, {"platform": {"type": 21, "x": 329, "y": 753}}, {"platform": {"type": 1, "x": 136, "y": 805}}, {"platform": {"type": 1, "x": 272, "y": 919}}, {"platform": {"type": 1, "x": 22, "y": 965}}, {"platform": {"type": 21, "x": 272, "y": 1009}}, {"platform": {"type": 1, "x": 443, "y": 1072}}, {"platform": {"type": 1, "x": 106, "y": 1125}}, {"platform": {"type": 1, "x": 220, "y": 1248}}, {"platform": {"type": 1, "x": 526, "y": 1278}}, {"platform": {"type": 1, "x": 120, "y": 1356}}, {"platform": {"type": 1, "x": 443, "y": 1441}}, {"platform": {"type": 1, "x": 36, "y": 1474}}, {"platform": {"type": 1, "x": 272, "y": 1510}}]}}, {"id": 509, "weight": 8, "tags": [7], "stringName": "509", "config": {"composables": [{"platform": {"type": 1, "x": 1428, "y": 124}}, {"platform": {"type": 1, "x": 469, "y": 175}}, {"platform": {"type": 1, "x": 219, "y": 209, "tonToken": true}}, {"platform": {"type": 1, "x": 20, "y": 286}}, {"platform": {"type": 1, "x": 65, "y": 324}}, {"platform": {"type": 1, "x": 469, "y": 392}}, {"platform": {"type": 1, "x": 412, "y": 443}}, {"platform": {"type": 21, "x": 227, "y": 458}}, {"platform": {"type": 1, "x": 219, "y": 689}}, {"platform": {"type": 1, "x": 419, "y": 695}}, {"platform": {"type": 1, "x": 20, "y": 725}}, {"platform": {"type": 1, "x": 370, "y": 758}}, {"platform": {"type": 21, "x": 227, "y": 903}}, {"platform": {"type": 1, "x": 484, "y": 933}}, {"platform": {"type": 1, "x": 427, "y": 969}}, {"platform": {"type": 1, "x": 93, "y": 1109}}, {"platform": {"type": 1, "x": 227, "y": 1124}}, {"platform": {"type": 1, "x": 20, "y": 1147}}, {"platform": {"type": 1, "x": 427, "y": 1218}}, {"platform": {"type": 1, "x": 496, "y": 1263}}, {"platform": {"type": 21, "x": 227, "y": 1355}}, {"platform": {"type": 1, "x": 20, "y": 1453}}, {"platform": {"type": 1, "x": 370, "y": 1468}}, {"platform": {"type": 1, "x": 77, "y": 1498}}, {"platform": {"type": 1, "x": 313, "y": 1510}}]}}, {"id": 510, "weight": 10, "tags": [7], "stringName": "510", "config": {"composables": [{"platform": {"type": 1, "x": 1193, "y": 81}}, {"platform": {"type": 1, "x": 214, "y": 81}}, {"platform": {"type": 1, "x": 429, "y": 124}}, {"platform": {"type": 1, "x": 469, "y": 175}}, {"platform": {"type": 1, "x": 20, "y": 286}}, {"platform": {"type": 1, "x": 134, "y": 333}}, {"platform": {"type": 1, "x": 469, "y": 392}}, {"platform": {"type": 1, "x": 250, "y": 407}}, {"platform": {"type": 1, "x": 412, "y": 443}}, {"platform": {"type": 21, "x": 227, "y": 458}}, {"platform": {"type": 1, "x": 484, "y": 507, "tonToken": true}}, {"platform": {"type": 1, "x": 45, "y": 648}}, {"platform": {"type": 1, "x": 170, "y": 710}}, {"platform": {"type": 1, "x": 298, "y": 777}}, {"platform": {"type": 1, "x": 400, "y": 840}}, {"platform": {"type": 1, "x": 227, "y": 903}}, {"platform": {"type": 21, "x": 484, "y": 933}}, {"platform": {"type": 1, "x": 427, "y": 969}}, {"platform": {"type": 1, "x": 8, "y": 1010}}, {"platform": {"type": 1, "x": 113, "y": 1055}}, {"platform": {"type": 1, "x": 298, "y": 1139}}, {"platform": {"type": 1, "x": 427, "y": 1218}}, {"platform": {"type": 1, "x": 8, "y": 1254}}, {"platform": {"type": 1, "x": 496, "y": 1263}}, {"platform": {"type": 21, "x": 227, "y": 1355}}, {"platform": {"type": 1, "x": 20, "y": 1453}}, {"platform": {"type": 1, "x": 370, "y": 1468}}, {"platform": {"type": 1, "x": 77, "y": 1498}}, {"platform": {"type": 1, "x": 313, "y": 1510}}, {"platform": {"type": 1, "x": 496, "y": 1525}}]}}, {"id": 511, "weight": 10, "tags": [7], "stringName": "511", "config": {"composables": [{"platform": {"type": 1, "x": 1383, "y": 81}}, {"platform": {"type": 1, "x": 214, "y": 81}}, {"platform": {"type": 1, "x": 429, "y": 124}}, {"platform": {"type": 1, "x": 20, "y": 286}}, {"platform": {"type": 1, "x": 227, "y": 332, "tonToken": true}}, {"platform": {"type": 1, "x": 412, "y": 422}}, {"platform": {"type": 21, "x": 227, "y": 458}}, {"platform": {"type": 1, "x": 77, "y": 477}}, {"platform": {"type": 1, "x": 484, "y": 507}}, {"platform": {"type": 1, "x": 457, "y": 633}}, {"platform": {"type": 1, "x": 45, "y": 648}}, {"platform": {"type": 1, "x": 241, "y": 678}}, {"platform": {"type": 1, "x": 400, "y": 825}}, {"platform": {"type": 1, "x": 102, "y": 865}}, {"platform": {"type": 21, "x": 227, "y": 903}}, {"platform": {"type": 1, "x": 8, "y": 1010}}, {"platform": {"type": 1, "x": 496, "y": 1044}}, {"platform": {"type": 1, "x": 145, "y": 1093}}, {"platform": {"type": 1, "x": 286, "y": 1224}}, {"platform": {"type": 1, "x": 8, "y": 1254}}, {"platform": {"type": 1, "x": 496, "y": 1263}}, {"platform": {"type": 21, "x": 227, "y": 1355}}, {"platform": {"type": 1, "x": 343, "y": 1438}}, {"platform": {"type": 1, "x": 20, "y": 1453}}, {"platform": {"type": 1, "x": 134, "y": 1498}}, {"platform": {"type": 1, "x": 496, "y": 1525}}]}}, {"id": 512, "weight": 10, "tags": [7], "stringName": "512", "config": {"composables": [{"platform": {"type": 1, "y": 124}}, {"platform": {"type": 1, "x": 469, "y": 175}}, {"platform": {"type": 1, "x": 219, "y": 209}}, {"platform": {"type": 1, "x": 65, "y": 324}}, {"platform": {"type": 1, "x": 469, "y": 392}}, {"platform": {"type": 21, "x": 227, "y": 458}}, {"platform": {"type": 1, "x": 237, "y": 517, "tonToken": true}}, {"platform": {"type": 1, "x": 219, "y": 689}}, {"platform": {"type": 1, "x": 491, "y": 695}}, {"platform": {"type": 1, "x": 20, "y": 725}}, {"platform": {"type": 1, "x": 93, "y": 903}}, {"platform": {"type": 21, "x": 227, "y": 903}}, {"platform": {"type": 1, "x": 427, "y": 969}}, {"platform": {"type": 1, "x": 122, "y": 1084}}, {"platform": {"type": 1, "x": 496, "y": 1114}}, {"platform": {"type": 1, "x": 227, "y": 1124}}, {"platform": {"type": 1, "x": 20, "y": 1147}}, {"platform": {"type": 1, "x": 427, "y": 1288}}, {"platform": {"type": 21, "x": 227, "y": 1355}}, {"platform": {"type": 1, "x": 20, "y": 1453}}, {"platform": {"type": 1, "x": 496, "y": 1478}}, {"platform": {"type": 1, "x": 113, "y": 1495}}, {"platform": {"type": 1, "x": 313, "y": 1510}}]}}, {"id": 601, "weight": 10, "tags": [8], "stringName": "601", "config": {"composables": [{"platform": {"type": 1, "x": 21, "y": 83}}, {"platform": {"type": 1, "x": 483, "y": 105}}, {"platform": {"type": 21, "x": 280, "y": 137}}, {"platform": {"type": 1, "x": 78, "y": 236, "tickets": false}}, {"platform": {"type": 1, "x": 426, "y": 339}}, {"platform": {"type": 1, "x": 280, "y": 476}}, {"platform": {"type": 21, "x": 337, "y": 538, "tickets": false}}, {"platform": {"type": 1, "x": 21, "y": 609}}, {"platform": {"type": 1, "x": 369, "y": 712}}, {"platform": {"type": 21, "x": 41, "y": 805, "tickets": false}}, {"platform": {"type": 1, "x": 98, "y": 917, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 312, "y": 967}}, {"platform": {"type": 1, "x": 446, "y": 1176}}, {"platform": {"type": 1, "x": 41, "y": 1225}}, {"platform": {"type": 21, "x": 398, "y": 1273, "tickets": false}}, {"platform": {"type": 1, "x": 263, "y": 1381}}, {"platform": {"type": 1, "x": 41, "y": 1506}}, {"platform": {"type": 1, "x": 483, "y": 1521}}]}}, {"id": 602, "weight": 10, "tags": [8], "stringName": "602", "config": {"composables": [{"platform": {"type": 21, "x": 206, "y": 157, "tickets": false}}, {"platform": {"type": 1, "x": 389, "y": 166}}, {"platform": {"type": 1, "x": 62, "y": 226, "tickets": false}}, {"platform": {"type": 1, "x": 503, "y": 302}}, {"platform": {"type": 1, "x": 426, "y": 454}}, {"platform": {"type": 1, "x": 326, "y": 639}}, {"platform": {"type": 21, "x": 263, "y": 750, "tickets": false}}, {"platform": {"type": 1, "x": 206, "y": 872}}, {"platform": {"type": 21, "x": 236, "y": 1005, "tickets": false}}, {"platform": {"type": 1, "x": 85, "y": 1020, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 341, "y": 1086}}, {"platform": {"type": 1, "x": 503, "y": 1249}}, {"platform": {"type": 21, "x": 483, "y": 1349, "tickets": false}}, {"platform": {"type": 1, "x": 48, "y": 1449}}, {"platform": {"type": 1, "x": 483, "y": 1449}}, {"platform": {"type": 1, "x": 284, "y": 1530}}]}}, {"id": 603, "weight": 10, "tags": [8], "stringName": "603", "config": {"composables": [{"platform": {"type": 1, "x": 149, "y": 106, "tickets": false}}, {"platform": {"type": 1, "x": 353, "y": 196}}, {"platform": {"type": 21, "x": 526, "y": 302, "tickets": false}}, {"platform": {"type": 1, "x": 426, "y": 407}}, {"platform": {"type": 1, "x": 239, "y": 466}}, {"platform": {"type": 1, "x": 56, "y": 496}}, {"platform": {"type": 21, "x": 11, "y": 639, "tickets": false}}, {"platform": {"type": 1, "x": 125, "y": 750, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 263, "y": 858, "tickets": false}}, {"platform": {"type": 1, "x": 369, "y": 932}}, {"platform": {"type": 1, "x": 512, "y": 1101}}, {"platform": {"type": 1, "x": 394, "y": 1169}}, {"platform": {"type": 21, "x": 222, "y": 1249, "tickets": false}}, {"platform": {"type": 1, "x": 98, "y": 1344}}, {"platform": {"type": 1, "x": 11, "y": 1459}}, {"platform": {"type": 1, "x": 155, "y": 1520}}]}}, {"id": 604, "weight": 10, "tags": [8], "stringName": "604", "config": {"composables": [{"platform": {"type": 1, "x": 333, "y": 77}}, {"platform": {"type": 1, "x": 366, "y": 163}}, {"platform": {"type": 21, "x": 24, "y": 248}}, {"platform": {"type": 21, "x": 15, "y": 383}}, {"platform": {"type": 1, "x": 492, "y": 393}}, {"platform": {"type": 1, "x": 511, "y": 445, "tickets": false}}, {"platform": {"type": 1, "x": 410, "y": 711}}, {"platform": {"type": 1, "x": 77, "y": 798}}, {"platform": {"type": 1, "x": 496, "y": 805}}, {"platform": {"type": 1, "x": 252, "y": 891, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 137, "y": 934}}, {"platform": {"type": 1, "x": 465, "y": 989}}, {"platform": {"type": 1, "x": 514, "y": 1082}}, {"platform": {"type": 1, "x": 461, "y": 1221}}, {"platform": {"type": 1, "x": 100, "y": 1229}}, {"platform": {"type": 1, "x": 198, "y": 1369}}, {"platform": {"type": 1, "x": 391, "y": 1402}}, {"platform": {"type": 21, "x": 150, "y": 1454}}, {"platform": {"type": 1, "x": 268, "y": 1490}}, {"platform": {"type": 1, "x": 43, "y": 1496}}]}}, {"id": 605, "weight": 10, "tags": [8], "stringName": "605", "config": {"composables": [{"platform": {"type": 1, "x": 215, "y": 155}}, {"platform": {"type": 1, "x": 344, "y": 168}}, {"platform": {"type": 21, "x": 186, "y": 230}}, {"platform": {"type": 21, "x": 280, "y": 316}}, {"platform": {"type": 1, "x": 86, "y": 463}}, {"platform": {"type": 1, "x": 294, "y": 518}}, {"platform": {"type": 1, "x": 208, "y": 594, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 25, "y": 723}}, {"platform": {"type": 1, "x": 424, "y": 841}}, {"platform": {"type": 1, "x": 48, "y": 897}}, {"platform": {"type": 21, "x": 303, "y": 917}}, {"platform": {"type": 1, "x": 44, "y": 973}}, {"platform": {"type": 1, "x": 300, "y": 1009}}, {"platform": {"type": 1, "x": 459, "y": 1036}}, {"platform": {"type": 1, "x": 417, "y": 1139}}, {"platform": {"type": 1, "x": 202, "y": 1160}}, {"platform": {"type": 1, "x": 341, "y": 1347}}, {"platform": {"type": 1, "x": 483, "y": 1355}}, {"platform": {"type": 21, "x": 117, "y": 1357}}, {"platform": {"type": 1, "x": 269, "y": 1504}}]}}, {"id": 606, "weight": 10, "tags": [8], "stringName": "606", "config": {"composables": [{"platform": {"type": 1, "x": 78, "y": 84}}, {"platform": {"type": 1, "x": 458, "y": 84}}, {"platform": {"type": 21, "x": 377, "y": 302}}, {"platform": {"type": 1, "x": 296, "y": 332, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 135, "y": 561}}, {"platform": {"type": 1, "x": 458, "y": 585}}, {"platform": {"type": 1, "x": 68, "y": 600}}, {"platform": {"type": 1, "x": 515, "y": 624}}, {"platform": {"type": 21, "x": 11, "y": 639}}, {"platform": {"type": 1, "x": 327, "y": 828}}, {"platform": {"type": 21, "x": 263, "y": 858}}, {"platform": {"type": 1, "x": 182, "y": 888}}, {"platform": {"type": 1, "x": 310, "y": 1127}}, {"platform": {"type": 1, "x": 252, "y": 1180}}, {"platform": {"type": 21, "x": 222, "y": 1249}}, {"platform": {"type": 1, "x": 41, "y": 1290}}, {"platform": {"type": 1, "x": 509, "y": 1423}}, {"platform": {"type": 1, "x": 54, "y": 1438}}, {"platform": {"type": 1, "x": 474, "y": 1480}}, {"platform": {"type": 1, "x": 155, "y": 1520}}]}}, {"id": 607, "weight": 10, "tags": [8], "stringName": "607", "config": {"composables": [{"platform": {"type": 1, "x": 253, "y": 84}}, {"platform": {"type": 1, "x": 458, "y": 84}}, {"platform": {"type": 1, "x": 139, "y": 130}}, {"platform": {"type": 1, "x": 41, "y": 186}}, {"platform": {"type": 21, "x": 41, "y": 282}}, {"platform": {"type": 1, "x": 253, "y": 297}}, {"platform": {"type": 1, "x": 409, "y": 422}}, {"platform": {"type": 1, "x": 41, "y": 456}}, {"platform": {"type": 21, "x": 41, "y": 538}}, {"platform": {"type": 1, "x": 466, "y": 568}}, {"platform": {"type": 1, "x": 279, "y": 639, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 41, "y": 805}}, {"platform": {"type": 21, "x": 41, "y": 858}}, {"platform": {"type": 1, "x": 279, "y": 925}}, {"platform": {"type": 1, "x": 393, "y": 979}}, {"platform": {"type": 1, "x": 41, "y": 1032}}, {"platform": {"type": 21, "x": 41, "y": 1180}}, {"platform": {"type": 1, "x": 488, "y": 1230}}, {"platform": {"type": 21, "x": 222, "y": 1249}}, {"platform": {"type": 1, "x": 41, "y": 1290}}, {"platform": {"type": 1, "x": 296, "y": 1290}}, {"platform": {"type": 1, "x": 474, "y": 1480}}, {"platform": {"type": 1, "x": 30, "y": 1510}}, {"platform": {"type": 1, "x": 155, "y": 1520}}, {"platform": {"type": 1, "x": 374, "y": 1520}}]}}, {"id": 608, "weight": 10, "tags": [8], "stringName": "608", "config": {"composables": [{"platform": {"type": 1, "x": 28, "y": 99}}, {"platform": {"type": 1, "x": 220, "y": 99}}, {"platform": {"type": 1, "x": 497, "y": 115}}, {"platform": {"type": 1, "x": 296, "y": 199}}, {"platform": {"type": 21, "x": 41, "y": 229}}, {"platform": {"type": 1, "x": 424, "y": 260}}, {"platform": {"type": 21, "x": 497, "y": 290}}, {"platform": {"type": 1, "x": 41, "y": 400}}, {"platform": {"type": 1, "x": 310, "y": 464}}, {"platform": {"type": 1, "x": 310, "y": 538}}, {"platform": {"type": 21, "x": 296, "y": 577}}, {"platform": {"type": 1, "x": 320, "y": 630}}, {"platform": {"type": 1, "x": 182, "y": 644}}, {"platform": {"type": 21, "x": 263, "y": 683}}, {"platform": {"type": 1, "x": 468, "y": 790}}, {"platform": {"type": 1, "x": 41, "y": 805}}, {"platform": {"type": 1, "x": 239, "y": 976}}, {"platform": {"type": 1, "x": 367, "y": 976, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 182, "y": 1017}}, {"platform": {"type": 21, "x": 206, "y": 1069}}, {"platform": {"type": 1, "x": 50, "y": 1102}}, {"platform": {"type": 21, "x": 296, "y": 1117}}, {"platform": {"type": 21, "x": 367, "y": 1166}}, {"platform": {"type": 1, "x": 336, "y": 1219}}, {"platform": {"type": 21, "x": 222, "y": 1249}}, {"platform": {"type": 1, "x": 92, "y": 1357}}, {"platform": {"type": 1, "x": 139, "y": 1483}}, {"platform": {"type": 1, "x": 512, "y": 1498}}, {"platform": {"type": 1, "x": 377, "y": 1505}}, {"platform": {"type": 1, "x": 25, "y": 1520}}]}}, {"id": 609, "weight": 10, "tags": [8], "stringName": "609", "config": {"composables": [{"platform": {"type": 1, "x": 220, "y": 99}}, {"platform": {"type": 1, "x": 497, "y": 115}}, {"platform": {"type": 1, "x": 35, "y": 130}}, {"platform": {"type": 21, "x": 41, "y": 229}}, {"platform": {"type": 21, "x": 440, "y": 252}}, {"platform": {"type": 1, "x": 98, "y": 275}}, {"platform": {"type": 1, "x": 367, "y": 297}}, {"platform": {"type": 1, "x": 310, "y": 464}}, {"platform": {"type": 1, "x": 310, "y": 538}}, {"platform": {"type": 1, "x": 71, "y": 559}}, {"platform": {"type": 21, "x": 263, "y": 683}}, {"platform": {"type": 1, "x": 481, "y": 713, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 41, "y": 806}}, {"platform": {"type": 1, "x": 320, "y": 876}}, {"platform": {"type": 21, "x": 353, "y": 925}}, {"platform": {"type": 1, "x": 367, "y": 1023}}, {"platform": {"type": 1, "x": 174, "y": 1053}}, {"platform": {"type": 21, "x": 398, "y": 1068}}, {"platform": {"type": 1, "x": 41, "y": 1212}}, {"platform": {"type": 21, "x": 82, "y": 1249}}, {"platform": {"type": 1, "x": 367, "y": 1340}}, {"platform": {"type": 1, "x": 92, "y": 1357}}, {"platform": {"type": 1, "x": 512, "y": 1513}}, {"platform": {"type": 1, "x": 60, "y": 1520}}]}}, {"id": 610, "weight": 10, "tags": [8], "stringName": "610", "config": {"composables": [{"platform": {"type": 1, "x": 26, "y": 85}}, {"platform": {"type": 1, "x": 239, "y": 125}}, {"platform": {"type": 1, "x": 481, "y": 140}}, {"platform": {"type": 21, "x": 155, "y": 199}}, {"platform": {"type": 1, "x": 41, "y": 229}}, {"platform": {"type": 1, "x": 334, "y": 267}}, {"platform": {"type": 1, "x": 440, "y": 434}}, {"platform": {"type": 1, "x": 174, "y": 494}}, {"platform": {"type": 1, "x": 310, "y": 538}}, {"platform": {"type": 1, "x": 71, "y": 559}}, {"platform": {"type": 21, "x": 60, "y": 683}}, {"platform": {"type": 21, "x": 353, "y": 701}}, {"platform": {"type": 1, "x": 481, "y": 713}}, {"platform": {"type": 1, "x": 41, "y": 806, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 320, "y": 876}}, {"platform": {"type": 1, "x": 497, "y": 1003}}, {"platform": {"type": 21, "x": 398, "y": 1068}}, {"platform": {"type": 1, "x": 424, "y": 1133}}, {"platform": {"type": 1, "x": 269, "y": 1136}}, {"platform": {"type": 1, "x": 41, "y": 1212}}, {"platform": {"type": 21, "x": 82, "y": 1249}}, {"platform": {"type": 21, "x": 25, "y": 1301}}, {"platform": {"type": 1, "x": 424, "y": 1331}}, {"platform": {"type": 1, "x": 512, "y": 1513}}, {"platform": {"type": 1, "x": 60, "y": 1520}}, {"platform": {"type": 1, "x": 310, "y": 1528}}]}}, {"id": 701, "weight": 8, "tags": [6], "stringName": "701", "config": {"composables": [{"platform": {"type": 1, "x": 329, "y": 115}}, {"platform": {"type": 1, "x": 423, "y": 216}}, {"platform": {"type": 21, "x": 500, "y": 309, "tickets": false}}, {"platform": {"type": 1, "x": 162, "y": 354, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 386, "y": 587}}, {"platform": {"type": 1, "x": 70, "y": 617}}, {"platform": {"type": 1, "x": 289, "y": 659}}, {"platform": {"type": 1, "x": 465, "y": 911}}, {"platform": {"type": 21, "x": 127, "y": 1012, "tickets": false}}, {"platform": {"type": 0, "x": 415, "y": 1159, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 119, "y": 1174, "tickets": false}}, {"platform": {"type": 1, "x": 284, "y": 1400}}, {"platform": {"type": 1, "x": 474, "y": 1445}}, {"platform": {"type": 1, "x": 127, "y": 1504, "tickets": false}, "booster": {"type": 500}}]}}, {"id": 702, "weight": 8, "tags": [6], "stringName": "702", "config": {"composables": [{"platform": {"type": 1, "x": 284, "y": 127}}, {"platform": {"type": 1, "x": 48, "y": 145}}, {"platform": {"type": 1, "x": 465, "y": 187}}, {"platform": {"type": 21, "x": 341, "y": 324, "tickets": false}}, {"platform": {"type": 1, "x": 162, "y": 354, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 474, "y": 602}}, {"platform": {"type": 1, "x": 299, "y": 617}}, {"platform": {"type": 1, "x": 160, "y": 675}}, {"platform": {"type": 21, "x": 471, "y": 867, "tickets": false}}, {"platform": {"type": 1, "x": 127, "y": 925}}, {"platform": {"type": 21, "x": 170, "y": 1003, "tickets": false}}, {"platform": {"type": 0, "x": 471, "y": 1085, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 119, "y": 1174}}, {"platform": {"type": 1, "x": 375, "y": 1301}}, {"platform": {"type": 1, "x": 515, "y": 1489}}, {"platform": {"type": 1, "x": 127, "y": 1504, "tickets": false}, "booster": {"type": 500}}]}}, {"id": 703, "weight": 8, "tags": [6], "stringName": "703", "config": {"composables": [{"platform": {"type": 1, "x": 329, "y": 115}}, {"platform": {"type": 1, "x": 423, "y": 216}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 320, "y": 385, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 22, "y": 557}}, {"platform": {"type": 1, "x": 145, "y": 617}}, {"platform": {"type": 1, "x": 346, "y": 805}}, {"platform": {"type": 1, "x": 170, "y": 982}}, {"platform": {"type": 0, "x": 61, "y": 1012, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 184, "y": 1278}}, {"platform": {"type": 1, "x": 15, "y": 1351, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 360, "y": 1439}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}, {"id": 704, "weight": 8, "tags": [6], "stringName": "704", "config": {"composables": [{"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 135, "y": 299}}, {"platform": {"type": 1, "x": 305, "y": 329, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 1, "x": 458, "y": 544}}, {"platform": {"type": 1, "x": 151, "y": 739}}, {"platform": {"type": 0, "x": 305, "y": 814, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 94, "y": 856, "tickets": false}}, {"platform": {"type": 1, "x": 419, "y": 930}}, {"platform": {"type": 1, "x": 284, "y": 1117}}, {"platform": {"type": 1, "x": 398, "y": 1336}}, {"platform": {"type": 1, "x": 70, "y": 1373, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 515, "y": 1489}}]}}, {"id": 705, "weight": 8, "tags": [6], "stringName": "705", "config": {"composables": [{"platform": {"type": 1, "x": 434, "y": 149}}, {"platform": {"type": 1, "x": 79, "y": 189}}, {"platform": {"type": 1, "x": 288, "y": 400, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 145, "y": 441}}, {"platform": {"type": 1, "x": 402, "y": 441}}, {"platform": {"type": 1, "x": 41, "y": 632}}, {"platform": {"type": 21, "x": 491, "y": 647}}, {"platform": {"type": 1, "x": 166, "y": 805}}, {"platform": {"type": 1, "x": 377, "y": 835}}, {"platform": {"type": 0, "x": 166, "y": 949, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 0, "x": 259, "y": 1012}}, {"platform": {"type": 1, "x": 22, "y": 1027}}, {"platform": {"type": 1, "x": 474, "y": 1057}}, {"platform": {"type": 21, "x": 330, "y": 1137}}, {"platform": {"type": 1, "x": 244, "y": 1256}}, {"platform": {"type": 1, "x": 46, "y": 1403, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 445, "y": 1403}}, {"platform": {"type": 1, "x": 259, "y": 1526}}]}}, {"id": 706, "weight": 8, "tags": [6], "stringName": "706", "config": {"composables": [{"platform": {"type": 1, "x": 434, "y": 149}}, {"platform": {"type": 1, "x": 79, "y": 189}}, {"platform": {"type": 1, "x": 500, "y": 274}}, {"platform": {"type": 1, "x": 259, "y": 344, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 434, "y": 398}}, {"platform": {"type": 1, "x": 106, "y": 400}}, {"platform": {"type": 1, "x": 31, "y": 574}}, {"platform": {"type": 1, "x": 500, "y": 589}}, {"platform": {"type": 1, "x": 163, "y": 739}}, {"platform": {"type": 1, "x": 360, "y": 739}}, {"platform": {"type": 0, "x": 259, "y": 949, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 31, "y": 989}}, {"platform": {"type": 1, "x": 500, "y": 997}}, {"platform": {"type": 1, "x": 179, "y": 1177}}, {"platform": {"type": 1, "x": 398, "y": 1295}}, {"platform": {"type": 1, "x": 46, "y": 1403, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 491, "y": 1469}}, {"platform": {"type": 1, "x": 259, "y": 1526}}]}}, {"id": 707, "weight": 8, "tags": [6], "stringName": "707", "config": {"composables": [{"platform": {"type": 1, "x": 515, "y": 87}}, {"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 265, "y": 147}}, {"platform": {"type": 1, "x": 106, "y": 202}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 349, "y": 334, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 21, "x": 46, "y": 617}}, {"platform": {"type": 1, "x": 406, "y": 644}}, {"platform": {"type": 1, "x": 151, "y": 739}}, {"platform": {"type": 21, "x": 305, "y": 748}}, {"platform": {"type": 0, "x": 305, "y": 814, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 94, "y": 856}}, {"platform": {"type": 21, "x": 512, "y": 856}}, {"platform": {"type": 1, "x": 455, "y": 940}}, {"platform": {"type": 21, "x": 292, "y": 959}}, {"platform": {"type": 21, "x": 489, "y": 1102}}, {"platform": {"type": 1, "x": 248, "y": 1117}}, {"platform": {"type": 21, "x": 134, "y": 1256}}, {"platform": {"type": 1, "x": 398, "y": 1336, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 70, "y": 1373}}, {"platform": {"type": 1, "x": 515, "y": 1489}}]}}, {"id": 708, "weight": 8, "tags": [6], "stringName": "708", "config": {"composables": [{"platform": {"type": 1, "x": 362, "y": 112}}, {"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 135, "y": 299}}, {"platform": {"type": 1, "x": 305, "y": 329, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 21, "x": 449, "y": 380}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 21, "x": 401, "y": 496}}, {"platform": {"type": 0, "x": 525, "y": 603, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 295, "y": 627}}, {"platform": {"type": 1, "x": 37, "y": 669}}, {"platform": {"type": 21, "x": 305, "y": 749}}, {"platform": {"type": 1, "x": 151, "y": 809}}, {"platform": {"type": 21, "x": 463, "y": 821}}, {"platform": {"type": 1, "x": 305, "y": 895}}, {"platform": {"type": 0, "x": 77, "y": 1070, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 477, "y": 1112}}, {"platform": {"type": 1, "x": 238, "y": 1221}}, {"platform": {"type": 1, "x": 419, "y": 1343}}, {"platform": {"type": 1, "x": 70, "y": 1373, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 515, "y": 1489}}, {"platform": {"type": 1, "x": 216, "y": 1533}}]}}, {"id": 710, "weight": 6, "tags": [6], "stringName": "710", "config": {"composables": [{"platform": {"type": 1, "x": 49, "y": 108}}, {"platform": {"type": 1, "x": 500, "y": 135}}, {"platform": {"type": 1, "x": 234, "y": 180, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 406, "y": 295}}, {"platform": {"type": 1, "x": 463, "y": 425}}, {"platform": {"type": 1, "x": 78, "y": 447}}, {"platform": {"type": 1, "x": 443, "y": 632}}, {"platform": {"type": 1, "x": 142, "y": 650}}, {"platform": {"type": 0, "x": 276, "y": 748, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 463, "y": 918}}, {"platform": {"type": 1, "x": 265, "y": 1072}}, {"platform": {"type": 1, "x": 406, "y": 1247}}, {"platform": {"type": 1, "x": 70, "y": 1277, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 500, "y": 1431}}, {"platform": {"type": 1, "x": 184, "y": 1474}}]}}, {"id": 711, "weight": 6, "tags": [6], "stringName": "711", "config": {"composables": [{"platform": {"type": 1, "x": 163, "y": 98}}, {"platform": {"type": 1, "x": 500, "y": 98}}, {"platform": {"type": 1, "x": 329, "y": 155}}, {"platform": {"type": 1, "x": 22, "y": 233}}, {"platform": {"type": 1, "x": 295, "y": 355, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 434, "y": 408}}, {"platform": {"type": 1, "x": 136, "y": 415}}, {"platform": {"type": 1, "x": 49, "y": 549}}, {"platform": {"type": 1, "x": 500, "y": 589}}, {"platform": {"type": 21, "x": 49, "y": 685}}, {"platform": {"type": 1, "x": 320, "y": 738, "tickets": false}}, {"platform": {"type": 0, "x": 440, "y": 738, "tickets": false}, "mob": {"type": 1002}}, {"platform": {"type": 1, "x": 443, "y": 738, "tickets": false}}, {"platform": {"type": 21, "x": 136, "y": 788}}, {"platform": {"type": 1, "x": 287, "y": 884, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 21, "x": 287, "y": 1124}}, {"platform": {"type": 1, "x": 287, "y": 1192}}, {"platform": {"type": 1, "x": 434, "y": 1314}}, {"platform": {"type": 1, "x": 106, "y": 1406}}, {"platform": {"type": 1, "x": 466, "y": 1434}}, {"platform": {"type": 1, "x": 193, "y": 1494}}, {"platform": {"type": 1, "x": 352, "y": 1494}}]}}, {"id": 712, "weight": 7, "tags": [6], "stringName": "712", "config": {"composables": [{"platform": {"type": 1, "x": 41, "y": 85}}, {"platform": {"type": 1, "x": 320, "y": 121}}, {"platform": {"type": 1, "x": 488, "y": 225}}, {"platform": {"type": 1, "x": 130, "y": 284}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 434, "y": 389, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 244, "y": 490}}, {"platform": {"type": 1, "x": 98, "y": 659}}, {"platform": {"type": 21, "x": 423, "y": 829}}, {"platform": {"type": 1, "x": 212, "y": 844}}, {"platform": {"type": 1, "x": 409, "y": 971}}, {"platform": {"type": 0, "x": 438, "y": 1028, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 155, "y": 1073}}, {"platform": {"type": 1, "x": 70, "y": 1159}}, {"platform": {"type": 1, "x": 352, "y": 1205}}, {"platform": {"type": 1, "x": 127, "y": 1356}}, {"platform": {"type": 1, "x": 423, "y": 1413, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 184, "y": 1521}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}, {"id": 801, "weight": 8, "tags": [12], "stringName": "801", "config": {"composables": [{"platform": {"type": 1, "x": 426, "y": 134}}, {"platform": {"type": 1, "x": 287, "y": 192}}, {"platform": {"type": 1, "x": 69, "y": 222, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 21, "x": 500, "y": 309}}, {"platform": {"type": 10, "x": 263, "y": 441}}, {"platform": {"type": 1, "x": 426, "y": 644, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 263, "y": 851, "tickets": false, "tonToken": true}}, {"platform": {"type": 11, "x": 39, "y": 1028, "movementRange": [150, 150], "movementDuration": [2800, 3000]}}, {"platform": {"type": 1, "x": 390, "y": 1129}}, {"platform": {"type": 21, "x": 483, "y": 1204}}, {"platform": {"type": 1, "x": 101, "y": 1209}}, {"platform": {"type": 10, "x": 276, "y": 1347}}, {"platform": {"type": 1, "x": 215, "y": 1488}}]}}, {"id": 802, "weight": 8, "tags": [12], "stringName": "802", "config": {"composables": [{"platform": {"type": 1, "x": 60, "y": 93}}, {"platform": {"type": 1, "x": 263, "y": 294, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 433, "y": 493}}, {"platform": {"type": 1, "x": 69, "y": 608, "tickets": false}, "booster": {"type": 502}}, {"platform": {"type": 1, "x": 366, "y": 694}}, {"platform": {"type": 21, "x": 475, "y": 748}}, {"platform": {"type": 10, "x": 206, "y": 871}}, {"platform": {"type": 1, "x": 84, "y": 1088}}, {"platform": {"type": 1, "x": 443, "y": 1352}}, {"platform": {"type": 21, "x": 109, "y": 1367}}, {"platform": {"type": 1, "x": 206, "y": 1416}}, {"platform": {"type": 1, "x": 480, "y": 1493, "tickets": false}, "booster": {"type": 503}}]}}, {"id": 807, "weight": 8, "tags": [12], "stringName": "807", "config": {"composables": [{"platform": {"type": 1, "x": 499, "y": 93}}, {"platform": {"type": 1, "x": 206, "y": 108}}, {"platform": {"type": 1, "x": 56, "y": 138}}, {"platform": {"type": 1, "x": 326, "y": 162}}, {"platform": {"type": 1, "x": 239, "y": 251}}, {"platform": {"type": 21, "x": 62, "y": 435}}, {"platform": {"type": 1, "x": 385, "y": 538, "tickets": false, "tonToken": true}}, {"platform": {"type": 21, "x": 492, "y": 658}}, {"platform": {"type": 1, "x": 59, "y": 849}}, {"platform": {"type": 21, "x": 271, "y": 879}}, {"platform": {"type": 1, "x": 84, "y": 1143}}, {"platform": {"type": 1, "x": 404, "y": 1143}}, {"platform": {"type": 21, "x": 41, "y": 1278}}, {"platform": {"type": 1, "x": 499, "y": 1336}}, {"platform": {"type": 10, "x": 415, "y": 1412}}, {"platform": {"type": 21, "x": 499, "y": 1496}}, {"platform": {"type": 1, "x": 5, "y": 1526}}]}}, {"id": 808, "weight": 8, "tags": [12], "stringName": "808", "config": {"composables": [{"platform": {"type": 1, "x": 500, "y": 99}}, {"platform": {"type": 1, "x": 55, "y": 101, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 209, "y": 165}}, {"platform": {"type": 20, "x": 315, "y": 236}}, {"platform": {"type": 20, "x": 356, "y": 423}}, {"platform": {"type": 20, "x": 55, "y": 534}}, {"platform": {"type": 20, "x": 426, "y": 597}}, {"platform": {"type": 20, "x": 131, "y": 717}}, {"platform": {"type": 1, "x": 277, "y": 820, "tickets": false, "tonToken": true}}, {"platform": {"type": 20, "x": 483, "y": 886}}, {"platform": {"type": 20, "x": 320, "y": 1021}}, {"platform": {"type": 10, "x": 272, "y": 1268}}, {"platform": {"type": 20, "x": 95, "y": 1395}}, {"platform": {"type": 20, "x": 397, "y": 1408}}, {"platform": {"type": 1, "x": 38, "y": 1438}}, {"platform": {"type": 20, "x": 500, "y": 1441}}, {"platform": {"type": 1, "x": 283, "y": 1521}}]}}, {"id": 809, "weight": 8, "tags": [12], "stringName": "809", "config": {"composables": [{"platform": {"type": 1, "x": 30, "y": 99}}, {"platform": {"type": 1, "x": 426, "y": 114}}, {"platform": {"type": 1, "x": 206, "y": 129}}, {"platform": {"type": 10, "x": 38, "y": 265}}, {"platform": {"type": 10, "x": 483, "y": 378}}, {"platform": {"type": 10, "x": 67, "y": 534}}, {"platform": {"type": 20, "x": 426, "y": 597}}, {"platform": {"type": 20, "x": 131, "y": 717}}, {"platform": {"type": 20, "x": 511, "y": 746}}, {"platform": {"type": 1, "x": 277, "y": 820, "tickets": false, "tonToken": true}}, {"platform": {"type": 20, "x": 50, "y": 873}}, {"platform": {"type": 20, "x": 454, "y": 930}}, {"platform": {"type": 20, "x": 251, "y": 1006}}, {"platform": {"type": 20, "x": 38, "y": 1113}}, {"platform": {"type": 20, "x": 413, "y": 1113}}, {"platform": {"type": 20, "x": 219, "y": 1253}}, {"platform": {"type": 20, "x": 397, "y": 1365}}, {"platform": {"type": 20, "x": 95, "y": 1395}}, {"platform": {"type": 20, "x": 501, "y": 1491}}]}}, {"id": 811, "weight": 7, "tags": [12], "stringName": "811", "config": {"composables": [{"platform": {"type": 1, "x": 30, "y": 99}}, {"platform": {"type": 1, "x": 426, "y": 114}}, {"platform": {"type": 1, "x": 206, "y": 134}}, {"platform": {"type": 20, "x": 87, "y": 271}}, {"platform": {"type": 1, "x": 283, "y": 377, "tickets": false, "tonToken": true}}, {"platform": {"type": 20, "x": 454, "y": 567}}, {"platform": {"type": 20, "x": 87, "y": 608}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 20, "x": 454, "y": 930}}, {"platform": {"type": 20, "x": 50, "y": 996}}, {"platform": {"type": 20, "x": 444, "y": 1094}}, {"platform": {"type": 1, "x": 263, "y": 1129}}, {"platform": {"type": 20, "x": 95, "y": 1159}}, {"platform": {"type": 1, "x": 38, "y": 1289}}, {"platform": {"type": 1, "x": 470, "y": 1289}}, {"platform": {"type": 1, "x": 426, "y": 1432}}, {"platform": {"type": 1, "x": 38, "y": 1496}}, {"platform": {"type": 1, "x": 263, "y": 1511}}]}}, {"id": 812, "weight": 6, "tags": [12], "stringName": "812", "config": {"composables": [{"platform": {"type": 1, "x": 41, "y": 85}}, {"platform": {"type": 1, "x": 263, "y": 104}}, {"platform": {"type": 1, "x": 491, "y": 192}}, {"platform": {"type": 1, "x": 81, "y": 271}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 434, "y": 389}}, {"platform": {"type": 1, "x": 295, "y": 516}}, {"platform": {"type": 1, "x": 98, "y": 659, "tickets": false, "tonToken": true}}, {"platform": {"type": 0, "x": 442, "y": 674, "tickets": false}}, {"platform": {"type": 1, "x": 212, "y": 844}}, {"platform": {"type": 21, "x": 445, "y": 866}}, {"platform": {"type": 1, "x": 41, "y": 998}}, {"platform": {"type": 21, "x": 155, "y": 1073}}, {"platform": {"type": 1, "x": 269, "y": 1193}}, {"platform": {"type": 1, "x": 41, "y": 1333}}, {"platform": {"type": 1, "x": 445, "y": 1363}}, {"platform": {"type": 1, "x": 184, "y": 1521}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}, {"id": 813, "weight": 6, "tags": [12], "stringName": "813", "config": {"composables": [{"platform": {"type": 1, "x": 41, "y": 104}}, {"platform": {"type": 1, "x": 331, "y": 104}}, {"platform": {"type": 1, "x": 502, "y": 177}}, {"platform": {"type": 21, "x": 366, "y": 241}}, {"platform": {"type": 1, "x": 184, "y": 271}}, {"platform": {"type": 1, "x": 81, "y": 457, "tickets": false, "tonToken": true}}, {"platform": {"type": 1, "x": 217, "y": 656}}, {"platform": {"type": 1, "x": 469, "y": 761}}, {"platform": {"type": 21, "x": 445, "y": 866}}, {"platform": {"type": 1, "x": 331, "y": 968}}, {"platform": {"type": 1, "x": 41, "y": 998}}, {"platform": {"type": 21, "x": 195, "y": 1085}}, {"platform": {"type": 0, "x": 100, "y": 1115, "tickets": false}}, {"platform": {"type": 1, "x": 309, "y": 1208}}, {"platform": {"type": 1, "x": 469, "y": 1363}}, {"platform": {"type": 1, "x": 184, "y": 1521}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}, {"id": 902, "weight": 10, "tags": [13], "stringName": "902", "config": {"composables": [{"platform": {"type": 1, "x": 43, "y": 150, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 245, "y": 184, "tickets": false}}, {"platform": {"type": 1, "x": 470, "y": 240, "tickets": false}}, {"platform": {"type": 20, "x": 149, "y": 307, "tickets": false}}, {"platform": {"type": 1, "x": 312, "y": 389, "tickets": false}}, {"platform": {"type": 20, "x": 33, "y": 434, "tickets": false}}, {"platform": {"type": 1, "x": 426, "y": 538, "tickets": false}}, {"platform": {"type": 20, "x": 198, "y": 651, "tickets": false}}, {"platform": {"type": 1, "x": 512, "y": 748, "tickets": false}}, {"platform": {"type": 20, "x": 426, "y": 853, "tickets": false}}, {"platform": {"type": 1, "x": 59, "y": 926}}, {"platform": {"type": 10, "x": 223, "y": 1130}}, {"platform": {"type": 20, "x": 423, "y": 1216}}, {"platform": {"type": 1, "x": 483, "y": 1314}}, {"platform": {"type": 1, "x": 18, "y": 1329}}, {"platform": {"type": 20, "x": 149, "y": 1405}}, {"platform": {"type": 1, "x": 26, "y": 1501}}]}}, {"id": 903, "weight": 10, "tags": [13], "stringName": "903", "config": {"composables": [{"platform": {"type": 1, "x": 19, "y": 133, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 173, "y": 175}}, {"platform": {"type": 1, "x": 483, "y": 190}}, {"platform": {"type": 1, "x": 273, "y": 281}}, {"platform": {"type": 10, "x": 263, "y": 441}}, {"platform": {"type": 11, "x": 500, "y": 633, "movementRange": [450, 450], "movementDuration": [3000, 3000]}}, {"platform": {"type": 1, "x": 263, "y": 737}}, {"platform": {"type": 1, "x": 263, "y": 1025}}, {"platform": {"type": 11, "x": 39, "y": 1202, "movementRange": [-450, -450], "movementDuration": [3000, 3000]}}, {"platform": {"type": 10, "x": 263, "y": 1327}}, {"platform": {"type": 1, "x": 60, "y": 1477}}, {"platform": {"type": 1, "x": 469, "y": 1507}}]}}, {"id": 904, "weight": 10, "tags": [13], "stringName": "904", "config": {"composables": [{"platform": {"type": 1, "x": 368, "y": 124}}, {"platform": {"type": 1, "x": 19, "y": 133, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 173, "y": 175}}, {"platform": {"type": 1, "x": 483, "y": 190}}, {"platform": {"type": 1, "x": 263, "y": 509}}, {"platform": {"type": 1, "x": 320, "y": 592}}, {"platform": {"type": 10, "x": 500, "y": 697}}, {"platform": {"type": 1, "x": 263, "y": 892}}, {"platform": {"type": 10, "x": 195, "y": 944}}, {"platform": {"type": 11, "x": 42, "y": 1004, "movementRange": [150, 150], "movementDuration": [2800, 3000]}}, {"platform": {"type": 1, "x": 500, "y": 1085}}, {"platform": {"type": 1, "x": 263, "y": 1206}}, {"platform": {"type": 1, "x": 412, "y": 1340}}, {"platform": {"type": 1, "x": 500, "y": 1497}}, {"platform": {"type": 1, "x": 156, "y": 1512}}]}}, {"id": 905, "weight": 10, "tags": [13], "stringName": "905", "config": {"composables": [{"platform": {"type": 1, "x": 43, "y": 150, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 245, "y": 184, "tickets": false}}, {"platform": {"type": 1, "x": 470, "y": 240, "tickets": false}}, {"platform": {"type": 1, "x": 272, "y": 386, "tickets": false}}, {"platform": {"type": 1, "x": 55, "y": 473, "tickets": false}}, {"platform": {"type": 1, "x": 483, "y": 549, "tickets": false}}, {"platform": {"type": 20, "x": 272, "y": 582, "tickets": false}}, {"platform": {"type": 20, "x": 131, "y": 661, "tickets": false}}, {"platform": {"type": 20, "x": 426, "y": 676, "tickets": false}}, {"platform": {"type": 20, "x": 55, "y": 781, "tickets": false}}, {"platform": {"type": 20, "x": 500, "y": 790, "tickets": false}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 20, "x": 55, "y": 886}}, {"platform": {"type": 20, "x": 483, "y": 886}}, {"platform": {"type": 20, "x": 386, "y": 979}}, {"platform": {"type": 20, "x": 158, "y": 983}}, {"platform": {"type": 20, "x": 277, "y": 1039}}, {"platform": {"type": 1, "x": 470, "y": 1069, "tickets": false}}, {"platform": {"type": 10, "x": 272, "y": 1268}}, {"platform": {"type": 1, "x": 38, "y": 1438, "tickets": false}}, {"platform": {"type": 1, "x": 283, "y": 1521, "tickets": false}}]}}, {"id": 907, "weight": 10, "tags": [13], "stringName": "907", "config": {"composables": [{"platform": {"type": 1, "x": 277, "y": 103}}, {"platform": {"type": 1, "x": 19, "y": 133}}, {"platform": {"type": 1, "x": 186, "y": 154}}, {"platform": {"type": 10, "x": 116, "y": 250}}, {"platform": {"type": 10, "x": 372, "y": 407}}, {"platform": {"type": 10, "x": 42, "y": 560}}, {"platform": {"type": 10, "x": 509, "y": 740}}, {"platform": {"type": 1, "x": 263, "y": 892}}, {"platform": {"type": 10, "x": 486, "y": 990}}, {"platform": {"type": 10, "x": 176, "y": 1130}}, {"platform": {"type": 10, "x": 386, "y": 1266}}, {"platform": {"type": 10, "x": 56, "y": 1377}}, {"platform": {"type": 1, "x": 33, "y": 1467}}, {"platform": {"type": 1, "x": 500, "y": 1497}}, {"platform": {"type": 1, "x": 156, "y": 1512}}]}}, {"id": 908, "weight": 10, "tags": [13], "stringName": "908", "config": {"composables": [{"platform": {"type": 1, "x": 17, "y": 89, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 506, "y": 89}}, {"platform": {"type": 1, "x": 223, "y": 125}}, {"platform": {"type": 20, "x": 280, "y": 231}}, {"platform": {"type": 20, "x": 245, "y": 328}}, {"platform": {"type": 1, "x": 131, "y": 510}}, {"platform": {"type": 1, "x": 59, "y": 733}}, {"platform": {"type": 20, "x": 443, "y": 829}}, {"platform": {"type": 20, "x": 512, "y": 965}}, {"platform": {"type": 20, "x": 43, "y": 981}}, {"platform": {"type": 10, "x": 223, "y": 1106}}, {"platform": {"type": 20, "x": 426, "y": 1160}}, {"platform": {"type": 20, "x": 90, "y": 1284}}, {"platform": {"type": 1, "x": 18, "y": 1329}}, {"platform": {"type": 20, "x": 443, "y": 1389}}, {"platform": {"type": 1, "x": 236, "y": 1501}}, {"platform": {"type": 20, "x": 38, "y": 1523}}, {"platform": {"type": 1, "x": 500, "y": 1525}}]}}, {"id": 1001, "weight": 15, "tags": [14], "stringName": "1001", "config": {"composables": [{"platform": {"type": 1, "x": 500, "y": 118, "tickets": false}, "booster": {"type": 503}}, {"platform": {"type": 1, "x": 245, "y": 133}}, {"platform": {"type": 1, "x": 60, "y": 232}}, {"platform": {"type": 10, "x": 272, "y": 407}}, {"platform": {"type": 1, "x": 287, "y": 572}}, {"platform": {"type": 10, "x": 39, "y": 659}}, {"platform": {"type": 1, "x": 39, "y": 787}}, {"platform": {"type": 10, "x": 272, "y": 867}}, {"platform": {"type": 1, "x": 426, "y": 1072}}, {"platform": {"type": 10, "x": 60, "y": 1266}}, {"platform": {"type": 1, "x": 312, "y": 1401}}, {"platform": {"type": 1, "x": 158, "y": 1448}}, {"platform": {"type": 1, "x": 483, "y": 1502}}]}}, {"id": 1005, "weight": 10, "tags": [14], "stringName": "1005", "config": {"composables": [{"platform": {"type": 1, "x": 61, "y": 89}}, {"platform": {"type": 1, "x": 253, "y": 160}}, {"platform": {"type": 1, "x": 497, "y": 217}}, {"platform": {"type": 1, "x": 39, "y": 313}}, {"platform": {"type": 1, "x": 326, "y": 363}}, {"platform": {"type": 1, "x": 160, "y": 451}}, {"platform": {"type": 1, "x": 440, "y": 530}}, {"platform": {"type": 1, "x": 10, "y": 613}}, {"platform": {"type": 10, "x": 39, "y": 691}}, {"platform": {"type": 1, "x": 237, "y": 820}}, {"platform": {"type": 11, "x": 39, "y": 970, "tickets": false, "movementRange": [240, 240], "movementDuration": [2800, 3000]}}, {"platform": {"type": 11, "x": 326, "y": 970, "tickets": false, "movementRange": [240, 240], "movementDuration": [2800, 3000]}}, {"platform": {"type": 11, "x": 188, "y": 1208, "tickets": false, "movementRange": [-240, -240], "movementDuration": [2800, 3000]}}, {"platform": {"type": 11, "x": 483, "y": 1208, "tickets": false, "movementRange": [-240, -240], "movementDuration": [2800, 3000]}}, {"platform": {"type": 1, "x": 294, "y": 1417}}, {"platform": {"type": 1, "x": 408, "y": 1452}}, {"platform": {"type": 1, "x": 81, "y": 1477}}, {"platform": {"type": 1, "x": 505, "y": 1492}}, {"platform": {"type": 1, "x": 14, "y": 1522}}]}}, {"id": 1008, "weight": 15, "tags": [14], "stringName": "1008", "config": {"composables": [{"platform": {"type": 1, "x": 61, "y": 89}}, {"platform": {"type": 1, "x": 497, "y": 104}}, {"platform": {"type": 1, "x": 403, "y": 145}}, {"platform": {"type": 1, "x": 253, "y": 160}}, {"platform": {"type": 21, "x": 81, "y": 268}}, {"platform": {"type": 1, "x": 39, "y": 313}}, {"platform": {"type": 10, "x": 485, "y": 422}}, {"platform": {"type": 1, "x": 505, "y": 481}}, {"platform": {"type": 1, "x": 96, "y": 545}}, {"platform": {"type": 1, "x": 10, "y": 613}}, {"platform": {"type": 10, "x": 39, "y": 719}}, {"platform": {"type": 1, "x": 237, "y": 820}}, {"platform": {"type": 21, "x": 294, "y": 871}}, {"platform": {"type": 10, "x": 448, "y": 967}}, {"platform": {"type": 1, "x": 237, "y": 1088}}, {"platform": {"type": 21, "x": 371, "y": 1088}}, {"platform": {"type": 10, "x": 39, "y": 1215}}, {"platform": {"type": 1, "x": 517, "y": 1328}}, {"platform": {"type": 1, "x": 408, "y": 1452}}, {"platform": {"type": 1, "x": 24, "y": 1472}}, {"platform": {"type": 1, "x": 237, "y": 1477}}, {"platform": {"type": 1, "x": 505, "y": 1492}}, {"platform": {"type": 1, "x": 118, "y": 1516}}, {"platform": {"type": 1, "x": 289, "y": 1522}}]}}, {"id": 1104, "weight": 10, "tags": [18], "stringName": "1104", "config": {"composables": [{"platform": {"type": 21, "x": 206, "y": 157}}, {"platform": {"type": 1, "x": 389, "y": 166}}, {"platform": {"type": 1, "x": 62, "y": 226}}, {"platform": {"type": 1, "x": 503, "y": 302}}, {"platform": {"type": 1, "x": 426, "y": 454}}, {"platform": {"type": 1, "x": 326, "y": 639}}, {"platform": {"type": 21, "x": 263, "y": 750}}, {"platform": {"type": 1, "x": 206, "y": 872}}, {"platform": {"type": 21, "x": 236, "y": 1005}}, {"platform": {"type": 1, "x": 85, "y": 1020}}, {"platform": {"type": 1, "x": 341, "y": 1131}}, {"platform": {"type": 1, "x": 503, "y": 1249}}, {"platform": {"type": 21, "x": 483, "y": 1349}}, {"platform": {"type": 1, "x": 48, "y": 1449}}, {"platform": {"type": 1, "x": 483, "y": 1449}}, {"platform": {"type": 1, "x": 284, "y": 1530}}]}}, {"id": 1105, "weight": 10, "tags": [18], "stringName": "1105", "config": {"composables": [{"platform": {"type": 1, "x": 173, "y": 88}}, {"platform": {"type": 1, "x": 343, "y": 103}}, {"platform": {"type": 21, "x": 478, "y": 118}}, {"platform": {"type": 1, "x": 478, "y": 315}}, {"platform": {"type": 1, "x": 272, "y": 392}}, {"platform": {"type": 11, "x": 39, "y": 452, "movementRange": [-250, -250], "movementDuration": [2400, 2400]}}, {"platform": {"type": 11, "x": 272, "y": 452, "movementRange": [150, 150], "movementDuration": [2200, 2200]}}, {"platform": {"type": 21, "x": 39, "y": 549}}, {"platform": {"type": 1, "x": 39, "y": 637}}, {"platform": {"type": 1, "x": 39, "y": 840}}, {"platform": {"type": 21, "x": 129, "y": 961}}, {"platform": {"type": 1, "x": 272, "y": 1033}}, {"platform": {"type": 11, "x": 500, "y": 1033, "movementRange": [-350, -350], "movementDuration": [3000, 3000]}}, {"platform": {"type": 1, "x": 173, "y": 1154}}, {"platform": {"type": 1, "x": 386, "y": 1154}}, {"platform": {"type": 1, "x": 39, "y": 1217}}, {"platform": {"type": 10, "x": 272, "y": 1332}}, {"platform": {"type": 1, "x": 59, "y": 1432}}, {"platform": {"type": 1, "x": 483, "y": 1462}}]}}, {"id": 1107, "weight": 10, "tags": [18], "stringName": "1107", "config": {"composables": [{"platform": {"type": 1, "x": 65, "y": 88}}, {"platform": {"type": 1, "x": 434, "y": 88}}, {"platform": {"type": 1, "x": 255, "y": 118}}, {"platform": {"type": 10, "x": 288, "y": 197}}, {"platform": {"type": 10, "x": 515, "y": 325}}, {"platform": {"type": 10, "x": 331, "y": 470}}, {"platform": {"type": 10, "x": 179, "y": 599}}, {"platform": {"type": 10, "x": 27, "y": 720}}, {"platform": {"type": 10, "x": 158, "y": 865}}, {"platform": {"type": 10, "x": 320, "y": 967}}, {"platform": {"type": 10, "x": 479, "y": 1080}}, {"platform": {"type": 10, "x": 313, "y": 1197}}, {"platform": {"type": 10, "x": 173, "y": 1297}}, {"platform": {"type": 10, "x": 39, "y": 1397}}, {"platform": {"type": 1, "x": 287, "y": 1457}}]}}, {"id": 1108, "weight": 10, "tags": [18], "stringName": "1108", "config": {"composables": [{"platform": {"type": 1, "x": 65, "y": 88}}, {"platform": {"type": 1, "x": 434, "y": 88}}, {"platform": {"type": 1, "x": 255, "y": 118}}, {"platform": {"type": 10, "x": 495, "y": 247}}, {"platform": {"type": 10, "x": 411, "y": 342}}, {"platform": {"type": 10, "x": 324, "y": 444}}, {"platform": {"type": 10, "x": 259, "y": 557}}, {"platform": {"type": 1, "x": 491, "y": 614}}, {"platform": {"type": 11, "x": 345, "y": 675, "movementRange": [390, 390], "movementDuration": [2800, 2800]}}, {"platform": {"type": 11, "x": 198, "y": 1065, "movementRange": [-390, -390], "movementDuration": [2800, 2800]}}, {"platform": {"type": 1, "x": 39, "y": 1095}}, {"platform": {"type": 10, "x": 174, "y": 1197}}, {"platform": {"type": 10, "x": 96, "y": 1297}}, {"platform": {"type": 10, "x": 39, "y": 1397}}, {"platform": {"type": 1, "x": 287, "y": 1457}}, {"platform": {"type": 1, "x": 495, "y": 1492}}]}}, {"id": 1109, "weight": 10, "tags": [18], "stringName": "1109", "config": {"composables": [{"platform": {"type": 1, "x": 65, "y": 88}}, {"platform": {"type": 1, "x": 434, "y": 88}}, {"platform": {"type": 1, "x": 255, "y": 118}}, {"platform": {"type": 21, "x": 491, "y": 133}}, {"platform": {"type": 11, "x": 15, "y": 237, "movementRange": [390, 390], "movementDuration": [2500, 2500]}}, {"platform": {"type": 11, "x": 140, "y": 237, "movementRange": [390, 390], "movementDuration": [2450, 2450]}}, {"platform": {"type": 11, "x": 266, "y": 237, "movementRange": [390, 390], "movementDuration": [2400, 2400]}}, {"platform": {"type": 11, "x": 390, "y": 237, "movementRange": [390, 390], "movementDuration": [2350, 2350]}}, {"platform": {"type": 11, "x": 510, "y": 237, "movementRange": [390, 390], "movementDuration": [2300, 2300]}}, {"platform": {"type": 1, "x": 359, "y": 796}}, {"platform": {"type": 1, "x": 173, "y": 826}}, {"platform": {"type": 21, "x": 510, "y": 826}}, {"platform": {"type": 21, "x": 15, "y": 841}}, {"platform": {"type": 11, "x": 510, "y": 928, "movementRange": [390, 390], "movementDuration": [2300, 2300]}}, {"platform": {"type": 11, "x": 390, "y": 928, "movementRange": [390, 390], "movementDuration": [2350, 2350]}}, {"platform": {"type": 11, "x": 266, "y": 928, "movementRange": [390, 390], "movementDuration": [2400, 2400]}}, {"platform": {"type": 11, "x": 140, "y": 928, "movementRange": [390, 390], "movementDuration": [2450, 2450]}}, {"platform": {"type": 11, "x": 15, "y": 928, "movementRange": [390, 390], "movementDuration": [2500, 2500]}}, {"platform": {"type": 21, "x": 416, "y": 1427}}, {"platform": {"type": 21, "x": 129, "y": 1457}}, {"platform": {"type": 1, "x": 287, "y": 1457}}, {"platform": {"type": 1, "x": 495, "y": 1492}}, {"platform": {"type": 1, "x": 15, "y": 1507}}]}}, {"id": 1110, "weight": 10, "tags": [18], "stringName": "1110", "config": {"composables": [{"platform": {"type": 1, "x": 81, "y": 91, "tickets": false}}, {"platform": {"type": 20, "x": 485, "y": 106, "tickets": false}}, {"platform": {"type": 20, "x": 327, "y": 247, "tickets": false}}, {"platform": {"type": 20, "x": 167, "y": 443, "tickets": false}}, {"platform": {"type": 20, "x": 371, "y": 518, "tickets": false}}, {"platform": {"type": 20, "x": 70, "y": 542, "tickets": false}}, {"platform": {"type": 20, "x": 485, "y": 602, "tickets": false}}, {"platform": {"type": 10, "x": 485, "y": 757, "tickets": false}}, {"platform": {"type": 10, "x": 24, "y": 982, "tickets": false}}, {"platform": {"type": 20, "x": 252, "y": 1109, "tickets": false}}, {"platform": {"type": 20, "x": 498, "y": 1163, "tickets": false}}, {"platform": {"type": 20, "x": 81, "y": 1209, "tickets": false}}, {"platform": {"type": 20, "x": 485, "y": 1299, "tickets": false}}, {"platform": {"type": 20, "x": 384, "y": 1435, "tickets": false}}, {"platform": {"type": 20, "x": 125, "y": 1465, "tickets": false}}, {"platform": {"type": 20, "x": 24, "y": 1523, "tickets": false}}]}}, {"id": 1201, "weight": 9, "tags": [15], "stringName": "1201", "config": {"composables": [{"platform": {"type": 1, "x": 362, "y": 112}}, {"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 10, "x": 375, "y": 339}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 21, "x": 401, "y": 496}}, {"platform": {"type": 0, "x": 525, "y": 573, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 295, "y": 627}}, {"platform": {"type": 1, "x": 37, "y": 669}}, {"platform": {"type": 21, "x": 305, "y": 749}}, {"platform": {"type": 1, "x": 151, "y": 809}}, {"platform": {"type": 21, "x": 463, "y": 821}}, {"platform": {"type": 1, "x": 305, "y": 895}}, {"platform": {"type": 20, "x": 138, "y": 985}}, {"platform": {"type": 20, "x": 77, "y": 1141}}, {"platform": {"type": 1, "x": 252, "y": 1216}}, {"platform": {"type": 20, "x": 24, "y": 1313}}, {"platform": {"type": 21, "x": 305, "y": 1321}}, {"platform": {"type": 1, "x": 272, "y": 1383, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 20, "x": 71, "y": 1474}}, {"platform": {"type": 1, "x": 442, "y": 1519}}]}}, {"id": 1203, "weight": 9, "tags": [15], "stringName": "1203", "config": {"composables": [{"platform": {"type": 1, "x": 494, "y": 85}}, {"platform": {"type": 1, "x": 100, "y": 100}}, {"platform": {"type": 1, "x": 245, "y": 130}}, {"platform": {"type": 1, "x": 28, "y": 306}}, {"platform": {"type": 10, "x": 338, "y": 415}}, {"platform": {"type": 10, "x": 58, "y": 520}}, {"platform": {"type": 1, "x": 340, "y": 694}}, {"platform": {"type": 0, "x": 117, "y": 698, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 245, "y": 885}}, {"platform": {"type": 21, "x": 58, "y": 915}}, {"platform": {"type": 10, "x": 341, "y": 1039}}, {"platform": {"type": 10, "x": 131, "y": 1113}}, {"platform": {"type": 0, "x": 522, "y": 1163, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 17, "y": 1340}}, {"platform": {"type": 1, "x": 509, "y": 1388}}, {"platform": {"type": 1, "x": 354, "y": 1496}}, {"platform": {"type": 1, "x": 142, "y": 1516}}]}}, {"id": 1204, "weight": 9, "tags": [15], "stringName": "1204", "config": {"composables": [{"platform": {"type": 1, "x": 119, "y": 156}}, {"platform": {"type": 1, "x": 254, "y": 171, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 27, "y": 277}}, {"platform": {"type": 20, "x": 27, "y": 328}}, {"platform": {"type": 1, "x": 43, "y": 468}}, {"platform": {"type": 20, "x": 359, "y": 468}}, {"platform": {"type": 1, "x": 455, "y": 579}}, {"platform": {"type": 1, "x": 116, "y": 664}}, {"platform": {"type": 20, "x": 341, "y": 664}}, {"platform": {"type": 0, "x": 509, "y": 704, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 20, "x": 302, "y": 854}}, {"platform": {"type": 1, "x": 43, "y": 871}}, {"platform": {"type": 20, "x": 412, "y": 1020}}, {"platform": {"type": 1, "x": 59, "y": 1063, "tickets": false}}, {"platform": {"type": 0, "x": 165, "y": 1063, "tickets": false}, "mob": {"type": 1002}}, {"platform": {"type": 1, "x": 176, "y": 1063, "tickets": false}}, {"platform": {"type": 20, "x": 485, "y": 1210}}, {"platform": {"type": 20, "x": 483, "y": 1384}}, {"platform": {"type": 1, "x": 194, "y": 1406, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 20, "x": 332, "y": 1486}}, {"platform": {"type": 1, "x": 27, "y": 1498}}, {"platform": {"type": 1, "x": 504, "y": 1516}}]}}, {"id": 1205, "weight": 9, "tags": [15], "stringName": "1205", "config": {"composables": [{"platform": {"type": 1, "x": 38, "y": 101, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 251, "y": 186}}, {"platform": {"type": 20, "x": 485, "y": 186}}, {"platform": {"type": 20, "x": 344, "y": 307}}, {"platform": {"type": 1, "x": 485, "y": 382}}, {"platform": {"type": 20, "x": 163, "y": 402}}, {"platform": {"type": 20, "x": 62, "y": 534}}, {"platform": {"type": 1, "x": 391, "y": 540}}, {"platform": {"type": 0, "x": 310, "y": 611, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 436, "y": 698}}, {"platform": {"type": 20, "x": 38, "y": 713}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 0, "x": 149, "y": 955, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 20, "x": 302, "y": 1035}}, {"platform": {"type": 1, "x": 458, "y": 1063}}, {"platform": {"type": 20, "x": 19, "y": 1143}}, {"platform": {"type": 20, "x": 448, "y": 1195}}, {"platform": {"type": 10, "x": 287, "y": 1327}}, {"platform": {"type": 1, "x": 505, "y": 1414, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 1, "x": 302, "y": 1477}}, {"platform": {"type": 1, "x": 62, "y": 1513}}]}}, {"id": 1208, "weight": 9, "tags": [15], "stringName": "1208", "config": {"composables": [{"platform": {"type": 1, "x": 31, "y": 95}}, {"platform": {"type": 1, "x": 490, "y": 106}}, {"platform": {"type": 1, "x": 339, "y": 121}}, {"platform": {"type": 1, "x": 240, "y": 155}}, {"platform": {"type": 0, "x": 509, "y": 276, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 252, "y": 332}}, {"platform": {"type": 10, "x": 107, "y": 458}}, {"platform": {"type": 10, "x": 320, "y": 642}}, {"platform": {"type": 10, "x": 50, "y": 757}}, {"platform": {"type": 0, "x": 100, "y": 861, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 490, "y": 892}}, {"platform": {"type": 1, "x": 293, "y": 927}}, {"platform": {"type": 10, "x": 339, "y": 1068}}, {"platform": {"type": 10, "x": 119, "y": 1227}}, {"platform": {"type": 0, "x": 509, "y": 1293, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 1, "x": 36, "y": 1399}}, {"platform": {"type": 1, "x": 240, "y": 1451, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 21, "x": 402, "y": 1494}}, {"platform": {"type": 1, "x": 12, "y": 1533}}]}}, {"id": 1210, "weight": 7, "tags": [15], "stringName": "1210", "config": {"composables": [{"platform": {"type": 1, "x": 62, "y": 91}}, {"platform": {"type": 1, "x": 417, "y": 182}}, {"platform": {"type": 21, "x": 335, "y": 270}}, {"platform": {"type": 1, "x": 160, "y": 374, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 398, "y": 485}}, {"platform": {"type": 1, "x": 48, "y": 572}}, {"platform": {"type": 21, "x": 333, "y": 612}}, {"platform": {"type": 1, "x": 206, "y": 719}}, {"platform": {"type": 1, "x": 351, "y": 837}}, {"platform": {"type": 0, "x": 373, "y": 883, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 105, "y": 1038}}, {"platform": {"type": 1, "x": 333, "y": 1235}}, {"platform": {"type": 21, "x": 424, "y": 1288}}, {"platform": {"type": 1, "x": 46, "y": 1303}}, {"platform": {"type": 1, "x": 474, "y": 1436}}, {"platform": {"type": 1, "x": 105, "y": 1483}}]}}, {"id": 1211, "weight": 6, "tags": [15], "stringName": "1211", "config": {"composables": [{"platform": {"type": 1, "x": 13, "y": 93, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 1, "x": 480, "y": 128}}, {"platform": {"type": 1, "x": 246, "y": 187}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 88, "y": 354}}, {"platform": {"type": 0, "x": 344, "y": 518, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 70, "y": 592}}, {"platform": {"type": 1, "x": 225, "y": 819}}, {"platform": {"type": 21, "x": 423, "y": 829}}, {"platform": {"type": 1, "x": 423, "y": 994}}, {"platform": {"type": 21, "x": 70, "y": 1127}}, {"platform": {"type": 1, "x": 246, "y": 1240}}, {"platform": {"type": 1, "x": 386, "y": 1411}}, {"platform": {"type": 1, "x": 56, "y": 1426}}, {"platform": {"type": 1, "x": 500, "y": 1492}}]}}, {"id": 1301, "weight": 6, "tags": [17], "stringName": "1301", "config": {"composables": [{"platform": {"type": 1, "x": 44, "y": 119, "tickets": false}}, {"platform": {"type": 1, "x": 215, "y": 128}}, {"platform": {"type": 1, "x": 447, "y": 158}}, {"platform": {"type": 21, "x": 384, "y": 205, "tickets": false}}, {"platform": {"type": 11, "x": 44, "y": 324, "movementRange": [700, 700], "movementDuration": [4500, 4500]}}, {"platform": {"type": 1, "x": 263, "y": 851}}, {"platform": {"type": 11, "x": 483, "y": 1028, "movementRange": [-700, -700], "movementDuration": [4500, 4500]}}, {"platform": {"type": 21, "x": 171, "y": 1082, "tickets": false}}, {"platform": {"type": 1, "x": 263, "y": 1129}}, {"platform": {"type": 10, "x": 270, "y": 1313}}, {"platform": {"type": 1, "x": 406, "y": 1520}}, {"platform": {"type": 1, "x": 44, "y": 1535}}]}}, {"id": 1310, "weight": 6, "tags": [17], "stringName": "1310", "config": {"composables": [{"platform": {"type": 1, "x": 483, "y": 87, "tickets": false}}, {"platform": {"type": 20, "x": 263, "y": 144, "tickets": false}}, {"platform": {"type": 20, "x": 52, "y": 299, "tickets": false}}, {"platform": {"type": 20, "x": 478, "y": 343, "tickets": false}}, {"platform": {"type": 20, "x": 83, "y": 511, "tickets": false}}, {"platform": {"type": 20, "x": 421, "y": 600, "tickets": false}}, {"platform": {"type": 20, "x": 149, "y": 712, "tickets": false}}, {"platform": {"type": 20, "x": 478, "y": 853, "tickets": false}}, {"platform": {"type": 20, "x": 92, "y": 924, "tickets": false}}, {"platform": {"type": 1, "x": 255, "y": 935}}, {"platform": {"type": 20, "x": 455, "y": 1106, "tickets": false}}, {"platform": {"type": 20, "x": 52, "y": 1136, "tickets": false}}, {"platform": {"type": 20, "x": 320, "y": 1284, "tickets": false}}, {"platform": {"type": 20, "x": 52, "y": 1309, "tickets": false}}, {"platform": {"type": 20, "x": 458, "y": 1482, "tickets": false}}, {"platform": {"type": 20, "x": 26, "y": 1501, "tickets": false}}, {"platform": {"type": 1, "x": 236, "y": 1501}}]}}, {"id": 1311, "weight": 6, "tags": [17], "stringName": "1311", "config": {"composables": [{"platform": {"type": 1, "x": 263, "y": 85, "tickets": false}}, {"platform": {"type": 20, "x": 488, "y": 121, "tickets": false}}, {"platform": {"type": 20, "x": 38, "y": 223, "tickets": false}}, {"platform": {"type": 20, "x": 391, "y": 280, "tickets": false}}, {"platform": {"type": 20, "x": 152, "y": 434, "tickets": false}}, {"platform": {"type": 20, "x": 272, "y": 591, "tickets": false}}, {"platform": {"type": 20, "x": 55, "y": 781, "tickets": false}}, {"platform": {"type": 20, "x": 500, "y": 790, "tickets": false}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 20, "x": 277, "y": 1039, "tickets": false}}, {"platform": {"type": 20, "x": 121, "y": 1185, "tickets": false}}, {"platform": {"type": 20, "x": 448, "y": 1345, "tickets": false}}, {"platform": {"type": 10, "x": 291, "y": 1495}}]}}, {"id": 1313, "weight": 6, "tags": [17], "stringName": "1313", "config": {"composables": [{"platform": {"type": 1, "x": 38, "y": 135, "tickets": false}}, {"platform": {"type": 20, "x": 500, "y": 135, "tickets": false}}, {"platform": {"type": 20, "x": 356, "y": 392, "tickets": false}}, {"platform": {"type": 20, "x": 38, "y": 630, "tickets": false}}, {"platform": {"type": 20, "x": 470, "y": 630, "tickets": false}}, {"platform": {"type": 10, "x": 277, "y": 793}}, {"platform": {"type": 20, "x": 461, "y": 1001, "tickets": false}}, {"platform": {"type": 20, "x": 25, "y": 1128, "tickets": false}}, {"platform": {"type": 0, "x": 277, "y": 1129, "tickets": false}}, {"platform": {"type": 20, "x": 242, "y": 1281, "tickets": false}}, {"platform": {"type": 20, "x": 500, "y": 1408, "tickets": false}}, {"platform": {"type": 1, "x": 38, "y": 1438}}, {"platform": {"type": 1, "x": 283, "y": 1521}}, {"platform": {"type": 1, "x": 62, "y": 1535, "tickets": false}}]}}, {"id": 1314, "weight": 6, "tags": [17], "stringName": "1314", "config": {"composables": [{"platform": {"type": 10, "x": 230, "y": 112}}, {"platform": {"type": 10, "x": 510, "y": 319}}, {"platform": {"type": 10, "x": 230, "y": 557}}, {"platform": {"type": 10, "x": 27, "y": 758}}, {"platform": {"type": 10, "x": 344, "y": 987}}, {"platform": {"type": 10, "x": 84, "y": 1227}}, {"platform": {"type": 1, "x": 287, "y": 1457}}, {"platform": {"type": 10, "x": 491, "y": 1507}}]}}, {"id": 1401, "weight": 6, "tags": [19], "stringName": "1401", "config": {"composables": [{"platform": {"type": 21, "x": 181, "y": 100, "tickets": false}}, {"platform": {"type": 1, "x": 341, "y": 160}}, {"platform": {"type": 10, "x": 105, "y": 375}}, {"platform": {"type": 0, "x": 116, "y": 466, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 10, "x": 409, "y": 652}}, {"platform": {"type": 10, "x": 48, "y": 776}}, {"platform": {"type": 10, "x": 489, "y": 868}}, {"platform": {"type": 0, "x": 402, "y": 942, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 10, "x": 466, "y": 1118}}, {"platform": {"type": 10, "x": 156, "y": 1258}}, {"platform": {"type": 10, "x": 366, "y": 1394}}, {"platform": {"type": 1, "x": 252, "y": 1455, "tickets": false}, "booster": {"type": 501}}, {"platform": {"type": 1, "x": 75, "y": 1535, "tickets": false}}]}}, {"id": 1402, "weight": 6, "tags": [19], "stringName": "1402", "config": {"composables": [{"platform": {"type": 20, "x": 512, "y": 91, "tickets": false}}, {"platform": {"type": 1, "x": 280, "y": 121, "tickets": false}}, {"platform": {"type": 20, "x": 71, "y": 140, "tickets": false}}, {"platform": {"type": 20, "x": 369, "y": 226, "tickets": false}}, {"platform": {"type": 21, "x": 476, "y": 256, "tickets": false}}, {"platform": {"type": 20, "x": 14, "y": 301, "tickets": false}}, {"platform": {"type": 20, "x": 165, "y": 422, "tickets": false}}, {"platform": {"type": 0, "x": 107, "y": 579, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 20, "x": 337, "y": 579, "tickets": false}}, {"platform": {"type": 20, "x": 455, "y": 618, "tickets": false}}, {"platform": {"type": 20, "x": 155, "y": 750, "tickets": false}}, {"platform": {"type": 20, "x": 404, "y": 820, "tickets": false}}, {"platform": {"type": 21, "x": 263, "y": 858, "tickets": false}}, {"platform": {"type": 20, "x": 86, "y": 983, "tickets": false}}, {"platform": {"type": 1, "x": 385, "y": 1010, "tickets": false}}, {"platform": {"type": 0, "x": 495, "y": 1010, "tickets": false}, "mob": {"type": 1002}}, {"platform": {"type": 1, "x": 503, "y": 1010, "tickets": false}}, {"platform": {"type": 20, "x": 223, "y": 1170, "tickets": false}}, {"platform": {"type": 21, "x": 222, "y": 1249, "tickets": false}}, {"platform": {"type": 21, "x": 86, "y": 1271, "tickets": false}}, {"platform": {"type": 20, "x": 394, "y": 1312, "tickets": false}}, {"platform": {"type": 1, "x": 11, "y": 1439, "tickets": false}}, {"platform": {"type": 20, "x": 499, "y": 1474, "tickets": false}}, {"platform": {"type": 1, "x": 155, "y": 1520}}]}}, {"id": 1405, "weight": 9, "tags": [19], "stringName": "1405", "config": {"composables": [{"platform": {"type": 1, "x": 37, "y": 82}}, {"platform": {"type": 1, "x": 499, "y": 82}}, {"platform": {"type": 10, "x": 263, "y": 196}}, {"platform": {"type": 20, "x": 77, "y": 387, "tickets": false}}, {"platform": {"type": 20, "x": 464, "y": 402, "tickets": false}}, {"platform": {"type": 20, "x": 328, "y": 544, "tickets": false}}, {"platform": {"type": 0, "x": 528, "y": 583, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 20, "x": 320, "y": 734, "tickets": false}}, {"platform": {"type": 20, "x": 54, "y": 820, "tickets": false}}, {"platform": {"type": 20, "x": 434, "y": 880, "tickets": false}}, {"platform": {"type": 1, "x": 94, "y": 966}}, {"platform": {"type": 0, "x": 440, "y": 1065, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 20, "x": 208, "y": 1111, "tickets": false}}, {"platform": {"type": 20, "x": 385, "y": 1281, "tickets": false}}, {"platform": {"type": 1, "x": 272, "y": 1383, "tickets": false}}, {"platform": {"type": 21, "x": 54, "y": 1435, "tickets": false}}, {"platform": {"type": 1, "x": 499, "y": 1506, "tickets": false}}]}}, {"id": 1406, "weight": 9, "tags": [19], "stringName": "1406", "config": {"composables": [{"platform": {"type": 20, "x": 31, "y": 89, "tickets": false}}, {"platform": {"type": 20, "x": 262, "y": 110, "tickets": false}}, {"platform": {"type": 1, "x": 126, "y": 125, "tickets": false}}, {"platform": {"type": 21, "x": 490, "y": 155, "tickets": false}}, {"platform": {"type": 20, "x": 294, "y": 208, "tickets": false}}, {"platform": {"type": 0, "x": 489, "y": 276, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 20, "x": 459, "y": 467, "tickets": false}}, {"platform": {"type": 20, "x": 252, "y": 526, "tickets": false}}, {"platform": {"type": 20, "x": 78, "y": 679, "tickets": false}}, {"platform": {"type": 0, "x": 95, "y": 861, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 490, "y": 892, "tickets": false}}, {"platform": {"type": 20, "x": 183, "y": 897, "tickets": false}}, {"platform": {"type": 20, "x": 330, "y": 1031, "tickets": false}}, {"platform": {"type": 20, "x": 21, "y": 1089, "tickets": false}}, {"platform": {"type": 21, "x": 102, "y": 1226, "tickets": false}}, {"platform": {"type": 20, "x": 403, "y": 1226, "tickets": false}}, {"platform": {"type": 0, "x": 453, "y": 1293, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 20, "x": 21, "y": 1341, "tickets": false}}, {"platform": {"type": 20, "x": 220, "y": 1421, "tickets": false}}, {"platform": {"type": 1, "x": 508, "y": 1480, "tickets": false}}]}}, {"id": 1410, "weight": 9, "tags": [19], "stringName": "1410", "config": {"composables": [{"platform": {"type": 1, "x": 31, "y": 95}}, {"platform": {"type": 1, "x": 490, "y": 106}}, {"platform": {"type": 1, "x": 339, "y": 121}}, {"platform": {"type": 21, "x": 126, "y": 125, "tickets": false}}, {"platform": {"type": 1, "x": 240, "y": 155, "tickets": false}, "booster": {"type": 504}}, {"platform": {"type": 11, "x": 484, "y": 239, "movementRange": [532, 532], "movementDuration": [2000, 2000]}}, {"platform": {"type": 0, "x": 285, "y": 389, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 256, "y": 688}}, {"platform": {"type": 11, "x": 31, "y": 771, "movementRange": [-532, -532], "movementDuration": [2000, 2000]}}, {"platform": {"type": 11, "x": 183, "y": 780, "movementRange": [393, 393], "movementDuration": [2000, 2000]}}, {"platform": {"type": 0, "x": 81, "y": 845, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 0, "x": 524, "y": 977, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 11, "x": 320, "y": 1173, "movementRange": [-393, -393], "movementDuration": [2000, 2000]}}, {"platform": {"type": 1, "x": 46, "y": 1267}}, {"platform": {"type": 1, "x": 199, "y": 1375, "tickets": false}}, {"platform": {"type": 0, "x": 310, "y": 1375, "tickets": false}, "mob": {"type": 1002}}, {"platform": {"type": 1, "x": 320, "y": 1375, "tickets": false}}, {"platform": {"type": 1, "x": 502, "y": 1419, "tickets": false}, "booster": {"type": 500}}, {"platform": {"type": 21, "x": 81, "y": 1449, "tickets": false}}, {"platform": {"type": 1, "x": 12, "y": 1533, "tickets": false}}]}}, {"id": 1411, "weight": 9, "tags": [19], "stringName": "1411", "config": {"composables": [{"platform": {"type": 1, "x": 81, "y": 91, "tickets": false}}, {"platform": {"type": 20, "x": 384, "y": 116, "tickets": false}}, {"platform": {"type": 20, "x": 320, "y": 204, "tickets": false}}, {"platform": {"type": 20, "x": 478, "y": 304, "tickets": false}}, {"platform": {"type": 0, "x": 182, "y": 422, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 20, "x": 441, "y": 540, "tickets": false}}, {"platform": {"type": 20, "x": 39, "y": 653, "tickets": false}}, {"platform": {"type": 20, "x": 263, "y": 698, "tickets": false}}, {"platform": {"type": 20, "x": 478, "y": 840, "tickets": false}}, {"platform": {"type": 20, "x": 239, "y": 928, "tickets": false}}, {"platform": {"type": 20, "x": 58, "y": 1040, "tickets": false}}, {"platform": {"type": 0, "x": 397, "y": 1130, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 20, "x": 81, "y": 1198, "tickets": false}}, {"platform": {"type": 20, "x": 125, "y": 1340, "tickets": false}}, {"platform": {"type": 20, "x": 421, "y": 1370, "tickets": false}}, {"platform": {"type": 20, "x": 296, "y": 1444, "tickets": false}}, {"platform": {"type": 20, "x": 58, "y": 1459, "tickets": false}}, {"platform": {"type": 20, "x": 498, "y": 1474, "tickets": false}}]}}, {"id": 1501, "weight": 7, "tags": [16], "stringName": "1501", "config": {"composables": [{"platform": {"type": 1, "x": 457, "y": 84, "tickets": false}}, {"platform": {"type": 20, "x": 214, "y": 217, "tickets": false}}, {"platform": {"type": 20, "x": 52, "y": 381, "tickets": false}}, {"platform": {"type": 20, "x": 413, "y": 381, "tickets": false}}, {"platform": {"type": 20, "x": 448, "y": 541, "tickets": false}}, {"platform": {"type": 20, "x": 206, "y": 639, "tickets": false}}, {"platform": {"type": 20, "x": 140, "y": 790, "tickets": false}}, {"platform": {"type": 20, "x": 428, "y": 844, "tickets": false}}, {"platform": {"type": 20, "x": 197, "y": 1021, "tickets": false}}, {"platform": {"type": 20, "x": 474, "y": 1098, "tickets": false}}, {"platform": {"type": 20, "x": 170, "y": 1230, "tickets": false}}, {"platform": {"type": 20, "x": 459, "y": 1352, "tickets": false}}, {"platform": {"type": 20, "x": 254, "y": 1400, "tickets": false}}, {"platform": {"type": 20, "x": 37, "y": 1531, "tickets": false}}]}}, {"id": 1504, "weight": 7, "tags": [16], "stringName": "1504", "config": {"composables": [{"platform": {"type": 1, "x": 70, "y": 107, "tickets": false}}, {"platform": {"type": 20, "x": 442, "y": 210, "tickets": false}}, {"platform": {"type": 20, "x": 158, "y": 439, "tickets": false}}, {"platform": {"type": 20, "x": 87, "y": 651, "tickets": false}}, {"platform": {"type": 20, "x": 349, "y": 826, "tickets": false}}, {"platform": {"type": 20, "x": 429, "y": 924, "tickets": false}}, {"platform": {"type": 20, "x": 127, "y": 1172, "tickets": false}}, {"platform": {"type": 20, "x": 215, "y": 1308, "tickets": false}}, {"platform": {"type": 20, "x": 442, "y": 1488, "tickets": false}}]}}, {"id": 1505, "weight": 7, "tags": [16], "stringName": "1505", "config": {"composables": [{"platform": {"type": 1, "x": 494, "y": 85}}, {"platform": {"type": 1, "x": 100, "y": 100}}, {"platform": {"type": 1, "x": 245, "y": 130}}, {"platform": {"type": 1, "x": 28, "y": 306}}, {"platform": {"type": 10, "x": 338, "y": 415}}, {"platform": {"type": 10, "x": 58, "y": 520}}, {"platform": {"type": 1, "x": 340, "y": 694}}, {"platform": {"type": 0, "x": 117, "y": 698, "tickets": false}}, {"platform": {"type": 1, "x": 245, "y": 885}}, {"platform": {"type": 21, "x": 58, "y": 915}}, {"platform": {"type": 10, "x": 341, "y": 1039}}, {"platform": {"type": 10, "x": 131, "y": 1113}}, {"platform": {"type": 0, "x": 522, "y": 1163, "tickets": false}}, {"platform": {"type": 1, "x": 17, "y": 1340}}, {"platform": {"type": 1, "x": 509, "y": 1388}}, {"platform": {"type": 1, "x": 354, "y": 1496}}, {"platform": {"type": 1, "x": 142, "y": 1516}}]}}, {"id": 1506, "weight": 7, "tags": [16], "stringName": "1506", "config": {"composables": [{"platform": {"type": 1, "x": 38, "y": 101}}, {"platform": {"type": 1, "x": 251, "y": 186}}, {"platform": {"type": 21, "x": 33, "y": 343}}, {"platform": {"type": 1, "x": 485, "y": 382}}, {"platform": {"type": 1, "x": 391, "y": 540}}, {"platform": {"type": 0, "x": 310, "y": 611, "tickets": false}}, {"platform": {"type": 1, "x": 436, "y": 698}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 1, "x": 149, "y": 955, "tickets": false}}, {"platform": {"type": 1, "x": 458, "y": 1063}}, {"platform": {"type": 21, "x": 263, "y": 1195}}, {"platform": {"type": 10, "x": 287, "y": 1327}}, {"platform": {"type": 1, "x": 302, "y": 1477}}, {"platform": {"type": 1, "x": 62, "y": 1513}}]}}, {"id": 1507, "weight": 7, "tags": [16], "stringName": "1507", "config": {"composables": [{"platform": {"type": 1, "x": 31, "y": 95}}, {"platform": {"type": 1, "x": 490, "y": 106}}, {"platform": {"type": 1, "x": 339, "y": 121}}, {"platform": {"type": 0, "x": 509, "y": 276, "tickets": false}}, {"platform": {"type": 1, "x": 252, "y": 332}}, {"platform": {"type": 10, "x": 107, "y": 458}}, {"platform": {"type": 10, "x": 320, "y": 642}}, {"platform": {"type": 10, "x": 50, "y": 757}}, {"platform": {"type": 0, "x": 100, "y": 861, "tickets": false}}, {"platform": {"type": 10, "x": 339, "y": 1068}}, {"platform": {"type": 10, "x": 119, "y": 1227}}, {"platform": {"type": 0, "x": 509, "y": 1293, "tickets": false}}, {"platform": {"type": 1, "x": 240, "y": 1451, "tickets": false}}, {"platform": {"type": 1, "x": 402, "y": 1494}}]}}, {"id": 1508, "weight": 7, "tags": [16], "stringName": "1508", "config": {"composables": [{"platform": {"type": 1, "x": 119, "y": 156}}, {"platform": {"type": 1, "x": 254, "y": 171}}, {"platform": {"type": 1, "x": 27, "y": 277}}, {"platform": {"type": 1, "x": 43, "y": 468}}, {"platform": {"type": 1, "x": 455, "y": 579}}, {"platform": {"type": 1, "x": 116, "y": 664}}, {"platform": {"type": 0, "x": 509, "y": 704, "tickets": false}}, {"platform": {"type": 1, "x": 43, "y": 871}}, {"platform": {"type": 1, "x": 59, "y": 1063, "tickets": false}}, {"platform": {"type": 0, "x": 165, "y": 1063, "tickets": false}}, {"platform": {"type": 1, "x": 176, "y": 1063, "tickets": false}}, {"platform": {"type": 1, "x": 194, "y": 1406, "tickets": false}}, {"platform": {"type": 1, "x": 27, "y": 1498}}, {"platform": {"type": 1, "x": 504, "y": 1516}}]}}, {"id": 1511, "weight": 7, "tags": [16], "stringName": "1511", "config": {"composables": [{"platform": {"type": 1, "x": 426, "y": 116}}, {"platform": {"type": 1, "x": 54, "y": 131}}, {"platform": {"type": 10, "x": 245, "y": 189}}, {"platform": {"type": 1, "x": 501, "y": 280}}, {"platform": {"type": 1, "x": 96, "y": 348}}, {"platform": {"type": 1, "x": 272, "y": 512}}, {"platform": {"type": 10, "x": 22, "y": 619}}, {"platform": {"type": 10, "x": 483, "y": 705}}, {"platform": {"type": 1, "x": 39, "y": 787}}, {"platform": {"type": 0, "x": 85, "y": 840, "tickets": false}}, {"platform": {"type": 10, "x": 483, "y": 1040}}, {"platform": {"type": 10, "x": 60, "y": 1215}}, {"platform": {"type": 1, "x": 461, "y": 1318}}, {"platform": {"type": 1, "x": 312, "y": 1401}}, {"platform": {"type": 1, "x": 504, "y": 1458}}, {"platform": {"type": 1, "x": 39, "y": 1500}}]}}, {"id": 1512, "weight": 7, "tags": [16], "stringName": "1512", "config": {"composables": [{"platform": {"type": 1, "x": 43, "y": 150, "tickets": false}}, {"platform": {"type": 1, "x": 245, "y": 184}}, {"platform": {"type": 1, "x": 470, "y": 240}}, {"platform": {"type": 20, "x": 258, "y": 367}}, {"platform": {"type": 1, "x": 44, "y": 438}}, {"platform": {"type": 20, "x": 359, "y": 468}}, {"platform": {"type": 1, "x": 483, "y": 549}}, {"platform": {"type": 20, "x": 18, "y": 591}}, {"platform": {"type": 0, "x": 520, "y": 664, "tickets": false}}, {"platform": {"type": 20, "x": 101, "y": 745}}, {"platform": {"type": 1, "x": 277, "y": 820}}, {"platform": {"type": 20, "x": 44, "y": 924}}, {"platform": {"type": 20, "x": 470, "y": 924}}, {"platform": {"type": 1, "x": 470, "y": 1069}}, {"platform": {"type": 1, "x": 258, "y": 1084}}, {"platform": {"type": 20, "x": 62, "y": 1173}}, {"platform": {"type": 10, "x": 272, "y": 1268}}, {"platform": {"type": 20, "x": 483, "y": 1384}}, {"platform": {"type": 1, "x": 38, "y": 1438}}, {"platform": {"type": 20, "x": 163, "y": 1475}}, {"platform": {"type": 1, "x": 283, "y": 1521}}]}}, {"id": 1513, "weight": 10, "tags": [16], "stringName": "1513", "config": {"composables": [{"platform": {"type": 1, "x": 13, "y": 76}}, {"platform": {"type": 1, "x": 500, "y": 91}}, {"platform": {"type": 1, "x": 335, "y": 132}}, {"platform": {"type": 20, "x": 220, "y": 255, "tickets": false}}, {"platform": {"type": 1, "x": 263, "y": 343}}, {"platform": {"type": 20, "x": 63, "y": 351, "tickets": false}}, {"platform": {"type": 20, "x": 177, "y": 483, "tickets": false}}, {"platform": {"type": 1, "x": 17, "y": 523}}, {"platform": {"type": 20, "x": 263, "y": 600, "tickets": false}}, {"platform": {"type": 1, "x": 506, "y": 725}}, {"platform": {"type": 20, "x": 403, "y": 772, "tickets": false}}, {"platform": {"type": 0, "x": 328, "y": 901, "tickets": false}}, {"platform": {"type": 1, "x": 63, "y": 937}}, {"platform": {"type": 20, "x": 500, "y": 954, "tickets": false}}, {"platform": {"type": 20, "x": 17, "y": 1090, "tickets": false}}, {"platform": {"type": 20, "x": 392, "y": 1113, "tickets": false}}, {"platform": {"type": 20, "x": 184, "y": 1128, "tickets": false}}, {"platform": {"type": 10, "x": 470, "y": 1231}}, {"platform": {"type": 20, "x": 274, "y": 1291, "tickets": false}}, {"platform": {"type": 1, "x": 483, "y": 1360}}, {"platform": {"type": 1, "x": 17, "y": 1408}}, {"platform": {"type": 1, "x": 245, "y": 1424}}, {"platform": {"type": 1, "x": 413, "y": 1519}}]}}, {"id": 1514, "weight": 6, "tags": [16], "stringName": "1514", "config": {"composables": [{"platform": {"type": 1, "x": 175, "y": 113}}, {"platform": {"type": 1, "x": 16, "y": 143}}, {"platform": {"type": 1, "x": 338, "y": 143}}, {"platform": {"type": 1, "x": 484, "y": 173}}, {"platform": {"type": 21, "x": 61, "y": 224}}, {"platform": {"type": 1, "x": 130, "y": 287}}, {"platform": {"type": 21, "x": 295, "y": 385}}, {"platform": {"type": 1, "x": 427, "y": 463}}, {"platform": {"type": 1, "x": 106, "y": 544}}, {"platform": {"type": 21, "x": 386, "y": 585}}, {"platform": {"type": 21, "x": 181, "y": 653}}, {"platform": {"type": 1, "x": 320, "y": 700}}, {"platform": {"type": 1, "x": 22, "y": 830}}, {"platform": {"type": 1, "x": 491, "y": 852}}, {"platform": {"type": 1, "x": 238, "y": 937, "tickets": false}}, {"platform": {"type": 0, "x": 348, "y": 937, "tickets": false}}, {"platform": {"type": 1, "x": 359, "y": 937, "tickets": false}}, {"platform": {"type": 1, "x": 491, "y": 1109}}, {"platform": {"type": 1, "x": 287, "y": 1192, "tickets": false}}, {"platform": {"type": 1, "x": 35, "y": 1249}}, {"platform": {"type": 1, "x": 500, "y": 1306}}, {"platform": {"type": 10, "x": 39, "y": 1393}}, {"platform": {"type": 1, "x": 386, "y": 1508}}]}}, {"id": 1515, "weight": 10, "tags": [16], "stringName": "1515", "config": {"composables": [{"platform": {"type": 1, "x": 22, "y": 90}}, {"platform": {"type": 1, "x": 501, "y": 90}}, {"platform": {"type": 1, "x": 215, "y": 120}}, {"platform": {"type": 21, "x": 377, "y": 141}}, {"platform": {"type": 1, "x": 101, "y": 182}}, {"platform": {"type": 21, "x": 501, "y": 232}}, {"platform": {"type": 1, "x": 302, "y": 247}}, {"platform": {"type": 1, "x": 466, "y": 290}}, {"platform": {"type": 1, "x": 117, "y": 363}}, {"platform": {"type": 10, "x": 12, "y": 512}}, {"platform": {"type": 10, "x": 263, "y": 611}}, {"platform": {"type": 21, "x": 501, "y": 725}}, {"platform": {"type": 1, "x": 210, "y": 804}}, {"platform": {"type": 1, "x": 69, "y": 949}}, {"platform": {"type": 21, "x": 22, "y": 1019}}, {"platform": {"type": 1, "x": 151, "y": 1123}}, {"platform": {"type": 21, "x": 491, "y": 1138}}, {"platform": {"type": 1, "x": 96, "y": 1288}}, {"platform": {"type": 21, "x": 265, "y": 1318}}, {"platform": {"type": 21, "x": 461, "y": 1410}}, {"platform": {"type": 1, "x": 245, "y": 1425}}, {"platform": {"type": 1, "x": 404, "y": 1490}}, {"platform": {"type": 1, "x": 39, "y": 1500}}, {"platform": {"type": 1, "x": 501, "y": 1530}}]}}, {"id": 1516, "weight": 10, "tags": [16], "stringName": "1516", "config": {"composables": [{"platform": {"type": 1, "x": 206, "y": 108}}, {"platform": {"type": 1, "x": 32, "y": 123}}, {"platform": {"type": 1, "x": 490, "y": 138}}, {"platform": {"type": 10, "x": 32, "y": 232}}, {"platform": {"type": 1, "x": 478, "y": 315}}, {"platform": {"type": 1, "x": 320, "y": 382}}, {"platform": {"type": 1, "x": 96, "y": 397}}, {"platform": {"type": 21, "x": 215, "y": 442}}, {"platform": {"type": 21, "x": 39, "y": 549}}, {"platform": {"type": 1, "x": 287, "y": 652}}, {"platform": {"type": 11, "x": 500, "y": 695, "tickets": false, "movementRange": [150, 150], "movementDuration": [2000, 2000]}}, {"platform": {"type": 11, "x": 79, "y": 840, "tickets": false, "movementRange": [150, 150], "movementDuration": [2000, 2000]}}, {"platform": {"type": 1, "x": 173, "y": 1154}}, {"platform": {"type": 21, "x": 337, "y": 1154}}, {"platform": {"type": 1, "x": 478, "y": 1202}}, {"platform": {"type": 1, "x": 39, "y": 1217}}, {"platform": {"type": 10, "x": 272, "y": 1332}}, {"platform": {"type": 1, "x": 59, "y": 1432}}, {"platform": {"type": 1, "x": 478, "y": 1447}}, {"platform": {"type": 1, "x": 293, "y": 1462}}, {"platform": {"type": 1, "x": 173, "y": 1482}}]}}, {"id": 1904, "weight": 8, "tags": [9], "stringName": "1904", "config": {"composables": [{"platform": {"type": 1, "x": 500, "y": 99}}, {"platform": {"type": 1, "x": 55, "y": 101, "tickets": false}}, {"platform": {"type": 1, "x": 209, "y": 165}}, {"platform": {"type": 20, "x": 315, "y": 236}}, {"platform": {"type": 20, "x": 283, "y": 378}}, {"platform": {"type": 20, "x": 356, "y": 423}}, {"platform": {"type": 20, "x": 55, "y": 534}}, {"platform": {"type": 1, "x": 483, "y": 549}}, {"platform": {"type": 20, "x": 426, "y": 597}}, {"platform": {"type": 20, "x": 131, "y": 717}}, {"platform": {"type": 1, "x": 277, "y": 820, "tickets": false}}]}}, {"id": 1905, "weight": 8, "tags": [9], "stringName": "1905", "config": {"composables": [{"platform": {"type": 20, "x": 457, "y": 84, "tickets": false}}, {"platform": {"type": 20, "x": 214, "y": 217, "tickets": false}}, {"platform": {"type": 20, "x": 52, "y": 381, "tickets": false}}, {"platform": {"type": 20, "x": 413, "y": 381, "tickets": false}}, {"platform": {"type": 20, "x": 448, "y": 541, "tickets": false}}, {"platform": {"type": 20, "x": 206, "y": 639, "tickets": false}}, {"platform": {"type": 20, "x": 140, "y": 790, "tickets": false}}]}}, {"id": 1907, "weight": 8, "tags": [9], "stringName": "1907", "config": {"composables": [{"platform": {"type": 1, "x": 263, "y": 85, "tickets": false}}, {"platform": {"type": 20, "x": 488, "y": 121, "tickets": false}}, {"platform": {"type": 20, "x": 38, "y": 223, "tickets": false}}, {"platform": {"type": 20, "x": 391, "y": 280, "tickets": false}}, {"platform": {"type": 20, "x": 152, "y": 434, "tickets": false}}, {"platform": {"type": 20, "x": 272, "y": 591, "tickets": false}}, {"platform": {"type": 20, "x": 55, "y": 781, "tickets": false}}, {"platform": {"type": 20, "x": 500, "y": 790, "tickets": false}}, {"platform": {"type": 1, "x": 277, "y": 820}}]}}, {"id": 1908, "weight": 8, "tags": [9], "stringName": "1908", "config": {"composables": [{"platform": {"type": 1, "x": 173, "y": 88}}, {"platform": {"type": 1, "x": 343, "y": 103}}, {"platform": {"type": 21, "x": 478, "y": 118}}, {"platform": {"type": 1, "x": 478, "y": 315}}, {"platform": {"type": 1, "x": 272, "y": 392}}, {"platform": {"type": 11, "x": 39, "y": 452, "movementRange": [-250, -250], "movementDuration": [2400, 2400]}}, {"platform": {"type": 11, "x": 272, "y": 452, "movementRange": [150, 150], "movementDuration": [2200, 2200]}}, {"platform": {"type": 21, "x": 39, "y": 549}}, {"platform": {"type": 1, "x": 39, "y": 637}}, {"platform": {"type": 1, "x": 39, "y": 840}}]}}, {"id": 1910, "weight": 8, "tags": [9], "stringName": "1910", "config": {"composables": [{"platform": {"type": 20, "x": 512, "y": 91, "tickets": false}}, {"platform": {"type": 20, "x": 280, "y": 121, "tickets": false}}, {"platform": {"type": 20, "x": 71, "y": 140, "tickets": false}}, {"platform": {"type": 20, "x": 369, "y": 226, "tickets": false}}, {"platform": {"type": 21, "x": 476, "y": 256, "tickets": false}}, {"platform": {"type": 20, "x": 14, "y": 301, "tickets": false}}, {"platform": {"type": 20, "x": 165, "y": 422, "tickets": false}}, {"platform": {"type": 0, "x": 107, "y": 579, "tickets": false}}, {"platform": {"type": 20, "x": 337, "y": 579, "tickets": false}}, {"platform": {"type": 20, "x": 455, "y": 618, "tickets": false}}, {"platform": {"type": 20, "x": 155, "y": 750, "tickets": false}}, {"platform": {"type": 20, "x": 404, "y": 820, "tickets": false}}]}}, {"id": 1911, "weight": 8, "tags": [9], "stringName": "1911", "config": {"composables": [{"platform": {"type": 1, "x": 490, "y": 106}}, {"platform": {"type": 21, "x": 56, "y": 125}}, {"platform": {"type": 1, "x": 240, "y": 155}}, {"platform": {"type": 1, "x": 20, "y": 245}}, {"platform": {"type": 1, "x": 393, "y": 328}}, {"platform": {"type": 1, "x": 137, "y": 387}}, {"platform": {"type": 1, "x": 507, "y": 466}}, {"platform": {"type": 11, "x": 46, "y": 548, "movementRange": [400, 400], "movementDuration": [3000, 3000]}}, {"platform": {"type": 1, "x": 330, "y": 548}}, {"platform": {"type": 1, "x": 327, "y": 705, "tickets": false}}, {"platform": {"type": 1, "x": 327, "y": 892, "tickets": false}}, {"platform": {"type": 11, "x": 490, "y": 958, "movementRange": [-400, -400], "movementDuration": [3000, 3000]}}]}}, {"id": 1913, "weight": 8, "tags": [9], "stringName": "1913", "config": {"composables": [{"platform": {"type": 1, "x": 65, "y": 88}}, {"platform": {"type": 1, "x": 434, "y": 88}}, {"platform": {"type": 1, "x": 255, "y": 118}}, {"platform": {"type": 21, "x": 419, "y": 133}}, {"platform": {"type": 10, "x": 495, "y": 247}}, {"platform": {"type": 10, "x": 411, "y": 342}}, {"platform": {"type": 21, "x": 495, "y": 402}}, {"platform": {"type": 10, "x": 324, "y": 444}}, {"platform": {"type": 21, "x": 60, "y": 499}}, {"platform": {"type": 10, "x": 259, "y": 557}}, {"platform": {"type": 1, "x": 491, "y": 614}}, {"platform": {"type": 11, "x": 345, "y": 675, "movementRange": [200, 200], "movementDuration": [2800, 2800]}}, {"platform": {"type": 1, "x": 26, "y": 790}}, {"platform": {"type": 1, "x": 491, "y": 790}}, {"platform": {"type": 11, "x": 198, "y": 875, "movementRange": [-200, -200], "movementDuration": [2800, 2800]}}]}}, {"id": 1914, "weight": 8, "tags": [9], "stringName": "1914", "config": {"composables": [{"platform": {"type": 1, "x": 65, "y": 88}}, {"platform": {"type": 1, "x": 434, "y": 88}}, {"platform": {"type": 1, "x": 255, "y": 118}}, {"platform": {"type": 21, "x": 491, "y": 133}}, {"platform": {"type": 11, "x": 15, "y": 237, "movementRange": [390, 390], "movementDuration": [2500, 2500]}}, {"platform": {"type": 11, "x": 140, "y": 237, "movementRange": [390, 390], "movementDuration": [2450, 2450]}}, {"platform": {"type": 11, "x": 266, "y": 237, "movementRange": [390, 390], "movementDuration": [2400, 2400]}}, {"platform": {"type": 11, "x": 390, "y": 237, "movementRange": [390, 390], "movementDuration": [2350, 2350]}}, {"platform": {"type": 11, "x": 510, "y": 237, "movementRange": [390, 390], "movementDuration": [2300, 2300]}}, {"platform": {"type": 1, "x": 359, "y": 796}}, {"platform": {"type": 1, "x": 173, "y": 826}}, {"platform": {"type": 21, "x": 510, "y": 826}}, {"platform": {"type": 21, "x": 15, "y": 826}}]}}, {"id": 2001, "weight": 8, "tags": [10], "stringName": "2001", "config": {"composables": [{"platform": {"type": 1, "x": 329, "y": 115}}, {"platform": {"type": 1, "x": 423, "y": 216}}, {"platform": {"type": 21, "x": 500, "y": 309, "tickets": false}}, {"platform": {"type": 1, "x": 162, "y": 354}}, {"platform": {"type": 1, "x": 386, "y": 587}}, {"platform": {"type": 1, "x": 70, "y": 617}}, {"platform": {"type": 1, "x": 289, "y": 659}}, {"platform": {"type": 1, "x": 465, "y": 911}}, {"platform": {"type": 21, "x": 127, "y": 1012, "tickets": false}}, {"platform": {"type": 0, "x": 415, "y": 1159, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 119, "y": 1174, "tickets": false}}, {"platform": {"type": 1, "x": 284, "y": 1400}}, {"platform": {"type": 1, "x": 474, "y": 1445}}, {"platform": {"type": 1, "x": 127, "y": 1504}}]}}, {"id": 2002, "weight": 8, "tags": [10], "stringName": "2002", "config": {"composables": [{"platform": {"type": 1, "x": 284, "y": 127}}, {"platform": {"type": 1, "x": 48, "y": 145}}, {"platform": {"type": 1, "x": 465, "y": 187}}, {"platform": {"type": 21, "x": 341, "y": 324, "tickets": false}}, {"platform": {"type": 1, "x": 162, "y": 354}}, {"platform": {"type": 1, "x": 474, "y": 602}}, {"platform": {"type": 1, "x": 299, "y": 617}}, {"platform": {"type": 1, "x": 160, "y": 675}}, {"platform": {"type": 21, "x": 471, "y": 867, "tickets": false}}, {"platform": {"type": 1, "x": 127, "y": 925}}, {"platform": {"type": 21, "x": 170, "y": 1003, "tickets": false}}, {"platform": {"type": 0, "x": 471, "y": 1085, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 119, "y": 1174}}, {"platform": {"type": 1, "x": 375, "y": 1301}}, {"platform": {"type": 1, "x": 515, "y": 1489}}, {"platform": {"type": 1, "x": 127, "y": 1504}}]}}, {"id": 2003, "weight": 8, "tags": [10], "stringName": "2003", "config": {"composables": [{"platform": {"type": 1, "x": 329, "y": 115}}, {"platform": {"type": 1, "x": 423, "y": 216}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 320, "y": 385}}, {"platform": {"type": 1, "x": 22, "y": 557}}, {"platform": {"type": 1, "x": 145, "y": 617}}, {"platform": {"type": 1, "x": 346, "y": 805}}, {"platform": {"type": 1, "x": 170, "y": 982}}, {"platform": {"type": 0, "x": 61, "y": 1012, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 184, "y": 1278}}, {"platform": {"type": 1, "x": 15, "y": 1351}}, {"platform": {"type": 1, "x": 360, "y": 1439}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}, {"id": 2004, "weight": 8, "tags": [10], "stringName": "2004", "config": {"composables": [{"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 135, "y": 299}}, {"platform": {"type": 1, "x": 305, "y": 329}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 1, "x": 458, "y": 544}}, {"platform": {"type": 1, "x": 151, "y": 739}}, {"platform": {"type": 0, "x": 305, "y": 814, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 94, "y": 856, "tickets": false}}, {"platform": {"type": 1, "x": 419, "y": 930}}, {"platform": {"type": 1, "x": 284, "y": 1117}}, {"platform": {"type": 1, "x": 398, "y": 1336}}, {"platform": {"type": 1, "x": 70, "y": 1373}}, {"platform": {"type": 1, "x": 515, "y": 1489}}]}}, {"id": 2005, "weight": 8, "tags": [10], "stringName": "2005", "config": {"composables": [{"platform": {"type": 1, "x": 434, "y": 149}}, {"platform": {"type": 1, "x": 79, "y": 189}}, {"platform": {"type": 1, "x": 288, "y": 400}}, {"platform": {"type": 1, "x": 145, "y": 441}}, {"platform": {"type": 1, "x": 402, "y": 441}}, {"platform": {"type": 1, "x": 41, "y": 632}}, {"platform": {"type": 21, "x": 491, "y": 647}}, {"platform": {"type": 1, "x": 166, "y": 805}}, {"platform": {"type": 1, "x": 377, "y": 835}}, {"platform": {"type": 0, "x": 166, "y": 949, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 0, "x": 259, "y": 1012}}, {"platform": {"type": 1, "x": 22, "y": 1027}}, {"platform": {"type": 1, "x": 474, "y": 1057}}, {"platform": {"type": 21, "x": 330, "y": 1137}}, {"platform": {"type": 1, "x": 244, "y": 1256}}, {"platform": {"type": 1, "x": 46, "y": 1403}}, {"platform": {"type": 1, "x": 445, "y": 1403}}, {"platform": {"type": 1, "x": 259, "y": 1526}}]}}, {"id": 2006, "weight": 8, "tags": [10], "stringName": "2006", "config": {"composables": [{"platform": {"type": 1, "x": 434, "y": 149}}, {"platform": {"type": 1, "x": 79, "y": 189}}, {"platform": {"type": 1, "x": 500, "y": 274}}, {"platform": {"type": 1, "x": 259, "y": 344}}, {"platform": {"type": 1, "x": 434, "y": 398}}, {"platform": {"type": 1, "x": 106, "y": 400}}, {"platform": {"type": 1, "x": 31, "y": 574}}, {"platform": {"type": 1, "x": 500, "y": 589}}, {"platform": {"type": 1, "x": 163, "y": 739}}, {"platform": {"type": 1, "x": 360, "y": 739}}, {"platform": {"type": 0, "x": 259, "y": 949, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 31, "y": 989}}, {"platform": {"type": 1, "x": 500, "y": 997}}, {"platform": {"type": 1, "x": 179, "y": 1177}}, {"platform": {"type": 1, "x": 398, "y": 1295}}, {"platform": {"type": 1, "x": 46, "y": 1403}}, {"platform": {"type": 1, "x": 491, "y": 1469}}, {"platform": {"type": 1, "x": 259, "y": 1526}}]}}, {"id": 2007, "weight": 8, "tags": [10], "stringName": "2007", "config": {"composables": [{"platform": {"type": 1, "x": 515, "y": 87}}, {"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 265, "y": 147}}, {"platform": {"type": 1, "x": 106, "y": 202}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 349, "y": 334}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 21, "x": 46, "y": 617}}, {"platform": {"type": 1, "x": 406, "y": 644}}, {"platform": {"type": 1, "x": 151, "y": 739}}, {"platform": {"type": 21, "x": 305, "y": 748}}, {"platform": {"type": 0, "x": 305, "y": 814, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 94, "y": 856}}, {"platform": {"type": 21, "x": 512, "y": 856}}, {"platform": {"type": 1, "x": 455, "y": 940}}, {"platform": {"type": 21, "x": 292, "y": 959}}, {"platform": {"type": 21, "x": 489, "y": 1102}}, {"platform": {"type": 1, "x": 248, "y": 1117}}, {"platform": {"type": 21, "x": 134, "y": 1256}}, {"platform": {"type": 1, "x": 398, "y": 1336}}, {"platform": {"type": 1, "x": 70, "y": 1373}}, {"platform": {"type": 1, "x": 515, "y": 1489}}]}}, {"id": 2008, "weight": 8, "tags": [10], "stringName": "2008", "config": {"composables": [{"platform": {"type": 1, "x": 362, "y": 112}}, {"platform": {"type": 1, "x": 21, "y": 117}}, {"platform": {"type": 1, "x": 489, "y": 217}}, {"platform": {"type": 1, "x": 135, "y": 299}}, {"platform": {"type": 1, "x": 305, "y": 329}}, {"platform": {"type": 21, "x": 449, "y": 380}}, {"platform": {"type": 1, "x": 70, "y": 481}}, {"platform": {"type": 21, "x": 401, "y": 496}}, {"platform": {"type": 0, "x": 525, "y": 603, "tickets": false}, "mob": {"type": 1003}}, {"platform": {"type": 21, "x": 295, "y": 627}}, {"platform": {"type": 1, "x": 37, "y": 669}}, {"platform": {"type": 21, "x": 305, "y": 749}}, {"platform": {"type": 1, "x": 151, "y": 809}}, {"platform": {"type": 21, "x": 463, "y": 821}}, {"platform": {"type": 1, "x": 305, "y": 895}}, {"platform": {"type": 0, "x": 77, "y": 1070, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 477, "y": 1112}}, {"platform": {"type": 1, "x": 238, "y": 1221}}, {"platform": {"type": 1, "x": 419, "y": 1343}}, {"platform": {"type": 1, "x": 70, "y": 1373}}, {"platform": {"type": 1, "x": 515, "y": 1489}}, {"platform": {"type": 1, "x": 216, "y": 1533}}]}}, {"id": 2010, "weight": 6, "tags": [10], "stringName": "2010", "config": {"composables": [{"platform": {"type": 1, "x": 49, "y": 108}}, {"platform": {"type": 1, "x": 500, "y": 135}}, {"platform": {"type": 1, "x": 234, "y": 180}}, {"platform": {"type": 1, "x": 406, "y": 295}}, {"platform": {"type": 1, "x": 463, "y": 425}}, {"platform": {"type": 1, "x": 78, "y": 447}}, {"platform": {"type": 1, "x": 443, "y": 632}}, {"platform": {"type": 1, "x": 142, "y": 650}}, {"platform": {"type": 0, "x": 276, "y": 748, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 1, "x": 463, "y": 918}}, {"platform": {"type": 1, "x": 265, "y": 1072}}, {"platform": {"type": 1, "x": 406, "y": 1247}}, {"platform": {"type": 1, "x": 70, "y": 1277}}, {"platform": {"type": 1, "x": 500, "y": 1431}}, {"platform": {"type": 1, "x": 184, "y": 1474}}]}}, {"id": 2011, "weight": 6, "tags": [10], "stringName": "2011", "config": {"composables": [{"platform": {"type": 1, "x": 163, "y": 98}}, {"platform": {"type": 1, "x": 500, "y": 98}}, {"platform": {"type": 1, "x": 329, "y": 155}}, {"platform": {"type": 1, "x": 22, "y": 233}}, {"platform": {"type": 1, "x": 295, "y": 355}}, {"platform": {"type": 1, "x": 434, "y": 408}}, {"platform": {"type": 1, "x": 136, "y": 415}}, {"platform": {"type": 1, "x": 49, "y": 549}}, {"platform": {"type": 1, "x": 500, "y": 589}}, {"platform": {"type": 21, "x": 49, "y": 685}}, {"platform": {"type": 1, "x": 320, "y": 738, "tickets": false}}, {"platform": {"type": 0, "x": 440, "y": 738, "tickets": false}, "mob": {"type": 1002}}, {"platform": {"type": 1, "x": 443, "y": 738, "tickets": false}}, {"platform": {"type": 21, "x": 136, "y": 788}}, {"platform": {"type": 1, "x": 287, "y": 884}}, {"platform": {"type": 21, "x": 287, "y": 1124}}, {"platform": {"type": 1, "x": 287, "y": 1192}}, {"platform": {"type": 1, "x": 434, "y": 1314}}, {"platform": {"type": 1, "x": 106, "y": 1406}}, {"platform": {"type": 1, "x": 466, "y": 1434}}, {"platform": {"type": 1, "x": 193, "y": 1494}}, {"platform": {"type": 1, "x": 352, "y": 1494}}]}}, {"id": 2012, "weight": 7, "tags": [10], "stringName": "2012", "config": {"composables": [{"platform": {"type": 1, "x": 41, "y": 85}}, {"platform": {"type": 1, "x": 320, "y": 121}}, {"platform": {"type": 1, "x": 488, "y": 225}}, {"platform": {"type": 1, "x": 130, "y": 284}}, {"platform": {"type": 21, "x": 443, "y": 317}}, {"platform": {"type": 1, "x": 434, "y": 389}}, {"platform": {"type": 1, "x": 244, "y": 490}}, {"platform": {"type": 1, "x": 98, "y": 659}}, {"platform": {"type": 21, "x": 423, "y": 829}}, {"platform": {"type": 1, "x": 212, "y": 844}}, {"platform": {"type": 1, "x": 409, "y": 971}}, {"platform": {"type": 0, "x": 438, "y": 1028, "tickets": false}, "mob": {"type": 1001}}, {"platform": {"type": 21, "x": 155, "y": 1073}}, {"platform": {"type": 1, "x": 70, "y": 1159}}, {"platform": {"type": 1, "x": 352, "y": 1205}}, {"platform": {"type": 1, "x": 127, "y": 1356}}, {"platform": {"type": 1, "x": 423, "y": 1413}}, {"platform": {"type": 1, "x": 184, "y": 1521}}, {"platform": {"type": 1, "x": 502, "y": 1521}}]}}]}