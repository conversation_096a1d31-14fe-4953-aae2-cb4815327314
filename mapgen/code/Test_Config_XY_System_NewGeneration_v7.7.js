/* eslint-disable */

useNewConfig()

// All Staged Variables

// Staged Variables General

applyStagedVariables({
  score: 0,
  general: {
    ticketsFraction: 1,
    ticketsPerPlatform: 1,
    movementDuration: [12000, 12000]
  }
})

applyStagedVariables({
  score: 1000,

  general: {
    ticketsFraction: 1,
    ticketsPerPlatform: 1,
    movementDuration: [11000, 11000]
  }
})

applyStagedVariables({
  score: 5000,

  general: {
    ticketsFraction: 1,
    ticketsPerPlatform: 1,
    movementDuration: [10000, 10000]
  }
})

applyStagedVariables({
  score: 7500,

  general: {
    ticketsFraction: 1,
    ticketsPerPlatform: 1,
    movementDuration: [9000, 9000]
  }
})

applyStagedVariables({
  score: 10000,

  general: {
    ticketsFraction: 1,
    ticketsPerPlatform: 1,
    movementDuration: [8000, 8000]
  }
})

applyStagedVariables({
  score: 11000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 1,
    movementDuration: [8000, 8000]
  }
})

applyStagedVariables({
  score: 15000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 2,
    movementDuration: [6500, 6500]
  }
})

applyStagedVariables({
  score: 20000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 2,
    movementDuration: [6000, 6000]
  }
})

applyStagedVariables({
  score: 21000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 2,
    movementDuration: [6000, 6000]
  }
})

applyStagedVariables({
  score: 25000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 2,
    movementDuration: [5000, 5000]
  }
})

applyStagedVariables({
  score: 30000,

  general: {
    ticketsFraction: 2,
    ticketsPerPlatform: 3,
    movementDuration: [4500, 4500]
  }
})

applyStagedVariables({
  score: 31000,

  general: {
    ticketsFraction: 3,
    ticketsPerPlatform: 3,
    movementDuration: [4400, 4400]
  }
})

applyStagedVariables({
  score: 40000,

  general: {
    ticketsFraction: 3,
    ticketsPerPlatform: 3,
    movementDuration: [4000, 4000]
  }
})

applyStagedVariables({
  score: 41000,

  general: {
    ticketsFraction: 3,
    ticketsPerPlatform: 3,
    movementDuration: [3900, 3900]
  }
})

applyStagedVariables({
  score: 50000,

  general: {
    ticketsFraction: 3,
    ticketsPerPlatform: 3,
    movementDuration: [3500, 3500]
  }
})

applyStagedVariables({
  score: 51000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [3250, 3250]
  }
})

applyStagedVariables({
  score: 60000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [3000, 3000]
  }
})

applyStagedVariables({
  score: 61000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [2750, 2750]
  }
})

applyStagedVariables({
  score: 75000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [2500, 2500]
  }
})

applyStagedVariables({
  score: 82000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [2250, 2250]
  }
})

applyStagedVariables({
  score: 90000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [2000, 2000]
  }
})

applyStagedVariables({
  score: 100000,

  general: {
    ticketsFraction: 4,
    ticketsPerPlatform: 3,
    movementDuration: [1750, 1750]
  }
})

applyStagedVariables({
  score: 101000,

  general: {
    ticketsFraction: 6,
    ticketsPerPlatform: 3,
    movementDuration: [1500, 1500]
  }
})

applyStagedVariables({
  score: 115000,

  general: {
    ticketsFraction: 6,
    ticketsPerPlatform: 3,
    movementDuration: [1450, 1450]
  }
})

applyStagedVariables({
  score: 120000,

  general: {
    ticketsFraction: 6,
    ticketsPerPlatform: 3,
    movementDuration: [1400, 1400]
  }
})

applyStagedVariables({
  score: 125000,

  general: {
    ticketsFraction: 6,
    ticketsPerPlatform: 3,
    movementDuration: [1350, 1350]
  }
})

applyStagedVariables({
  score: 130000,

  general: {
    ticketsFraction: 6,
    ticketsPerPlatform: 3,
    movementDuration: [1300, 1300]
  }
})

// Staged Variables Procedural

applyStagedVariables({
  score: 100,
  procedural: {
    score: [200, 200],
    distance: [70, 100],
    distanceWreckable: [60, 100],
    dynamicHProb: undefined,
    wreckableProb: 7,
    disposableProb: undefined,
    dynamicHMax: 1,
    boostSmallJumpCount: [2, 3],
    boostSmallJumpProb: 30,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 12
  }
})

applyStagedVariables({
  score: 1500,
  procedural: {
    score: [750, 750],
    distance: [100, 120],
    distanceWreckable: [80, 100],
    dynamicHProb: 10,
    wreckableProb: 7,
    disposableProb: undefined,
    dynamicHMax: 1,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 5000,
  procedural: {
    score: [750, 750],
    distance: [100, 130],
    distanceWreckable: [80, 120],
    dynamicHProb: 10,
    wreckableProb: 25,
    disposableProb: undefined,
    dynamicHMax: 1,
    boostSmallJumpCount: [1, 3],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 10000,
  procedural: {
    score: [750, 750],
    distance: [100, 130],
    distanceWreckable: [80, 120],
    dynamicHProb: 15,
    wreckableProb: 25,
    disposableProb: undefined,
    dynamicHMax: 1,
    boostSmallJumpCount: [1, 1],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})
applyStagedVariables({
  score: 13000,
  procedural: {
    score: [750, 750],
    distance: [110, 130],
    distanceWreckable: [90, 120],
    dynamicHProb: 15,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 2,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 15000,
  procedural: {
    score: [750, 750],
    distance: [110, 130],
    distanceWreckable: [90, 120],
    dynamicHProb: 15,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 2,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 23000,
  procedural: {
    score: [750, 750],
    distance: [120, 140],
    distanceWreckable: [100, 140],
    dynamicHProb: 30,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 2,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 25000,
  procedural: {
    score: [750, 750],
    distance: [120, 140],
    distanceWreckable: [100, 140],
    dynamicHProb: 30,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 2,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 30000,
  procedural: {
    score: [750, 750],
    distance: [130, 140],
    distanceWreckable: [130, 140],
    dynamicHProb: 60,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 9,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 33000,
  procedural: {
    score: [750, 750],
    distance: [130, 140],
    distanceWreckable: [130, 140],
    dynamicHProb: 60,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 9,
    boostSmallJumpCount: [1, 1],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 39000,
  procedural: {
    score: [750, 750],
    distance: [140, 150],
    distanceWreckable: [140, 150],
    dynamicHProb: 50,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 4,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 41000,
  procedural: {
    score: [750, 750],
    distance: [140, 150],
    distanceWreckable: [140, 150],
    dynamicHProb: 50,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 4,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 49000,
  procedural: {
    score: [750, 750],
    distance: [150, 160],
    distanceWreckable: [150, 160],
    dynamicHProb: 60,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 6,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 51000,
  procedural: {
    score: [750, 750],
    distance: [150, 160],
    distanceWreckable: [150, 160],
    dynamicHProb: 60,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 6,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 60000,
  procedural: {
    score: [750, 750],
    distance: [160, 170],
    distanceWreckable: [160, 170],
    dynamicHProb: 80,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 8,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 62000,
  procedural: {
    score: [750, 750],
    distance: [160, 170],
    distanceWreckable: [160, 170],
    dynamicHProb: 80,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 8,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 73000,
  procedural: {
    score: [750, 750],
    distance: [170, 180],
    distanceWreckable: [170, 180],
    dynamicHProb: 90,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 10,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 75000,
  procedural: {
    score: [750, 750],
    distance: [170, 180],
    distanceWreckable: [170, 180],
    dynamicHProb: 90,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 10,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 83000,
  procedural: {
    score: [750, 750],
    distance: [190, 200],
    distanceWreckable: [190, 200],
    dynamicHProb: 95,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 10,
    boostSmallJumpCount: [0, 0],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 85000,
  procedural: {
    score: [750, 750],
    distance: [190, 200],
    distanceWreckable: [190, 200],
    dynamicHProb: 95,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 15,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 90000,
  procedural: {
    score: [1500, 2000],
    distance: [200, 210],
    distanceWreckable: [200, 210],
    dynamicHProb: 95,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 20,
    boostSmallJumpCount: [1, 2],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [1, 2],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 100000,
  procedural: {
    score: [1500, 2000],
    distance: [210, 220],
    distanceWreckable: [210, 220],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [1, 1],
    boostSmallJumpProb: 30,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 12
  }
})

applyStagedVariables({
  score: 110000,
  procedural: {
    score: [1500, 2000],
    distance: [220, 230],
    distanceWreckable: [220, 230],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [1, 1],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 115000,
  procedural: {
    score: [1500, 2000],
    distance: [230, 240],
    distanceWreckable: [230, 240],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [1, 1],
    boostSmallJumpProb: 0,
    boostBigJumpCount: [0, 0],
    boostBigJumpProb: 0
  }
})

applyStagedVariables({
  score: 120000,
  procedural: {
    score: [1500, 2000],
    distance: [240, 250],
    distanceWreckable: [240, 250],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [0, 1],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 125000,
  procedural: {
    score: [1500, 2000],
    distance: [240, 250],
    distanceWreckable: [240, 250],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [0, 1],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 15
  }
})

applyStagedVariables({
  score: 130000,
  procedural: {
    score: [1500, 2000],
    distance: [240, 250],
    distanceWreckable: [250, 250],
    dynamicHProb: 100,
    wreckableProb: undefined,
    disposableProb: undefined,
    dynamicHMax: 30,
    boostSmallJumpCount: [0, 1],
    boostSmallJumpProb: 20,
    boostBigJumpCount: [0, 1],
    boostBigJumpProb: 15
  }
})

// Staged Variables Booster

applyStagedVariables({
  score: 0,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})
applyStagedVariables({
  score: 1500,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 7000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 11000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 15000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 20000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 27000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 32000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 58000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 65000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 96000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

applyStagedVariables({
  score: 105000,
  booster: {
    boostBigFlyWeight: undefined,
    boostSmallFlyWeight: undefined
  }
})

// All Global Weights

applyGlobalWeights({
  score: [0, 50],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 1,
  tags: [{ option: '100', weight: 100 }]
})

applyGlobalWeights({
  score: [50, 250],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 1,
  maxCompositionSeries: 0,
  tags: [{ option: '100', weight: 100 }]
})

applyGlobalWeights({
  score: [250, 1000],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 1,
  tags: [{ option: '200', weight: 100 }]
})

applyGlobalWeights({
  score: [1000, 2250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [2250, 3000],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '400', weight: 100 }]
})

applyGlobalWeights({
  score: [3000, 3750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '500', weight: 100 }]
})

applyGlobalWeights({
  score: [3750, 4500],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '600', weight: 100 }]
})

applyGlobalWeights({
  score: [4500, 5250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '700', weight: 100 }]
})

applyGlobalWeights({
  score: [5250, 6750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [
    { option: '800', weight: 50 },
    { option: '900', weight: 50 }
  ]
})

applyGlobalWeights({
  score: [6750, 9750],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [9750, 13750],
  procedural: 10,
  composition: 90,
  maxProceduralSeries: 1,
  maxCompositionSeries: 2,
  tags: [
    { option: '1000', weight: 50 },
    { option: '1100', weight: 50 }
  ]
})

applyGlobalWeights({
  score: [13750, 14500],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [14500, 15250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '1200', weight: 100 }]
})

applyGlobalWeights({
  score: [15250, 16750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '1300', weight: 100 }]
})

applyGlobalWeights({
  score: [16750, 19750],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [19750, 23250],
  procedural: 20,
  composition: 80,
  maxProceduralSeries: 1,
  maxCompositionSeries: 2,
  tags: [{ option: '1300', weight: 100 }]
})

applyGlobalWeights({
  score: [23250, 24000],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [24000, 25250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '1400', weight: 100 }]
})

applyGlobalWeights({
  score: [25250, 26750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [{ option: '1500', weight: 100 }]
})

applyGlobalWeights({
  score: [26750, 29750],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [29750, 31500],
  procedural: 50,
  composition: 50,
  maxProceduralSeries: 1,
  maxCompositionSeries: 3,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [31500, 32250],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [32250, 33000],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 2,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [33000, 34500],
  procedural: 60,
  composition: 40,
  maxProceduralSeries: 1,
  maxCompositionSeries: 2,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [34500, 37500],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [37500, 40000],
  procedural: 70,
  composition: 30,
  maxProceduralSeries: 2,
  maxCompositionSeries: 2,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [39250, 40000],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [40000, 40750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 3,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [40750, 42250],
  procedural: 70,
  composition: 30,
  maxProceduralSeries: 2,
  maxCompositionSeries: 2,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [42250, 45250],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [45250, 49750],
  procedural: 80,
  composition: 20,
  maxProceduralSeries: 2,
  maxCompositionSeries: 2,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [49750, 50500],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [50500, 51250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 3,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [51250, 52750],
  procedural: 80,
  composition: 20,
  maxProceduralSeries: 2,
  maxCompositionSeries: 2,
  tags: [
    { option: '1500', weight: 40 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [52750, 56500],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [56500, 60250],
  procedural: 85,
  composition: 15,
  maxProceduralSeries: 3,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 35 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 15 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [60250, 61000],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [61000, 61750],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 3,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [61750, 63250],
  procedural: 85,
  composition: 15,
  maxProceduralSeries: 3,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 30 },
    { option: '1300', weight: 15 },
    { option: '1100', weight: 15 },
    { option: '1000', weight: 15 },
    { option: '900', weight: 15 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [63250, 66250],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [66250, 73750],
  procedural: 85,
  composition: 15,
  maxProceduralSeries: 3,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 25 },
    { option: '1300', weight: 25 },
    { option: '1100', weight: 20 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [73750, 74500],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [74500, 75250],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 3,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [75250, 76750],
  procedural: 85,
  composition: 15,
  maxProceduralSeries: 3,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 25 },
    { option: '1300', weight: 25 },
    { option: '1100', weight: 20 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [76750, 79750],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [79750, 83500],
  procedural: 85,
  composition: 15,
  maxProceduralSeries: 4,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 25 },
    { option: '1300', weight: 25 },
    { option: '1100', weight: 20 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [83500, 84250],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [84250, 85000],
  procedural: 0,
  composition: 100,
  maxProceduralSeries: 0,
  maxCompositionSeries: 3,
  tags: [
    { option: '700', weight: 33 },
    { option: '1200', weight: 33 },
    { option: '1400', weight: 34 }
  ]
})

applyGlobalWeights({
  score: [85000, 86500],
  procedural: 90,
  composition: 10,
  maxProceduralSeries: 4,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 25 },
    { option: '1300', weight: 25 },
    { option: '1100', weight: 20 },
    { option: '1000', weight: 10 },
    { option: '900', weight: 10 },
    { option: '800', weight: 10 }
  ]
})

applyGlobalWeights({
  score: [86500, 90000],
  procedural: 100,
  composition: 0,
  maxProceduralSeries: 10,
  maxCompositionSeries: 0,
  tags: [{ option: '300', weight: 100 }]
})

applyGlobalWeights({
  score: [90000, 120000],
  procedural: 93,
  composition: 7,
  maxProceduralSeries: 5,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 9 },
    { option: '1300', weight: 9 },
    { option: '1100', weight: 8 },
    { option: '1000', weight: 7 },
    { option: '900', weight: 5 },
    { option: '800', weight: 5 },
    { option: '700', weight: 17 },
    { option: '1200', weight: 20 },
    { option: '1400', weight: 20 }
  ]
})

applyGlobalWeights({
  score: [120000, 0],
  procedural: 95,
  composition: 5,
  maxProceduralSeries: 5,
  maxCompositionSeries: 1,
  tags: [
    { option: '1500', weight: 8 },
    { option: '1300', weight: 7 },
    { option: '700', weight: 25 },
    { option: '1200', weight: 30 },
    { option: '1400', weight: 30 }
  ]
})

applyPreset(
  { id: 11, weight: 100, stringName: '11', tags: ['10'] },
  `preset {
static: {x: 510; y: 100; tickets: false; };
static: {x: 8; y: 102; tickets: false; };
static: {x: 259; y: 102; tickets: false; };
static: {x: 389; y: 115; tickets: false; };
static: {x: 138; y: 146; tickets: false; };

}`
)

applyPreset(
  { id: 21, weight: 100, stringName: '21', tags: ['20'] },
  `preset {
static: {x: 510; y: 100; tickets: false; };
static: {x: 8; y: 102; tickets: false; };
static: {x: 259; y: 102; tickets: false; };
static: {x: 389; y: 115; tickets: false; };
static: {x: 138; y: 146; tickets: false; };
empty: {x: 500; y: 201; tickets: false; decoration:  0001;};
empty: {x: 214; y: 250; tickets: false; decoration: 0204;};
static: {x: 154; y: 478; tickets: false; token: ton; };
empty: {x: 455; y: 492; tickets: false; decoration: 0002_area; decoration: 0002;};
static: {x: 275; y: 526; tickets: false; };
static: {x: 503; y: 541; tickets: false; };
static: {x: 31; y: 553; tickets: false; };
static: {x: 389; y: 571; tickets: false; };
empty: {x: 227; y: 715; tickets: false; decoration: 0200;};
empty: {x: 185; y: 770; tickets: false; decoration: 0003_text;};
empty: {x: 309; y: 808; tickets: false; decoration: 0202;};
wreckable: {x: 47; y: 864; tickets: false; };
static: {x: 470; y: 879; tickets: false; };
empty: {x: 227; y: 885; tickets: false; decoration: 0203;};
empty: {x: 644; y: 991; tickets: false; decoration: 0201;};
empty: {x: 510; y: 1041; tickets: false; decoration: 0004_text;};
empty: {x: 285; y: 1058; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 503; y: 1087;};
empty: {x: 131; y: 1160; tickets: false; mobType: 'black-hole'};
static: {x: 379; y: 1229; tickets: false; };
static: {x: 240; y: 1307; tickets: false; };
static: {x: 126; y: 1356; tickets: false; };
static: {x: 508; y: 1397; tickets: false; };
static: {x: 12; y: 1397; tickets: false; };
static: {x: 259; y: 1412; tickets: false; };
static: {x: 394; y: 1482; tickets: false; };
}`
)

applyPreset(
  { id: 102, weight: 25, stringName: '102', tags: ['100'] },
  `preset {
static: {x: 124; y: 117;};
static: {x: 369; y: 117;};
static: {x: 245; y: 137;};
static: {x: 493; y: 137;};
static: {x: 10; y: 141;};
wreckable: {x: 55; y: 201;};
}`
)

applyPreset(
  { id: 103, weight: 25, stringName: '103', tags: ['100'] },
  `preset {
static: {x: 119; y: 118;};
static: {x: 424; y: 133;};
static: {x: 238; y: 141;};
static: {x: 5; y: 148;};
static: {x: 360; y: 171;};
static: {x: 521; y: 171;};
wreckable: {x: 155; y: 201;};
}`
)

applyPreset(
  { id: 104, weight: 25, stringName: '104', tags: ['100'] },
  `preset {
static: {x: 5; y: 81;};
static: {x: 215; y: 81;};
static: {x: 343; y: 81;};
static: {x: 514; y: 81;};
static: {x: 113; y: 119;};
static: {x: 435; y: 119;};
wreckable: {x: 155; y: 201;};
}`
)

applyPreset(
  { id: 105, weight: 25, stringName: '105', tags: ['100'] },
  `preset {
static: {x: 301; y: 80;};
static: {x: 180; y: 99;};
static: {x: 400; y: 110;};
static: {x: 77; y: 129;};
static: {x: 514; y: 134;};
static: {x: 8; y: 164;};
static: {x: 219; y: 209;};
static: {x: 219; y: 209;};
}`
)

applyPreset(
  { id: 201, weight: 10, stringName: '201', tags: ['200'] },
  `preset {
static: {x: 141; y: 127;};
static: {x: 406; y: 201; boosterType: 'small-jump'};
wreckable: {x: 500; y: 309;};
static: {x: 294; y: 384;};
static: {x: 155; y: 527; boosterType: 'small-jump'};
static: {x: 26; y: 629;};
static: {x: 506; y: 669;};
static: {x: 294; y: 745;};
static: {x: 62; y: 942;};
static: {x: 486; y: 975;};
wreckable: {x: 119; y: 1080;};
static: {x: 119; y: 1174;};
wreckable: {x: 483; y: 1204;};
static: {x: 386; y: 1241;};
static: {x: 41; y: 1403;};
static: {x: 500; y: 1423;};
static: {x: 269; y: 1524;};
}`
)

applyPreset(
  { id: 202, weight: 10, stringName: '202', tags: ['200'] },
  `preset {
static: {x: 141; y: 127;};
static: {x: 269; y: 170;};
static: {x: 406; y: 210;boosterType: 'small-jump'};
wreckable: {x: 500; y: 309;};
static: {x: 50; y: 344;};
static: {x: 443; y: 407;};
static: {x: 365; y: 517;};
static: {x: 155; y: 527;};
static: {x: 26; y: 629;};
static: {x: 506; y: 686;};
static: {x: 326; y: 805;};
static: {x: 107; y: 917;};
wreckable: {x: 377; y: 965;};
static: {x: 500; y: 965;};
static: {x: 27; y: 1044;boosterType: 'small-jump'};
wreckable: {x: 107; y: 1128;};
static: {x: 180; y: 1192;boosterType: 'big-jump'};
static: {x: 320; y: 1198;};
static: {x: 479; y: 1324;};
static: {x: 41; y: 1339;};
static: {x: 250; y: 1398;};
static: {x: 479; y: 1489;};
static: {x: 83; y: 1525;};
}`
)

applyPreset(
  { id: 203, weight: 10, stringName: '203', tags: ['200'] },
  `preset {
static: {x: 159; y: 127;};
static: {x: 429; y: 207;};
static: {x: 59; y: 210;boosterType: 'small-jump'};
static: {x: 486; y: 274;};
wreckable: {x: 139; y: 309;};
static: {x: 406; y: 422;};
static: {x: 155; y: 527;};
static: {x: 62; y: 624;};
static: {x: 66; y: 745;};
static: {x: 466; y: 815;};
wreckable: {x: 159; y: 823;};
static: {x: 294; y: 912;};
wreckable: {x: 377; y: 965;};
static: {x: 155; y: 1022;};
static: {x: 465; y: 1077;boosterType: 'small-jump'};
static: {x: 25; y: 1198;boosterType: 'big-jump'};
static: {x: 250; y: 1243;};
static: {x: 390; y: 1349;};
static: {x: 118; y: 1439;};
static: {x: 408; y: 1469;};
static: {x: 45; y: 1515;};
}`
)

applyPreset(
  { id: 204, weight: 10, stringName: '204', tags: ['200'] },
  `preset {
static: {x: 420; y: 108;};
static: {x: 114; y: 188;};
static: {x: 44; y: 323;};
static: {x: 60; y: 402; tickets: false; boosterType: 'small-jump};
wreckable: {x: 245; y: 451;};
static: {x: 364; y: 490;};
static: {x: 294; y: 558; tickets: false; boosterType: 'small-jump};
static: {x: 506; y: 574;};
static: {x: 473; y: 642;};
static: {x: 91; y: 669;};
wreckable: {x: 304; y: 833;};
static: {x: 38; y: 973;};
wreckable: {x: 199; y: 988;};
static: {x: 330; y: 997;};
static: {x: 161; y: 1042;};
static: {x: 366; y: 1075;};
static: {x: 465; y: 1153;};
static: {x: 305; y: 1244;};
static: {x: 462; y: 1248;};
static: {x: 284; y: 1426;};
static: {x: 169; y: 1520;};
}`
)

applyPreset(
  { id: 205, weight: 10, stringName: '205', tags: ['200'] },
  `preset {
static: {x: 36; y: 75; tickets: false; boosterType: 'small-jump};
wreckable: {x: 194; y: 88;};
static: {x: 300; y: 132;};
static: {x: 228; y: 176;};
static: {x: 439; y: 319; tickets: false; boosterType: 'small-jump};
static: {x: 258; y: 326;};
static: {x: 240; y: 411;};
wreckable: {x: 124; y: 425;};
wreckable: {x: 204; y: 495;};
static: {x: 363; y: 529;};
wreckable: {x: 349; y: 614;};
static: {x: 303; y: 693;};
wreckable: {x: 347; y: 862;};
static: {x: 468; y: 940;};
static: {x: 271; y: 947;};
static: {x: 149; y: 1008;};
static: {x: 56; y: 1136;};
static: {x: 209; y: 1143;};
static: {x: 33; y: 1260;};
static: {x: 338; y: 1263;};
static: {x: 217; y: 1394;};
}`
)

applyPreset(
  { id: 206, weight: 10, stringName: '206', tags: ['200'] },
  `preset {
static: {x: 356; y: 125;};
static: {x: 473; y: 149;};
static: {x: 270; y: 198;};
static: {x: 194; y: 279;};
static: {x: 469; y: 286;};
static: {x: 39; y: 295; tickets: false; boosterType: 'small-jump};
wreckable: {x: 269; y: 340;};
static: {x: 493; y: 341;};
static: {x: 206; y: 433;};
wreckable: {x: 87; y: 445;};
static: {x: 448; y: 627;};
static: {x: 211; y: 703; tickets: false; boosterType: 'small-jump};
static: {x: 510; y: 842;};
static: {x: 365; y: 914;};
wreckable: {x: 15; y: 915;};
static: {x: 422; y: 960;};
static: {x: 408; y: 1069;};
static: {x: 227; y: 1250;};
static: {x: 82; y: 1259;};
wreckable: {x: 456; y: 1339;};
static: {x: 5; y: 1340;};
static: {x: 377; y: 1524;};
}`
)

applyPreset(
  { id: 207, weight: 10, stringName: '207', tags: ['200'] },
  `preset {
static: {x: 460; y: 121;};
static: {x: 150; y: 199;};
static: {x: 349; y: 291;};
static: {x: 39; y: 346; tickets: false; boosterType: 'small-jump};
wreckable: {x: 376; y: 533;};
static: {x: 207; y: 580;};
wreckable: {x: 517; y: 590;};
static: {x: 349; y: 636;};
static: {x: 521; y: 775; tickets: false; boosterType: 'small-jump};
static: {x: 341; y: 783;};
static: {x: 106; y: 845;};
static: {x: 261; y: 865;};
static: {x: 255; y: 921;};
static: {x: 227; y: 1017;};
static: {x: 47; y: 1057;};
static: {x: 525; y: 1099;};
static: {x: 65; y: 1195;};
static: {x: 329; y: 1199;};
static: {x: 204; y: 1353;};
static: {x: 21; y: 1390;};
wreckable: {x: 46; y: 1438;};
static: {x: 330; y: 1441;};
}`
)

applyPreset(
  { id: 208, weight: 10, stringName: '208', tags: ['200'] },
  `preset {
static: {x: 132; y: 135;};
static: {x: 210; y: 243; tickets: false; boosterType: 'small-jump};
static: {x: 325; y: 437;};
wreckable: {x: 52; y: 450;};
static: {x: 272; y: 485; tickets: false; boosterType: 'small-jump};
static: {x: 117; y: 531;};
static: {x: 249; y: 726;};
wreckable: {x: 84; y: 734;};
static: {x: 378; y: 734;};
static: {x: 115; y: 834;};
static: {x: 389; y: 838;};
static: {x: 282; y: 990;};
static: {x: 305; y: 1048;};
static: {x: 77; y: 1050;};
static: {x: 245; y: 1118;};
static: {x: 201; y: 1252;};
static: {x: 73; y: 1280;};
wreckable: {x: 164; y: 1355;};
static: {x: 322; y: 1363;};
static: {x: 231; y: 1395;};
static: {x: 17; y: 1449;};
}`
)

applyPreset(
  { id: 209, weight: 10, stringName: '209', tags: ['200'] },
  `preset {
static: {x: 7; y: 131;};
static: {x: 492; y: 135;};
static: {x: 372; y: 168;};
static: {x: 53; y: 243;};
static: {x: 404; y: 252;};
static: {x: 194; y: 282;};
static: {x: 171; y: 339;};
static: {x: 450; y: 349;};
wreckable: {x: 482; y: 385;};
wreckable: {x: 186; y: 457;};
static: {x: 461; y: 467;};
static: {x: 273; y: 556;};
static: {x: 89; y: 654; tickets: false; boosterType: 'small-jump};
static: {x: 222; y: 664;};
static: {x: 518; y: 754;};
static: {x: 476; y: 979;};
static: {x: 273; y: 986;};
static: {x: 379; y: 1050; tickets: false; boosterType: 'small-jump};
wreckable: {x: 186; y: 1239;};
wreckable: {x: 469; y: 1273;};
static: {x: 330; y: 1303;};
static: {x: 170; y: 1328;};
static: {x: 493; y: 1378;};
static: {x: 193; y: 1474;};
}`
)

applyPreset(
  { id: 210, weight: 10, stringName: '210', tags: ['200'] },
  `preset {
static: {x: 375; y: 153;};
static: {x: 170; y: 213;};
static: {x: 55; y: 346; tickets: false; boosterType: 'small-jump};
static: {x: 255; y: 378;};
static: {x: 292; y: 446;};
wreckable: {x: 195; y: 512;};
static: {x: 344; y: 537;};
static: {x: 123; y: 705;};
static: {x: 274; y: 750; tickets: false; boosterType: 'small-jump};
static: {x: 463; y: 828;};
static: {x: 448; y: 909;};
wreckable: {x: 21; y: 921;};
static: {x: 424; y: 1149;};
static: {x: 167; y: 1176;};
wreckable: {x: 45; y: 1230;};
static: {x: 496; y: 1330;};
static: {x: 212; y: 1356;};
static: {x: 436; y: 1400;};
}`
)

applyPreset(
  { id: 301, weight: 6, stringName: '301', tags: ['300'] },
  `preset {
static: {x: 25; y: 125;};
static: {x: 356; y: 140;};
static: {x: 470; y: 188;};
static: {x: 164; y: 309;};
wreckable: {x: 25; y: 392;};
static: {x: 334; y: 425;};
static: {x: 97; y: 437;};
static: {x: 440; y: 544;};
static: {x: 497; y: 675;};
static: {x: 61; y: 710; tickets: false; boosterType: 'small-jump'};
static: {x: 293; y: 725;};
static: {x: 448; y: 805;};
static: {x: 249; y: 945;tickets: false; boosterType: 'big-jump'};
static: {x: 440; y: 1041;};
static: {x: 50; y: 1071;};
static: {x: 119; y: 1174;};
static: {x: 505; y: 1234;};
wreckable: {x: 278; y: 1292;};
static: {x: 456; y: 1414;};
static: {x: 236; y: 1471;};
static: {x: 26; y: 1491;};
}`
)

applyPreset(
  { id: 302, weight: 6, stringName: '302', tags: ['300'] },
  `preset {
static: {x: 455; y: 100;};
static: {x: 356; y: 140;};
static: {x: 168; y: 309;};
static: {x: 311; y: 339;};
wreckable: {x: 25; y: 392;};
static: {x: 470; y: 397;};
static: {x: 97; y: 437;};
static: {x: 282; y: 460;};
static: {x: 440; y: 544;tickets: false; boosterType: 'big-jump'};
static: {x: 61; y: 710;};
static: {x: 254; y: 742;};
static: {x: 132; y: 795;tickets: false; boosterType: 'small-jump'};
static: {x: 383; y: 837;};
wreckable: {x: 475; y: 909;};
static: {x: 418; y: 950;};
static: {x: 403; y: 1061;};
static: {x: 26; y: 1091;};
static: {x: 289; y: 1144;};
static: {x: 119; y: 1174;};
static: {x: 383; y: 1275;};
static: {x: 456; y: 1414;};
static: {x: 242; y: 1449;};
static: {x: 26; y: 1491;};
}`
)

applyPreset(
  { id: 303, weight: 6, stringName: '303', tags: ['300'] },
  `preset {
static: {x: 514; y: 113;};
static: {x: 234; y: 152;};
static: {x: 444; y: 357; tickets: false; boosterType: 'small-jump'};
static: {x: 98; y: 363;};
static: {x: 34; y: 450; tickets: false; boosterType: 'small-jump'};
static: {x: 190; y: 591;};
static: {x: 498; y: 598;};
static: {x: 276; y: 672;};
static: {x: 140; y: 698;};
static: {x: 87; y: 852;};
wreckable: {x: 251; y: 927;};
static: {x: 493; y: 936;};
wreckable: {x: 280; y: 986;};
static: {x: 7; y: 1106;};
static: {x: 277; y: 1122;};
static: {x: 206; y: 1270;};
static: {x: 439; y: 1327;};
wreckable: {x: 522; y: 1395;};
static: {x: 33; y: 1403;};
static: {x: 163; y: 1422;};
static: {x: 388; y: 1484;};
static: {x: 251; y: 1566;};
}`
)

applyPreset(
  { id: 304, weight: 6, stringName: '304', tags: ['300'] },
  `preset {
static: {x: 223; y: 121;};
static: {x: 444; y: 137;};
static: {x: 519; y: 191;};
static: {x: 501; y: 329;};
static: {x: 58; y: 332; tickets: false; boosterType: 'small-jump'};
static: {x: 296; y: 443;};
static: {x: 465; y: 621;};
static: {x: 282; y: 621;};
static: {x: 100; y: 756; tickets: false; boosterType: 'small-jump'};
wreckable: {x: 246; y: 866;};
static: {x: 480; y: 880;};
wreckable: {x: 395; y: 938;};
static: {x: 497; y: 986;};
static: {x: 82; y: 1026;};
wreckable: {x: 113; y: 1124;};
wreckable: {x: 450; y: 1128;};
static: {x: 370; y: 1165;};
static: {x: 153; y: 1175;};
static: {x: 10; y: 1220;};
static: {x: 332; y: 1372;};
static: {x: 317; y: 1429;};
static: {x: 177; y: 1459;};
}`
)

applyPreset(
  { id: 305, weight: 6, stringName: '305', tags: ['300'] },
  `preset {
static: {x: 54; y: 122;};
static: {x: 457; y: 130;};
static: {x: 280; y: 171;};
wreckable: {x: 488; y: 277;};
static: {x: 436; y: 368;};
static: {x: 316; y: 426;};
static: {x: 495; y: 428;};
static: {x: 354; y: 507;};
static: {x: 344; y: 627; tickets: false; boosterType: 'small-jump'};
static: {x: 124; y: 691;};
static: {x: 47; y: 741; tickets: false; boosterType: 'small-jump'};
static: {x: 482; y: 947;};
static: {x: 300; y: 1027;};
static: {x: 415; y: 1147;};
static: {x: 217; y: 1216;};
wreckable: {x: 47; y: 1219;};
static: {x: 489; y: 1296;};
wreckable: {x: 311; y: 1303;};
static: {x: 267; y: 1388;};
wreckable: {x: 427; y: 1476;};
static: {x: 26; y: 1477;};
}`
)

applyPreset(
  { id: 306, weight: 6, stringName: '306', tags: ['300'] },
  `preset {
static: {x: 505; y: 146;};
static: {x: 99; y: 155;};
static: {x: 281; y: 196;};
static: {x: 110; y: 343;};
static: {x: 511; y: 459;};
wreckable: {x: 353; y: 467;};
static: {x: 151; y: 525;};
static: {x: 375; y: 568;};
static: {x: 58; y: 588; tickets: false; boosterType: 'small-jump'};
static: {x: 211; y: 612;};
wreckable: {x: 468; y: 680;};
static: {x: 386; y: 728;};
static: {x: 28; y: 815;};
static: {x: 410; y: 825;};
static: {x: 398; y: 918; tickets: false; boosterType: 'small-jump'};
static: {x: 165; y: 1088;};
static: {x: 239; y: 1145;};
static: {x: 113; y: 1298;};
wreckable: {x: 473; y: 1318;};
static: {x: 255; y: 1457;};
}`
)

applyPreset(
  { id: 307, weight: 6, stringName: '307', tags: ['300'] },
  `preset {
static: {x: 57; y: 120;};
static: {x: 305; y: 158;};
wreckable: {x: 269; y: 363;};
static: {x: 151; y: 388;};
static: {x: 20; y: 451;};
wreckable: {x: 500; y: 459;};
static: {x: 384; y: 489;};
static: {x: 68; y: 535;};
static: {x: 304; y: 568;};
static: {x: 30; y: 781;};
static: {x: 320; y: 831; tickets: false; boosterType: 'small-jump'};
static: {x: 26; y: 870;};
static: {x: 56; y: 1083;};
static: {x: 474; y: 1101;};
static: {x: 108; y: 1170;};
static: {x: 500; y: 1182;};
static: {x: 59; y: 1270;};
static: {x: 197; y: 1379;};
static: {x: 463; y: 1425;};
static: {x: 156; y: 1489;};
}`
)

applyPreset(
  { id: 308, weight: 6, stringName: '308', tags: ['300'] },
  `preset {
static: {x: 433; y: 112;};
static: {x: 108; y: 143;};
static: {x: 230; y: 200;};
wreckable: {x: 439; y: 225;};
static: {x: 326; y: 277;};
static: {x: 410; y: 326;};
wreckable: {x: 256; y: 351;};
static: {x: 181; y: 418;};
static: {x: 89; y: 531;};
wreckable: {x: 245; y: 666;};
static: {x: 47; y: 685; tickets: false; boosterType: 'small-jump'};
static: {x: 345; y: 890;};
static: {x: 507; y: 936;};
static: {x: 136; y: 1140;};
static: {x: 457; y: 1154;};
static: {x: 74; y: 1271;};
static: {x: 312; y: 1314;};
static: {x: 466; y: 1381;};
wreckable: {x: 511; y: 1473;};
static: {x: 57; y: 1506;};
}`
)

applyPreset(
  { id: 309, weight: 6, stringName: '309', tags: ['300'] },
  `preset {
static: {x: 212; y: 94;};
static: {x: 66; y: 109;};
static: {x: 486; y: 166; tickets: false; boosterType: 'big-jump'};
static: {x: 372; y: 184;};
wreckable: {x: 139; y: 309;};
static: {x: 45; y: 358;};
static: {x: 159; y: 377; tickets: false; boosterType: 'small-jump'};
static: {x: 452; y: 499;};
static: {x: 509; y: 550;};
static: {x: 196; y: 772;};
static: {x: 315; y: 772;};
wreckable: {x: 159; y: 823;};
wreckable: {x: 377; y: 965;};
static: {x: 155; y: 1022;};
static: {x: 239; y: 1060; tickets: false; boosterType: 'big-jump'};
static: {x: 41; y: 1290;};
static: {x: 123; y: 1339;};
static: {x: 282; y: 1354;};
static: {x: 408; y: 1469;};
static: {x: 45; y: 1515;};
static: {x: 507; y: 1515;};
}`
)

applyPreset(
  { id: 310, weight: 6, stringName: '310', tags: ['300'] },
  `preset {
static: {x: 31; y: 75;};
static: {x: 159; y: 90; tickets: false; boosterType: 'big-jump'};
static: {x: 495; y: 180;};
wreckable: {x: 139; y: 309;};
static: {x: 282; y: 368;};
static: {x: 159; y: 377; tickets: false; boosterType: 'small-jump'};
static: {x: 452; y: 499;};
static: {x: 349; y: 529;};
static: {x: 25; y: 603;};
static: {x: 520; y: 633;};
static: {x: 315; y: 772;};
wreckable: {x: 196; y: 778;};
static: {x: 406; y: 808;};
wreckable: {x: 182; y: 1000;};
static: {x: 45; y: 1030; tickets: false; boosterType: 'small-jump'};
static: {x: 155; y: 1060;};
static: {x: 491; y: 1060;};
static: {x: 476; y: 1290;};
static: {x: 381; y: 1320;};
static: {x: 282; y: 1354;};
static: {x: 491; y: 1485;};
static: {x: 374; y: 1515;};
}`
)

applyPreset(
  { id: 311, weight: 6, stringName: '311', tags: ['300'] },
  `preset {
static: {x: 489; y: 75;};
static: {x: 361; y: 90; tickets: false; boosterType: 'big-jump'};
static: {x: 25; y: 180;};
wreckable: {x: 381; y: 309;};
static: {x: 238; y: 368;};
static: {x: 361; y: 377; tickets: false; boosterType: 'small-jump'};
static: {x: 68; y: 499;};
static: {x: 171; y: 529;};
static: {x: 495; y: 603;};
static: {x: 11; y: 633;};
static: {x: 205; y: 772;};
wreckable: {x: 324; y: 778;};
static: {x: 114; y: 808;};
wreckable: {x: 338; y: 1000;};
static: {x: 475; y: 1030; tickets: false; boosterType: 'small-jump'};
static: {x: 29; y: 1060;};
static: {x: 365; y: 1060;};
static: {x: 44; y: 1290;};
static: {x: 139; y: 1320;};
static: {x: 238; y: 1354;};
static: {x: 29; y: 1485;};
static: {x: 515; y: 1485;};
static: {x: 146; y: 1515;};
static: {x: 422; y: 1517;};
}`
)

applyPreset(
  { id: 312, weight: 6, stringName: '312', tags: ['300'] },
  `preset {
static: {x: 212; y: 94;};
static: {x: 66; y: 109;};
static: {x: 509; y: 139;};
static: {x: 334; y: 173; tickets: false; boosterType: 'big-jump'};
wreckable: {x: 139; y: 309;};
static: {x: 159; y: 377; tickets: false; boosterType: 'small-jump'};
static: {x: 351; y: 422;};
static: {x: 25; y: 514;};
static: {x: 509; y: 550;};
static: {x: 82; y: 727;};
wreckable: {x: 159; y: 823;};
static: {x: 401; y: 838;};
wreckable: {x: 377; y: 965;};
static: {x: 139; y: 980;};
static: {x: 282; y: 1126; tickets: false; boosterType: 'small-jump'};
static: {x: 491; y: 1207;};
static: {x: 32; y: 1237;};
static: {x: 339; y: 1309;};
static: {x: 123; y: 1339;};
static: {x: 344; y: 1455;};
static: {x: 66; y: 1522;};
static: {x: 508; y: 1535;};
}`
)

applyPreset(
  { id: 313, weight: 6, stringName: '313', tags: ['300'] },
  `preset {
static: {x: 194; y: 94;};
static: {x: 18; y: 109;};
static: {x: 509; y: 139;};
static: {x: 344; y: 154;};
wreckable: {x: 139; y: 309;};
static: {x: 159; y: 377; tickets: false; boosterType: 'small-jump'};
static: {x: 287; y: 377; tickets: false; boosterType: 'big-jump'};
static: {x: 32; y: 558;};
static: {x: 469; y: 573;};
static: {x: 145; y: 757;};
wreckable: {x: 216; y: 813;};
static: {x: 434; y: 880;};
static: {x: 139; y: 980;};
wreckable: {x: 66; y: 1020;};
static: {x: 282; y: 1126; tickets: false; boosterType: 'small-jump'};
static: {x: 501; y: 1246;};
static: {x: 25; y: 1261;};
static: {x: 248; y: 1345;};
static: {x: 444; y: 1375;};
static: {x: 3; y: 1485;};
static: {x: 248; y: 1512;};
static: {x: 508; y: 1535;};
}`
)

applyPreset(
  { id: 314, weight: 6, stringName: '314', tags: ['300'] },
  `preset {
static: {x: 353; y: 94;};
static: {x: 513; y: 94;};
static: {x: 18; y: 109;};
static: {x: 180; y: 154;};
static: {x: 273; y: 247; tickets: false; boosterType: 'big-jump'};
wreckable: {x: 139; y: 309;};
static: {x: 44; y: 407;};
static: {x: 273; y: 507; tickets: false; boosterType: 'small-jump'};
wreckable: {x: 399; y: 592;};
static: {x: 491; y: 655;};
static: {x: 56; y: 805;};
static: {x: 456; y: 862;};
static: {x: 273; y: 910; tickets: false; boosterType: 'small-jump'};
static: {x: 82; y: 1074;};
wreckable: {x: 320; y: 1159;};
static: {x: 501; y: 1189;};
static: {x: 239; y: 1273;};
static: {x: 434; y: 1360;};
static: {x: 25; y: 1375;};
static: {x: 113; y: 1450;};
static: {x: 513; y: 1482;};
static: {x: 239; y: 1535;};
}`
)

applyPreset(
  { id: 315, weight: 6, stringName: '315', tags: ['300'] },
  `preset {
static: {x: 168; y: 94;};
static: {x: 513; y: 94;};
static: {x: 18; y: 109;};
static: {x: 353; y: 167;};
static: {x: 479; y: 294;};
wreckable: {x: 139; y: 309;};
static: {x: 44; y: 407;};
wreckable: {x: 273; y: 437;};
static: {x: 273; y: 507; tickets: false; boosterType: 'small-jump'};
static: {x: 75; y: 607; tickets: false; boosterType: 'big-jump'};
static: {x: 266; y: 610;};
static: {x: 456; y: 616; tickets: false; boosterType: 'small-jump'};
static: {x: 373; y: 805;};
static: {x: 139; y: 892;};
static: {x: 66; y: 1107;};
static: {x: 469; y: 1107;};
wreckable: {x: 168; y: 1174;};
static: {x: 273; y: 1241;};
static: {x: 456; y: 1345;};
static: {x: 123; y: 1375;};
static: {x: 513; y: 1482;};
static: {x: 18; y: 1505;};
static: {x: 239; y: 1535;};
}`
)

applyPreset(
  { id: 316, weight: 6, stringName: '316', tags: ['300'] },
  `preset {
static: {x: 342; y: 94;};
static: {x: 18; y: 109;};
static: {x: 487; y: 167;};
static: {x: 196; y: 197;};
wreckable: {x: 139; y: 309;};
static: {x: 430; y: 382; tickets: false; boosterType: 'small-jump'};
static: {x: 25; y: 412; tickets: false; boosterType: 'small-jump'};
wreckable: {x: 273; y: 437;};
static: {x: 253; y: 537; tickets: false; boosterType: 'big-jump'};
static: {x: 487; y: 688;};
static: {x: 285; y: 790;};
static: {x: 48; y: 904;};
static: {x: 416; y: 1016;};
static: {x: 111; y: 1092;};
wreckable: {x: 168; y: 1174;};
static: {x: 346; y: 1204;};
static: {x: 48; y: 1298;};
static: {x: 510; y: 1315;};
static: {x: 285; y: 1369;};
static: {x: 473; y: 1497;};
static: {x: 85; y: 1513;};
}`
)

applyPreset(
  { id: 317, weight: 4, stringName: '317', tags: ['300'] },
  `preset {
static: {x: 342; y: 94;};
static: {x: 18; y: 109;};
static: {x: 487; y: 167;};
wreckable: {x: 97; y: 258;};
static: {x: 416; y: 258; tickets: false; boosterType: 'small-jump'};
static: {x: 217; y: 452;};
wreckable: {x: 373; y: 552;};
static: {x: 54; y: 652; tickets: false; boosterType: 'small-jump'};
static: {x: 440; y: 667;};
static: {x: 232; y: 815;};
static: {x: 476; y: 933;};
static: {x: 75; y: 945;};
static: {x: 253; y: 1077; tickets: false; boosterType: 'big-jump'};
static: {x: 40; y: 1174;};
static: {x: 430; y: 1174;};
wreckable: {x: 206; y: 1204;};
static: {x: 48; y: 1298;};
static: {x: 320; y: 1328;};
static: {x: 476; y: 1467;};
static: {x: 132; y: 1527;};
}`
)

applyPreset(
  { id: 401, weight: 8, stringName: '401', tags: ['400'] },
  `preset {
static: {x: 476; y: 127;};
static: {x: 83; y: 157;};
wreckable: {x: 98; y: 216;};
static: {x: 313; y: 216;boosterType: 'small-fly'};
static: {x: 41; y: 505;};
static: {x: 62; y: 719;};
static: {x: 419; y: 749;};
static: {x: 41; y: 980;};
static: {x: 416; y: 1031;};
wreckable: {x: 44; y: 1060;};
wreckable: {x: 483; y: 1204;};
static: {x: 263; y: 1234;};
static: {x: 206; y: 1428;};
static: {x: 27; y: 1452;};
static: {x: 483; y: 1513;};
}`
)

applyPreset(
  { id: 402, weight: 8, stringName: '402', tags: ['400'] },
  `preset {
static: {x: 476; y: 127;};
static: {x: 27; y: 156;};
static: {x: 176; y: 186;};
wreckable: {x: 382; y: 186;};
static: {x: 302; y: 231;};
static: {x: 141; y: 434;boosterType: 'small-fly'};
static: {x: 44; y: 634;};
static: {x: 245; y: 749;};
static: {x: 416; y: 867;};
static: {x: 158; y: 995;};
static: {x: 496; y: 1020;};
wreckable: {x: 44; y: 1060;};
wreckable: {x: 483; y: 1204;};
static: {x: 44; y: 1219;};
static: {x: 215; y: 1347;};
static: {x: 382; y: 1428;};
static: {x: 27; y: 1452;};
static: {x: 483; y: 1513;};
}`
)

applyPreset(
  { id: 403, weight: 8, stringName: '403', tags: ['400'] },
  `preset {
static: {x: 315; y: 135;};
static: {x: 28; y: 142;};
static: {x: 486; y: 210;};
static: {x: 177; y: 225;};
static: {x: 429; y: 388;boosterType: 'small-fly'};
static: {x: 142; y: 536;};
wreckable: {x: 263; y: 566;};
static: {x: 44; y: 772;};
static: {x: 158; y: 995;};
static: {x: 496; y: 1020;};
static: {x: 44; y: 1060;};
wreckable: {x: 483; y: 1204;};
static: {x: 44; y: 1219;};
static: {x: 407; y: 1271;};
static: {x: 238; y: 1373;};
static: {x: 62; y: 1498;};
static: {x: 483; y: 1513;};
}`
)

applyPreset(
  { id: 404, weight: 8, stringName: '404', tags: ['400'] },
  `preset {
static: {x: 437; y: 136;};
wreckable: {x: 392; y: 208;};
static: {x: 131; y: 245; tickets: false; boosterType: 'small-fly'};
wreckable: {x: 264; y: 258;};
static: {x: 428; y: 303;};
static: {x: 429; y: 379;};
static: {x: 210; y: 405; tickets: false; boosterType: 'small-jump'};
static: {x: 94; y: 471;};
static: {x: 252; y: 496;};
static: {x: 206; y: 606;};
static: {x: 58; y: 608;};
static: {x: 57; y: 717;};
static: {x: 333; y: 742;};
static: {x: 416; y: 810;};
static: {x: 524; y: 916;};
static: {x: 114; y: 1128;};
static: {x: 307; y: 1140;};
static: {x: 422; y: 1301;};
static: {x: 298; y: 1358;};
static: {x: 507; y: 1382;};
static: {x: 41; y: 1397;};
static: {x: 193; y: 1463;};
}`
)

applyPreset(
  { id: 405, weight: 8, stringName: '405', tags: ['400'] },
  `preset {
static: {x: 258; y: 126;};
static: {x: 495; y: 198;};
static: {x: 180; y: 228;};
wreckable: {x: 198; y: 267;};
static: {x: 334; y: 322; tickets: false; boosterType: 'small-fly'};
static: {x: 523; y: 548; tickets: false; boosterType: 'small-jump'};
wreckable: {x: 322; y: 686;};
static: {x: 142; y: 751;};
static: {x: 13; y: 755;};
static: {x: 330; y: 763;};
static: {x: 185; y: 800;};
static: {x: 136; y: 910;};
static: {x: 322; y: 925;};
static: {x: 140; y: 965;};
static: {x: 268; y: 1077;};
static: {x: 308; y: 1156;};
static: {x: 100; y: 1159;};
static: {x: 438; y: 1319;};
static: {x: 312; y: 1322;};
static: {x: 192; y: 1374;};
static: {x: 432; y: 1460;};
}`
)

applyPreset(
  { id: 406, weight: 8, stringName: '406', tags: ['400'] },
  `preset {
static: {x: 304; y: 161;};
static: {x: 59; y: 221; tickets: false; boosterType: 'small-fly'};
wreckable: {x: 481; y: 236;};
static: {x: 219; y: 342;};
static: {x: 16; y: 456;};
static: {x: 158; y: 471;};
static: {x: 314; y: 553;};
static: {x: 454; y: 561;};
static: {x: 18; y: 767;};
static: {x: 522; y: 846;};
static: {x: 115; y: 889;};
wreckable: {x: 206; y: 934;};
static: {x: 507; y: 968;};
static: {x: 177; y: 1049;};
static: {x: 469; y: 1121;};
static: {x: 83; y: 1140;};
wreckable: {x: 15; y: 1177;};
static: {x: 425; y: 1209;};
static: {x: 513; y: 1272;};
static: {x: 334; y: 1273;};
static: {x: 202; y: 1334;};
}`
)

applyPreset(
  { id: 407, weight: 8, stringName: '407', tags: ['400'] },
  `preset {
static: {x: 428; y: 121;};
static: {x: 31; y: 176; tickets: false; boosterType: 'small-fly'};
static: {x: 262; y: 242;};
static: {x: 93; y: 358; tickets: false; boosterType: 'small-jump'};
static: {x: 252; y: 449;};
static: {x: 170; y: 498;};
static: {x: 72; y: 622;};
wreckable: {x: 397; y: 642;};
static: {x: 278; y: 669;};
wreckable: {x: 11; y: 694;};
static: {x: 432; y: 870;};
static: {x: 513; y: 919;};
wreckable: {x: 315; y: 947;};
static: {x: 10; y: 995;};
static: {x: 485; y: 1047;};
static: {x: 41; y: 1048;};
static: {x: 319; y: 1165;};
static: {x: 186; y: 1292;};
static: {x: 352; y: 1313;};
static: {x: 496; y: 1318;};
static: {x: 174; y: 1343;};
static: {x: 15; y: 1402;};
}`
)

applyPreset(
  { id: 408, weight: 8, stringName: '408', tags: ['400'] },
  `preset {
static: {x: 269; y: 94;};
wreckable: {x: 31; y: 132;};
static: {x: 489; y: 190;};
static: {x: 126; y: 205; tickets: false; boosterType: 'small-fly'};
static: {x: 515; y: 422;};
static: {x: 401; y: 598;};
wreckable: {x: 248; y: 720;};
static: {x: 71; y: 735;};
wreckable: {x: 71; y: 901;};
static: {x: 85; y: 1020;};
static: {x: 341; y: 1086;};
static: {x: 511; y: 1189;};
wreckable: {x: 526; y: 1362;};
static: {x: 28; y: 1434;};
static: {x: 320; y: 1434;};
static: {x: 105; y: 1517;};
}`
)

applyPreset(
  { id: 409, weight: 8, stringName: '409', tags: ['400'] },
  `preset {
static: {x: 382; y: 85;};
static: {x: 503; y: 105; tickets: false; boosterType: 'small-fly'};
static: {x: 332; y: 245;};
static: {x: 166; y: 275;};
static: {x: 44; y: 407;};
static: {x: 446; y: 536;};
wreckable: {x: 263; y: 566;};
static: {x: 101; y: 646;};
static: {x: 434; y: 740;};
static: {x: 118; y: 841;};
static: {x: 484; y: 933;};
wreckable: {x: 44; y: 1060;};
static: {x: 227; y: 1124;};
wreckable: {x: 496; y: 1145;};
static: {x: 44; y: 1191;};
static: {x: 526; y: 1348;};
static: {x: 372; y: 1483;};
static: {x: 62; y: 1498;};
}`
)

applyPreset(
  { id: 410, weight: 8, stringName: '410', tags: ['400'] },
  `preset {
static: {x: 382; y: 85;};
static: {x: 41; y: 124; tickets: false; boosterType: 'small-fly'};
static: {x: 382; y: 163;};
static: {x: 486; y: 210;};
static: {x: 206; y: 407;};
static: {x: 412; y: 444;};
wreckable: {x: 263; y: 566;};
static: {x: 101; y: 646;};
static: {x: 491; y: 661;};
static: {x: 412; y: 716;};
static: {x: 118; y: 841;};
static: {x: 484; y: 933;};
static: {x: 263; y: 1024;};
wreckable: {x: 176; y: 1076;};
static: {x: 44; y: 1191;};
static: {x: 101; y: 1251;};
wreckable: {x: 469; y: 1281;};
static: {x: 496; y: 1336;};
static: {x: 62; y: 1498;};
static: {x: 439; y: 1513;};
}`
)

applyPreset(
  { id: 411, weight: 10, stringName: '411', tags: ['400'] },
  `preset {
static: {x: 304; y: 91;};
static: {x: 247; y: 139; tickets: false; boosterType: 'small-fly'};
static: {x: 26; y: 316;};
static: {x: 526; y: 331;};
static: {x: 175; y: 414;};
static: {x: 412; y: 444;};
wreckable: {x: 263; y: 566;};
static: {x: 44; y: 612;};
static: {x: 499; y: 676;};
static: {x: 298; y: 753;};
static: {x: 44; y: 901;};
static: {x: 263; y: 974;};
wreckable: {x: 184; y: 1009;};
static: {x: 487; y: 1024;};
static: {x: 44; y: 1191;};
static: {x: 241; y: 1251;};
wreckable: {x: 469; y: 1281;};
static: {x: 439; y: 1321;};
static: {x: 127; y: 1444;};
static: {x: 320; y: 1528;};
}`
)

applyPreset(
  { id: 412, weight: 10, stringName: '412', tags: ['400'] },
  `preset {
static: {x: 11; y: 77;};
static: {x: 487; y: 92;};
static: {x: 289; y: 190; tickets: false; boosterType: 'small-fly'};
static: {x: 20; y: 392;};
static: {x: 470; y: 480;};
static: {x: 68; y: 487;};
static: {x: 267; y: 517;};
wreckable: {x: 263; y: 566;};
static: {x: 101; y: 729;};
static: {x: 508; y: 790;};
static: {x: 267; y: 798;};
wreckable: {x: 263; y: 934;};
static: {x: 60; y: 987;};
static: {x: 477; y: 1035;};
static: {x: 287; y: 1176;};
static: {x: 44; y: 1232;};
wreckable: {x: 394; y: 1262;};
static: {x: 469; y: 1322;};
static: {x: 158; y: 1408;};
static: {x: 432; y: 1513;};
}`
)

applyPreset(
  { id: 501, weight: 8, stringName: '501', tags: ['500'] },
  `preset {
wreckable: {x: 41; y: 158;};
static: {x: 443; y: 244;};
static: {x: 65; y: 293;};
static: {x: 497; y: 293;};
static: {x: 280; y: 476;};
static: {x: 47; y: 547;};
static: {x: 440; y: 661;};
wreckable: {x: 263; y: 805;};
static: {x: 98; y: 917;boosterType: 'small-jump'};
static: {x: 426; y: 917;};
wreckable: {x: 483; y: 956;};
static: {x: 406; y: 1122;};
static: {x: 236; y: 1246;};
static: {x: 122; y: 1354;};
static: {x: 500; y: 1475;};
static: {x: 369; y: 1505;};
}`
)

applyPreset(
  { id: 502, weight: 8, stringName: '502', tags: ['500'] },
  `preset {
wreckable: {x: 241; y: 111;};
static: {x: 47; y: 143;boosterType: 'big-jump'};
static: {x: 440; y: 143;};
static: {x: 241; y: 254;};
static: {x: 469; y: 482;};
static: {x: 83; y: 512;};
wreckable: {x: 184; y: 646;};
static: {x: 355; y: 735;boosterType: 'small-jump'};
wreckable: {x: 500; y: 872;};
static: {x: 469; y: 966;};
static: {x: 70; y: 1033;};
static: {x: 412; y: 1176;};
static: {x: 301; y: 1324;};
static: {x: 68; y: 1424;};
static: {x: 282; y: 1455;};
static: {x: 497; y: 1515;};
}`
)

applyPreset(
  { id: 503, weight: 8, stringName: '503', tags: ['500'] },
  `preset {
static: {x: 320; y: 157;};
static: {x: 166; y: 178;};
static: {x: 41; y: 242;boosterType: 'small-jump'};
static: {x: 473; y: 257;boosterType: 'small-jump'};
wreckable: {x: 263; y: 381;};
static: {x: 305; y: 427;};
static: {x: 263; y: 603;};
static: {x: 84; y: 731;};
wreckable: {x: 443; y: 751;};
static: {x: 443; y: 861;};
wreckable: {x: 263; y: 1038;};
static: {x: 41; y: 1053;};
static: {x: 483; y: 1166;};
wreckable: {x: 386; y: 1291;};
static: {x: 121; y: 1389;};
static: {x: 362; y: 1465;};
static: {x: 27; y: 1514;};
}`
)

applyPreset(
  { id: 504, weight: 8, stringName: '504', tags: ['500'] },
  `preset {
static: {x: 437; y: 86;};
wreckable: {x: 392; y: 138;};
static: {x: 131; y: 175;boosterType: 'small-jump'};
wreckable: {x: 264; y: 188;};
static: {x: 428; y: 233;};
static: {x: 429; y: 309;};
static: {x: 210; y: 335;};
static: {x: 94; y: 401;};
static: {x: 252; y: 426;};
static: {x: 206; y: 536;};
static: {x: 58; y: 538;};
static: {x: 57; y: 647;};
static: {x: 333; y: 672;};
static: {x: 416; y: 740;};
static: {x: 524; y: 846;};
static: {x: 114; y: 1058;};
static: {x: 307; y: 1070;};
static: {x: 422; y: 1231;};
static: {x: 298; y: 1288;};
static: {x: 507; y: 1312;};
wreckable: {x: 328; y: 1508;};
}`
)

applyPreset(
  { id: 505, weight: 8, stringName: '505', tags: ['500'] },
  `preset {
static: {x: 312; y: 12;};
static: {x: 460; y: 51;};
static: {x: 276; y: 88;};
static: {x: 86; y: 155;boosterType: 'small-jump'};
static: {x: 260; y: 165;};
wreckable: {x: 361; y: 221;};
static: {x: 170; y: 245;};
static: {x: 222; y: 392;};
static: {x: 33; y: 515;};
static: {x: 63; y: 701;};
static: {x: 309; y: 711;};
static: {x: 433; y: 726;};
wreckable: {x: 427; y: 799;};
static: {x: 386; y: 946;};
static: {x: 195; y: 1031;};
static: {x: 415; y: 1039;};
static: {x: 466; y: 1079;};
static: {x: 400; y: 1262;};
static: {x: 96; y: 1271;};
static: {x: 38; y: 1332;};
wreckable: {x: 432; y: 1411;};
static: {x: 144; y: 1421;};
static: {x: 45; y: 1484;};
}`
)

applyPreset(
  { id: 506, weight: 8, stringName: '506', tags: ['500'] },
  `preset {
static: {x: 516; y: 95;};
static: {x: 426; y: 132;};
static: {x: 347; y: 178; tikets: false; boosterType: 'small-jump'};
static: {x: 27; y: 370; tikets: false; boosterType: 'small-jump'};
wreckable: {x: 263; y: 381;};
static: {x: 359; y: 550;};
static: {x: 263; y: 603;};
static: {x: 84; y: 731;};
wreckable: {x: 443; y: 751;};
static: {x: 27; y: 820;};
wreckable: {x: 263; y: 1038;};
static: {x: 369; y: 1108;};
static: {x: 483; y: 1166;};
wreckable: {x: 386; y: 1291;};
static: {x: 161; y: 1495;};
static: {x: 27; y: 1514;};
static: {x: 290; y: 1525;};
}`
)

applyPreset(
  { id: 507, weight: 8, stringName: '507', tags: ['500'] },
  `preset {
static: {x: 22; y: 83;};
static: {x: 491; y: 91;};
static: {x: 320; y: 106;};
static: {x: 136; y: 116;};
static: {x: 295; y: 355;};
static: {x: 434; y: 408;};
static: {x: 136; y: 415;};
static: {x: 49; y: 549;};
static: {x: 500; y: 589;};
wreckable: {x: 49; y: 685;};
wreckable: {x: 434; y: 750;};
static: {x: 136; y: 805;};
static: {x: 307; y: 856;};
static: {x: 220; y: 890;};
static: {x: 106; y: 924;};
static: {x: 22; y: 965;};
wreckable: {x: 272; y: 1009;};
static: {x: 386; y: 1145;};
static: {x: 491; y: 1293;};
static: {x: 443; y: 1441;};
static: {x: 79; y: 1456;};
static: {x: 272; y: 1510;};
}`
)

applyPreset(
  { id: 508, weight: 8, stringName: '508', tags: ['500'] },
  `preset {
static: {x: 22; y: 83;};
static: {x: 329; y: 83;};
static: {x: 491; y: 124;};
static: {x: 79; y: 183;};
static: {x: 355; y: 254;};
static: {x: 63; y: 284;};
static: {x: 412; y: 403;};
static: {x: 49; y: 514;};
static: {x: 500; y: 562;};
wreckable: {x: 49; y: 685;};
static: {x: 434; y: 704;};
wreckable: {x: 329; y: 753;};
static: {x: 136; y: 805;};
static: {x: 272; y: 919;};
static: {x: 22; y: 965;};
wreckable: {x: 272; y: 1009;};
static: {x: 443; y: 1072;};
static: {x: 106; y: 1125;};
static: {x: 220; y: 1248;};
static: {x: 526; y: 1278;};
static: {x: 120; y: 1356;};
static: {x: 443; y: 1441;};
static: {x: 36; y: 1474;};
static: {x: 272; y: 1510;};
}`
)

applyPreset(
  { id: 509, weight: 8, stringName: '509', tags: ['500'] },
  `preset {
static: {x: 1428; y: 124;};
static: {x: 469; y: 175;};
static: {x: 219; y: 209;};
static: {x: 20; y: 286;};
static: {x: 65; y: 324;};
static: {x: 469; y: 392;};
static: {x: 412; y: 443;};
wreckable: {x: 227; y: 458;};
static: {x: 219; y: 689;};
static: {x: 419; y: 695;};
static: {x: 20; y: 725;};
static: {x: 370; y: 758;};
wreckable: {x: 227; y: 903;};
static: {x: 484; y: 933;};
static: {x: 427; y: 969;};
static: {x: 93; y: 1109;};
static: {x: 227; y: 1124;};
static: {x: 20; y: 1147;};
static: {x: 427; y: 1218;};
static: {x: 496; y: 1263;};
wreckable: {x: 227; y: 1355;};
static: {x: 20; y: 1453;};
static: {x: 370; y: 1468;};
static: {x: 77; y: 1498;};
static: {x: 313; y: 1510;};
}`
)

applyPreset(
  { id: 510, weight: 10, stringName: '510', tags: ['500'] },
  `preset {
static: {x: 1193; y: 81;};
static: {x: 214; y: 81;};
static: {x: 429; y: 124;};
static: {x: 469; y: 175;};
static: {x: 20; y: 286;};
static: {x: 134; y: 333;};
static: {x: 469; y: 392;};
static: {x: 250; y: 407;};
static: {x: 412; y: 443;};
wreckable: {x: 227; y: 458;};
static: {x: 484; y: 507;};
static: {x: 45; y: 648;};
static: {x: 170; y: 710;};
static: {x: 298; y: 777;};
static: {x: 400; y: 840;};
static: {x: 227; y: 903;};
wreckable: {x: 484; y: 933;};
static: {x: 427; y: 969;};
static: {x: 8; y: 1010;};
static: {x: 113; y: 1055;};
static: {x: 298; y: 1139;};
static: {x: 427; y: 1218;};
static: {x: 8; y: 1254;};
static: {x: 496; y: 1263;};
wreckable: {x: 227; y: 1355;};
static: {x: 20; y: 1453;};
static: {x: 370; y: 1468;};
static: {x: 77; y: 1498;};
static: {x: 313; y: 1510;};
static: {x: 496; y: 1525;};
}`
)

applyPreset(
  { id: 511, weight: 10, stringName: '511', tags: ['500'] },
  `preset {
static: {x: 1383; y: 81;};
static: {x: 214; y: 81;};
static: {x: 429; y: 124;};
static: {x: 20; y: 286;};
static: {x: 227; y: 332;};
static: {x: 412; y: 422;};
wreckable: {x: 227; y: 458;};
static: {x: 77; y: 477;};
static: {x: 484; y: 507;};
static: {x: 457; y: 633;};
static: {x: 45; y: 648;};
static: {x: 241; y: 678;};
static: {x: 400; y: 825;};
static: {x: 102; y: 865;};
wreckable: {x: 227; y: 903;};
static: {x: 8; y: 1010;};
static: {x: 496; y: 1044;};
static: {x: 145; y: 1093;};
static: {x: 286; y: 1224;};
static: {x: 8; y: 1254;};
static: {x: 496; y: 1263;};
wreckable: {x: 227; y: 1355;};
static: {x: 343; y: 1438;};
static: {x: 20; y: 1453;};
static: {x: 134; y: 1498;};
static: {x: 496; y: 1525;};
}`
)

applyPreset(
  { id: 512, weight: 10, stringName: '512', tags: ['500'] },
  `preset {
static: {x: ; y: 124;};
static: {x: 469; y: 175;};
static: {x: 219; y: 209;};
static: {x: 65; y: 324;};
static: {x: 469; y: 392;};
wreckable: {x: 227; y: 458;};
static: {x: 237; y: 517;};
static: {x: 219; y: 689;};
static: {x: 491; y: 695;};
static: {x: 20; y: 725;};
static: {x: 93; y: 903;};
wreckable: {x: 227; y: 903;};
static: {x: 427; y: 969;};
static: {x: 122; y: 1084;};
static: {x: 496; y: 1114;};
static: {x: 227; y: 1124;};
static: {x: 20; y: 1147;};
static: {x: 427; y: 1288;};
wreckable: {x: 227; y: 1355;};
static: {x: 20; y: 1453;};
static: {x: 496; y: 1478;};
static: {x: 113; y: 1495;};
static: {x: 313; y: 1510;};
}`
)

applyPreset(
  { id: 601, weight: 10, stringName: '601', tags: ['600'] },
  `preset {
static: {x: 21; y: 83;};
static: {x: 483; y: 105;};
wreckable: {x: 280; y: 137;};
static: {x: 78; y: 236; tickets: false; };
static: {x: 426; y: 339;};
static: {x: 280; y: 476;};
wreckable: {x: 337; y: 538; tickets: false; };
static: {x: 21; y: 609;};
static: {x: 369; y: 712;};
wreckable: {x: 41; y: 805; tickets: false; };
static: {x: 98; y: 917;};
static: {x: 312; y: 967;};
static: {x: 446; y: 1176;};
static: {x: 41; y: 1225;};
wreckable: {x: 398; y: 1273; tickets: false; };
static: {x: 263; y: 1381;};
static: {x: 41; y: 1506;};
static: {x: 483; y: 1521;};
}`
)

applyPreset(
  { id: 602, weight: 10, stringName: '602', tags: ['600'] },
  `preset {
wreckable: {x: 206; y: 157; tickets: false; };
static: {x: 389; y: 166;};
static: {x: 62; y: 226; tickets: false; };
static: {x: 503; y: 302;};
static: {x: 426; y: 454;};
static: {x: 326; y: 639;};
wreckable: {x: 263; y: 750; tickets: false; };
static: {x: 206; y: 872;};
wreckable: {x: 236; y: 1005; tickets: false; };
static: {x: 85; y: 1020;};
static: {x: 341; y: 1086;};
static: {x: 503; y: 1249;};
wreckable: {x: 483; y: 1349; tickets: false; };
static: {x: 48; y: 1449;};
static: {x: 483; y: 1449;};
static: {x: 284; y: 1530;};
}`
)

applyPreset(
  { id: 603, weight: 10, stringName: '603', tags: ['600'] },
  `preset {
static: {x: 149; y: 106; tickets: false; };
static: {x: 353; y: 196;};
wreckable: {x: 526; y: 302; tickets: false; };
static: {x: 426; y: 407;};
static: {x: 239; y: 466;};
static: {x: 56; y: 496;};
wreckable: {x: 11; y: 639; tickets: false; };
static: {x: 125; y: 750;};
wreckable: {x: 263; y: 858; tickets: false; };
static: {x: 369; y: 932;};
static: {x: 512; y: 1101;};
static: {x: 394; y: 1169;};
wreckable: {x: 222; y: 1249; tickets: false; };
static: {x: 98; y: 1344;};
static: {x: 11; y: 1459;};
static: {x: 155; y: 1520;};
}`
)

applyPreset(
  { id: 604, weight: 10, stringName: '604', tags: ['600'] },
  `preset {
static: {x: 333; y: 77;};
static: {x: 366; y: 163;};
wreckable: {x: 24; y: 248;};
wreckable: {x: 15; y: 383;};
static: {x: 492; y: 393;};
static: {x: 511; y: 445; tickets: false; };
static: {x: 410; y: 711;};
static: {x: 77; y: 798;};
static: {x: 496; y: 805;};
static: {x: 252; y: 891;};
static: {x: 137; y: 934;};
static: {x: 465; y: 989;};
static: {x: 514; y: 1082;};
static: {x: 461; y: 1221;};
static: {x: 100; y: 1229;};
static: {x: 198; y: 1369;};
static: {x: 391; y: 1402;};
wreckable: {x: 150; y: 1454;};
static: {x: 268; y: 1490;};
static: {x: 43; y: 1496;};
}`
)

applyPreset(
  { id: 605, weight: 10, stringName: '605', tags: ['600'] },
  `preset {
static: {x: 215; y: 155;};
static: {x: 344; y: 168;};
wreckable: {x: 186; y: 230;};
wreckable: {x: 280; y: 316;};
static: {x: 86; y: 463;};
static: {x: 294; y: 518;};
static: {x: 208; y: 594;};
static: {x: 25; y: 723;};
static: {x: 424; y: 841;};
static: {x: 48; y: 897;};
wreckable: {x: 303; y: 917;};
static: {x: 44; y: 973;};
static: {x: 300; y: 1009;};
static: {x: 459; y: 1036;};
static: {x: 417; y: 1139;};
static: {x: 202; y: 1160;};
static: {x: 341; y: 1347;};
static: {x: 483; y: 1355;};
wreckable: {x: 117; y: 1357;};
static: {x: 269; y: 1504;};
}`
)

applyPreset(
  { id: 606, weight: 10, stringName: '606', tags: ['600'] },
  `preset {
static: {x: 78; y: 84;};
static: {x: 458; y: 84;};
wreckable: {x: 377; y: 302;};
static: {x: 296; y: 332;};
static: {x: 135; y: 561;};
static: {x: 458; y: 585;};
static: {x: 68; y: 600;};
static: {x: 515; y: 624;};
wreckable: {x: 11; y: 639;};
static: {x: 327; y: 828;};
wreckable: {x: 263; y: 858;};
static: {x: 182; y: 888;};
static: {x: 310; y: 1127;};
static: {x: 252; y: 1180;};
wreckable: {x: 222; y: 1249;};
static: {x: 41; y: 1290;};
static: {x: 509; y: 1423;};
static: {x: 54; y: 1438;};
static: {x: 474; y: 1480;};
static: {x: 155; y: 1520;};
}`
)

applyPreset(
  { id: 607, weight: 10, stringName: '607', tags: ['600'] },
  `preset {
static: {x: 253; y: 84;};
static: {x: 458; y: 84;};
static: {x: 139; y: 130;};
static: {x: 41; y: 186;};
wreckable: {x: 41; y: 282;};
static: {x: 253; y: 297;};
static: {x: 409; y: 422;};
static: {x: 41; y: 456;};
wreckable: {x: 41; y: 538;};
static: {x: 466; y: 568;};
static: {x: 279; y: 639;};
static: {x: 41; y: 805;};
wreckable: {x: 41; y: 858;};
static: {x: 279; y: 925;};
static: {x: 393; y: 979;};
static: {x: 41; y: 1032;};
wreckable: {x: 41; y: 1180;};
static: {x: 488; y: 1230;};
wreckable: {x: 222; y: 1249;};
static: {x: 41; y: 1290;};
static: {x: 296; y: 1290;};
static: {x: 474; y: 1480;};
static: {x: 30; y: 1510;};
static: {x: 155; y: 1520;};
static: {x: 374; y: 1520;};
}`
)

applyPreset(
  { id: 608, weight: 10, stringName: '608', tags: ['600'] },
  `preset {
static: {x: 28; y: 99;};
static: {x: 220; y: 99;};
static: {x: 497; y: 115;};
static: {x: 296; y: 199;};
wreckable: {x: 41; y: 229;};
static: {x: 424; y: 260;};
wreckable: {x: 497; y: 290;};
static: {x: 41; y: 400;};
static: {x: 310; y: 464;};
static: {x: 310; y: 538;};
wreckable: {x: 296; y: 577;};
static: {x: 320; y: 630;};
static: {x: 182; y: 644;};
wreckable: {x: 263; y: 683;};
static: {x: 468; y: 790;};
static: {x: 41; y: 805;};
static: {x: 239; y: 976;};
static: {x: 367; y: 976;};
wreckable: {x: 182; y: 1017;};
wreckable: {x: 206; y: 1069;};
static: {x: 50; y: 1102;};
wreckable: {x: 296; y: 1117;};
wreckable: {x: 367; y: 1166;};
static: {x: 336; y: 1219;};
wreckable: {x: 222; y: 1249;};
static: {x: 92; y: 1357;};
static: {x: 139; y: 1483;};
static: {x: 512; y: 1498;};
static: {x: 377; y: 1505;};
static: {x: 25; y: 1520;};
}`
)

applyPreset(
  { id: 609, weight: 10, stringName: '609', tags: ['600'] },
  `preset {
static: {x: 220; y: 99;};
static: {x: 497; y: 115;};
static: {x: 35; y: 130;};
wreckable: {x: 41; y: 229;};
wreckable: {x: 440; y: 252;};
static: {x: 98; y: 275;};
static: {x: 367; y: 297;};
static: {x: 310; y: 464;};
static: {x: 310; y: 538;};
static: {x: 71; y: 559;};
wreckable: {x: 263; y: 683;};
static: {x: 481; y: 713;};
static: {x: 41; y: 806;};
static: {x: 320; y: 876;};
wreckable: {x: 353; y: 925;};
static: {x: 367; y: 1023;};
static: {x: 174; y: 1053;};
wreckable: {x: 398; y: 1068;};
static: {x: 41; y: 1212;};
wreckable: {x: 82; y: 1249;};
static: {x: 367; y: 1340;};
static: {x: 92; y: 1357;};
static: {x: 512; y: 1513;};
static: {x: 60; y: 1520;};
}`
)

applyPreset(
  { id: 610, weight: 10, stringName: '610', tags: ['600'] },
  `preset {
static: {x: 26; y: 85;};
static: {x: 239; y: 125;};
static: {x: 481; y: 140;};
wreckable: {x: 155; y: 199;};
static: {x: 41; y: 229;};
static: {x: 334; y: 267;};
static: {x: 440; y: 434;};
static: {x: 174; y: 494;};
static: {x: 310; y: 538;};
static: {x: 71; y: 559;};
wreckable: {x: 60; y: 683;};
wreckable: {x: 353; y: 701;};
static: {x: 481; y: 713;};
static: {x: 41; y: 806;};
wreckable: {x: 320; y: 876;};
static: {x: 497; y: 1003;};
wreckable: {x: 398; y: 1068;};
static: {x: 424; y: 1133;};
static: {x: 269; y: 1136;};
static: {x: 41; y: 1212;};
wreckable: {x: 82; y: 1249;};
wreckable: {x: 25; y: 1301;};
static: {x: 424; y: 1331;};
static: {x: 512; y: 1513;};
static: {x: 60; y: 1520;};
static: {x: 310; y: 1528;};
}`
)

applyPreset(
  { id: 701, weight: 8, stringName: '701', tags: ['700'] },
  `preset {
static: {x: 329; y: 115;};
static: {x: 423; y: 216;};
wreckable: {x: 500; y: 309; tickets: false; };
static: {x: 162; y: 354;};
static: {x: 386; y: 587;};
static: {x: 70; y: 617;};
static: {x: 289; y: 659;};
static: {x: 465; y: 911;};
wreckable: {x: 127; y: 1012; tickets: false; };
empty: {x: 415; y: 1159; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 119; y: 1174; tickets: false; boosterType: 'big-jump'};
static: {x: 284; y: 1400;};
static: {x: 474; y: 1445;};
static: {x: 127; y: 1504; tickets: false; boosterType: 'big-fly'};
}`
)

applyPreset(
  { id: 702, weight: 8, stringName: '702', tags: ['700'] },
  `preset {
static: {x: 284; y: 127;};
static: {x: 48; y: 145;};
static: {x: 465; y: 187;};
wreckable: {x: 341; y: 324; tickets: false; };
static: {x: 162; y: 354;};
static: {x: 474; y: 602;};
static: {x: 299; y: 617;};
static: {x: 160; y: 675;};
wreckable: {x: 471; y: 867; tickets: false; };
static: {x: 127; y: 925;};
wreckable: {x: 170; y: 1003; tickets: false; };
empty: {x: 471; y: 1085; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 119; y: 1174;};
static: {x: 375; y: 1301;};
static: {x: 515; y: 1489;};
static: {x: 127; y: 1504; tickets: false; boosterType: 'big-fly'};
}`
)

applyPreset(
  { id: 703, weight: 8, stringName: '703', tags: ['700'] },
  `preset {
static: {x: 21; y: 117;};
static: {x: 489; y: 217;};
static: {x: 135; y: 299;};
static: {x: 305; y: 329;};
static: {x: 70; y: 481;};
static: {x: 458; y: 544;};
static: {x: 151; y: 739;};
wreckable: {x: 94; y: 856; tickets: false; };
empty: {x: 305; y: 814; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 419; y: 930;};
static: {x: 284; y: 1117;};
static: {x: 398; y: 1336;};
static: {x: 70; y: 1373; tickets: false; boosterType: 'big-fly'};
static: {x: 515; y: 1489;};
}`
)

applyPreset(
  { id: 704, weight: 8, stringName: '704', tags: ['700'] },
  `preset {
static: {x: 329; y: 115;};
static: {x: 423; y: 216;};
wreckable: {x: 443; y: 317;};
static: {x: 320; y: 385;};
static: {x: 22; y: 557;};
static: {x: 145; y: 617;};
static: {x: 346; y: 805;};
static: {x: 170; y: 982;};
empty: {x: 61; y: 1012; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 184; y: 1278;};
static: {x: 15; y: 1351; tickets: false; boosterType: 'big-fly'};
static: {x: 360; y: 1439;};
static: {x: 502; y: 1521;};
}`
)

applyPreset(
  { id: 705, weight: 8, stringName: '705', tags: ['700'] },
  `preset {
static: {x: 434; y: 149;};
static: {x: 79; y: 189;};
static: {x: 288; y: 400;};
static: {x: 145; y: 441;};
static: {x: 402; y: 441;};
static: {x: 41; y: 632;};
wreckable: {x: 491; y: 647;};
static: {x: 166; y: 805;};
static: {x: 377; y: 835;};
wreckable: {x: 166; y: 949; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 22; y: 1027;};
empty: {x: 259; y: 1012;};
static: {x: 474; y: 1057;};
wreckable: {x: 330; y: 1137;};
static: {x: 244; y: 1256;};
static: {x: 46; y: 1403; tickets: false; boosterType: 'big-fly'};
static: {x: 445; y: 1403;};
static: {x: 259; y: 1526;};
}`
)

applyPreset(
  { id: 706, weight: 8, stringName: '706', tags: ['700'] },
  `preset {
static: {x: 434; y: 149;};
static: {x: 79; y: 189;};
static: {x: 500; y: 274;};
static: {x: 259; y: 344;};
static: {x: 434; y: 398;};
static: {x: 106; y: 400;};
static: {x: 31; y: 574;};
static: {x: 500; y: 589;};
static: {x: 163; y: 739;};
static: {x: 360; y: 739;};
static: {x: 31; y: 989;};
empty: {x: 259; y: 949; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 500; y: 997;};
static: {x: 179; y: 1177;};
static: {x: 398; y: 1295;};
static: {x: 46; y: 1403; tickets: false; boosterType: 'big-fly'};
static: {x: 491; y: 1469;};
static: {x: 259; y: 1526;};
}`
)

applyPreset(
  { id: 707, weight: 8, stringName: '707', tags: ['700'] },
  `preset {
static: {x: 515; y: 87;};
static: {x: 21; y: 117;};
static: {x: 265; y: 147;};
static: {x: 106; y: 202;};
static: {x: 489; y: 217;};
static: {x: 349; y: 334;};
static: {x: 70; y: 481;};
wreckable: {x: 46; y: 617;};
static: {x: 406; y: 644;};
static: {x: 151; y: 739;};
wreckable: {x: 305; y: 748;};
wreckable: {x: 94; y: 856;};
empty: {x: 305; y: 814; tickets: false; mobType: 'dynamic-horizontal-small'};
wreckable: {x: 512; y: 856;};
static: {x: 455; y: 940;};
wreckable: {x: 292; y: 959;};
wreckable: {x: 489; y: 1102;};
static: {x: 248; y: 1117;};
wreckable: {x: 134; y: 1256;};
static: {x: 398; y: 1336; tickets: false; boosterType: 'big-fly'};
static: {x: 70; y: 1373;};
static: {x: 515; y: 1489;};
}`
)

applyPreset(
  { id: 708, weight: 8, stringName: '708', tags: ['700'] },
  `preset {
static: {x: 362; y: 112;};
static: {x: 21; y: 117;};
static: {x: 489; y: 217;};
static: {x: 135; y: 299;};
static: {x: 305; y: 329;};
wreckable: {x: 449; y: 380;};
static: {x: 70; y: 481;};
wreckable: {x: 401; y: 496;};
wreckable: {x: 295; y: 627;};
static: {x: 37; y: 669;};
empty: {x: 525; y: 603; tickets: false; mobType: 'black-hole'};
wreckable: {x: 305; y: 749;};
static: {x: 151; y: 809;};
wreckable: {x: 463; y: 821;};
static: {x: 305; y: 895;};
empty: {x: 77; y: 1070; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 477; y: 1112;};
static: {x: 238; y: 1221;};
static: {x: 419; y: 1343;};
static: {x: 70; y: 1373; tickets: false; boosterType: 'big-fly'};
static: {x: 515; y: 1489;};
static: {x: 216; y: 1533;};
}`
)

applyPreset(
  { id: 709, weight: 8, stringName: '709', tags: ['700'] },
  `preset {
static: {x: 21; y: 117;};
static: {x: 499; y: 117;};
static: {x: 263; y: 169;};
static: {x: 419; y: 284;};
static: {x: 102; y: 324;};
wreckable: {x: 499; y: 380;};
static: {x: 273; y: 476;};
static: {x: 184; y: 624;};
static: {x: 385; y: 639;};
wreckable: {x: 216; y: 704;};
static: {x: 46; y: 714;};
wreckable: {x: 362; y: 721;};
static: {x: 499; y: 772;};
empty: {x: 23; y: 874; tickets: false; mobType: 'dynamic-horizontal-middle' };
wreckable: {x: 21; y: 1021;};
wreckable: {x: 184; y: 1040;};
wreckable: {x: 330; y: 1067;};
static: {x: 499; y: 1067;};
static: {x: 305; y: 1206;};
static: {x: 21; y: 1236;};
static: {x: 70; y: 1373; tickets: false; boosterType: 'big-fly'};
static: {x: 476; y: 1403;};
static: {x: 295; y: 1519;};
}`
)

applyPreset(
  { id: 710, weight: 6, stringName: '710', tags: ['700'] },
  `preset {
static: {x: 49; y: 108;};
static: {x: 500; y: 135;};
static: {x: 234; y: 180;};
static: {x: 406; y: 295;};
static: {x: 463; y: 425;};
static: {x: 78; y: 447;};
static: {x: 443; y: 632;};
static: {x: 142; y: 650;};
static: {x: 276; y: 748; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 463; y: 918;};
static: {x: 265; y: 1072;};
static: {x: 406; y: 1247;};
static: {x: 70; y: 1277; tickets: false; boosterType: 'big-fly'};
static: {x: 500; y: 1431;};
static: {x: 184; y: 1474;};
}`
)

applyPreset(
  { id: 711, weight: 6, stringName: '711', tags: ['700'] },
  `preset {
static: {x: 163; y: 98;};
static: {x: 500; y: 98;};
static: {x: 329; y: 155;};
static: {x: 22; y: 233;};
static: {x: 295; y: 355;};
static: {x: 434; y: 408;};
static: {x: 136; y: 415;};
static: {x: 49; y: 549;};
static: {x: 500; y: 589;};
wreckable: {x: 49; y: 685;};
static: {x: 320; y: 738; tickets: false; };
empty: {x: 440; y: 738; tickets: false; mobType: 'static'};
static: {x: 443; y: 738; tickets: false; };
wreckable: {x: 136; y: 788;};
static: {x: 287; y: 884; tickets: false; boosterType: 'big-fly'};
wreckable: {x: 287; y: 1124;};
static: {x: 287; y: 1192;};
static: {x: 434; y: 1314;};
static: {x: 106; y: 1406;};
static: {x: 466; y: 1434;};
static: {x: 193; y: 1494;};
static: {x: 352; y: 1494;};
}`
)

applyPreset(
  { id: 712, weight: 7, stringName: '712', tags: ['700'] },
  `preset {
static: {x: 41; y: 85;};
static: {x: 320; y: 121;};
static: {x: 488; y: 225;};
static: {x: 130; y: 284;};
wreckable: {x: 443; y: 317;};
static: {x: 434; y: 389;};
static: {x: 244; y: 490;};
static: {x: 98; y: 659;};
wreckable: {x: 423; y: 829;};
static: {x: 212; y: 844;};
static: {x: 409; y: 971;};
static: {x: 438; y: 1028; tickets: false; mobType: 'dynamic-horizontal-small'};
wreckable: {x: 155; y: 1073;};
static: {x: 70; y: 1159;};
static: {x: 352; y: 1205;};
static: {x: 127; y: 1356;};
static: {x: 423; y: 1413; tickets: false; boosterType: 'big-fly'};
static: {x: 184; y: 1521;};
static: {x: 502; y: 1521;};
}`
)

applyPreset(
  { id: 801, weight: 8, stringName: '801', tags: ['800'] },
  `preset {
static: {x: 426; y: 134;};
static: {x: 287; y: 192;};
static: {x: 69; y: 222; tickets: false; boosterType: 'small-jump'};
wreckable: {x: 500; y: 309;};
dynamic_h: {x: 263; y: 441;};
static: {x: 426; y: 644; tickets: false; boosterType: 'big-jump'};
static: {x: 263; y: 851;};
dynamic_v: {x: 39; y: 1028; movementRange: [150, 150];  movementDuration: [2800, 3000]; };
static: {x: 390; y: 1129;};
wreckable: {x: 483; y: 1204;};
static: {x: 101; y: 1209;};
dynamic_h: {x: 276; y: 1347;};
static: {x: 215; y: 1488;};
}`
)

applyPreset(
  { id: 802, weight: 8, stringName: '802', tags: ['800'] },
  `preset {
static: {x: 60; y: 93;};
static: {x: 263; y: 294;};
static: {x: 433; y: 493;};
static: {x: 69; y: 608; tickets: false; boosterType: 'big-jump'};
static: {x: 366; y: 694;};
wreckable: {x: 475; y: 748;};
dynamic_h: {x: 206; y: 871;};
static: {x: 84; y: 1088;};
static: {x: 443; y: 1352;};
wreckable: {x: 109; y: 1367;};
static: {x: 206; y: 1416;};
static: {x: 480; y: 1493; tickets: false; boosterType: 'small-jump'};
}`
)

applyPreset(
  { id: 803, weight: 8, stringName: '803', tags: ['800'] },
  `preset {
static: {x: 206; y: 108;};
invisible: {x: 350; y: 191; visible: true};
static: {x: 35; y: 232; tickets: false; boosterType: 'small-jump'};
static: {x: 236; y: 362;};
invisible: {x: 455; y: 366; tickets: false;  visible: false};
static: {x: 92; y: 456; tickets: false; boosterType: 'big-jump'};
static: {x: 426; y: 538;};
invisible: {x: 252; y: 553; tickets: false;  visible: false};
wreckable: {x: 492; y: 658;};
invisible: {x: 236; y: 778; tickets: false;  visible: false};
static: {x: 426; y: 778;};
dynamic_h: {x: 475; y: 987;};
static: {x: 84; y: 1143;};
static: {x: 378; y: 1234;};
wreckable: {x: 33; y: 1321;};
static: {x: 206; y: 1397;};
static: {x: 480; y: 1493; tickets: false; boosterType: 'small-jump'};
static: {x: 33; y: 1523;};
}`
)

applyPreset(
  { id: 804, weight: 8, stringName: '804', tags: ['800'] },
  `preset {
static: {x: 206; y: 108;};
wreckable: {x: 65; y: 153;};
static: {x: 350; y: 181; tickets: false; boosterType: 'small-jump'};
static: {x: 35; y: 232;};
static: {x: 492; y: 247;};
static: {x: 236; y: 362;};
static: {x: 35; y: 423;};
static: {x: 475; y: 481;};
shifting: {x: 211; y: 572;};
shifting: {x: 268; y: 688;};
dynamic_h: {x: 345; y: 815;};
shifting: {x: 179; y: 918;};
shifting: {x: 382; y: 1040;};
shifting: {x: 206; y: 1147;};
shifting: {x: 361; y: 1289;};
static: {x: 206; y: 1397;};
static: {x: 476; y: 1451;};
static: {x: 28; y: 1466;};
wreckable: {x: 293; y: 1466;};
}`
)

applyPreset(
  { id: 805, weight: 8, stringName: '805', tags: ['800'] },
  `preset {
static: {x: 28; y: 78;};
static: {x: 263; y: 93;};
static: {x: 496; y: 108;};
wreckable: {x: 136; y: 138;};
wreckable: {x: 377; y: 166;};
shifting: {x: 56; y: 232;};
shifting: {x: 193; y: 423;};
wreckable: {x: 359; y: 481;};
shifting: {x: 476; y: 587;};
shifting: {x: 250; y: 718;};
wreckable: {x: 142; y: 805;};
shifting: {x: 85; y: 888;};
shifting: {x: 170; y: 1070;};
shifting: {x: 284; y: 1177;};
shifting: {x: 307; y: 1289;};
static: {x: 476; y: 1451;};
static: {x: 28; y: 1466;};
static: {x: 382; y: 1496;};
}`
)

applyPreset(
  { id: 806, weight: 8, stringName: '806', tags: ['800'] },
  `preset {
static: {x: 13; y: 93;};
static: {x: 496; y: 93;};
static: {x: 274; y: 123;};
shifting: {x: 56; y: 232;};
shifting: {x: 70; y: 390;};
shifting: {x: 79; y: 548;};
shifting: {x: 85; y: 676;};
static: {x: 79; y: 766;};
static: {x: 467; y: 805;};
shifting: {x: 449; y: 912;};
shifting: {x: 449; y: 1076;};
shifting: {x: 449; y: 1248;};
shifting: {x: 378; y: 1397;};
static: {x: 492; y: 1466;};
static: {x: 240; y: 1481;};
static: {x: 79; y: 1496;};
static: {x: 382; y: 1496;};
}`
)

applyPreset(
  { id: 807, weight: 8, stringName: '807', tags: ['800'] },
  `preset {
static: {x: 499; y: 93;};
static: {x: 206; y: 108;};
static: {x: 56; y: 138;};
static: {x: 326; y: 162;};
static: {x: 239; y: 251;};
wreckable: {x: 62; y: 435;};
static: {x: 385; y: 538;};
wreckable: {x: 492; y: 658;};
static: {x: 59; y: 849;};
wreckable: {x: 271; y: 879;};
static: {x: 84; y: 1143;};
static: {x: 404; y: 1143;};
wreckable: {x: 41; y: 1278;};
static: {x: 499; y: 1336;};
dynamic_h: {x: 415; y: 1412;};
wreckable: {x: 499; y: 1496;};
static: {x: 5; y: 1526;};
}`
)

applyPreset(
  { id: 808, weight: 8, stringName: '808', tags: ['800'] },
  `preset {
static: {x: 500; y: 99;};
static: {x: 55; y: 101; tickets: false; boosterType: 'small-jump'};
static: {x: 209; y: 165;};
disposable: {x: 315; y: 236;};
disposable: {x: 356; y: 423;};
disposable: {x: 55; y: 534;};
disposable: {x: 426; y: 597;};
disposable: {x: 131; y: 717;};
static: {x: 277; y: 820;};
disposable: {x: 483; y: 886;};
disposable: {x: 320; y: 1021;};
dynamic_h: {x: 272; y: 1268;};
disposable: {x: 95; y: 1395;};
disposable: {x: 397; y: 1408;};
static: {x: 38; y: 1438;};
disposable: {x: 500; y: 1441;};
static: {x: 283; y: 1521;};
}`
)

applyPreset(
  { id: 809, weight: 8, stringName: '809', tags: ['800'] },
  `preset {
static: {x: 30; y: 99;};
static: {x: 426; y: 114;};
static: {x: 206; y: 129;};
dynamic_h: {x: 38; y: 265;};
dynamic_h: {x: 483; y: 378;};
dynamic_h: {x: 67; y: 534;};
disposable: {x: 426; y: 597;};
disposable: {x: 131; y: 717;};
disposable: {x: 511; y: 746;};
static: {x: 277; y: 820;};
disposable: {x: 50; y: 873;};
disposable: {x: 454; y: 930;};
disposable: {x: 251; y: 1006;};
disposable: {x: 38; y: 1113;};
disposable: {x: 413; y: 1113;};
disposable: {x: 219; y: 1253;};
disposable: {x: 397; y: 1365;};
disposable: {x: 95; y: 1395;};
disposable: {x: 501; y: 1491;};
}`
)

applyPreset(
  { id: 810, weight: 8, stringName: '810', tags: ['800'] },
  `preset {
static: {x: 206; y: 78;};
static: {x: 14; y: 93;};
static: {x: 509; y: 134;};
shifting: {x: 149; y: 244;};
invisible: {x: 286; y: 358; visible: true};
shifting: {x: 352; y: 524;};
invisible: {x: 198; y: 708; visible: false};
shifting: {x: 149; y: 858;};
invisible: {x: 442; y: 1012; visible: false};
shifting: {x: 189; y: 1150;};
dynamic_h: {x: 378; y: 1334;};
static: {x: 499; y: 1391;};
invisible: {x: 228; y: 1457; visible: false};
static: {x: 5; y: 1526;};
}`
)

applyPreset(
  { id: 811, weight: 7, stringName: '811', tags: ['800'] },
  `preset {
static: {x: 30; y: 99;};
static: {x: 426; y: 114;};
static: {x: 206; y: 134;};
disposable: {x: 87; y: 271;};
static: {x: 283; y: 377;};
disposable: {x: 454; y: 567;};
disposable: {x: 87; y: 608;};
static: {x: 277; y: 820;};
disposable: {x: 454; y: 930;};
disposable: {x: 50; y: 996;};
disposable: {x: 444; y: 1094;};
static: {x: 263; y: 1129;};
disposable: {x: 95; y: 1159;};
static: {x: 38; y: 1289;};
static: {x: 470; y: 1289;};
static: {x: 426; y: 1432;};
static: {x: 38; y: 1496;};
static: {x: 263; y: 1511;};
}`
)

applyPreset(
  { id: 812, weight: 6, stringName: '812', tags: ['800'] },
  `preset {
static: {x: 41; y: 85;};
static: {x: 263; y: 104;};
static: {x: 491; y: 192;};
static: {x: 81; y: 271;};
wreckable: {x: 443; y: 317;};
static: {x: 434; y: 389;};
static: {x: 295; y: 516;};
static: {x: 98; y: 659;};
empty: {x: 442; y: 674; tickets: false; mobType: 'black-hole'};
static: {x: 212; y: 844;};
wreckable: {x: 445; y: 866;};
static: {x: 41; y: 998;};
wreckable: {x: 155; y: 1073;};
static: {x: 269; y: 1193;};
static: {x: 41; y: 1333;};
static: {x: 445; y: 1363;};
static: {x: 184; y: 1521;};
static: {x: 502; y: 1521;};
}`
)

applyPreset(
  { id: 813, weight: 6, stringName: '813', tags: ['800'] },
  `preset {
static: {x: 41; y: 104;};
static: {x: 331; y: 104;};
static: {x: 502; y: 177;};
wreckable: {x: 366; y: 241;};
static: {x: 184; y: 271;};
static: {x: 81; y: 457;};
static: {x: 217; y: 656;};
static: {x: 469; y: 761;};
wreckable: {x: 445; y: 866;};
static: {x: 331; y: 968;};
static: {x: 41; y: 998;};
wreckable: {x: 195; y: 1085;};
empty: {x: 100; y: 1115; tickets: false; mobType: 'black-hole'};
static: {x: 309; y: 1208;};
static: {x: 469; y: 1363;};
static: {x: 184; y: 1521;};
static: {x: 502; y: 1521;};
}`
)

applyPreset(
  { id: 901, weight: 10, stringName: '901', tags: ['900'] },
  `preset {
static: {x: 60; y: 93;};
static: {x: 263; y: 294;};
dynamic_h: {x: 230; y: 372;};
static: {x: 401; y: 523;};
static: {x: 69; y: 608; tickets: false; boosterType: 'big-jump'};
invisible: {x: 238; y: 659;visible: true};
invisible: {x: 320; y: 835;visible: false};
invisible: {x: 252; y: 997;visible: false};
static: {x: 84; y: 1088;};
invisible: {x: 309; y: 1189;visible: false};
static: {x: 27; y: 1285; tickets: false; boosterType: 'small-jump'};
static: {x: 206; y: 1416;};
}`
)

applyPreset(
  { id: 902, weight: 10, stringName: '902', tags: ['900'] },
  `preset {
static: {x: 43; y: 150; tickets: false; boosterType: 'small-jump'};
static: {x: 245; y: 184; tickets: false; };
static: {x: 470; y: 240; tickets: false; };
disposable: {x: 149; y: 307; tickets: false; };
static: {x: 312; y: 389; tickets: false; };
disposable: {x: 33; y: 434; tickets: false; };
static: {x: 426; y: 538; tickets: false; };
disposable: {x: 198; y: 651; tickets: false; };
static: {x: 512; y: 748; tickets: false; };
disposable: {x: 426; y: 853; tickets: false; };
static: {x: 59; y: 926;};
dynamic_h: {x: 223; y: 1130;};
disposable: {x: 423; y: 1216;};
static: {x: 483; y: 1314;};
static: {x: 18; y: 1329;};
disposable: {x: 149; y: 1405;};
disposable: {x: 26; y: 1501;};
}`
)

applyPreset(
  { id: 903, weight: 10, stringName: '903', tags: ['900'] },
  `preset {
static: {x: 19; y: 133; tickets: false; boosterType: 'small-jump'};
static: {x: 173; y: 175;};
static: {x: 483; y: 190;};
static: {x: 273; y: 281;};
dynamic_h: {x: 263; y: 441;};
static: {x: 263; y: 737;};
dynamic_v: {x: 500; y: 633;movementRange: [450, 450];  movementDuration: [3000, 3000];};
dynamic_v: {x: 39; y: 1202;movementRange: [-450, -450];  movementDuration: [3000, 3000];};
static: {x: 263; y: 1025;};
dynamic_h: {x: 263; y: 1327;};
static: {x: 60; y: 1477;};
static: {x: 469; y: 1507;};
}`
)

applyPreset(
  { id: 904, weight: 10, stringName: '904', tags: ['900'] },
  `preset {
static: {x: 368; y: 124;};
static: {x: 19; y: 133; tickets: false; boosterType: 'small-jump'};
static: {x: 173; y: 175;};
static: {x: 483; y: 190;};
static: {x: 263; y: 509;};
static: {x: 320; y: 592;};
dynamic_h: {x: 500; y: 697;};
static: {x: 263; y: 892;};
dynamic_h: {x: 195; y: 944;};
dynamic_v: {x: 42; y: 1004; movementRange: [150, 150];  movementDuration: [2800, 3000]; };
static: {x: 500; y: 1085;};
static: {x: 263; y: 1206;};
static: {x: 412; y: 1340;};
static: {x: 500; y: 1497;};
static: {x: 156; y: 1512;};
}`
)

applyPreset(
  { id: 905, weight: 10, stringName: '905', tags: ['900'] },
  `preset {
static: {x: 43; y: 150; tickets: false; boosterType: 'small-jump'};
static: {x: 245; y: 184; tickets: false; };
static: {x: 470; y: 240; tickets: false; };
static: {x: 272; y: 386; tickets: false; };
static: {x: 55; y: 473; tickets: false; };
static: {x: 483; y: 549; tickets: false; };
disposable: {x: 272; y: 582; tickets: false; };
disposable: {x: 131; y: 661; tickets: false; };
disposable: {x: 426; y: 676; tickets: false; };
disposable: {x: 55; y: 781; tickets: false; };
disposable: {x: 500; y: 790; tickets: false; };
static: {x: 277; y: 820;};
disposable: {x: 55; y: 886;};
disposable: {x: 483; y: 886;};
disposable: {x: 386; y: 979;};
disposable: {x: 158; y: 983;};
disposable: {x: 277; y: 1039;};
static: {x: 470; y: 1069; tickets: false; };
dynamic_h: {x: 272; y: 1268;};
static: {x: 38; y: 1438; tickets: false; };
static: {x: 283; y: 1521; tickets: false; };
}`
)

applyPreset(
  { id: 906, weight: 10, stringName: '906', tags: ['900'] },
  `preset {
static: {x: 60; y: 93;};
static: {x: 489; y: 93;};
dynamic_h: {x: 301; y: 183;};
invisible: {x: 432; y: 374;visible: true};
invisible: {x: 301; y: 584;visible: false};
invisible: {x: 272; y: 840;visible: false};
invisible: {x: 365; y: 1074;visible: false};
invisible: {x: 397; y: 1258;visible: false};
static: {x: 27; y: 1285;};
static: {x: 485; y: 1336;};
static: {x: 206; y: 1416;};
static: {x: 365; y: 1444;};
static: {x: 22; y: 1484;};
}`
)

applyPreset(
  { id: 907, weight: 10, stringName: '907', tags: ['900'] },
  `preset {
static: {x: 277; y: 103;};
static: {x: 19; y: 133;};
static: {x: 186; y: 154;};
dynamic_h: {x: 116; y: 250;};
dynamic_h: {x: 372; y: 407;};
dynamic_h: {x: 42; y: 560;};
dynamic_h: {x: 509; y: 740;};
static: {x: 263; y: 892;};
dynamic_h: {x: 486; y: 990;};
dynamic_h: {x: 176; y: 1130;};
dynamic_h: {x: 386; y: 1266;};
dynamic_h: {x: 56; y: 1377;};
static: {x: 33; y: 1467;};
static: {x: 500; y: 1497;};
static: {x: 156; y: 1512;};
}`
)

applyPreset(
  { id: 908, weight: 10, stringName: '908', tags: ['900'] },
  `preset {
static: {x: 17; y: 89; tickets: false; boosterType: 'small-jump'};
static: {x: 506; y: 89;};
static: {x: 223; y: 125;};
disposable: {x: 245; y: 328;};
static: {x: 131; y: 510;};
disposable: {x: 280; y: 231;};
static: {x: 59; y: 733;};
disposable: {x: 443; y: 829;};
disposable: {x: 512; y: 965;};
disposable: {x: 43; y: 981;};
dynamic_h: {x: 223; y: 1106;};
disposable: {x: 426; y: 1160;};
disposable: {x: 90; y: 1284;};
static: {x: 18; y: 1329;};
disposable: {x: 443; y: 1389;};
static: {x: 236; y: 1501;};
disposable: {x: 38; y: 1523;};
disposable: {x: 500; y: 1525;};
}`
)

applyPreset(
  { id: 909, weight: 10, stringName: '909', tags: ['900'] },
  `preset {
static: {x: 60; y: 93;};
static: {x: 515; y: 93; tickets: false; boosterType: 'small-jump'};
static: {x: 263; y: 294;};
invisible: {x: 117; y: 478;visible: true};
wreckable: {x: 35; y: 524;};
invisible: {x: 263; y: 671;visible: false};
dynamic_h: {x: 366; y: 980;};
static: {x: 92; y: 1145;};
invisible: {x: 423; y: 1325;visible: false};
disposable: {x: 401; y: 1510;};
}`
)

applyPreset(
  { id: 910, weight: 10, stringName: '910', tags: ['900'] },
  `preset {
static: {x: 35; y: 82; tickets: false; boosterType: 'small-jump'};
static: {x: 492; y: 93;};
dynamic_h: {x: 378; y: 264;};
invisible: {x: 413; y: 478;visible: true};
wreckable: {x: 35; y: 524;};
invisible: {x: 206; y: 686;visible: false};
dynamic_h: {x: 35; y: 844;};
wreckable: {x: 92; y: 890;};
dynamic_h: {x: 60; y: 1160;};
static: {x: 60; y: 1435;};
static: {x: 490; y: 1525;};
}`
)

applyPreset(
  { id: 1001, weight: 15, stringName: '1001', tags: ['1000'] },
  `preset {
static: {x: 500; y: 118; tickets: false; boosterType: 'small-jump'};
static: {x: 245; y: 133;};
static: {x: 60; y: 232;};
dynamic_h: {x: 272; y: 407;};
static: {x: 287; y: 572;};
dynamic_h: {x: 39; y: 659;};
static: {x: 39; y: 787;};
dynamic_h: {x: 272; y: 867;};
static: {x: 426; y: 1072;};
dynamic_h: {x: 60; y: 1266;};
static: {x: 312; y: 1401;};
static: {x: 158; y: 1448;};
static: {x: 483; y: 1502;};
}`
)

applyPreset(
  { id: 1004, weight: 10, stringName: '1004', tags: ['1000'] },
  `preset {
static: {x: 60; y: 93;};
invisible: {x: 459; y: 184; visible: true};
static: {x: 195; y: 255;};
invisible: {x: 389; y: 367; visible: false};
static: {x: 60; y: 408;};
static: {x: 367; y: 569; visible: false};
static: {x: 90; y: 591;};
static: {x: 503; y: 694; tickets: false; boosterType: 'small-jump'};
dynamic_h: {x: 174; y: 795;};
invisible: {x: 401; y: 954; visible: false};
static: {x: 138; y: 1054;};
invisible: {x: 333; y: 1188; visible: false};
static: {x: 116; y: 1298;};
static: {x: 478; y: 1337;};
static: {x: 33; y: 1491;};
}`
)

applyPreset(
  { id: 1005, weight: 10, stringName: '1005', tags: ['1000'] },
  `preset {
static: {x: 61; y: 89;};
static: {x: 253; y: 160;};
static: {x: 497; y: 217;};
static: {x: 39; y: 313;};
static: {x: 326; y: 363;};
static: {x: 160; y: 451;};
static: {x: 440; y: 530;};
static: {x: 10; y: 613;};
dynamic_h: {x: 39; y: 691;};
static: {x: 237; y: 820;};
dynamic_v: {x: 39; y: 970; tickets: false; movementRange: [240, 240];  movementDuration: [2800, 3000];};
dynamic_v: {x: 326; y: 970; tickets: false; movementRange: [240, 240];  movementDuration: [2800, 3000];};
dynamic_v: {x: 188; y: 1208; tickets: false; movementRange: [-240, -240];  movementDuration: [2800, 3000];};
dynamic_v: {x: 483; y: 1208; tickets: false; movementRange: [-240, -240];  movementDuration: [2800, 3000];};
static: {x: 294; y: 1417;};
static: {x: 408; y: 1452;};
static: {x: 81; y: 1477;};
static: {x: 505; y: 1492;};
static: {x: 14; y: 1522;};
}`
)

applyPreset(
  { id: 1007, weight: 10, stringName: '1007', tags: ['1000'] },
  `preset {
static: {x: 22; y: 90;};
static: {x: 501; y: 90;};
static: {x: 215; y: 120;};
wreckable: {x: 377; y: 141;};
static: {x: 101; y: 182;};
wreckable: {x: 501; y: 232;};
static: {x: 302; y: 247;};
static: {x: 466; y: 290;};
static: {x: 117; y: 363;};
dynamic_h: {x: 12; y: 512;};
dynamic_h: {x: 263; y: 611;};
wreckable: {x: 501; y: 725;};
static: {x: 210; y: 804;};
empty: {x: 278; y: 922; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 69; y: 949;};
wreckable: {x: 22; y: 1019;};
static: {x: 151; y: 1123;};
wreckable: {x: 491; y: 1138;};
static: {x: 96; y: 1288;};
wreckable: {x: 265; y: 1318;};
wreckable: {x: 461; y: 1410;};
static: {x: 245; y: 1425;};
static: {x: 404; y: 1490;};
static: {x: 39; y: 1500;};
static: {x: 501; y: 1530;};
}`
)

applyPreset(
  { id: 1008, weight: 15, stringName: '1008', tags: ['1000'] },
  `preset {
static: {x: 61; y: 89;};
static: {x: 497; y: 104;};
static: {x: 403; y: 145;};
static: {x: 253; y: 160;};
wreckable: {x: 81; y: 268;};
static: {x: 39; y: 313;};
dynamic_h: {x: 485; y: 422;};
static: {x: 505; y: 481;};
static: {x: 96; y: 545;};
static: {x: 10; y: 613;};
dynamic_h: {x: 39; y: 719;};
static: {x: 237; y: 820;};
wreckable: {x: 294; y: 871;};
dynamic_h: {x: 448; y: 967;};
static: {x: 237; y: 1088;};
wreckable: {x: 371; y: 1088;};
dynamic_h: {x: 39; y: 1215;};
static: {x: 517; y: 1328;};
static: {x: 408; y: 1452;};
static: {x: 24; y: 1472;};
static: {x: 237; y: 1477;};
static: {x: 505; y: 1492;};
static: {x: 118; y: 1516;};
wreckable: {x: 289; y: 1522;};
}`
)

applyPreset(
  { id: 1009, weight: 15, stringName: '1009', tags: ['1000'] },
  `preset {
static: {x: 22; y: 93;};
wreckable: {x: 215; y: 93;};
disposable: {x: 507; y: 123;};
shifting: {x: 44; y: 252;};
disposable: {x: 89; y: 349;};
shifting: {x: 432; y: 495;};
disposable: {x: 418; y: 636;};
shifting: {x: 167; y: 696;};
disposable: {x: 25; y: 885;};
shifting: {x: 272; y: 988;};
disposable: {x: 505; y: 1168;};
shifting: {x: 136; y: 1258;};
wreckable: {x: 92; y: 1354;};
disposable: {x: 5; y: 1481;};
disposable: {x: 505; y: 1511;};
}`
)

applyPreset(
  { id: 1010, weight: 15, stringName: '1010', tags: ['1000'] },
  `preset {
static: {x: 22; y: 93;};
disposable: {x: 352; y: 108;};
disposable: {x: 507; y: 123;};
disposable: {x: 167; y: 153;};
shifting: {x: 386; y: 367;};
disposable: {x: 263; y: 527;};
shifting: {x: 167; y: 696;};
disposable: {x: 263; y: 913;};
shifting: {x: 272; y: 988;};
shifting: {x: 136; y: 1234;};
disposable: {x: 263; y: 1339;};
shifting: {x: 500; y: 1442;};
disposable: {x: 363; y: 1511;};
disposable: {x: 121; y: 1526;};
}`
)

applyPreset(
  { id: 1011, weight: 10, stringName: '1011', tags: ['1000'] },
  `preset {
static: {x: 5; y: 75;};
static: {x: 511; y: 123;};
dynamic_h: {x: 375; y: 183;};
shifting: {x: 44; y: 252;};
invisible: {x: 432; y: 374; visible: true};
shifting: {x: 432; y: 495;};
invisible: {x: 301; y: 584; visible: false};
shifting: {x: 167; y: 696;};
invisible: {x: 272; y: 840; visible: false};
shifting: {x: 272; y: 988;};
invisible: {x: 365; y: 1074; visible: false};
shifting: {x: 136; y: 1258;};
invisible: {x: 344; y: 1306; visible: false};
static: {x: 13; y: 1390;};
static: {x: 365; y: 1444;};
static: {x: 22; y: 1484;};
static: {x: 517; y: 1499;};
}`
)

applyPreset(
  { id: 1101, weight: 10, stringName: '1101', tags: ['1100'] },
  `preset {
static: {x: 175; y: 113;};
static: {x: 16; y: 143;};
static: {x: 338; y: 143;};
static: {x: 484; y: 173;};
wreckable: {x: 61; y: 224;};
static: {x: 130; y: 287;};
wreckable: {x: 295; y: 385;};
static: {x: 427; y: 463;};
static: {x: 106; y: 544;};
wreckable: {x: 386; y: 585;};
wreckable: {x: 181; y: 653;};
static: {x: 320; y: 700;};
static: {x: 22; y: 830;};
static: {x: 491; y: 852;};
static: {x: 238; y: 937; tickets: false; };
empty: {x: 348; y: 937; tickets: false; mobType: 'static'};
static: {x: 359; y: 937; tickets: false; };
static: {x: 491; y: 1109;};
static: {x: 287; y: 1192; tickets: false;};
static: {x: 35; y: 1249;};
static: {x: 500; y: 1306;};
dynamic_h: {x: 39; y: 1393;};
static: {x: 386; y: 1508;};
}`
)

applyPreset(
  { id: 1102, weight: 10, stringName: '1102', tags: ['1100'] },
  `preset {
static: {x: 284; y: 91;};
static: {x: 49; y: 127;};
static: {x: 489; y: 180;};
shifting: {x: 333; y: 267;};
shifting: {x: 284; y: 337;};
shifting: {x: 341; y: 407;};
shifting: {x: 263; y: 497;};
shifting: {x: 149; y: 558;};
shifting: {x: 377; y: 810;};
shifting: {x: 263; y: 897;};
shifting: {x: 313; y: 979;};
shifting: {x: 227; y: 1061;};
shifting: {x: 313; y: 1166;};
static: {x: 162; y: 1294;};
static: {x: 489; y: 1342;};
static: {x: 35; y: 1417;};
static: {x: 360; y: 1442;};
static: {x: 219; y: 1372;};
static: {x: 489; y: 1522;};
}`
)

applyPreset(
  { id: 1103, weight: 10, stringName: '1103', tags: ['1100'] },
  `preset {
static: {x: 284; y: 91;};
static: {x: 49; y: 127;};
static: {x: 489; y: 180;};
shifting: {x: 227; y: 332;};
shifting: {x: 132; y: 477;};
wreckable: {x: 459; y: 602;};
static: {x: 75; y: 632;};
wreckable: {x: 313; y: 688;};
static: {x: 503; y: 762;};
shifting: {x: 199; y: 845;};
shifting: {x: 320; y: 968;};
wreckable: {x: 501; y: 1056;};
static: {x: 132; y: 1131;};
static: {x: 33; y: 1212;};
static: {x: 489; y: 1342;};
static: {x: 263; y: 1412;};
static: {x: 427; y: 1502;};
static: {x: 48; y: 1508;};
}`
)

applyPreset(
  { id: 1104, weight: 10, stringName: '1104', tags: ['1100'] },
  `preset {
wreckable: {x: 206; y: 157;};
static: {x: 389; y: 166;};
static: {x: 62; y: 226;};
static: {x: 503; y: 302;};
static: {x: 426; y: 454;};
static: {x: 326; y: 639;};
wreckable: {x: 263; y: 750;};
static: {x: 206; y: 872;};
wreckable: {x: 236; y: 1005;};
static: {x: 85; y: 1020;};
static: {x: 341; y: 1131;};
static: {x: 503; y: 1249;};
wreckable: {x: 483; y: 1349;};
static: {x: 48; y: 1449;};
static: {x: 483; y: 1449;};
static: {x: 284; y: 1530;};
}`
)

applyPreset(
  { id: 1105, weight: 10, stringName: '1105', tags: ['1100'] },
  `preset {
static: {x: 173; y: 88;};
static: {x: 343; y: 103;};
wreckable: {x: 478; y: 118;};
static: {x: 478; y: 315;};
static: {x: 272; y: 392;};
dynamic_v: {x: 39; y: 452; movementRange: [-250, -250];  movementDuration: [2400, 2400]; };
dynamic_v: {x: 272; y: 452; movementRange: [150, 150];  movementDuration: [2200, 2200]; };
wreckable: {x: 39; y: 549;};
static: {x: 39; y: 637;};
static: {x: 39; y: 840;};
wreckable: {x: 129; y: 961;};
static: {x: 272; y: 1033;};
dynamic_v: {x: 500; y: 1033; movementRange: [-350, -350];  movementDuration: [3000, 3000]; };
static: {x: 173; y: 1154;};
static: {x: 386; y: 1154;};
static: {x: 39; y: 1217;};
dynamic_h: {x: 272; y: 1332;};
static: {x: 59; y: 1432;};
static: {x: 483; y: 1462;};
}`
)

applyPreset(
  { id: 1106, weight: 10, stringName: '1106', tags: ['1100'] },
  `preset {
static: {x: 206; y: 108;};
static: {x: 32; y: 123;};
static: {x: 490; y: 138;};
dynamic_h: {x: 32; y: 232;};
static: {x: 478; y: 315;};
static: {x: 320; y: 382;};
static: {x: 96; y: 397;};
wreckable: {x: 215; y: 442;};
wreckable: {x: 39; y: 549;};
static: {x: 287; y: 652;};
dynamic_v: {x: 500; y: 695; tickets: false;  movementRange: [150, 150];  movementDuration: [2000, 2000]; };
dynamic_v: {x: 79; y: 840; tickets: false;  movementRange: [150, 150];  movementDuration: [2000, 2000]; };
empty: {x: 320; y: 893; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 173; y: 1154;};
wreckable: {x: 337; y: 1154;};
static: {x: 478; y: 1202;};
static: {x: 39; y: 1217;};
dynamic_h: {x: 272; y: 1332;};
static: {x: 59; y: 1432;};
static: {x: 478; y: 1447;};
static: {x: 293; y: 1462;};
static: {x: 173; y: 1482;};
}`
)

applyPreset(
  { id: 1107, weight: 10, stringName: '1107', tags: ['1100'] },
  `preset {
static: {x: 65; y: 88;};
static: {x: 434; y: 88;};
static: {x: 255; y: 118;};
dynamic_h: {x: 288; y: 197;};
dynamic_h: {x: 515; y: 325;};
dynamic_h: {x: 331; y: 470;};
dynamic_h: {x: 179; y: 599;};
dynamic_h: {x: 27; y: 720;};
dynamic_h: {x: 158; y: 865;};
dynamic_h: {x: 320; y: 967;};
dynamic_h: {x: 479; y: 1080;};
dynamic_h: {x: 313; y: 1197;};
dynamic_h: {x: 173; y: 1297;};
dynamic_h: {x: 39; y: 1397;};
static: {x: 287; y: 1457;};
}`
)

applyPreset(
  { id: 1108, weight: 10, stringName: '1108', tags: ['1100'] },
  `preset {
static: {x: 65; y: 88;};
static: {x: 434; y: 88;};
static: {x: 255; y: 118;};
dynamic_h: {x: 495; y: 247;};
dynamic_h: {x: 411; y: 342;};
dynamic_h: {x: 324; y: 444;};
dynamic_h: {x: 259; y: 557;};
static: {x: 491; y: 614;};
dynamic_v: {x: 345; y: 675; movementRange: [390, 390];  movementDuration: [2800, 2800]; };
dynamic_v: {x: 198; y: 1065; movementRange: [-390, -390];  movementDuration: [2800, 2800]; };
static: {x: 39; y: 1095;};
dynamic_h: {x: 174; y: 1197;};
dynamic_h: {x: 96; y: 1297;};
dynamic_h: {x: 39; y: 1397;};
static: {x: 287; y: 1457;};
static: {x: 495; y: 1492;};
}`
)

applyPreset(
  { id: 1109, weight: 10, stringName: '1109', tags: ['1100'] },
  `preset {
static: {x: 65; y: 88;};
static: {x: 434; y: 88;};
static: {x: 255; y: 118;};
wreckable: {x: 491; y: 133;};
dynamic_v: {x: 15; y: 237; movementRange: [390, 390];  movementDuration: [2500, 2500]; };
dynamic_v: {x: 140; y: 237; movementRange: [390, 390];  movementDuration: [2450, 2450]; };
dynamic_v: {x: 266; y: 237; movementRange: [390, 390];  movementDuration: [2400, 2400]; };
dynamic_v: {x: 390; y: 237; movementRange: [390, 390];  movementDuration: [2350, 2350]; };
dynamic_v: {x: 510; y: 237; movementRange: [390, 390];  movementDuration: [2300, 2300]; };
static: {x: 359; y: 796;};
static: {x: 173; y: 826;};
wreckable: {x: 510; y: 826;};
wreckable: {x: 15; y: 841;};
dynamic_v: {x: 510; y: 928; movementRange: [390, 390];  movementDuration: [2300, 2300]; };
dynamic_v: {x: 390; y: 928; movementRange: [390, 390];  movementDuration: [2350, 2350]; };
dynamic_v: {x: 266; y: 928; movementRange: [390, 390];  movementDuration: [2400, 2400]; };
dynamic_v: {x: 140; y: 928; movementRange: [390, 390];  movementDuration: [2450, 2450]; };
dynamic_v: {x: 15; y: 928; movementRange: [390, 390];  movementDuration: [2500, 2500]; };
wreckable: {x: 416; y: 1427;};
wreckable: {x: 129; y: 1457;};
static: {x: 287; y: 1457;};
static: {x: 495; y: 1492;};
static: {x: 15; y: 1507;};
}`
)

applyPreset(
  { id: 1110, weight: 10, stringName: '1110', tags: ['1100'] },
  `preset {
static: {x: 81; y: 91; tickets: false; };
disposable: {x: 485; y: 106; tickets: false; };
disposable: {x: 327; y: 247; tickets: false; };
disposable: {x: 167; y: 443; tickets: false; };
disposable: {x: 371; y: 518; tickets: false; };
disposable: {x: 70; y: 542; tickets: false; };
disposable: {x: 485; y: 602; tickets: false; };
dynamic_h: {x: 485; y: 757; tickets: false; };
dynamic_h: {x: 24; y: 982; tickets: false; };
disposable: {x: 252; y: 1109; tickets: false; };
disposable: {x: 498; y: 1163; tickets: false; };
disposable: {x: 81; y: 1209; tickets: false; };
disposable: {x: 485; y: 1299; tickets: false; };
disposable: {x: 384; y: 1435; tickets: false; };
disposable: {x: 125; y: 1465; tickets: false; };
disposable: {x: 24; y: 1523; tickets: false; };
}`
)

applyPreset(
  { id: 1201, weight: 9, stringName: '1201', tags: ['1200'] },
  `preset {
static: {x: 362; y: 112;};
static: {x: 21; y: 117;};
static: {x: 489; y: 217;};
dynamic_h: {x: 375; y: 339;};
static: {x: 70; y: 481;};
wreckable: {x: 401; y: 496;};
empty: {x: 525; y: 573; tickets: false; mobType: 'black-hole'};
wreckable: {x: 295; y: 627;};
static: {x: 37; y: 669;};
wreckable: {x: 305; y: 749;};
static: {x: 151; y: 809;};
wreckable: {x: 463; y: 821;};
static: {x: 305; y: 895;};
disposable: {x: 138; y: 985;};
disposable: {x: 77; y: 1141;};
static: {x: 252; y: 1216;};
disposable: {x: 24; y: 1313;};
wreckable: {x: 305; y: 1321;};
static: {x: 272; y: 1383; tickets: false; boosterType: 'small-fly'};
disposable: {x: 71; y: 1474;};
static: {x: 442; y: 1519;};
}`
)

applyPreset(
  { id: 1202, weight: 9, stringName: '1202', tags: ['1200'] },
  `preset {
static: {x: 175; y: 113;};
static: {x: 16; y: 143;};
static: {x: 338; y: 143;};
static: {x: 484; y: 173;};
wreckable: {x: 130; y: 188;};
shifting: {x: 500; y: 274;};
shifting: {x: 443; y: 398;};
shifting: {x: 163; y: 472;};
shifting: {x: 466; y: 529;};
empty: {x: 22; y: 592; tickets: false; mobType: 'dynamic-horizontal-middle' };
shifting: {x: 116; y: 755;};
static: {x: 22; y: 830;};
static: {x: 491; y: 852;};
static: {x: 238; y: 937; tickets: false; };
empty: {x: 348; y: 937; tickets: false; mobType: 'static'};
static: {x: 359; y: 937; tickets: false; };
wreckable: {x: 287; y: 1124;};
static: {x: 287; y: 1192; tickets: false; boosterType: 'big-fly'};
static: {x: 434; y: 1314;};
static: {x: 106; y: 1406;};
static: {x: 466; y: 1434;};
static: {x: 193; y: 1494;};
static: {x: 352; y: 1494;};
}`
)

applyPreset(
  { id: 1203, weight: 9, stringName: '1203', tags: ['1200'] },
  `preset {
static: {x: 494; y: 85;};
static: {x: 100; y: 100;};
static: {x: 245; y: 130;};
static: {x: 28; y: 306;};
dynamic_h: {x: 338; y: 415;};
dynamic_h: {x: 58; y: 520;};
static: {x: 340; y: 694;};
empty: {x: 117; y: 698; tickets: false; mobType: 'black-hole'};
static: {x: 245; y: 885;};
wreckable: {x: 58; y: 915;};
dynamic_h: {x: 341; y: 1039;};
dynamic_h: {x: 131; y: 1113;};
empty: {x: 522; y: 1163; tickets: false; mobType: 'black-hole'};
static: {x: 17; y: 1340;};
static: {x: 509; y: 1388;};
static: {x: 354; y: 1496;};
static: {x: 142; y: 1516;};
}`
)

applyPreset(
  { id: 1204, weight: 9, stringName: '1204', tags: ['1200'] },
  `preset {
static: {x: 119; y: 156;};
static: {x: 254; y: 171;};
static: {x: 27; y: 277;};
disposable: {x: 27; y: 328;};
static: {x: 43; y: 468;};
disposable: {x: 359; y: 468;};
static: {x: 455; y: 579;};
static: {x: 116; y: 664;};
disposable: {x: 341; y: 664;};
empty: {x: 509; y: 704; tickets: false; mobType: 'black-hole'};
disposable: {x: 302; y: 854;};
static: {x: 43; y: 871;};
disposable: {x: 412; y: 1020;};
static: {x: 59; y: 1063; tickets: false; };
empty: {x: 165; y: 1063; tickets: false; mobType: 'static'};
static: {x: 176; y: 1063; tickets: false; };
disposable: {x: 485; y: 1210;};
disposable: {x: 483; y: 1384;};
static: {x: 194; y: 1406; tickets: false; boosterType: 'big-fly'};
disposable: {x: 332; y: 1486;};
static: {x: 27; y: 1498;};
static: {x: 504; y: 1516;};
}`
)

applyPreset(
  { id: 1205, weight: 9, stringName: '1205', tags: ['1200'] },
  `preset {
static: {x: 38; y: 101;};
static: {x: 251; y: 186;};
disposable: {x: 485; y: 186;};
disposable: {x: 344; y: 307;};
static: {x: 485; y: 382;};
disposable: {x: 163; y: 402;};
disposable: {x: 62; y: 534;};
static: {x: 391; y: 540;};
empty: {x: 310; y: 611; tickets: false; mobType: 'black-hole'};
static: {x: 436; y: 698;};
disposable: {x: 38; y: 713;};
static: {x: 277; y: 820;};
static: {x: 149; y: 955; tickets: false; mobType: 'dynamic-horizontal-small'};
disposable: {x: 302; y: 1035;};
static: {x: 458; y: 1063;};
disposable: {x: 19; y: 1143;};
disposable: {x: 448; y: 1195;};
dynamic_h: {x: 287; y: 1327;};
static: {x: 505; y: 1414; tickets: false; boosterType: 'big-fly'};
static: {x: 302; y: 1477;};
static: {x: 62; y: 1513;};
}`
)

applyPreset(
  { id: 1206, weight: 9, stringName: '1206', tags: ['1200'] },
  `preset {
static: {x: 490; y: 106;};
wreckable: {x: 56; y: 125;};
static: {x: 319; y: 191;};
static: {x: 118; y: 245;};
static: {x: 46; y: 387;};
static: {x: 232; y: 451;};
static: {x: 433; y: 451;};
empty: {x: 509; y: 563; tickets: false; mobType: 'dynamic-horizontal-middle' };
static: {x: 36; y: 748;};
static: {x: 509; y: 763;};
static: {x: 273; y: 802;};
wreckable: {x: 419; y: 836;};
static: {x: 184; y: 950;};
static: {x: 385; y: 965;};
wreckable: {x: 216; y: 1030;};
static: {x: 46; y: 1040;};
wreckable: {x: 362; y: 1047;};
static: {x: 499; y: 1098;};
empty: {x: 23; y: 1161; tickets: false; mobType: 'dynamic-horizontal-middle' };
static: {x: 371; y: 1317;};
wreckable: {x: 21; y: 1347;};
wreckable: {x: 184; y: 1366;};
wreckable: {x: 330; y: 1393;};
static: {x: 499; y: 1393; tickets: false; boosterType: 'small-fly'};
static: {x: 23; y: 1434;};
static: {x: 263; y: 1473;};
}`
)

applyPreset(
  { id: 1207, weight: 9, stringName: '1207', tags: ['1200'] },
  `preset {
static: {x: 490; y: 106;};
wreckable: {x: 56; y: 125;};
static: {x: 240; y: 155;};
static: {x: 20; y: 245;};
static: {x: 393; y: 328;};
static: {x: 137; y: 387;};
static: {x: 507; y: 466;};
dynamic_v: {x: 46; y: 548; movementRange: [400, 400];  movementDuration: [3000, 3000]; };
static: {x: 330; y: 548;};
empty: {x: 327; y: 705; tickets: false; mobType: 'black-hole'};
dynamic_v: {x: 490; y: 958; movementRange: [-400, -400];  movementDuration: [3000, 3000]; };
wreckable: {x: 216; y: 1030;};
static: {x: 46; y: 1040;};
wreckable: {x: 362; y: 1047;};
static: {x: 499; y: 1068;};
empty: {x: 526; y: 1167; tickets: false; mobType: 'dynamic-horizontal-middle' };
static: {x: 320; y: 1325;};
wreckable: {x: 175; y: 1389;};
static: {x: 499; y: 1393;};
static: {x: 23; y: 1434; tickets: false; boosterType: 'big-fly'};
static: {x: 263; y: 1473;};
wreckable: {x: 434; y: 1503;};
}`
)

applyPreset(
  { id: 1208, weight: 9, stringName: '1208', tags: ['1200'] },
  `preset {
static: {x: 31; y: 95;};
static: {x: 490; y: 106;};
static: {x: 339; y: 121;};
static: {x: 240; y: 155;};
empty: {x: 509; y: 276; tickets: false; mobType: 'black-hole'};
static: {x: 252; y: 332;};
dynamic_h: {x: 107; y: 458;};
dynamic_h: {x: 320; y: 642;};
dynamic_h: {x: 50; y: 757;};
empty: {x: 100; y: 861; tickets: false; mobType: 'black-hole'};
wreckable: {x: 490; y: 892;};
static: {x: 293; y: 927;};
dynamic_h: {x: 339; y: 1068;};
dynamic_h: {x: 119; y: 1227;};
empty: {x: 509; y: 1293; tickets: false; mobType: 'black-hole'};
static: {x: 36; y: 1399;};
static: {x: 240; y: 1451; tickets: false; boosterType: 'big-fly'};
wreckable: {x: 402; y: 1494;};
wreckable: {x: 12; y: 1533;};
}`
)

applyPreset(
  { id: 1209, weight: 9, stringName: '1209', tags: ['1200'] },
  `preset {
static: {x: 308; y: 106;};
wreckable: {x: 459; y: 121;};
wreckable: {x: 126; y: 125;};
static: {x: 136; y: 172;};
static: {x: 490; y: 172;};
static: {x: 22; y: 236;};
static: {x: 88; y: 335;};
dynamic_h: {x: 459; y: 465; tickets: false; };
empty: {x: 509; y: 578; tickets: false; mobType: 'black-hole'};
static: {x: 225; y: 629;};
wreckable: {x: 418; y: 767;};
dynamic_h: {x: 41; y: 835; tickets: false; };
empty: {x: 50; y: 910; tickets: false; mobType: 'dynamic-horizontal-middle' };
dynamic_h: {x: 428; y: 1066; tickets: false; };
empty: {x: 509; y: 1152; tickets: false; mobType: 'black-hole'};
static: {x: 145; y: 1203;};
static: {x: 385; y: 1374;};
wreckable: {x: 22; y: 1404;};
static: {x: 116; y: 1451;};
static: {x: 516; y: 1452; tickets: false; boosterType: 'small-fly'};
wreckable: {x: 271; y: 1527;};
}`
)

applyPreset(
  { id: 1210, weight: 7, stringName: '1210', tags: ['1200'] },
  `preset {
static: {x: 62; y: 91;};
static: {x: 417; y: 182;};
wreckable: {x: 335; y: 270;};
static: {x: 160; y: 374;};
static: {x: 398; y: 485;};
static: {x: 48; y: 572;};
wreckable: {x: 333; y: 612;};
static: {x: 206; y: 719;};
static: {x: 351; y: 837;};
empty: {x: 373; y: 883; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 105; y: 1038;};
static: {x: 333; y: 1235;};
wreckable: {x: 424; y: 1288;};
static: {x: 46; y: 1303;};
static: {x: 474; y: 1436;};
static: {x: 105; y: 1483;};
}`
)

applyPreset(
  { id: 1211, weight: 6, stringName: '1211', tags: ['1200'] },
  `preset {
static: {x: 13; y: 93;};
static: {x: 480; y: 128;};
static: {x: 246; y: 187;};
wreckable: {x: 443; y: 317;};
static: {x: 88; y: 354;};
empty: {x: 344; y: 518; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 70; y: 592;};
static: {x: 225; y: 819;};
wreckable: {x: 423; y: 829;};
static: {x: 423; y: 994;};
wreckable: {x: 70; y: 1127;};
static: {x: 246; y: 1240;};
static: {x: 386; y: 1411;};
static: {x: 56; y: 1426;};
static: {x: 500; y: 1492;};
}`
)

applyPreset(
  { id: 1212, weight: 6, stringName: '1212', tags: ['1200'] },
  `preset {
static: {x: 496; y: 103;};
static: {x: 306; y: 157;};
static: {x: 83; y: 254;};
static: {x: 83; y: 384;};
static: {x: 62; y: 587;};
static: {x: 455; y: 631;};
wreckable: {x: 170; y: 641;};
static: {x: 284; y: 704;};
wreckable: {x: 420; y: 805;};
static: {x: 113; y: 858;};
empty: {x: 320; y: 929; tickets: false; mobType: 'dynamic-horizontal-middle' };
static: {x: 113; y: 1066;};
wreckable: {x: 176; y: 1144;};
static: {x: 306; y: 1207;};
static: {x: 496; y: 1351;};
static: {x: 290; y: 1467;};
static: {x: 62; y: 1503;};
}`
)

applyPreset(
  { id: 1301, weight: 6, stringName: '1301', tags: ['1300'] },
  `preset {
static: {x: 44; y: 119; tickets: false; boosterType: 'big-jump'};
static: {x: 215; y: 128;};
static: {x: 447; y: 158;};
wreckable: {x: 384; y: 205; tickets: false; };
dynamic_v: {x: 44; y: 324; movementRange: [700, 700];  movementDuration: [4500, 4500]; };
static: {x: 263; y: 851;};
dynamic_v: {x: 483; y: 1028;movementRange: [-700, -700]; movementDuration: [4500, 4500];};
wreckable: {x: 171; y: 1082; tickets: false; };
static: {x: 263; y: 1129;};
dynamic_h: {x: 270; y: 1313;};
static: {x: 406; y: 1520;};
static: {x: 44; y: 1535;};
}`
)

applyPreset(
  { id: 1302, weight: 6, stringName: '1302', tags: ['1300'] },
  `preset {
static: {x: 493; y: 82;};
static: {x: 13; y: 118;};
static: {x: 252; y: 133;};
invisible: {x: 379; y: 312; tickets: false;  visible: true};
invisible: {x: 411; y: 596; tickets: false;  visible: false};
dynamic_h: {x: 45; y: 737;};
wreckable: {x: 488; y: 788; tickets: false; };
invisible: {x: 297; y: 881; tickets: false;  visible: false};
invisible: {x: 166; y: 1143; tickets: false;  visible: false};
static: {x: 366; y: 1236;};
wreckable: {x: 109; y: 1367; tickets: false; };
invisible: {x: 13; y: 1431; tickets: false;  visible: false};
static: {x: 493; y: 1491;};
static: {x: 102; y: 1507;};
}`
)

applyPreset(
  { id: 1303, weight: 6, stringName: '1303', tags: ['1300'] },
  `preset {
static: {x: 512; y: 90;};
static: {x: 206; y: 108;};
static: {x: 24; y: 93;};
invisible: {x: 350; y: 191; tickets: false;  visible: true};
static: {x: 35; y: 232;};
invisible: {x: 455; y: 468; tickets: false;  visible: false};
wreckable: {x: 512; y: 539; tickets: false; };
invisible: {x: 90; y: 587; tickets: false;  visible: false};
wreckable: {x: 206; y: 773; tickets: false; };
invisible: {x: 293; y: 820; tickets: false;  visible: false};
dynamic_h: {x: 492; y: 1066;};
dynamic_h: {x: 33; y: 1321; tickets: false; };
static: {x: 435; y: 1451;};
static: {x: 138; y: 1484;};
static: {x: 33; y: 1523;};
}`
)

applyPreset(
  { id: 1304, weight: 6, stringName: '1304', tags: ['1300'] },
  `preset {
static: {x: 206; y: 78;};
wreckable: {x: 65; y: 93; tickets: false; };
shifting: {x: 350; y: 154;};
shifting: {x: 293; y: 285;};
shifting: {x: 263; y: 445;};
shifting: {x: 263; y: 587;};
shifting: {x: 247; y: 718;};
shifting: {x: 179; y: 918;};
shifting: {x: 382; y: 1040;};
shifting: {x: 206; y: 1147;};
shifting: {x: 268; y: 1304;};
shifting: {x: 361; y: 1401;};
static: {x: 28; y: 1466;};
wreckable: {x: 293; y: 1466; tickets: false; };
static: {x: 439; y: 1491;};
static: {x: 133; y: 1516;};
}`
)

applyPreset(
  { id: 1305, weight: 6, stringName: '1305', tags: ['1300'] },
  `preset {
static: {x: 65; y: 93; tickets: false; };
shifting: {x: 350; y: 154;};
shifting: {x: 293; y: 285;};
shifting: {x: 263; y: 445;};
shifting: {x: 350; y: 587;};
shifting: {x: 247; y: 718;};
shifting: {x: 179; y: 918;};
shifting: {x: 325; y: 1040;};
shifting: {x: 206; y: 1147;};
shifting: {x: 268; y: 1304;};
shifting: {x: 268; y: 1446;};
wreckable: {x: 122; y: 1516; tickets: false; };
}`
)

applyPreset(
  { id: 1306, weight: 6, stringName: '1306', tags: ['1300'] },
  `preset {
static: {x: 302; y: 94;};
shifting: {x: 142; y: 207;};
wreckable: {x: 67; y: 311; tickets: false; };
shifting: {x: 193; y: 423;};
wreckable: {x: 359; y: 481;};
shifting: {x: 451; y: 565;};
wreckable: {x: 382; y: 658; tickets: false; };
shifting: {x: 250; y: 718;};
wreckable: {x: 142; y: 805; tickets: false; };
shifting: {x: 85; y: 888;};
wreckable: {x: 85; y: 980; tickets: false; };
shifting: {x: 170; y: 1070;};
wreckable: {x: 284; y: 1177; tickets: false; };
shifting: {x: 307; y: 1289;};
shifting: {x: 250; y: 1417;};
dynamic_h: {x: 341; y: 1497;};
}`
)

applyPreset(
  { id: 1307, weight: 6, stringName: '1307', tags: ['1300'] },
  `preset {
static: {x: 13; y: 93;};
static: {x: 184; y: 93;};
static: {x: 496; y: 93;};
wreckable: {x: 439; y: 138; tickets: false; };
shifting: {x: 56; y: 232;};
wreckable: {x: 464; y: 294; tickets: false; };
shifting: {x: 70; y: 390;};
wreckable: {x: 184; y: 474; tickets: false; };
shifting: {x: 79; y: 548;};
shifting: {x: 85; y: 676;};
shifting: {x: 239; y: 742;};
shifting: {x: 449; y: 912;};
shifting: {x: 449; y: 1076;};
wreckable: {x: 79; y: 1158; tickets: false; };
shifting: {x: 449; y: 1248;};
shifting: {x: 378; y: 1397;};
static: {x: 521; y: 1466;};
static: {x: 28; y: 1486;};
static: {x: 182; y: 1511;};
static: {x: 392; y: 1525;};
}`
)

applyPreset(
  { id: 1308, weight: 6, stringName: '1308', tags: ['1300'] },
  `preset {
static: {x: 499; y: 93;};
static: {x: 206; y: 108;};
static: {x: 206; y: 108;};
static: {x: 326; y: 162;};
invisible: {x: 329; y: 251; tickets: false;  visible: true};
invisible: {x: 483; y: 364; tickets: false;  visible: false};
wreckable: {x: 62; y: 382; tickets: false; };
invisible: {x: 385; y: 538; tickets: false;  visible: false};
wreckable: {x: 492; y: 658; tickets: false; };
invisible: {x: 198; y: 673; tickets: false;  visible: false};
invisible: {x: 59; y: 849; tickets: false;  visible: false};
wreckable: {x: 271; y: 879; tickets: false; };
invisible: {x: 172; y: 1027; tickets: false;  visible: false};
static: {x: 84; y: 1143;};
invisible: {x: 404; y: 1143; tickets: false;  visible: false};
wreckable: {x: 41; y: 1278; tickets: false; };
static: {x: 499; y: 1336;};
dynamic_h: {x: 415; y: 1412;};
wreckable: {x: 499; y: 1496; tickets: false; };
static: {x: 5; y: 1526;};
}`
)

applyPreset(
  { id: 1309, weight: 6, stringName: '1309', tags: ['1300'] },
  `preset {
static: {x: 60; y: 93;};
static: {x: 263; y: 108;};
dynamic_h: {x: 459; y: 169;};
invisible: {x: 195; y: 328; tickets: false;  visible: true};
wreckable: {x: 33; y: 493; tickets: false; };
invisible: {x: 288; y: 493; tickets: false;  visible: false};
invisible: {x: 252; y: 673; tickets: false;  visible: false};
invisible: {x: 320; y: 835; tickets: false;  visible: false};
wreckable: {x: 402; y: 913; tickets: false; };
invisible: {x: 252; y: 997; tickets: false;  visible: false};
invisible: {x: 309; y: 1189; tickets: false;  visible: false};
invisible: {x: 206; y: 1348; tickets: false;  visible: false};
static: {x: 521; y: 1496;};
static: {x: 22; y: 1505;};
static: {x: 206; y: 1520;};
}`
)

applyPreset(
  { id: 1310, weight: 6, stringName: '1310', tags: ['1300'] },
  `preset {
static: {x: 483; y: 87; tickets: false; boosterType: 'small-jump'};
disposable: {x: 263; y: 144; tickets: false; };
disposable: {x: 52; y: 299; tickets: false; };
disposable: {x: 478; y: 343; tickets: false; };
disposable: {x: 83; y: 511; tickets: false; };
disposable: {x: 421; y: 600; tickets: false; };
disposable: {x: 149; y: 712; tickets: false; };
disposable: {x: 478; y: 853; tickets: false; };
disposable: {x: 92; y: 924; tickets: false; };
static: {x: 255; y: 935;};
disposable: {x: 455; y: 1106; tickets: false; };
disposable: {x: 52; y: 1136; tickets: false; };
disposable: {x: 320; y: 1284; tickets: false; };
disposable: {x: 52; y: 1309; tickets: false; };
disposable: {x: 458; y: 1482; tickets: false; };
disposable: {x: 26; y: 1501; tickets: false; };
static: {x: 236; y: 1501;};
}`
)

applyPreset(
  { id: 1311, weight: 6, stringName: '1311', tags: ['1300'] },
  `preset {
static: {x: 263; y: 85; tickets: false; boosterType: 'small-jump'};
disposable: {x: 488; y: 121; tickets: false; };
disposable: {x: 38; y: 223; tickets: false; };
disposable: {x: 391; y: 280; tickets: false; };
disposable: {x: 152; y: 434; tickets: false; };
disposable: {x: 272; y: 591; tickets: false; };
disposable: {x: 55; y: 781; tickets: false; };
disposable: {x: 500; y: 790; tickets: false; };
static: {x: 277; y: 820;};
disposable: {x: 277; y: 1039; tickets: false; };
disposable: {x: 121; y: 1185; tickets: false; };
disposable: {x: 448; y: 1345; tickets: false; };
dynamic_h: {x: 291; y: 1495;};
}`
)

applyPreset(
  { id: 1312, weight: 6, stringName: '1312', tags: ['1300'] },
  `preset {
wreckable: {x: 35; y: 93; tickets: false; };
static: {x: 251; y: 93;};
static: {x: 422; y: 123;};
dynamic_h: {x: 432; y: 451;};
invisible: {x: 422; y: 614; tickets: false;  visible: true};
invisible: {x: 101; y: 821; tickets: false;  visible: false};
dynamic_h: {x: 44; y: 1128;};
invisible: {x: 243; y: 1321; tickets: false;  visible: false};
static: {x: 504; y: 1448;};
wreckable: {x: 308; y: 1518; tickets: false; };
static: {x: 72; y: 1533;};
}`
)

applyPreset(
  { id: 1313, weight: 6, stringName: '1313', tags: ['1300'] },
  `preset {
static: {x: 38; y: 135; tickets: false; };
disposable: {x: 500; y: 135; tickets: false; };
disposable: {x: 356; y: 392; tickets: false; };
disposable: {x: 38; y: 630; tickets: false; };
disposable: {x: 470; y: 630; tickets: false; };
dynamic_h: {x: 277; y: 793;};
disposable: {x: 461; y: 1001; tickets: false; };
disposable: {x: 25; y: 1128; tickets: false; };
empty: {x: 277; y: 1129; tickets: false; mobType: 'dynamic-horizontal-small'};
disposable: {x: 242; y: 1281; tickets: false; };
disposable: {x: 500; y: 1408; tickets: false; };
static: {x: 38; y: 1438;};
static: {x: 283; y: 1521;};
disposable: {x: 62; y: 1535; tickets: false; };
}`
)

applyPreset(
  { id: 1314, weight: 6, stringName: '1314', tags: ['1300'] },
  `preset {
dynamic_h: {x: 230; y: 112;};
dynamic_h: {x: 510; y: 319;};
dynamic_h: {x: 230; y: 557;};
dynamic_h: {x: 27; y: 758;};
dynamic_h: {x: 344; y: 987;};
dynamic_h: {x: 84; y: 1227;};
static: {x: 287; y: 1457;};
dynamic_h: {x: 491; y: 1507;};
}`
)

applyPreset(
  { id: 1315, weight: 6, stringName: '1315', tags: ['1300'] },
  `preset {
dynamic_h: {x: 359; y: 83;};
shifting: {x: 142; y: 207;};
shifting: {x: 193; y: 423;};
shifting: {x: 416; y: 582;};
wreckable: {x: 382; y: 658; tickets: false; };
dynamic_h: {x: 320; y: 829;};
shifting: {x: 434; y: 1051;};
dynamic_h: {x: 79; y: 1270;};
shifting: {x: 181; y: 1476;};
}`
)

applyPreset(
  { id: 1316, weight: 5, stringName: '1316', tags: ['1300'] },
  `preset {
static: {x: 28; y: 83; tickets: false; };
disposable: {x: 466; y: 107; tickets: false; };
shifting: {x: 142; y: 207;};
disposable: {x: 481; y: 262; tickets: false; };
shifting: {x: 193; y: 423;};
shifting: {x: 416; y: 582;};
wreckable: {x: 199; y: 757; tickets: false; };
disposable: {x: 404; y: 817; tickets: false; };
disposable: {x: 21; y: 859; tickets: false; };
shifting: {x: 434; y: 1051;};
disposable: {x: 467; y: 1270; tickets: false; };
disposable: {x: 21; y: 1294; tickets: false; };
shifting: {x: 181; y: 1476;};
}`
)

applyPreset(
  { id: 1317, weight: 5, stringName: '1317', tags: ['1300'] },
  `preset {
dynamic_h: {x: 359; y: 83;};
disposable: {x: 505; y: 194; tickets: false; };
disposable: {x: 50; y: 254; tickets: false; };
shifting: {x: 193; y: 423;};
disposable: {x: 496; y: 628; tickets: false; };
disposable: {x: 22; y: 688; tickets: false; };
disposable: {x: 294; y: 752; tickets: false; };
dynamic_h: {x: 320; y: 829;};
disposable: {x: 49; y: 1081; tickets: false; };
disposable: {x: 282; y: 1096; tickets: false; };
disposable: {x: 460; y: 1126; tickets: false; };
dynamic_h: {x: 79; y: 1270;};
disposable: {x: 377; y: 1446; tickets: false; };
disposable: {x: 67; y: 1476; tickets: false; };
disposable: {x: 517; y: 1515; tickets: false; };
}`
)

applyPreset(
  { id: 1401, weight: 6, stringName: '1401', tags: ['1400'] },
  `preset {
wreckable: {x: 181; y: 100; tickets: false; };
static: {x: 341; y: 160;};
dynamic_h: {x: 105; y: 375;};
empty: {x: 116; y: 466; tickets: false; mobType: 'black-hole'};
dynamic_h: {x: 409; y: 652;};
dynamic_h: {x: 48; y: 776;};
dynamic_h: {x: 489; y: 868;};
empty: {x: 402; y: 942; tickets: false; mobType: 'black-hole'};
dynamic_h: {x: 466; y: 1118;};
dynamic_h: {x: 156; y: 1258;};
dynamic_h: {x: 366; y: 1394;};
static: {x: 252; y: 1455; tickets: false; boosterType: 'small-fly'};
wreckable: {x: 75; y: 1535; tickets: false; };
}`
)

applyPreset(
  { id: 1402, weight: 6, stringName: '1402', tags: ['1400'] },
  `preset {
disposable: {x: 512; y: 91; tickets: false; };
static: {x: 280; y: 121; tickets: false; };
disposable: {x: 71; y: 140; tickets: false; };
disposable: {x: 369; y: 226; tickets: false; };
wreckable: {x: 476; y: 256; tickets: false; };
disposable: {x: 14; y: 301; tickets: false; };
disposable: {x: 165; y: 422; tickets: false; };
empty: {x: 107; y: 579; tickets: false; mobType: 'black-hole'};
disposable: {x: 337; y: 579; tickets: false; };
disposable: {x: 455; y: 618; tickets: false; };
disposable: {x: 155; y: 750; tickets: false; };
disposable: {x: 404; y: 820; tickets: false; };
wreckable: {x: 263; y: 858; tickets: false; };
disposable: {x: 86; y: 983; tickets: false; };
static: {x: 385; y: 1010; tickets: false; };
empty: {x: 495; y: 1010; tickets: false; mobType: 'static'};
static: {x: 503; y: 1010; tickets: false; };
disposable: {x: 223; y: 1170; tickets: false; };
wreckable: {x: 222; y: 1249; tickets: false; };
wreckable: {x: 86; y: 1271; tickets: false; };
disposable: {x: 394; y: 1312; tickets: false; };
static: {x: 11; y: 1439; tickets: false;};
disposable: {x: 499; y: 1474; tickets: false; };
static: {x: 155; y: 1520;};
}`
)

applyPreset(
  { id: 1403, weight: 7, stringName: '1403', tags: ['1400'] },
  `preset {
dynamic_h: {x: 227; y: 89;};
dynamic_h: {x: 434; y: 229;};
dynamic_h: {x: 60; y: 375;};
dynamic_h: {x: 377; y: 539;};
empty: {x: 527; y: 674; tickets: false; mobType: 'black-hole'};
static: {x: 263; y: 763;};
empty: {x: 111; y: 913; tickets: false; mobType: 'black-hole'};
wreckable: {x: 296; y: 930; tickets: false; };
static: {x: 206; y: 964;};
invisible: {x: 68; y: 1138; tickets: false;  visible: true};
invisible: {x: 223; y: 1340; tickets: false;  visible: false};
static: {x: 190; y: 1459; tickets: false;};
invisible: {x: 341; y: 1518; tickets: false;  visible: false};
}`
)

applyPreset(
  { id: 1404, weight: 9, stringName: '1404', tags: ['1400'] },
  `preset {
wreckable: {x: 170; y: 93; tickets: false; };
static: {x: 512; y: 93;};
static: {x: 28; y: 106;};
static: {x: 284; y: 136;};
wreckable: {x: 434; y: 151; tickets: false; };
shifting: {x: 434; y: 326;};
wreckable: {x: 359; y: 481; tickets: false; };
shifting: {x: 476; y: 587;};
shifting: {x: 398; y: 643;};
shifting: {x: 284; y: 805;};
empty: {x: 16; y: 892; tickets: false; mobType: 'dynamic-horizontal-middle' };
shifting: {x: 103; y: 1022;};
shifting: {x: 170; y: 1070;};
wreckable: {x: 284; y: 1177; tickets: false; };
shifting: {x: 256; y: 1286;};
static: {x: 466; y: 1464; tickets: false; boosterType: 'small-fly'};
static: {x: 17; y: 1496;};
}`
)

applyPreset(
  { id: 1405, weight: 9, stringName: '1405', tags: ['1400'] },
  `preset {
static: {x: 37; y: 82;};
static: {x: 499; y: 82;};
dynamic_h: {x: 263; y: 196;};
disposable: {x: 77; y: 387; tickets: false; };
disposable: {x: 464; y: 402; tickets: false; };
disposable: {x: 328; y: 544; tickets: false; };
empty: {x: 528; y: 583; tickets: false; mobType: 'black-hole'};
disposable: {x: 320; y: 734; tickets: false; };
disposable: {x: 54; y: 820; tickets: false; };
disposable: {x: 434; y: 880; tickets: false; };
static: {x: 94; y: 966;};
empty: {x: 440; y: 1065; tickets: false; mobType: 'dynamic-horizontal-small'};
disposable: {x: 208; y: 1111; tickets: false; };
disposable: {x: 385; y: 1281; tickets: false; };
static: {x: 272; y: 1383; tickets: false;};
wreckable: {x: 54; y: 1435; tickets: false; };
disposable: {x: 499; y: 1506; tickets: false; };
}`
)

applyPreset(
  { id: 1406, weight: 9, stringName: '1406', tags: ['1400'] },
  `preset {
disposable: {x: 31; y: 89; tickets: false; };
disposable: {x: 262; y: 110; tickets: false; };
static: {x: 126; y: 125; tickets: false; };
wreckable: {x: 490; y: 155; tickets: false; };
disposable: {x: 294; y: 208; tickets: false; };
empty: {x: 489; y: 276; tickets: false; mobType: 'black-hole'};
disposable: {x: 459; y: 467; tickets: false; };
disposable: {x: 252; y: 526; tickets: false; };
disposable: {x: 78; y: 679; tickets: false; };
empty: {x: 95; y: 861; tickets: false; mobType: 'black-hole'};
wreckable: {x: 490; y: 892; tickets: false; };
disposable: {x: 183; y: 897; tickets: false; };
disposable: {x: 330; y: 1031; tickets: false; };
disposable: {x: 21; y: 1089; tickets: false; };
wreckable: {x: 102; y: 1226; tickets: false; };
disposable: {x: 403; y: 1226; tickets: false; };
empty: {x: 453; y: 1293; tickets: false; mobType: 'black-hole'};
disposable: {x: 21; y: 1341; tickets: false; };
disposable: {x: 220; y: 1421; tickets: false; };
static: {x: 508; y: 1480; tickets: false;};
}`
)

applyPreset(
  { id: 1407, weight: 9, stringName: '1407', tags: ['1400'] },
  `preset {
static: {x: 462; y: 102;};
shifting: {x: 258; y: 244;};
shifting: {x: 178; y: 446;};
wreckable: {x: 337; y: 505; tickets: false; };
shifting: {x: 381; y: 636;};
static: {x: 22; y: 726; tickets: false; };
empty: {x: 126; y: 726; tickets: false; mobType: 'static'};
static: {x: 136; y: 726; tickets: false; };
shifting: {x: 279; y: 916;};
wreckable: {x: 249; y: 968; tickets: false; };
shifting: {x: 381; y: 1046;};
static: {x: 385; y: 1170; tickets: false; };
empty: {x: 498; y: 1170; tickets: false; mobType: 'static'};
static: {x: 508; y: 1170; tickets: false; };
shifting: {x: 263; y: 1323;};
static: {x: 271; y: 1469; tickets: false; boosterType: 'small-fly'};
wreckable: {x: 22; y: 1516; tickets: false; };
}`
)

applyPreset(
  { id: 1408, weight: 9, stringName: '1408', tags: ['1400'] },
  `preset {
static: {x: 263; y: 93;};
static: {x: 489; y: 108;};
static: {x: 489; y: 108;};
wreckable: {x: 203; y: 248; tickets: false; };
invisible: {x: 447; y: 316; tickets: false;  visible: true};
wreckable: {x: 85; y: 433; tickets: false; };
invisible: {x: 298; y: 514; tickets: false;  visible: false};
wreckable: {x: 177; y: 626; tickets: false; };
static: {x: 276; y: 748;};
wreckable: {x: 120; y: 810; tickets: false; };
static: {x: 483; y: 944; tickets: false; mobType: 'dynamic-horizontal-small'};
wreckable: {x: 370; y: 1032; tickets: false; };
invisible: {x: 105; y: 1061; tickets: false;  visible: false};
wreckable: {x: 241; y: 1175; tickets: false; };
invisible: {x: 349; y: 1228; tickets: false;  visible: false};
wreckable: {x: 105; y: 1347; tickets: false; };
static: {x: 298; y: 1442; tickets: false;};
wreckable: {x: 28; y: 1513; tickets: false; };
wreckable: {x: 512; y: 1528; tickets: false; };
}`
)

applyPreset(
  { id: 1409, weight: 9, stringName: '1409', tags: ['1400'] },
  `preset {
static: {x: 16; y: 94;};
shifting: {x: 320; y: 188;};
wreckable: {x: 149; y: 287; tickets: false; };
shifting: {x: 238; y: 394;};
shifting: {x: 287; y: 559;};
empty: {x: 22; y: 612; tickets: false; mobType: 'dynamic-horizontal-middle' };
shifting: {x: 92; y: 784;};
static: {x: 491; y: 862;};
static: {x: 238; y: 937;};
static: {x: 359; y: 937;};
wreckable: {x: 92; y: 1037; tickets: false; };
shifting: {x: 22; y: 1103; tickets: false; mobType: 'dynamic-horizontal-middle' };
empty: {x: 524; y: 1271;};
shifting: {x: 173; y: 1411;};
static: {x: 287; y: 1458; tickets: false;};
}`
)

applyPreset(
  { id: 1410, weight: 9, stringName: '1410', tags: ['1400'] },
  `preset {
static: {x: 31; y: 95;};
static: {x: 490; y: 106;};
static: {x: 339; y: 121;};
wreckable: {x: 126; y: 125; tickets: false; };
static: {x: 240; y: 155;};
dynamic_v: {x: 484; y: 239; movementRange: [532, 532];  movementDuration: [2000, 2000]; };
empty: {x: 285; y: 389; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 256; y: 688;};
dynamic_v: {x: 31; y: 771; movementRange: [-532, -532];  movementDuration: [2000, 2000]; };
dynamic_v: {x: 183; y: 780; movementRange: [393, 393];  movementDuration: [2000, 2000]; };
empty: {x: 81; y: 845; tickets: false; mobType: 'dynamic-horizontal-small'};
empty: {x: 524; y: 977; tickets: false; mobType: 'dynamic-horizontal-small'};
dynamic_v: {x: 320; y: 1173;movementRange: [-393, -393]; movementDuration: [2000, 2000];};
static: {x: 46; y: 1267;};
static: {x: 199; y: 1375; tickets: false; };
empty: {x: 310; y: 1375; tickets: false; mobType: 'static'};
static: {x: 320; y: 1375; tickets: false; };
static: {x: 502; y: 1419; tickets: false;};
wreckable: {x: 81; y: 1449; tickets: false; };
wreckable: {x: 12; y: 1533; tickets: false; };
}`
)

applyPreset(
  { id: 1411, weight: 9, stringName: '1411', tags: ['1400'] },
  `preset {
static: {x: 81; y: 91; tickets: false; };
disposable: {x: 384; y: 116; tickets: false; };
disposable: {x: 320; y: 204; tickets: false; };
disposable: {x: 478; y: 304; tickets: false; };
empty: {x: 182; y: 422; tickets: false; mobType: 'dynamic-horizontal-small'};
disposable: {x: 441; y: 540; tickets: false; };
disposable: {x: 39; y: 653; tickets: false; };
disposable: {x: 263; y: 698; tickets: false; };
disposable: {x: 478; y: 840; tickets: false; };
disposable: {x: 239; y: 928; tickets: false; };
disposable: {x: 58; y: 1040; tickets: false; };
empty: {x: 397; y: 1130; tickets: false; mobType: 'dynamic-horizontal-small'};
disposable: {x: 81; y: 1198; tickets: false; };
disposable: {x: 125; y: 1340; tickets: false; };
disposable: {x: 421; y: 1370; tickets: false; };
disposable: {x: 296; y: 1444; tickets: false; };
disposable: {x: 58; y: 1459; tickets: false; };
disposable: {x: 498; y: 1474; tickets: false; };
}`
)

applyPreset(
  { id: 1412, weight: 9, stringName: '1412', tags: ['1400'] },
  `preset {
disposable: {x: 81; y: 91; tickets: false; };
static: {x: 301; y: 121; tickets: false; };
disposable: {x: 320; y: 204; tickets: false; };
disposable: {x: 478; y: 304; tickets: false; };
disposable: {x: 195; y: 447; tickets: false; };
disposable: {x: 441; y: 540; tickets: false; };
disposable: {x: 67; y: 591; tickets: false; };
empty: {x: 67; y: 775; tickets: false; mobType: 'dynamic-horizontal-middle' };
disposable: {x: 115; y: 951; tickets: false; };
disposable: {x: 434; y: 1025; tickets: false; };
disposable: {x: 278; y: 1100; tickets: false; };
disposable: {x: 81; y: 1213; tickets: false; };
disposable: {x: 125; y: 1340; tickets: false; };
disposable: {x: 421; y: 1364; tickets: false; };
disposable: {x: 296; y: 1444; tickets: false; };
disposable: {x: 498; y: 1474; tickets: false; };
disposable: {x: 58; y: 1504; tickets: false; };
}`
)

applyPreset(
  { id: 1501, weight: 10, stringName: '1501', tags: ['1500'] },
  `preset {
static: {x: 457; y: 84; tickets: false; };
disposable: {x: 214; y: 217; tickets: false; };
disposable: {x: 52; y: 381; tickets: false; };
disposable: {x: 413; y: 381; tickets: false; };
disposable: {x: 448; y: 541; tickets: false; };
disposable: {x: 206; y: 639; tickets: false; };
disposable: {x: 140; y: 790; tickets: false; };
disposable: {x: 428; y: 844; tickets: false; };
disposable: {x: 197; y: 1021; tickets: false; };
disposable: {x: 474; y: 1098; tickets: false; };
disposable: {x: 170; y: 1230; tickets: false; };
disposable: {x: 459; y: 1352; tickets: false; };
disposable: {x: 254; y: 1400; tickets: false; };
disposable: {x: 37; y: 1531; tickets: false; };
}`
)

applyPreset(
  { id: 1502, weight: 10, stringName: '1502', tags: ['1500'] },
  `preset {
disposable: {x: 254; y: 84; tickets: false; };
disposable: {x: 239; y: 411; tickets: false; };
invisible: {x: 434; y: 608; tickets: false;  visible: true};
disposable: {x: 239; y: 778; tickets: false; };
invisible: {x: 29; y: 896; tickets: false;  visible: false};
invisible: {x: 421; y: 1078; tickets: false;  visible: false};
disposable: {x: 227; y: 1215; tickets: false; };
invisible: {x: 384; y: 1390; tickets: false;  visible: false};
invisible: {x: 37; y: 1531; tickets: false;  visible: false};
}`
)

applyPreset(
  { id: 1503, weight: 10, stringName: '1503', tags: ['1500'] },
  `preset {
static: {x: 81; y: 91; tickets: false; };
shifting: {x: 320; y: 204; tickets: false; };
shifting: {x: 478; y: 345; tickets: false; };
shifting: {x: 399; y: 540; tickets: false; };
shifting: {x: 115; y: 663; tickets: false; };
shifting: {x: 38; y: 778; tickets: false; };
shifting: {x: 239; y: 928; tickets: false; };
shifting: {x: 421; y: 1078; tickets: false; };
shifting: {x: 513; y: 1278; tickets: false; };
shifting: {x: 384; y: 1435; tickets: false; };
shifting: {x: 24; y: 1523; tickets: false; };
}`
)

applyPreset(
  { id: 1504, weight: 10, stringName: '1504', tags: ['1500'] },
  `preset {
static: {x: 70; y: 107; tickets: false; };
disposable: {x: 442; y: 210; tickets: false; };
disposable: {x: 158; y: 439; tickets: false; };
disposable: {x: 87; y: 651; tickets: false; };
disposable: {x: 349; y: 826; tickets: false; };
disposable: {x: 429; y: 924; tickets: false; };
disposable: {x: 127; y: 1172; tickets: false; };
disposable: {x: 215; y: 1308; tickets: false; };
disposable: {x: 442; y: 1488; tickets: false; };
}`
)

applyPreset(
  { id: 1505, weight: 10, stringName: '1505', tags: ['1500'] },
  `preset {
static: {x: 494; y: 85;};
static: {x: 100; y: 100;};
static: {x: 245; y: 130;};
static: {x: 28; y: 306;};
dynamic_h: {x: 338; y: 415;};
dynamic_h: {x: 58; y: 520;};
static: {x: 340; y: 694;};
empty: {x: 117; y: 698; tickets: false; mobType: 'black-hole'};
static: {x: 245; y: 885;};
wreckable: {x: 58; y: 915;};
dynamic_h: {x: 341; y: 1039;};
dynamic_h: {x: 131; y: 1113;};
empty: {x: 522; y: 1163; tickets: false; mobType: 'black-hole'};
static: {x: 17; y: 1340;};
static: {x: 509; y: 1388;};
static: {x: 354; y: 1496;};
static: {x: 142; y: 1516;};
}`
)

applyPreset(
  { id: 1506, weight: 10, stringName: '1506', tags: ['1500'] },
  `preset {
static: {x: 38; y: 101;};
static: {x: 251; y: 186;};
wreckable: {x: 33; y: 343;};
static: {x: 485; y: 382;};
static: {x: 391; y: 540;};
empty: {x: 310; y: 611; tickets: false; mobType: 'black-hole'};
static: {x: 436; y: 698;};
static: {x: 277; y: 820;};
static: {x: 149; y: 955; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 458; y: 1063;};
wreckable: {x: 263; y: 1195;};
dynamic_h: {x: 287; y: 1327;};
static: {x: 302; y: 1477;};
static: {x: 62; y: 1513;};
}`
)

applyPreset(
  { id: 1507, weight: 10, stringName: '1507', tags: ['1500'] },
  `preset {
static: {x: 31; y: 95;};
static: {x: 490; y: 106;};
static: {x: 339; y: 121;};
empty: {x: 509; y: 276; tickets: false; mobType: 'black-hole'};
static: {x: 252; y: 332;};
dynamic_h: {x: 107; y: 458;};
dynamic_h: {x: 320; y: 642;};
dynamic_h: {x: 50; y: 757;};
empty: {x: 100; y: 861; tickets: false; mobType: 'black-hole'};
dynamic_h: {x: 339; y: 1068;};
dynamic_h: {x: 119; y: 1227;};
empty: {x: 509; y: 1293; tickets: false; mobType: 'black-hole'};
static: {x: 240; y: 1451; tickets: false;};
wreckable: {x: 402; y: 1494;};
}`
)

applyPreset(
  { id: 1508, weight: 10, stringName: '1508', tags: ['1500'] },
  `preset {
static: {x: 119; y: 156;};
static: {x: 254; y: 171;};
static: {x: 27; y: 277;};
static: {x: 43; y: 468;};
static: {x: 455; y: 579;};
static: {x: 116; y: 664;};
empty: {x: 509; y: 704; tickets: false; mobType: 'black-hole'};
static: {x: 43; y: 871;};
static: {x: 59; y: 1063; tickets: false; };
empty: {x: 165; y: 1063; tickets: false; mobType: 'static'};
static: {x: 176; y: 1063; tickets: false; };
static: {x: 194; y: 1406; tickets: false;};
static: {x: 27; y: 1498;};
static: {x: 504; y: 1516;};
}`
)

applyPreset(
  { id: 1509, weight: 10, stringName: '1509', tags: ['1500'] },
  `preset {
static: {x: 263; y: 93;};
static: {x: 489; y: 108;};
static: {x: 489; y: 108;};
invisible: {x: 447; y: 316; tickets: false;  visible: true};
invisible: {x: 298; y: 514; tickets: false;  visible: false};
static: {x: 276; y: 748;};
static: {x: 483; y: 944; tickets: false; mobType: 'dynamic-horizontal-small'};
invisible: {x: 105; y: 1061; tickets: false;  visible: false};
invisible: {x: 349; y: 1228; tickets: false;  visible: false};
static: {x: 298; y: 1442; tickets: false;};
}`
)

applyPreset(
  { id: 1510, weight: 10, stringName: '1510', tags: ['1500'] },
  `preset {
dynamic_h: {x: 227; y: 89;};
dynamic_h: {x: 434; y: 229;};
dynamic_h: {x: 60; y: 375;};
dynamic_h: {x: 377; y: 539;};
empty: {x: 527; y: 674; tickets: false; mobType: 'black-hole'};
invisible: {x: 263; y: 763;tickets: false;  visible: true};
empty: {x: 111; y: 913; tickets: false; mobType: 'black-hole'};
invisible: {x: 206; y: 964;tickets: false;  visible: false};
invisible: {x: 68; y: 1138; tickets: false;  visible: false};
invisible: {x: 223; y: 1340; tickets: false;  visible: false};
invisible: {x: 341; y: 1518; tickets: false;  visible: false};
}`
)

applyPreset(
  { id: 1501, weight: 10, stringName: '1501', tags: ['1500'] },
  `preset {
static: {x: 457; y: 84; tickets: false; };
disposable: {x: 214; y: 217; tickets: false; };
disposable: {x: 52; y: 381; tickets: false; };
disposable: {x: 413; y: 381; tickets: false; };
disposable: {x: 448; y: 541; tickets: false; };
disposable: {x: 206; y: 639; tickets: false; };
disposable: {x: 140; y: 790; tickets: false; };
disposable: {x: 428; y: 844; tickets: false; };
disposable: {x: 197; y: 1021; tickets: false; };
disposable: {x: 474; y: 1098; tickets: false; };
disposable: {x: 170; y: 1230; tickets: false; };
disposable: {x: 459; y: 1352; tickets: false; };
disposable: {x: 254; y: 1400; tickets: false; };
disposable: {x: 37; y: 1531; tickets: false; };
}`
)

applyPreset(
  { id: 1502, weight: 7, stringName: '1502', tags: ['1500'] },
  `preset {
disposable: {x: 254; y: 84; tickets: false; };
disposable: {x: 239; y: 411; tickets: false; };
invisible: {x: 434; y: 608; tickets: false;  visible: true};
disposable: {x: 239; y: 778; tickets: false; };
invisible: {x: 29; y: 896; tickets: false;  visible: false};
invisible: {x: 421; y: 1078; tickets: false;  visible: false};
disposable: {x: 227; y: 1215; tickets: false; };
invisible: {x: 384; y: 1390; tickets: false;  visible: false};
invisible: {x: 37; y: 1531; tickets: false;  visible: false};
}`
)

applyPreset(
  { id: 1503, weight: 10, stringName: '1503', tags: ['1500'] },
  `preset {
static: {x: 81; y: 91; tickets: false; };
shifting: {x: 320; y: 204; tickets: false; };
shifting: {x: 478; y: 345; tickets: false; };
shifting: {x: 399; y: 540; tickets: false; };
shifting: {x: 115; y: 663; tickets: false; };
shifting: {x: 38; y: 778; tickets: false; };
shifting: {x: 239; y: 928; tickets: false; };
shifting: {x: 421; y: 1078; tickets: false; };
shifting: {x: 513; y: 1278; tickets: false; };
shifting: {x: 384; y: 1435; tickets: false; };
shifting: {x: 24; y: 1523; tickets: false; };
}`
)

applyPreset(
  { id: 1504, weight: 7, stringName: '1504', tags: ['1500'] },
  `preset {
static: {x: 70; y: 107; tickets: false; };
disposable: {x: 442; y: 210; tickets: false; };
disposable: {x: 158; y: 439; tickets: false; };
disposable: {x: 87; y: 651; tickets: false; };
disposable: {x: 349; y: 826; tickets: false; };
disposable: {x: 429; y: 924; tickets: false; };
disposable: {x: 127; y: 1172; tickets: false; };
disposable: {x: 215; y: 1308; tickets: false; };
disposable: {x: 442; y: 1488; tickets: false; };
}`
)

applyPreset(
  { id: 1505, weight: 7, stringName: '1505', tags: ['1500'] },
  `preset {
static: {x: 494; y: 85;};
static: {x: 100; y: 100;};
static: {x: 245; y: 130;};
static: {x: 28; y: 306;};
dynamic_h: {x: 338; y: 415;};
dynamic_h: {x: 58; y: 520;};
static: {x: 340; y: 694;};
empty: {x: 117; y: 698; tickets: false; mobType: 'black-hole'};
static: {x: 245; y: 885;};
wreckable: {x: 58; y: 915;};
dynamic_h: {x: 341; y: 1039;};
dynamic_h: {x: 131; y: 1113;};
empty: {x: 522; y: 1163; tickets: false; mobType: 'black-hole'};
static: {x: 17; y: 1340;};
static: {x: 509; y: 1388;};
static: {x: 354; y: 1496;};
static: {x: 142; y: 1516;};
}`
)

applyPreset(
  { id: 1506, weight: 7, stringName: '1506', tags: ['1500'] },
  `preset {
static: {x: 38; y: 101;};
static: {x: 251; y: 186;};
wreckable: {x: 33; y: 343;};
static: {x: 485; y: 382;};
static: {x: 391; y: 540;};
empty: {x: 310; y: 611; tickets: false; mobType: 'black-hole'};
static: {x: 436; y: 698;};
static: {x: 277; y: 820;};
static: {x: 149; y: 955; tickets: false; mobType: 'dynamic-horizontal-small'};
static: {x: 458; y: 1063;};
wreckable: {x: 263; y: 1195;};
dynamic_h: {x: 287; y: 1327;};
static: {x: 302; y: 1477;};
static: {x: 62; y: 1513;};
}`
)

applyPreset(
  { id: 1507, weight: 7, stringName: '1507', tags: ['1500'] },
  `preset {
static: {x: 31; y: 95;};
static: {x: 490; y: 106;};
static: {x: 339; y: 121;};
empty: {x: 509; y: 276; tickets: false; mobType: 'black-hole'};
static: {x: 252; y: 332;};
dynamic_h: {x: 107; y: 458;};
dynamic_h: {x: 320; y: 642;};
dynamic_h: {x: 50; y: 757;};
empty: {x: 100; y: 861; tickets: false; mobType: 'black-hole'};
dynamic_h: {x: 339; y: 1068;};
dynamic_h: {x: 119; y: 1227;};
empty: {x: 509; y: 1293; tickets: false; mobType: 'black-hole'};
static: {x: 240; y: 1451; tickets: false;};
wreckable: {x: 402; y: 1494;};
}`
)

applyPreset(
  { id: 1508, weight: 7, stringName: '1508', tags: ['1500'] },
  `preset {
static: {x: 119; y: 156;};
static: {x: 254; y: 171;};
static: {x: 27; y: 277;};
static: {x: 43; y: 468;};
static: {x: 455; y: 579;};
static: {x: 116; y: 664;};
empty: {x: 509; y: 704; tickets: false; mobType: 'black-hole'};
static: {x: 43; y: 871;};
static: {x: 59; y: 1063; tickets: false; };
empty: {x: 165; y: 1063; tickets: false; mobType: 'static'};
static: {x: 176; y: 1063; tickets: false; };
static: {x: 194; y: 1406; tickets: false;};
static: {x: 27; y: 1498;};
static: {x: 504; y: 1516;};
}`
)

applyPreset(
  { id: 1509, weight: 7, stringName: '1509', tags: ['1500'] },
  `preset {
static: {x: 263; y: 93;};
static: {x: 489; y: 108;};
static: {x: 489; y: 108;};
invisible: {x: 447; y: 316; tickets: false;  visible: true};
invisible: {x: 298; y: 514; tickets: false;  visible: false};
static: {x: 276; y: 748;};
static: {x: 483; y: 944; tickets: false; mobType: 'dynamic-horizontal-small'};
invisible: {x: 105; y: 1061; tickets: false;  visible: false};
invisible: {x: 349; y: 1228; tickets: false;  visible: false};
static: {x: 298; y: 1442; tickets: false;};
}`
)

applyPreset(
  { id: 1510, weight: 7, stringName: '1510', tags: ['1500'] },
  `preset {
dynamic_h: {x: 227; y: 89;};
dynamic_h: {x: 434; y: 229;};
dynamic_h: {x: 60; y: 375;};
dynamic_h: {x: 377; y: 539;};
empty: {x: 527; y: 674; tickets: false; mobType: 'black-hole'};
invisible: {x: 263; y: 763;tickets: false;  visible: true};
empty: {x: 111; y: 913; tickets: false; mobType: 'black-hole'};
invisible: {x: 206; y: 964;tickets: false;  visible: false};
invisible: {x: 68; y: 1138; tickets: false;  visible: false};
invisible: {x: 223; y: 1340; tickets: false;  visible: false};
invisible: {x: 341; y: 1518; tickets: false;  visible: false};
}`
)

applyPreset(
  { id: 1511, weight: 7, stringName: '1511', tags: ['1500'] },
  `preset {
static: {x: 426; y: 116;};
static: {x: 54; y: 131;};
dynamic_h: {x: 245; y: 189;};
static: {x: 501; y: 280;};
static: {x: 96; y: 348;};
static: {x: 272; y: 512;};
dynamic_h: {x: 22; y: 619;};
dynamic_h: {x: 483; y: 705;};
static: {x: 39; y: 787;};
empty: {x: 85; y: 840; tickets: false; mobType: 'dynamic-horizontal-middle' };
dynamic_h: {x: 483; y: 1040;};
dynamic_h: {x: 60; y: 1215;};
static: {x: 461; y: 1318;};
static: {x: 312; y: 1401;};
static: {x: 504; y: 1458;};
static: {x: 39; y: 1500;};
}`
)

applyPreset(
  { id: 1512, weight: 7, stringName: '1512', tags: ['1500'] },
  `preset {
static: {x: 43; y: 150; tickets: false; boosterType: 'small-jump'};
static: {x: 245; y: 184;};
static: {x: 470; y: 240;};
disposable: {x: 258; y: 367;};
static: {x: 44; y: 438;};
disposable: {x: 359; y: 468;};
static: {x: 483; y: 549;};
disposable: {x: 18; y: 591;};
empty: {x: 520; y: 664; tickets: false; mobType: 'black-hole'};
disposable: {x: 101; y: 745;};
static: {x: 277; y: 820;};
disposable: {x: 44; y: 924;};
disposable: {x: 470; y: 924;};
static: {x: 470; y: 1069;};
static: {x: 258; y: 1084;};
disposable: {x: 62; y: 1173;};
dynamic_h: {x: 272; y: 1268;};
disposable: {x: 483; y: 1384;};
static: {x: 38; y: 1438;};
disposable: {x: 163; y: 1475;};
static: {x: 283; y: 1521;};
}`
)

applyPreset(
  { id: 1513, weight: 10, stringName: '1513', tags: ['1500'] },
  `preset {
static: {x: 13; y: 76;};
static: {x: 500; y: 91;};
static: {x: 335; y: 132;};
disposable: {x: 220; y: 255; tickets: false; };
static: {x: 263; y: 343;};
disposable: {x: 63; y: 351; tickets: false; };
disposable: {x: 177; y: 483; tickets: false; };
static: {x: 17; y: 523;};
disposable: {x: 263; y: 600; tickets: false; };
static: {x: 506; y: 725;};
disposable: {x: 403; y: 772; tickets: false; };
empty: {x: 328; y: 901; tickets: false; mobType: 'black-hole'};
static: {x: 63; y: 937;};
disposable: {x: 500; y: 954; tickets: false; };
disposable: {x: 17; y: 1090; tickets: false; };
disposable: {x: 392; y: 1113; tickets: false; };
disposable: {x: 184; y: 1128; tickets: false; };
dynamic_h: {x: 470; y: 1231;};
disposable: {x: 274; y: 1291; tickets: false; };
static: {x: 483; y: 1360;};
static: {x: 17; y: 1408;};
static: {x: 245; y: 1424;};
static: {x: 413; y: 1519;};
}`
)

// END
copyNewConfig()
applyNewConfig()
