server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    location ~ ^/(game|menu)(/.*)?$ {
        return 302 /; # Temporary redirect
    }

    location / {
        root /app/dist;
        try_files $uri $uri/ /index.html;

        # Allow to cache in CDN but revalidate each time
        add_header Cache-Control 'public, s-maxage=86400, max-age=0, must-revalidate';
        expires 0;
    }

    # Cache static files for one year
    location ~* \.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|json|txt)$ {
        root /app/dist;
        try_files $uri $uri/ /index.html;

        # Cache for one year, with Cloudflare cache (s-maxage) for one year as well
        add_header Cache-Control "public, max-age=31536000, s-maxage=31536000, immutable";
        expires 1y;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
