{"name": "unijump-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "compile-proto": "node compile-protos.mjs", "dev": "vite", "en-locale": "node scripts/json-to-ts.cjs src/i18n/en/en.json", "eslint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "json-to-ts": "node scripts/json-to-ts.cjs", "lint": "npm run eslint", "openapi-ts": "openapi-ts && prettier -w ./src/services/openapi", "precommit": "lint-staged && npm run lint", "prepare": "husky install", "prepush": "", "preview": "vite preview", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "test:unit": "vitest", "type-check": "vue-tsc --build --force"}, "dependencies": {"@datadog/browser-logs": "^5.35.1", "@datadog/browser-rum": "^5.35.1", "@esotericsoftware/spine-canvas": "^4.2.75", "@esotericsoftware/spine-canvaskit": "^4.2.75", "@esotericsoftware/spine-core": "^4.2.75", "@esotericsoftware/spine-phaser": "^4.2.75", "@esotericsoftware/spine-pixi": "^4.2.62", "@esotericsoftware/spine-player": "^4.2.75", "@esotericsoftware/spine-threejs": "^4.2.75", "@esotericsoftware/spine-webgl": "^4.2.75", "@floating-ui/vue": "^1.1.6", "@glidejs/glide": "^3.7.1", "@headlessui/vue": "^1.7.23", "@hey-api/client-fetch": "^0.4.4", "@tanstack/vue-query": "^5.67.1", "@telegram-apps/sdk": "^2.11.3", "@tonconnect/ui": "^2.0.11", "@vueuse/core": "^9.13.0", "bitecs": "^0.3.40", "bn.js": "^5.2.1", "dayjs": "^1.11.13", "disable-devtool": "^0.3.8", "eventemitter3": "^5.0.1", "pbf": "^4.0.1", "phaser": "^3.88.2", "phaser3-nineslice": "^0.5.0", "pinia": "^3.0.1", "protocol-buffers-schema": "^3.6.0", "sweetalert2": "^11.17.2", "vue": "^3.5.13", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vue3-roulette": "^0.3.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@hey-api/openapi-ts": "^0.53.12", "@rushstack/eslint-patch": "^1.10.5", "@tsconfig/node20": "^20.1.4", "@types/glidejs__glide": "^3.6.6", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.9", "@types/node-fetch": "^2.6.12", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "axios": "^1.8.1", "cypress": "^13.17.0", "eruda": "^3.4.1", "eslint": "^8.57.1", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-vue": "^9.33.0", "husky": "^8.0.3", "jsdom": "^24.1.3", "kubernetes-models": "^4.4.2", "lint-staged": "^15.4.3", "npm-run-all2": "^6.2.6", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "rollup-obfuscator": "^4.1.1", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.85.1", "start-server-and-test": "^2.0.10", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "vite": "^6.2.0", "vitest": "^2.1.9", "vue-tsc": "^2.2.8"}}