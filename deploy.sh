#!/usr/bin/env bash

kubectl version
cd deploy
npm i

kubectl --namespace=$KUBE_NAMESPACE get pods
#!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
#!!!! before create deploy, token with name "gitlab-deploy-token" !!!!!!!!!!!!!!!!!!!!!!
#!!!! can find in project Settings -> Repository -> Deploy tokens !!!!!!!!!!!!!!!!!!!!!!
#!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
kubectl create secret docker-registry gitlab-registry-uni-front \
  --docker-server=$CI_REGISTRY \
  --docker-username=$CI_DEPLOY_USER \
  --docker-password=$CI_DEPLOY_PASSWORD \
  --docker-email=<EMAIL> \
  --namespace=$KUBE_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
npx ts-node ./index.ts
for f in ./configs/*.json; do kubectl --namespace=$KUBE_NAMESPACE apply -f $f; done
